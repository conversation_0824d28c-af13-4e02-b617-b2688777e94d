const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const webpack = require('webpack'); // Required for ProvidePlugin

const isProduction = process.env.NODE_ENV === 'production';

// 设置不同环境的输出路径
const outputPath = isProduction 
    ? path.resolve(__dirname, '../backend/agent_analyzer', 'static', 'dist')
    : path.resolve(__dirname, '../dist');

module.exports = {
    mode: isProduction ? 'production' : 'development',
    context: path.resolve(__dirname, '../frontend'),
    entry: {
        main: './src/shared/services/main.js',
        contractAnalysisPage: './src/modules/contract-risk-analysis/pages/main.js',
        agentRelationshipPage: './src/modules/agent-relationship/pages/main.js',
        contractIntegrationPage: './src/modules/link-risk-analysis/pages/contract-integration-main.js',
        userAnalysisPage: './src/modules/user-analysis/pages/main-refactored.js',
        loginPage: './src/auth/login.js',  // 添加登录页面入口
        twofaVerifyPage: './src/auth/2fa-verify.js',  // 添加2FA验证页面入口
        userManagementPage: './src/modules/admin/pages/user-management-main.js',  // 用户管理页面
        systemDashboardPage: './src/modules/admin/pages/system-dashboard-main.js',  // 系统控制台页面
        permissionMatrixPage: './src/modules/admin/pages/permission-matrix-main.js',  // 权限矩阵页面
        configManagementPage: './src/modules/admin/pages/config-management-main.js'  // 配置管理页面
    },
    output: {
        filename: 'js/[name].[contenthash].js',
        path: outputPath,
        publicPath: isProduction ? '/static/dist/' : '/',
        clean: true,
    },
    devtool: isProduction ? 'source-map' : 'eval-source-map',
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env']
                    }
                }
            },
            {
                test: /\.css$/,
                use: [
                    isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
                    'css-loader'
                ]
            },
            {
                test: /\.(png|svg|jpg|jpeg|gif)$/i,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/images/[hash][ext][query]'
                }
            },
            {
                test: /\.(woff|woff2|eot|ttf|otf)$/i,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/fonts/[hash][ext][query]'
                }
            }
        ]
    },
    plugins: [
        new HtmlWebpackPlugin({
            template: './src/modules/agent-relationship/pages/index.html',
            filename: 'index.html', 
            chunks: ['main', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/modules/contract-risk-analysis/pages/contract-analysis.html',
            filename: 'contract_analysis.html', 
            chunks: ['contractAnalysisPage', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/modules/agent-relationship/pages/index.html',
            filename: 'agent_relationship.html', 
            chunks: ['agentRelationshipPage', 'vendors']
        }),

        new HtmlWebpackPlugin({
            template: './src/modules/link-risk-analysis/pages/contract-integration.html',
            filename: 'contract_integration.html', 
            chunks: ['contractIntegrationPage', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/modules/user-analysis/pages/user-analysis.html',
            filename: 'user_analysis.html', 
            chunks: ['userAnalysisPage', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/auth/login.html',
            filename: 'login.html',
            chunks: ['loginPage']  // 只使用loginPage，不需要vendors
        }),
        new HtmlWebpackPlugin({
            template: './src/auth/2fa-verify.html',
            filename: '2fa-verify.html',
            chunks: ['twofaVerifyPage']  // 只使用twofaVerifyPage，不需要vendors
        }),
        new HtmlWebpackPlugin({
            template: './src/modules/admin/pages/user-management.html',
            filename: 'user-management.html',
            chunks: ['userManagementPage', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/modules/admin/pages/system-dashboard.html',
            filename: 'system-dashboard.html',
            chunks: ['systemDashboardPage', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/modules/admin/pages/permission-matrix.html',
            filename: 'permission-matrix.html',
            chunks: ['permissionMatrixPage', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/modules/admin/pages/config-management.html',
            filename: 'config-management.html',
            chunks: ['configManagementPage', 'vendors']
        }),
        new MiniCssExtractPlugin({
            filename: 'css/[name].[contenthash].css',
        }),
        new webpack.ProvidePlugin({
            $: 'jquery',
            jQuery: 'jquery',
            Popper: ['@popperjs/core', 'default'],
            bootstrap: 'bootstrap'
        }),
    ],
    optimization: {
        minimize: isProduction,
        minimizer: [
            new TerserPlugin(),
        ],
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/](bootstrap|echarts|chart\.js|@babel|jquery|@popperjs|bootstrap-icons)[\\/]/,
                    name: 'vendors',
                    chunks: 'all',
                    priority: 10
                },
            }
        }
    },
    devServer: {
        static: [
            {
                directory: path.resolve(__dirname, '../dist'),
            },
            {
                directory: path.resolve(__dirname, '../frontend'),
                publicPath: '/',
            }
        ],
        historyApiFallback: {
            rewrites: [
                { from: /\/login/, to: '/login.html' },
                { from: /\/2fa-verify/, to: '/2fa-verify.html' },
                { from: /\/contract_analysis_page/, to: '/contract_analysis.html' },
                { from: /\/contract-integration/, to: '/contract_integration.html' },
                { from: /\/agent_relationship/, to: '/agent_relationship.html' },
                { from: /\/user_analysis/, to: '/user_analysis.html' },
                { from: /\/user-management/, to: '/user-management.html' },
                { from: /\/system-dashboard/, to: '/system-dashboard.html' },
                { from: /\/permission-matrix/, to: '/permission-matrix.html' },
                { from: /./, to: '/index.html' }
            ]
        },
        compress: true,
        port: 3000,
        host: '0.0.0.0',  // 允许外部访问
        hot: true,
        open: false,
        devMiddleware: {
            writeToDisk: true,
        },
        proxy: [
            {
                context: ['/api'],
                target: 'http://127.0.0.1:5005',  // 使用127.0.0.1而不是localhost
                changeOrigin: true,
                pathRewrite: { '^/api': '/api' },
                secure: false,
                logLevel: 'debug',
                cookieDomainRewrite: false,  // 不重写cookie域名
                timeout: 120000,  // 120秒超时（批量查询需要更长时间）
                proxyTimeout: 120000,  // 代理超时
                headers: {
                    'Connection': 'keep-alive'
                },
                onError: function(err, req, res) {
                    console.error('代理错误:', err.message);
                }
            },
            {
                // 代理2FA相关的页面路由到后端
                context: ['/2fa-verify', '/login', '/2fa-setup'],
                target: 'http://127.0.0.1:5005',
                changeOrigin: true,
                secure: false,
                logLevel: 'debug',
                timeout: 120000,
                proxyTimeout: 120000,
                headers: {
                    'Connection': 'keep-alive'
                },
                onError: function(err, req, res) {
                    console.error('2FA页面代理错误:', err.message);
                    res.writeHead(500, {
                        'Content-Type': 'application/json',
                    });
                    res.end(JSON.stringify({
                        error: '后端服务连接失败',
                        message: err.message
                    }));
                }
            }
        ]
    }
}; 
# =============================================================================
# 风险链路分析工具 - 生产环境配置文件
# 使用方法: source production.env 或在启动脚本中加载
# =============================================================================

# 服务器配置
# 监听地址 - 0.0.0.0 表示监听所有网络接口，localhost 仅监听本地
SERVER_HOST=0.0.0.0

# 端口配置
FRONTEND_PORT=3000
BACKEND_PORT=5005

# 如果需要使用不同的端口，可以修改上面的值
# 例如：BACKEND_PORT=8080

# =============================================================================
# Python虚拟环境配置
# =============================================================================

# 虚拟环境名称
VENV_NAME=risk_analysis_env

# 虚拟环境路径（相对于项目根目录）
VENV_PATH=./venv

# Python解释器版本
PYTHON_VERSION=python3

# 如果系统中有多个Python版本，可以指定具体版本
# 例如：PYTHON_VERSION=python3.9

# =============================================================================
# Redis配置
# =============================================================================

# Redis服务器地址
REDIS_HOST=localhost

# Redis端口
REDIS_PORT=6379

# Redis密码（如果没有密码则留空）
REDIS_PASSWORD=

# Redis数据库编号（0-15）
REDIS_DB=0

# 生产环境Redis密码（如果与开发环境不同）
REDIS_PROD_PASSWORD=

# =============================================================================
# 应用程序环境配置
# =============================================================================

# Flask环境
FLASK_ENV=production

# Node.js环境
NODE_ENV=production

# 调试模式（生产环境建议设为false）
DEBUG=false

# =============================================================================
# 安全配置
# =============================================================================

# 会话密钥（生产环境中应该设置一个强密钥）
SECRET_KEY=your-secret-key-change-this-in-production

# JWT密钥（如果使用JWT认证）
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production

# =============================================================================
# 数据库配置
# =============================================================================

# DuckDB数据库文件路径
DUCKDB_PATH=./data/risk_analysis.duckdb

# 数据备份目录
BACKUP_DIR=./data/backups

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=WARNING

# 日志文件路径
LOG_FILE=./logs/app.log

# 日志文件最大大小（MB）
LOG_MAX_SIZE=100

# 保留的日志文件数量
LOG_BACKUP_COUNT=5

# =============================================================================
# 性能配置
# =============================================================================

# 并行处理配置
ENABLE_PARALLEL=false
MAX_WORKERS=4
MIN_DATA_SIZE=5000
MIN_GROUPS_SIZE=100

# 缓存配置
CACHE_TTL=3600
USER_CACHE_TTL=1800
SESSION_CACHE_TTL=7200

# =============================================================================
# 网络配置
# =============================================================================

# 请求超时时间（秒）
REQUEST_TIMEOUT=120

# 代理超时时间（秒）
PROXY_TIMEOUT=120

# 最大请求大小（MB）
MAX_CONTENT_LENGTH=100

# =============================================================================
# SSL/TLS配置（如果使用HTTPS）
# =============================================================================

# 启用SSL
USE_SSL=false

# SSL证书文件路径
SSL_CERT_PATH=

# SSL私钥文件路径
SSL_KEY_PATH=

# =============================================================================
# 监控和健康检查
# =============================================================================

# 健康检查端点
HEALTH_CHECK_ENDPOINT=/health

# 监控端点
METRICS_ENDPOINT=/metrics

# 启用性能监控
ENABLE_MONITORING=true

# =============================================================================
# 外部服务配置
# =============================================================================

# 如果需要连接外部API或服务，在这里配置
# EXTERNAL_API_URL=
# EXTERNAL_API_KEY=

# =============================================================================
# 部署相关配置
# =============================================================================

# 部署环境标识
DEPLOYMENT_ENV=production

# 版本号
APP_VERSION=1.0.0

# 构建时间戳（自动生成）
BUILD_TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# =============================================================================
# 备份和恢复配置
# =============================================================================

# 自动备份间隔（小时）
AUTO_BACKUP_INTERVAL=24

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# =============================================================================
# 邮件配置（如果需要发送通知邮件）
# =============================================================================

# SMTP服务器配置
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true

# 发件人邮箱
FROM_EMAIL=

# 管理员邮箱（接收系统通知）
ADMIN_EMAIL=

# =============================================================================
# 使用说明
# =============================================================================

# 1. 复制此文件为 production.local.env 并根据实际环境修改配置
# 2. 在启动脚本中加载配置: source production.local.env
# 3. 或者直接修改此文件中的默认值
# 4. 敏感信息（如密码、密钥）建议通过环境变量或安全的配置管理系统设置

# 注意：
# - 生产环境中请务必修改所有默认密码和密钥
# - 确保Redis和数据库的安全配置
# - 定期备份重要数据
# - 监控系统资源使用情况

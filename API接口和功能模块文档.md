# 风险链接分析工具 - API接口和功能模块文档

## 项目概述
风险链接分析工具是一个基于Flask后端和前端模块化架构的金融风险分析系统，主要用于检测和分析交易中的异常行为、对敲交易、代理关系等风险模式。

## 系统架构
- **后端**: Flask + DuckDB，模块化设计
- **前端**: 原生JavaScript + Webpack，组件化架构
- **端口**: 前端3000，后端5005
- **数据库**: DuckDB (位置: `/data/risk_analysis.duckbd`)

---

## 一、API接口统计

### 1. 鉴权模块 (Auth Module)
**路径前缀**: `/api/auth`

#### 1.1 登录认证接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/auth/login` | POST | 用户登录 | 验证用户凭据，创建会话 |
| `/api/auth/logout` | POST | 用户登出 | 清除会话，记录登出日志 |
| `/api/auth/check` | GET | 检查登录状态 | 验证当前会话是否有效 |

#### 1.2 用户管理接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/auth/users` | GET | 获取用户列表 | 分页获取用户信息（仅管理员） |
| `/api/auth/users` | POST | 创建新用户 | 创建新的系统用户（仅管理员） |
| `/api/auth/users/<id>` | PUT | 更新用户信息 | 修改用户角色、状态等（仅管理员） |
| `/api/auth/users/<id>` | DELETE | 删除用户 | 删除指定用户（仅管理员） |
| `/api/auth/users/<id>/reset-password` | POST | 重置用户密码 | 生成新密码并重置（仅管理员） |

### 2. 合约风险分析模块 (Contract Risk Analysis)
**路径前缀**: `/api/contract`

#### 2.1 数据上传和分析接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/contract/upload` | POST | 上传合约数据 | 上传Excel/CSV文件进行风险分析 |
| `/api/contract/analyze` | POST | 即时分析 | 对JSON数据进行实时风险检测 |
| `/api/contract/config` | GET/POST | 分析配置管理 | 获取/设置分析算法配置参数 |

#### 2.2 任务管理接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/contract/tasks` | GET | 获取任务列表 | 获取所有合约分析任务 |
| `/api/contract/tasks/<task_id>` | GET | 获取任务详情 | 获取指定任务的分析结果 |
| `/api/contract/tasks/<task_id>` | DELETE | 删除任务 | 删除指定的分析任务 |

#### 2.3 结果查询接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/contract/results/<task_id>` | GET | 获取分析结果 | 获取完整的风险分析结果 |
| `/api/contract/export/<task_id>` | GET | 导出分析报告 | 导出Excel格式的分析报告 |

### 3. 代理关系分析模块 (Agent Relationship)
**路径前缀**: `/api/agent`

#### 3.1 数据处理接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/agent/upload` | POST | 上传代理数据 | 上传用户关系数据进行分析 |
| `/api/agent/analyze` | POST | 关系分析 | 分析用户间的代理关系网络 |

#### 3.2 结果查询接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/agent/tasks` | GET | 获取分析任务 | 获取代理关系分析任务列表 |
| `/api/agent/results/<task_id>` | GET | 获取分析结果 | 获取BD金字塔结构分析结果 |
| `/api/agent/export/<task_id>` | GET | 导出关系图 | 导出代理关系网络图表 |

### 4. 关联风险分析模块 (Link Risk Analysis)
**路径前缀**: `/api/link`

#### 4.1 整合分析接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/link/integrate` | POST | 关联分析 | 整合合约和代理数据进行关联分析 |
| `/api/link/full-analysis/<task_id>` | POST | 完整分析 | 执行完整的风险关联分析 |

#### 4.2 风险查询接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/link/risks` | GET | 获取风险事件 | 按类型获取风险事件列表 |
| `/api/link/risk-details/<risk_id>` | GET | 获取风险详情 | 获取特定风险事件的详细信息 |
| `/api/link/bd-analysis/<bd_name>` | GET | BD风险分析 | 分析特定BD的风险链路 |

### 5. 用户分析模块 (User Analysis)
**路径前缀**: `/api/user-analysis`

#### 5.1 用户行为分析接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/user-analysis/profile/<user_id>` | GET | 获取用户画像 | 获取用户交易行为画像 |
| `/api/user-analysis/behavior/<user_id>` | GET | 行为分析 | 分析用户交易行为模式 |
| `/api/user-analysis/batch-analyze` | POST | 批量分析 | 批量分析多个用户的行为 |

#### 5.2 任务管理接口
| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/user-analysis/available-tasks` | GET | 获取可用任务 | 获取可用的分析任务列表 |
| `/api/user-analysis/tasks` | GET | 获取任务列表 | 获取用户分析任务列表 |

### 6. 数据库管理接口
**路径前缀**: `/api/database`

| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/database/status` | GET | 数据库状态 | 获取数据库连接状态和表信息 |
| `/api/database/tables` | GET | 表结构信息 | 获取所有表的结构和记录数 |

### 7. 系统接口

| 接口 | 方法 | 功能 | 描述 |
|------|------|------|------|
| `/api/health` | GET | 健康检查 | 检查系统各模块运行状态 |
| `/api/tasks` | GET | 获取任务列表 | 获取所有类型的分析任务 |

---

## 二、功能模块详细说明

### 1. 鉴权模块 (Auth Module)
**位置**: `backend/modules/auth/`

#### 功能描述
提供完整的用户认证、会话管理和权限控制功能，确保系统安全性。

#### 核心组件
- **AuthService**: 用户认证服务，处理登录验证、密码管理
- **SessionManager**: 会话管理器，管理用户会话生命周期
- **PasswordUtils**: 密码工具类，提供密码加密和验证
- **权限装饰器**: `@login_required`, `@admin_required`

#### 主要功能
- 用户登录/登出
- 会话管理和验证
- 用户管理（创建、更新、删除）
- 密码策略和重置
- 操作日志记录
- 账户锁定机制

#### 数据表
- `auth_users`: 用户基本信息
- `auth_sessions`: 会话记录
- `auth_user_activities`: 用户活动日志
- `auth_system_config`: 系统配置

### 2. 合约风险分析模块 (Contract Risk Analysis)
**位置**: `backend/modules/contract_risk_analysis/`

#### 功能描述
核心风险检测模块，使用多种算法检测合约交易中的异常行为模式。

#### 核心组件
- **CTContractAnalyzer**: 主分析器，协调各种检测算法
- **WashTradingDetector**: 对敲交易检测器
- **HighFrequencyDetector**: 高频交易检测器
- **FundingArbitrageDetector**: 资金费率套利检测器
- **RegularBrushDetector**: 常规刷量检测器

#### 检测算法
1. **对敲交易检测**: 识别同一用户或关联用户间的虚假交易
2. **高频交易检测**: 检测异常高频的交易行为
3. **资金费率套利**: 识别利用资金费率差异的套利行为
4. **常规刷量**: 检测人为增加交易量的行为

#### 主要功能
- Excel/CSV数据上传和解析
- 多算法并行风险检测
- 实时分析和批量分析
- 结果存储和查询
- 分析报告导出
- 配置管理

#### 数据表
- `contract_risk_analysis`: 分析结果主表
- `algorithm_results`: 算法结果统一存储
- `wash_trading_results`: 对敲交易结果
- `wash_trading_pairs`: 对敲交易对详情

### 3. 代理关系分析模块 (Agent Relationship)
**位置**: `backend/modules/agent_relationship/`

#### 功能描述
分析用户间的代理关系，构建BD（Business Development）金字塔结构，识别代理网络。

#### 核心组件
- **DataProcessor**: 数据预处理器
- **RelationshipAnalyzer**: 关系分析器
- **PyramidBuilder**: 金字塔结构构建器
- **LevelAnalyzer**: 层级分析器

#### 主要功能
- 用户关系数据处理
- 共享IP/设备分析
- BD层级结构构建
- 代理关系网络可视化
- 金字塔统计分析
- 关系强度评估

#### 数据表
- `users`: 用户基本信息
- `user_relationships`: 用户关系记录
- `shared_relationships`: 共享关系（IP/设备）
- `agent_analysis`: 代理分析结果

### 4. 关联风险分析模块 (Link Risk Analysis)
**位置**: `backend/modules/link_risk_analysis/`

#### 功能描述
整合合约风险和代理关系数据，进行跨模块的关联分析，发现复杂的风险模式。

#### 核心组件
- **LinkAnalyzer**: 关联分析器
- **RiskAmplifier**: 风险放大器
- **NetworkBuilder**: 网络构建器
- **TimelineGenerator**: 时间线生成器

#### 主要功能
- 合约风险与代理关系关联
- 跨BD风险模式识别
- 风险网络构建
- 风险时间线分析
- 风险热力图生成
- 综合风险评估

#### 分析维度
- BD内部风险传播
- 跨BD风险关联
- 时间维度风险演化
- 网络拓扑风险分析

### 5. 用户分析模块 (User Analysis)
**位置**: `backend/modules/user_analysis/`

#### 功能描述
深度分析单个用户的交易行为，生成用户画像和专业度评分。

#### 核心组件
- **UserBehaviorAnalyzer**: 用户行为分析器
- **ProfileGenerator**: 画像生成器
- **MetricsCalculator**: 指标计算器
- **ScoreCalculator**: 评分计算器

#### 分析维度
1. **基础指标**: 交易量、频率、持仓时间等
2. **衍生指标**: 胜率、盈亏比、风险偏好等
3. **专业度评分**: 盈利能力、风险控制、交易策略
4. **行为偏好**: 合约偏好、时间偏好、杠杆使用
5. **资金规模**: 散户、中户、大户分类
6. **异常分析**: 异常交易行为识别

#### 主要功能
- 用户交易行为画像
- 专业度评分计算
- 币种胜率分析
- 交易偏好分析
- 异常行为检测
- 批量用户分析

#### 数据表
- `user_trading_profiles`: 用户交易画像
- `user_behavior_analysis`: 行为分析结果

---

## 三、前端页面模块

### 1. 登录页面
**文件**: `frontend/src/auth/login.html`
**路由**: `/login`, `/login.html`
**功能**: 用户登录认证界面

### 2. 合约分析页面
**文件**: `frontend/src/modules/contract-risk-analysis/pages/contract-analysis.html`
**路由**: `/contract_analysis.html`, `/contract-analysis`
**功能**: 
- 合约数据上传
- 风险分析配置
- 分析结果展示
- 异常交易详情查看

### 3. 代理关系页面
**文件**: `frontend/src/modules/agent-relationship/pages/agent_relationship.html`
**路由**: `/agent_relationship.html`, `/agent-relationship`
**功能**:
- 代理数据上传
- BD金字塔可视化
- 关系网络图表
- 层级统计分析

### 4. 关联分析页面
**文件**: `frontend/src/modules/link-risk-analysis/pages/contract_integration.html`
**路由**: `/contract_integration.html`, `/contract-integration`
**功能**:
- 跨模块数据整合
- 关联风险分析
- 风险网络可视化
- 综合风险报告

### 5. 用户分析页面
**文件**: `frontend/src/modules/user-analysis/pages/user-analysis.html`
**路由**: `/user_analysis.html`, `/user-analysis`
**功能**:
- 用户ID搜索
- 交易行为画像
- 专业度评分展示
- 异常交易详情

---

## 四、数据库表结构

### 核心业务表
1. **contract_risk_analysis**: 合约风险分析结果
2. **user_trading_profiles**: 用户交易画像
3. **wash_trading_pairs**: 对敲交易对详情
4. **algorithm_results**: 算法结果统一存储
5. **users**: 用户基本信息
6. **user_relationships**: 用户关系网络
7. **shared_relationships**: 共享关系（IP/设备）

### 鉴权相关表
1. **auth_users**: 系统用户
2. **auth_sessions**: 用户会话
3. **auth_user_activities**: 用户活动日志
4. **auth_system_config**: 系统配置

### 任务管理表
1. **tasks**: 分析任务记录
2. **agent_analysis**: 代理分析任务

---

## 五、开发指南

### API调用示例
```javascript
// 登录
const loginResponse = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'admin', password: 'password' })
});

// 上传合约数据
const formData = new FormData();
formData.append('file', file);
const uploadResponse = await fetch('/api/contract/upload', {
    method: 'POST',
    body: formData
});

// 获取分析结果
const resultResponse = await fetch(`/api/contract/results/${taskId}`);
const results = await resultResponse.json();
```

### 权限控制
- **@login_required**: 需要登录
- **@admin_required**: 需要管理员权限
- 会话自动过期机制
- 操作日志记录

### 错误处理
- 统一错误响应格式
- 详细错误日志记录
- 前端友好错误提示
- 异常回滚机制

---

## 六、部署和配置

### 环境要求
- Python 3.8+
- Node.js 16+
- DuckDB

### 启动命令
```bash
# 后端启动
cd backend
python app.py

# 前端开发服务器
npm run dev

# 前端构建
npm run build
```

### 配置文件
- `config/auth_settings.py`: 鉴权配置
- `config/webpack.config.js`: 前端构建配置
- `backend/config/`: 各模块配置文件

---

*文档版本: v2.2*
*更新时间: 2025-07-14*
*维护者: bear、yang*

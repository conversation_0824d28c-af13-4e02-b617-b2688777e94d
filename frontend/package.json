{"name": "agent-analysis-tool", "version": "1.0.0", "description": "Agent Analysis Tool with Webpack", "main": "src/js/main.js", "scripts": {"start": "webpack serve --mode development", "dev": "webpack serve --mode development", "build": "webpack --mode production"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "babel-loader": "^9.1.3", "bootstrap-icons": "^1.13.1", "css-loader": "^6.10.0", "echarts": "^5.6.0", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "^2.8.1", "style-loader": "^3.3.4", "terser-webpack-plugin": "^5.3.10", "webpack": "^5.90.3", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.2"}, "dependencies": {"bootstrap": "^5.3.3", "chart.js": "^4.4.9", "element-plus": "^2.9.11"}}
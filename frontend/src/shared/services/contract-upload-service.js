/**
 * 独立的合约上传服务模块
 * 防止重复绑定事件监听器
 */

class ContractUploadService {
    constructor() {
        this.initialized = false;
        this.isUploading = false;
    }

    // 初始化合约上传功能
    init() {
        if (this.initialized) {
            return;
        }

        this.bindEventListeners();
        this.initialized = true;
    }

    bindEventListeners() {
        // 绑定文件选择按钮
        const contractSelectFileBtn = document.getElementById('contractSelectFileBtn');
        const contractFileUpload = document.getElementById('contractFileUpload');

        if (contractSelectFileBtn && contractFileUpload) {
            // 移除可能存在的旧监听器
            contractSelectFileBtn.removeEventListener('click', this.handleSelectFile.bind(this));
            contractFileUpload.removeEventListener('change', this.handleFileChange.bind(this));
            
            // 绑定新监听器
            contractSelectFileBtn.addEventListener('click', this.handleSelectFile.bind(this));
            contractFileUpload.addEventListener('change', this.handleFileChange.bind(this));
            
    
        }
    }

    // 处理选择文件按钮点击
    handleSelectFile(event) {
        event.preventDefault();

        
        const contractFileUpload = document.getElementById('contractFileUpload');
        if (contractFileUpload) {
            contractFileUpload.click();
        }
    }

    // 处理文件选择
    handleFileChange(event) {
        const file = event.target.files[0];
        if (!file) return;

        
        this.uploadFile(file);
    }

    // 上传文件
    async uploadFile(file) {
        if (this.isUploading) {
            return;
        }

        try {
            this.isUploading = true;
            
            // 显示上传状态
            this.showUploadStatus('正在上传文件...');
            
            const formData = new FormData();
            formData.append('file', file);

            // 添加处理模式参数（默认为普通模式）
            const processingMode = document.getElementById('processingMode');
            const selectedMode = processingMode ? processingMode.value : 'normal';
            formData.append('processing_mode', selectedMode);
            
            const response = await fetch('/api/contract/upload', {
                method: 'POST',
                body: formData
            });
            
            // 检查HTTP状态码
            if (!response.ok) {
                if (response.status === 413) {
                                            throw new Error('文件过大，请确保文件大小不超过2GB');
                } else if (response.status === 400) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || '请求参数错误');
                } else if (response.status === 500) {
                    throw new Error('服务器内部错误，请稍后重试');
                } else {
                    throw new Error(`上传失败，状态码: ${response.status}`);
                }
            }
            
            // 检查响应内容类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('服务器返回了无效的响应格式');
            }
            
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            
            this.showUploadStatus(`上传成功，任务ID: ${data.task_id}`, 'success');
            
            // 触发自定义事件，通知其他模块
            this.dispatchUploadSuccess(data);
            
        } catch (error) {
            this.showUploadStatus(`上传失败: ${error.message}`, 'error');
        } finally {
            this.isUploading = false;
        }
    }

    // 显示上传状态
    showUploadStatus(message, type = 'info') {
        // 查找状态显示元素
        const statusElements = [
            document.getElementById('contractProcessing'),
            document.getElementById('uploadStatus'),
            document.querySelector('.upload-status')
        ];

        const statusElement = statusElements.find(el => el !== null);
        
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `alert alert-${type}`;
            statusElement.style.display = 'block';
        }


    }

    // 触发上传成功事件
    dispatchUploadSuccess(data) {
        const event = new CustomEvent('contractUploadSuccess', {
            detail: data
        });
        
        document.dispatchEvent(event);

    }

    // 重置服务状态
    reset() {
        this.isUploading = false;

    }
}

// 创建全局单例实例
const contractUploadService = new ContractUploadService();

// 导出服务实例
export default contractUploadService;

// 也将其设置为全局对象，供其他脚本使用
window.ContractUploadService = contractUploadService; 
/* 导航栏基础样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 0.75rem 0;
}

.navbar-brand {
    font-size: 1.5rem;
    text-decoration: none !important;
}

.navbar-brand:hover {
    color: #0d6efd !important;
}

/* 导航链接样式 */
.navbar-nav .nav-link {
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #6c757d;
    font-weight: 500;
}

.navbar-nav .nav-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: #0d6efd;
    color: white !important;
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
}

.navbar-nav .nav-link.active:hover {
    background-color: #0b5ed7;
    color: white !important;
}

/* 图标样式 */
.navbar-nav .nav-link i {
    margin-right: 0.5rem;
    font-size: 1.1em;
}

/* 响应式设计 */
@media (max-width: 991.98px) {
    .navbar-nav {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
        border-radius: 0.5rem;
    }
}

/* 动画效果 */
.navbar-collapse {
    transition: all 0.3s ease;
}

/* 品牌logo动画 */
.navbar-brand i {
    transition: transform 0.3s ease;
}

.navbar-brand:hover i {
    transform: rotate(10deg) scale(1.1);
} 
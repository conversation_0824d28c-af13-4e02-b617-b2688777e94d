/**
 * 统一前端日志管理工具
 * 用于控制开发环境和生产环境的日志输出
 */

class Logger {
    constructor() {
        // 判断是否为生产环境
        this.isProduction = process.env.NODE_ENV === 'production' || 
                           window.location.hostname !== 'localhost';
        
        // 日志级别配置
        this.logLevels = {
            ERROR: 0,
            WARN: 1,
            INFO: 2,
            DEBUG: 3
        };
        
        // 当前日志级别（生产环境只显示错误和警告）
        this.currentLevel = this.isProduction ? this.logLevels.WARN : this.logLevels.DEBUG;
    }

    /**
     * 错误日志（总是显示）
     */
    error(...args) {
        if (this.currentLevel >= this.logLevels.ERROR) {
            console.error('[ERROR]', ...args);
        }
    }

    /**
     * 警告日志
     */
    warn(...args) {
        if (this.currentLevel >= this.logLevels.WARN) {
            console.warn('[WARN]', ...args);
        }
    }

    /**
     * 信息日志（仅开发环境显示）
     */
    info(...args) {
        if (this.currentLevel >= this.logLevels.INFO) {
            console.log('[INFO]', ...args);
        }
    }

    /**
     * 调试日志（仅开发环境显示）
     */
    debug(...args) {
        if (this.currentLevel >= this.logLevels.DEBUG) {
            console.log('[DEBUG]', ...args);
        }
    }

    /**
     * 设置日志级别
     */
    setLevel(level) {
        if (this.logLevels[level] !== undefined) {
            this.currentLevel = this.logLevels[level];
        }
    }

    /**
     * 禁用所有日志（生产环境使用）
     */
    disable() {
        this.currentLevel = -1;
    }
}

// 创建全局日志实例
const logger = new Logger();

// 在生产环境下进一步禁用console.log
if (logger.isProduction) {
    // 保留原始console方法的引用
    const originalConsole = {
        log: console.log,
        info: console.info,
        debug: console.debug
    };
    
    // 重写console方法（保留error和warn）
    console.log = function() {
        // 生产环境下不输出普通日志
    };
    
    console.info = function() {
        // 生产环境下不输出信息日志
    };
    
    console.debug = function() {
        // 生产环境下不输出调试日志
    };
    
    // 如果需要在生产环境临时启用日志，可以调用这个函数
    window.enableLogs = function() {
        console.log = originalConsole.log;
        console.info = originalConsole.info;
        console.debug = originalConsole.debug;
        logger.setLevel('DEBUG');
        console.log('日志已启用');
    };
    
    // 如果需要在生产环境临时禁用所有日志
    window.disableAllLogs = function() {
        console.log = function() {};
        console.info = function() {};
        console.debug = function() {};
        console.warn = function() {};
        console.error = function() {};
        logger.disable();
    };
}

// 导出日志实例
export default logger;

// 也可以直接使用全局变量
window.logger = logger; 
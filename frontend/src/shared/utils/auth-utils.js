/**
 * 鉴权工具库 - 必须严格按照此实现
 */
class AuthUtils {
    // 动态获取API基础URL，支持不同环境
    static getApiBaseUrl() {
        // 如果是通过webpack dev server访问（3000端口），使用代理
        if (window.location.port === '3000') {
            return '';  // 使用相对路径，通过webpack代理转发
        }
        // 如果是直接访问后端（5005端口）或其他情况
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        return `${protocol}//${hostname}:5005`;
    }

    static get AUTH_CHECK_URL() {
        return `${this.getApiBaseUrl()}/api/auth/check`;
    }

    static LOGIN_URL = '/frontend/src/auth/login.html';  // 登录页面路径
    static STORAGE_KEY = 'userAuth';
    
    /**
     * 检查用户是否已登录
     */
    static async checkAuth() {
        try {
            const response = await fetch(this.AUTH_CHECK_URL, {
                method: 'GET',
                credentials: 'include' // 重要：包含cookies
            });
            
            if (!response.ok) {
                throw new Error('检查认证状态失败');
            }
            
            const data = await response.json();
            
            if (data.authenticated) {
                // 保存用户信息到localStorage
                localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data.user));
                return data.user;
            } else {
                // 清除本地存储
                localStorage.removeItem(this.STORAGE_KEY);
                return null;
            }
        } catch (error) {
            console.error('检查认证状态失败:', error);
            localStorage.removeItem(this.STORAGE_KEY);
            return null;
        }
    }
    
    /**
     * 获取当前用户信息
     */
    static getCurrentUser() {
        try {
            const userStr = localStorage.getItem(this.STORAGE_KEY);
            return userStr ? JSON.parse(userStr) : null;
        } catch {
            return null;
        }
    }
    
    /**
     * 检查用户是否有指定权限
     */
    static hasRole(requiredRole) {
        const user = this.getCurrentUser();
        if (!user) return false;
        
        // admin拥有所有权限
        if (user.role === 'admin') return true;
        
        // 检查是否有指定角色
        return user.role === requiredRole;
    }
    
    /**
     * 用户登录
     */
    static async login(username, password) {
        try {
            const response = await fetch(`${this.getApiBaseUrl()}/api/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({ username, password })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // 保存用户信息
                localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data.user));
                return { success: true, user: data.user };
            } else {
                return { success: false, error: data.error };
            }
        } catch (error) {
            console.error('登录失败:', error);
            return { success: false, error: '网络连接失败，请重试' };
        }
    }
    
    /**
     * 用户登出
     */
    static async logout() {
        try {
            await fetch(`${this.getApiBaseUrl()}/api/auth/logout`, {
                method: 'POST',
                credentials: 'include'
            });
        } catch (error) {
            console.error('登出请求失败:', error);
        } finally {
            // 无论请求是否成功都清除本地状态
            localStorage.removeItem(this.STORAGE_KEY);
            this.redirectToLogin();
        }
    }
    
    /**
     * 重定向到登录页面
     */
    static redirectToLogin() {
        const currentPath = window.location.pathname;
        const loginPaths = ['/login.html', '/login', '/frontend/src/auth/login.html'];

        // 检查当前是否已经在登录页面
        const isOnLoginPage = loginPaths.some(path => currentPath.includes(path));

        if (!isOnLoginPage) {
            console.log('🔄 重定向到登录页面...', 'from:', currentPath);
            window.location.href = this.LOGIN_URL;
        } else {
            console.log('✅ 已在登录页面，无需重定向');
        }
    }
    
    /**
     * 页面鉴权守卫 - 每个页面都必须调用
     */
    static async guardPage() {
        const user = await this.checkAuth();
        
        if (!user) {
            console.log('用户未登录，重定向到登录页面');
            this.redirectToLogin();
            return false;
        }
        

        return user;
    }
    
    /**
     * 初始化页面权限控制 - 每个页面都必须调用
     */
    static initPagePermissions() {
        const user = this.getCurrentUser();
        if (!user) return;
        
        // 显示用户信息
        this.displayUserInfo(user);
        
        // 根据角色控制UI元素
        if (user.role !== 'admin') {
            // 隐藏管理员专用功能
            document.querySelectorAll('.admin-only').forEach(element => {
                element.style.display = 'none';
            });
            
            // 禁用上传功能
            document.querySelectorAll('.upload-btn, input[type="file"]').forEach(element => {
                element.disabled = true;
                element.title = '您没有上传权限，请联系管理员';
            });
            
            // 禁用删除功能
            document.querySelectorAll('.delete-btn').forEach(element => {
                element.disabled = true;
                element.title = '您没有删除权限，请联系管理员';
            });
        }
    }
    
    /**
     * 显示用户信息
     */
    static displayUserInfo(user) {
        // 不再显示页面顶部的用户信息栏，只更新现有的用户信息显示元素

        // 更新现有的用户信息显示元素（如果存在）
        const usernameElements = document.querySelectorAll('#currentUsername');
        const roleElements = document.querySelectorAll('#currentUserRole');

        usernameElements.forEach(element => {
            element.textContent = user.username;
        });

        roleElements.forEach(element => {
            element.textContent = user.role === 'admin' ? '管理员' : '查看者';
            element.className = `badge ${user.role === 'admin' ? 'bg-danger' : 'bg-success'} ms-2`;
        });

        // 显示用户信息栏（如果页面中有预定义的）
        const userInfoBar = document.getElementById('userInfoBar');
        if (userInfoBar) {
            userInfoBar.style.display = 'block';
        }
    }
    
    /**
     * 修改密码
     */
    static async changePassword(oldPassword, newPassword) {
        try {
            const response = await fetch('http://localhost:5005/api/auth/change-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    old_password: oldPassword,
                    new_password: newPassword
                })
            });
            
            const data = await response.json();
            return data;
            
        } catch (error) {
            console.error('修改密码失败:', error);
            return { success: false, error: '网络连接失败，请重试' };
        }
    }
    
    /**
     * 显示权限错误提示
     */
    static showPermissionError(message = '您没有执行此操作的权限') {
        // 创建提示弹窗
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        `;
        
        modal.innerHTML = `
            <div style="
                background: white;
                padding: 30px;
                border-radius: 8px;
                max-width: 400px;
                text-align: center;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            ">
                <div style="font-size: 48px; margin-bottom: 16px;">🚫</div>
                <h3 style="color: #dc3545; margin-bottom: 16px;">权限不足</h3>
                <p style="color: #6c757d; margin-bottom: 24px;">${message}</p>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    cursor: pointer;
                ">确定</button>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 3秒后自动关闭
        setTimeout(() => {
            if (modal.parentElement) {
                modal.remove();
            }
        }, 3000);
    }
    
    /**
     * 创建权限受保护的按钮
     */
    static createProtectedButton(text, onClick, requiredRole = 'admin') {
        const button = document.createElement('button');
        button.textContent = text;
        
        if (this.hasRole(requiredRole)) {
            button.onclick = onClick;
            button.style.cssText = `
                background: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
            `;
        } else {
            button.disabled = true;
            button.onclick = () => this.showPermissionError();
            button.style.cssText = `
                background: #e9ecef;
                color: #6c757d;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: not-allowed;
            `;
            button.title = `需要${requiredRole}权限`;
        }
        
        return button;
    }
}

// 导出到全局作用域
window.AuthUtils = AuthUtils;

// ES6模块导出
export default AuthUtils; 
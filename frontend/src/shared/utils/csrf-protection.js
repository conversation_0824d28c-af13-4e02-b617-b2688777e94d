/**
 * CSRF保护工具类
 * 提供CSRF token的获取、存储和验证功能
 */
class CSRFProtection {
    constructor() {
        this.token = null;
        this.tokenExpiry = null;
        this.refreshPromise = null;
    }

    /**
     * 获取CSRF token
     * @returns {Promise<string>} CSRF token
     */
    async getToken() {
        // 如果token存在且未过期，直接返回
        if (this.token && this.tokenExpiry && Date.now() < this.tokenExpiry) {
            return this.token;
        }

        // 如果正在刷新token，等待刷新完成
        if (this.refreshPromise) {
            return await this.refreshPromise;
        }

        // 刷新token
        this.refreshPromise = this._refreshToken();
        try {
            return await this.refreshPromise;
        } finally {
            this.refreshPromise = null;
        }
    }

    /**
     * 刷新CSRF token
     * @private
     */
    async _refreshToken() {
        try {
            const response = await fetch('/api/csrf-token', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`获取CSRF token失败: ${response.status}`);
            }

            const data = await response.json();
            this.token = data.csrf_token;
            // token有效期设为50分钟（服务器端1小时，留10分钟缓冲）
            this.tokenExpiry = Date.now() + (50 * 60 * 1000);
            
            console.log('🔐 CSRF token已更新');
            return this.token;
        } catch (error) {
            console.error('❌ 获取CSRF token失败:', error);
            throw error;
        }
    }

    /**
     * 为请求添加CSRF token
     * @param {Object} options - fetch请求选项
     * @returns {Promise<Object>} 包含CSRF token的请求选项
     */
    async addTokenToRequest(options = {}) {
        const token = await this.getToken();
        
        // 确保headers存在
        if (!options.headers) {
            options.headers = {};
        }

        // 添加CSRF token到headers
        options.headers['X-CSRFToken'] = token;
        
        // 确保包含credentials
        options.credentials = 'include';

        return options;
    }

    /**
     * 安全的fetch请求（自动包含CSRF token）
     * @param {string} url - 请求URL
     * @param {Object} options - fetch选项
     * @returns {Promise<Response>} fetch响应
     */
    async secureFetch(url, options = {}) {
        // 只对POST、PUT、DELETE、PATCH请求添加CSRF token
        const method = (options.method || 'GET').toUpperCase();
        const needsCSRF = ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method);

        if (needsCSRF) {
            options = await this.addTokenToRequest(options);
        } else {
            // GET请求仍需要credentials
            options.credentials = 'include';
        }

        try {
            const response = await fetch(url, options);
            
            // 如果返回403且是CSRF错误，清除token并重试一次
            if (response.status === 400) {
                try {
                    const errorData = await response.clone().json();
                    if (errorData.error && errorData.error.includes('CSRF')) {
                        console.warn('🔄 CSRF token可能已过期，重新获取并重试');
                        this.token = null;
                        this.tokenExpiry = null;
                        
                        if (needsCSRF) {
                            options = await this.addTokenToRequest(options);
                            return await fetch(url, options);
                        }
                    }
                } catch (e) {
                    // 忽略JSON解析错误
                }
            }

            return response;
        } catch (error) {
            console.error('❌ 安全请求失败:', error);
            throw error;
        }
    }

    /**
     * 清除存储的token（用于登出等场景）
     */
    clearToken() {
        this.token = null;
        this.tokenExpiry = null;
        this.refreshPromise = null;
        console.log('🔐 CSRF token已清除');
    }
}

// 创建全局实例
const csrfProtection = new CSRFProtection();

// 导出
window.CSRFProtection = csrfProtection;

// 兼容性：替换全局fetch为安全版本
if (typeof window !== 'undefined') {
    const originalFetch = window.fetch;
    
    window.secureFetch = csrfProtection.secureFetch.bind(csrfProtection);
    
    // 可选：完全替换fetch（谨慎使用）
    // window.fetch = csrfProtection.secureFetch.bind(csrfProtection);
}

export default csrfProtection;

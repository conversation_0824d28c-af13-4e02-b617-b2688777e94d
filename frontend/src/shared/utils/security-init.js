/**
 * 安全初始化脚本
 * 统一加载和初始化所有安全相关功能
 */

// 动态加载CSRF保护
function loadCSRFProtection() {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = '/frontend/src/shared/utils/csrf-protection.js';
        script.onload = () => {
            console.log('✅ CSRF保护已加载');
            resolve();
        };
        script.onerror = () => {
            console.error('❌ CSRF保护加载失败');
            reject(new Error('CSRF保护加载失败'));
        };
        document.head.appendChild(script);
    });
}

// 动态加载HTTP拦截器
function loadHttpInterceptor() {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = '/frontend/src/shared/utils/http-interceptor.js';
        script.onload = () => {
            console.log('✅ HTTP拦截器已加载');
            resolve();
        };
        script.onerror = () => {
            console.error('❌ HTTP拦截器加载失败');
            reject(new Error('HTTP拦截器加载失败'));
        };
        document.head.appendChild(script);
    });
}

// 动态加载认证工具
function loadAuthUtils() {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = '/frontend/src/shared/utils/auth-utils.js';
        script.onload = () => {
            console.log('✅ 认证工具已加载');
            resolve();
        };
        script.onerror = () => {
            console.warn('⚠️ 认证工具加载失败，可能不存在');
            resolve(); // 不阻塞其他功能
        };
        document.head.appendChild(script);
    });
}

// 初始化安全功能
async function initSecurity() {
    try {
        console.log('🔐 开始初始化安全功能...');
        
        // 并行加载所有安全组件
        await Promise.all([
            loadCSRFProtection(),
            loadHttpInterceptor(),
            loadAuthUtils()
        ]);
        
        // 等待一小段时间确保所有脚本都已执行
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 初始化HTTP拦截器（如果存在）
        if (window.HttpInterceptor && typeof window.HttpInterceptor.init === 'function') {
            window.HttpInterceptor.init();
        }
        
        console.log('✅ 安全功能初始化完成');
        
        // 触发自定义事件，通知其他模块安全功能已就绪
        window.dispatchEvent(new CustomEvent('securityReady', {
            detail: {
                csrf: !!window.CSRFProtection,
                httpInterceptor: !!window.HttpInterceptor,
                authUtils: !!window.AuthUtils
            }
        }));
        
    } catch (error) {
        console.error('❌ 安全功能初始化失败:', error);
        
        // 显示错误提示
        showSecurityError('安全功能初始化失败，部分功能可能无法正常使用');
    }
}

// 显示安全错误提示
function showSecurityError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background: #dc3545;
        color: white;
        padding: 12px;
        text-align: center;
        z-index: 10001;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    `;
    errorDiv.innerHTML = `
        <span>⚠️ ${message}</span>
        <button onclick="this.parentElement.remove()" style="
            background: none;
            border: 1px solid white;
            color: white;
            margin-left: 15px;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        ">关闭</button>
    `;
    document.body.appendChild(errorDiv);
    
    // 10秒后自动消失
    setTimeout(() => {
        if (errorDiv.parentElement) {
            errorDiv.remove();
        }
    }, 10000);
}

// 检查是否需要安全功能（排除登录页面）
function needsSecurity() {
    const currentPath = window.location.pathname;
    const excludePaths = ['/login', '/login.html', '/frontend/src/auth/login.html'];
    
    return !excludePaths.some(path => currentPath.includes(path));
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    if (needsSecurity()) {
        initSecurity();
    } else {
        console.log('🔐 当前页面无需安全功能初始化');
    }
});

// 导出初始化函数供手动调用
window.initSecurity = initSecurity;

// 提供安全状态检查函数
window.checkSecurityStatus = function() {
    return {
        csrf: !!window.CSRFProtection,
        httpInterceptor: !!window.HttpInterceptor,
        authUtils: !!window.AuthUtils,
        ready: !!(window.CSRFProtection && window.HttpInterceptor)
    };
};

console.log('🔐 安全初始化脚本已加载');

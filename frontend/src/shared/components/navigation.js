class NavigationComponent {
    constructor() {
        this.currentPage = this.getCurrentPage();
    }

    getCurrentPage() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index.html') return 'home';
        if (path.includes('agent') || path.includes('index')) return 'agent';
        if (path.includes('contract_integration') || path.includes('contract-integration')) return 'contract-integration';
        if (path.includes('contract')) return 'contract';
        if (path.includes('link')) return 'link';
        if (path.includes('user_analysis') || path.includes('user-analysis')) return 'user-analysis';
        if (path.includes('user-management') || path.includes('admin')) return 'admin';
        return 'home';
    }

    /**
     * 获取当前用户信息
     */
    getCurrentUser() {
        try {
            const userStr = localStorage.getItem('userAuth');
            return userStr ? JSON.parse(userStr) : null;
        } catch {
            return null;
        }
    }

    /**
     * 检查用户是否有管理员权限
     */
    isAdmin() {
        const user = this.getCurrentUser();
        return user && user.role === 'admin';
    }

    generateNavigationHTML() {
        const isAdmin = this.isAdmin();
        const currentUser = this.getCurrentUser();

        return `
            <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom mb-4">
                <div class="container-fluid">
                    <span class="navbar-brand fw-bold text-primary">
                        <i class="bi bi-graph-up"></i> 风险分析系统
                    </span>

                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>

                    <div class="collapse navbar-collapse" id="mainNavigation">
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link ${this.currentPage === 'agent' ? 'active fw-bold' : ''}" href="/agent_relationship.html">
                                    <i class="bi bi-diagram-3"></i> 代理分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link ${this.currentPage === 'contract' ? 'active fw-bold' : ''}" href="/contract_analysis.html">
                                    <i class="bi bi-file-earmark-bar-graph"></i> 合约分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link ${this.currentPage === 'contract-integration' ? 'active fw-bold' : ''}" href="/contract_integration.html">
                                    <i class="bi bi-graph-up"></i> 链路分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link ${this.currentPage === 'user-analysis' ? 'active fw-bold' : ''}" href="/user_analysis.html">
                                    <i class="bi bi-person-check"></i> 用户分析
                                </a>
                            </li>
                        </ul>

                        <ul class="navbar-nav ms-auto">
                            ${isAdmin ? `
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle ${this.currentPage === 'admin' ? 'active fw-bold' : ''}"
                                   href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-gear-fill text-warning"></i> 系统管理
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                                    <li>
                                        <a class="dropdown-item" href="/user-management.html">
                                            <i class="bi bi-people-fill"></i> 用户管理
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="/system-dashboard.html">
                                            <i class="bi bi-speedometer2"></i> 系统控制台
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="/permission-matrix.html">
                                            <i class="bi bi-grid-3x3-gap"></i> 权限矩阵
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="/config-management.html">
                                            <i class="bi bi-sliders"></i> 系统配置
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="alert('操作日志功能开发中...')">
                                            <i class="bi bi-journal-text"></i> 操作日志
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="alert('会话管理功能开发中...')">
                                            <i class="bi bi-person-lines-fill"></i> 会话管理
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            ` : ''}

                            ${currentUser ? `
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person-circle"></i> ${currentUser.username}
                                    <span class="badge bg-primary ms-1">${currentUser.role === 'admin' ? '管理员' : '查看者'}</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li>
                                        <a class="dropdown-item" href="#" id="changePasswordLink">
                                            <i class="bi bi-key"></i> 修改密码
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="#" id="logoutLink">
                                            <i class="bi bi-box-arrow-right"></i> 退出登录
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            ` : `
                            <li class="nav-item">
                                <a class="nav-link" href="/frontend/src/auth/login.html">
                                    <i class="bi bi-box-arrow-in-right"></i> 登录
                                </a>
                            </li>
                            `}
                        </ul>
                    </div>
                </div>
            </nav>
        `;
    }

    render(containerId = null) {
        const navigationHTML = this.generateNavigationHTML();

        if (containerId) {
            // 如果指定了容器ID，插入到指定容器
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = navigationHTML;
                this.attachEventListeners();
            }
        } else {
            // 否则插入到body的开头
            document.body.insertAdjacentHTML('afterbegin', navigationHTML);
            this.attachEventListeners();
        }
    }

    /**
     * 附加事件监听器
     */
    attachEventListeners() {
        // 等待一下确保 AuthUtils 已加载
        setTimeout(() => {
            // 修改密码链接
            const changePasswordLink = document.getElementById('changePasswordLink');
            if (changePasswordLink) {
                changePasswordLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.handleChangePassword();
                });
            }

            // 退出登录链接
            const logoutLink = document.getElementById('logoutLink');
            if (logoutLink) {
                logoutLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.handleLogout();
                });
            }

            // 初始化Bootstrap下拉菜单
            this.initializeBootstrapDropdowns();
        }, 100);
    }

    /**
     * 初始化Bootstrap下拉菜单
     */
    initializeBootstrapDropdowns() {
        // 确保Bootstrap已加载
        if (typeof window.bootstrap !== 'undefined' && window.bootstrap.Dropdown) {
            // 初始化所有下拉菜单
            const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
            dropdownElementList.forEach(dropdownToggleEl => {
                // 检查是否已经初始化过
                if (!dropdownToggleEl.hasAttribute('data-bs-dropdown-initialized')) {
                    new window.bootstrap.Dropdown(dropdownToggleEl);
                    dropdownToggleEl.setAttribute('data-bs-dropdown-initialized', 'true');
                }
            });
        } else {
            // 如果Bootstrap还没加载，稍后再试
            setTimeout(() => this.initializeBootstrapDropdowns(), 200);
        }
    }

    /**
     * 处理修改密码
     */
    handleChangePassword() {
        if (window.AuthUtils && typeof window.AuthUtils.showChangePassword === 'function') {
            window.AuthUtils.showChangePassword();
        } else {
            alert('修改密码功能暂未实现');
        }
    }

    /**
     * 处理退出登录
     */
    handleLogout() {
        if (window.AuthUtils && typeof window.AuthUtils.logout === 'function') {
            console.log('🔄 使用 AuthUtils.logout() 退出登录');
            window.AuthUtils.logout();
        } else {
            // 备用退出逻辑
            console.log('⚠️ AuthUtils 不可用，使用备用退出逻辑');
            if (confirm('确定要退出登录吗？')) {
                // 清除所有认证信息
                localStorage.removeItem('userAuth');
                sessionStorage.removeItem('userAuth');

                // 清除所有可能的认证相关存储
                ['token', 'user', 'auth', 'session'].forEach(key => {
                    localStorage.removeItem(key);
                    sessionStorage.removeItem(key);
                });

                console.log('🔄 跳转到登录页面...');

                // 跳转到登录页面
                const loginUrl = '/frontend/src/auth/login.html';
                window.location.href = loginUrl;
            }
        }
    }

    // 静态方法，方便在其他地方调用
    static init(containerId = null) {
        const navigation = new NavigationComponent();
        navigation.render(containerId);
        return navigation;
    }
}

// 创建全局备用函数，以防直接调用
window.handleUserLogout = function() {
    console.log('🔄 全局备用退出登录函数被调用');
    if (window.AuthUtils && typeof window.AuthUtils.logout === 'function') {
        console.log('✅ 使用 AuthUtils.logout()');
        window.AuthUtils.logout();
    } else {
        console.log('⚠️ AuthUtils 不可用，使用备用逻辑');
        if (confirm('确定要退出登录吗？')) {
            // 清除认证信息
            localStorage.removeItem('userAuth');
            sessionStorage.removeItem('userAuth');

            console.log('🔄 跳转到登录页面...');
            window.location.href = '/frontend/src/auth/login.html';
        }
    }
};

window.handleChangePassword = function() {
    if (window.AuthUtils && typeof window.AuthUtils.showChangePassword === 'function') {
        window.AuthUtils.showChangePassword();
    } else {
        alert('修改密码功能暂未实现');
    }
};

// 导出供其他模块使用
export default NavigationComponent;
/* 确保页面内容居中显示 */
body {
    margin: 0;
    padding: 0;
    width: 100%;
}

.container-fluid {
    padding: 20px;
    max-width: 100%;
    margin: 0 auto;
    width: 100%;
}

/* 确保row和col正确对齐 */
.row {
    margin-left: 0;
    margin-right: 0;
    width: 100%;
}

[class*="col-"] {
    padding-left: 15px;
    padding-right: 15px;
}

.upload-container {
    border: 2px dashed #ddd;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    border-radius: 5px;
    background-color: #f9f9f9;
    transition: background-color 0.3s;
}
.upload-container:hover {
    background-color: #f1f1f1;
}
.results-container {
    margin-top: 30px;
}
.tab-content {
    padding: 20px;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 5px 5px;
}
.processing {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 200px;
}
.spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 10px;
}
table {
    width: 100%;
}
.tab-pane {
    padding: 15px 0;
}
.summary-card {
    text-align: center;
    margin-bottom: 20px;
}
.summary-card .card-body {
    padding: 10px;
}
.summary-card .card-title {
    margin-bottom: 0;
    font-size: 14px;
}
.summary-card .card-text {
    font-size: 24px;
    font-weight: bold;
}
/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
.pagination {
    display: inline-flex;
}
.pagination button {
    margin: 0 5px;
    padding: 5px 10px;
    border: 1px solid #ddd;
    background-color: white;
    cursor: pointer;
}
.pagination button.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}
.pagination button:disabled {
    color: #999;
    cursor: not-allowed;
}
.page-info {
    margin: 0 15px;
    padding: 5px 0;
}
/* 可复制ID的样式 */
.copyable-id {
    cursor: pointer;
    color: #007bff;
}
.copyable-id:hover {
    color: #0056b3;
}
/* 提示框样式 */
.tooltip {
    position: fixed;
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s;
}
.tooltip.show {
    opacity: 0.9;
}

/* 添加必要的样式 */
/* .results-container is already defined */

/* 确保tab内容正确显示 */
/* .tab-pane is already defined */

/* 确保表格正确显示 */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 20px;
}

/* 新增样式 */
.sql-display-container {
    margin-top: 1.5rem;
    padding: 0 15px;
}

#sqlTextarea {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    resize: vertical;
    min-height: 120px;
}

/* 自定义样式示例 */
.hidden {
    display: none;
} 
// 合约风险链路分析服务模块
// ===========================

const ContractIntegrationService = (function() {
    
    class Service {
        constructor() {
                    this.baseUrl = '/api/link';
        this.contractBaseUrl = '/api/contract';
            this.currentAnalysisResult = null;
            
            // 初始化任务数组，防止undefined错误
            this.contractTasks = [];
            this.agentTasks = [];
            this.selectedContractTaskId = null;
            this.selectedAgentTaskId = null;
            
            this.charts = {
                networkChart: null
            };
            
            // BD筛选状态
            this.bdFilterState = {
                selectedBD: ''
            };
            
            // 缓存事件处理函数，避免重复创建
            this._boundHandlers = {
                taskSelection: this.handleTaskSelection.bind(this),
                startIntegration: this.handleStartIntegration.bind(this),
                tabChange: this.handleTabChange.bind(this),
                bdFilterChange: this.handleBDFilterChange.bind(this),
                clearBDFilter: this.clearBDFilter.bind(this),
                showAllBDs: this.showAllBDs.bind(this),
                showSelectedBD: this.showSelectedBD.bind(this)
            };

            // 添加防抖函数缓存
            this._debouncedFunctions = new Map();
        }
        
        // 创建防抖函数
        _debounce(func, wait) {
            const key = func.name || 'anonymous';
            if (!this._debouncedFunctions.has(key)) {
                let timeout;
                this._debouncedFunctions.set(key, function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                });
            }
            return this._debouncedFunctions.get(key);
        }
        
        // 初始化服务
        async init() {
            
            try {
                // 加载合约任务
                await this.loadContractTasks();
                
                // 加载代理商任务列表（可选）
                await this.loadAgentTasks();
                
                // 任务加载完成，用户需要手动选择任务
            } catch (error) {
                console.error('❌ 合约整合服务初始化失败:', error);
                this.showError('服务初始化失败，请刷新页面重试');
            }
        }
        
        // 绑定事件 - 优化版本
        bindEvents() {
            // 先解绑所有事件，避免重复绑定
            this.unbindEvents();
            
            // 任务选择
            const taskSelect = document.getElementById('contractTaskSelector');
            if (taskSelect) {
                taskSelect.addEventListener('change', this._boundHandlers.taskSelection);
            } else {
                console.warn('⚠️ [合约整合] 未找到任务选择下拉框');
            }
            
            // 开始整合分析
            const startBtn = document.getElementById('startIntegrationBtn');
            if (startBtn) {
                startBtn.addEventListener('click', this._boundHandlers.startIntegration);
            } else {
                console.warn('⚠️ [合约整合] 未找到开始分析按钮');
            }
            
            // 标签页切换 - 使用事件委托
            const tabContainer = document.getElementById('detailAnalysisTabs');
            if (tabContainer) {
                tabContainer.addEventListener('shown.bs.tab', this._boundHandlers.tabChange);
            }
            


            // BD筛选控制
            this.bindBDFilterEvents();
        }

        // 解绑所有事件
        unbindEvents() {
            const taskSelect = document.getElementById('contractTaskSelector');
            const startBtn = document.getElementById('startIntegrationBtn');
            const tabContainer = document.getElementById('detailAnalysisTabs');
            
            if (taskSelect) taskSelect.removeEventListener('change', this._boundHandlers.taskSelection);
            if (startBtn) startBtn.removeEventListener('click', this._boundHandlers.startIntegration);
            if (tabContainer) tabContainer.removeEventListener('shown.bs.tab', this._boundHandlers.tabChange);
            
            this.unbindBDFilterEvents();
        }

        // 绑定BD筛选相关事件 - 优化版本
        bindBDFilterEvents() {
            // 解绑旧的事件监听器
            this.unbindBDFilterEvents();
            
            const eventMappings = [
                { id: 'bdSelectFilter', handler: this._boundHandlers.bdFilterChange },
                { id: 'clearBDFilter', handler: this._boundHandlers.clearBDFilter },
                { id: 'showAllBDs', handler: this._boundHandlers.showAllBDs },
                { id: 'showSelectedBD', handler: this._boundHandlers.showSelectedBD }
            ];

            eventMappings.forEach(({ id, handler }) => {
                const element = document.getElementById(id);
                if (element) {
                    const eventType = element.tagName === 'SELECT' ? 'change' : 'click';
                    element.addEventListener(eventType, handler);
                }
            });
        }

        // 解绑BD筛选事件 - 优化版本
        unbindBDFilterEvents() {
            const eventMappings = [
                { id: 'bdSelectFilter', handler: this._boundHandlers.bdFilterChange, event: 'change' },
                { id: 'clearBDFilter', handler: this._boundHandlers.clearBDFilter, event: 'click' },
                { id: 'showAllBDs', handler: this._boundHandlers.showAllBDs, event: 'click' },
                { id: 'showSelectedBD', handler: this._boundHandlers.showSelectedBD, event: 'click' }
            ];

            eventMappings.forEach(({ id, handler, event }) => {
                const element = document.getElementById(id);
                if (element && handler) {
                    element.removeEventListener(event, handler);
                }
            });
        }
        
        // 加载可用的合约分析任务（持久化存储支持）
        async loadContractTasks() {
            try {
                // 从持久化存储获取已完成的合约分析任务
                const response = await fetch('/api/contract/tasks', {
                credentials: 'include'
            });
                
                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    this.contractTasks = Array.isArray(data.tasks) ? data.tasks : [];
                    
                    // 更新合约任务选择器UI
                    this.updateContractTaskSelector();
                } else {
                    console.error('加载合约任务失败:', data.error);
                    this.contractTasks = [];
                    this.showError('加载任务列表失败: ' + (data.error || '未知错误'));
                }
            } catch (error) {
                console.error('加载合约任务时发生错误:', error);
                this.contractTasks = [];
                this.showError('加载任务列表时发生网络错误，请检查后端服务');
            }
        }

        // 加载代理商任务列表
        async loadAgentTasks() {
            try {
                const response = await fetch('/api/agent/tasks', {
                credentials: 'include'
            });
                
                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.status === 'success' && data.tasks) {
                    this.agentTasks = data.tasks;
                    
                    // 更新代理商任务选择器UI
                    this.updateAgentTaskSelector();
                } else {
                    console.error('加载代理商任务失败:', data.error);
                    this.agentTasks = [];
                    // 不显示错误，因为代理商任务是可选的
                    this.updateAgentTaskSelector();
                }
            } catch (error) {
                console.error('加载代理商任务时发生错误:', error);
                this.agentTasks = [];
                // 不显示错误，因为代理商任务是可选的
                this.updateAgentTaskSelector();
            }
        }

        // 移除了自动选择功能，用户需要手动选择任务

        // 更新合约任务选择器UI
        updateContractTaskSelector() {
            const selector = document.getElementById('contractTaskSelector');
            if (!selector) return;
            
            // 清空现有选项
            selector.innerHTML = '<option value="">选择合约分析任务</option>';
            
            // 添加任务选项
            this.contractTasks.forEach(task => {
                const option = document.createElement('option');
                option.value = task.task_id;
                option.textContent = `${task.filename} (${task.risks_found}个风险, ${new Date(task.created_at).toLocaleString()})`;
                
                // 如果是当前选中的任务，设置为选中状态
                if (task.task_id === this.selectedContractTaskId) {
                    option.selected = true;
                }
                
                selector.appendChild(option);
            });
        }

        // 更新代理商任务选择器UI
        updateAgentTaskSelector() {
            const selector = document.getElementById('agentTaskSelector');
            if (!selector) return;
            
            // 清空现有选项
            selector.innerHTML = '<option value="">选择代理商分析任务（可选）</option>';
            
            // 添加任务选项
            this.agentTasks.forEach(task => {
                const option = document.createElement('option');
                option.value = task.task_id;
                option.textContent = `${task.filename} (${task.total_members}个用户, ${new Date(task.created_at).toLocaleString()})`;
                
                // 如果是当前选中的任务，设置为选中状态
                if (task.task_id === this.selectedAgentTaskId) {
                    option.selected = true;
                }
                
                selector.appendChild(option);
            });
        }

        // 更新分析按钮状态
        updateAnalysisButtonState() {
            const startBtn = document.getElementById('startIntegrationBtn');
            if (!startBtn) return;
            
            // 只需要选择合约任务即可启用按钮
            const canAnalyze = this.selectedContractTaskId;
            
            if (canAnalyze) {
                startBtn.disabled = false;
                startBtn.classList.remove('btn-secondary');
                startBtn.classList.add('btn-primary');
                startBtn.innerHTML = '<i class="bi bi-play-circle"></i> 开始整合分析';
            } else {
                startBtn.disabled = true;
                startBtn.classList.remove('btn-primary');
                startBtn.classList.add('btn-secondary');
                startBtn.innerHTML = '<i class="bi bi-exclamation-circle"></i> 请先选择分析任务';
            }
        }

        // 显示任务选择状态
        showTaskSelectionStatus(message) {
            const statusDiv = document.getElementById('integrationStatus');
            if (!statusDiv) return;
            
            statusDiv.innerHTML = `
                <i class="bi bi-info-circle"></i> ${message}
            `;
            statusDiv.className = 'alert alert-info';
            statusDiv.style.display = 'block';
        }

        // 隐藏任务选择状态
        hideTaskSelectionStatus() {
            const statusDiv = document.getElementById('integrationStatus');
            if (statusDiv) {
                statusDiv.style.display = 'none';
            }
        }

        // 获取风险评分
        getRiskScore(risk) {
            if (risk.risk_score) return risk.risk_score;
            if (risk.severity) return this.severityToScore(risk.severity);
            return 50; // 默认中等风险评分
        }

        // 显示加载状态
        showLoadingStatus(message) {
            const statusDiv = document.getElementById('integrationStatus');
            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        <span>${message}</span>
                    </div>
                `;
                statusDiv.className = 'alert alert-info';
                statusDiv.style.display = 'block';
            }
        }

        // 隐藏加载状态
        hideLoadingStatus() {
            const statusDiv = document.getElementById('integrationStatus');
            if (statusDiv) {
                statusDiv.style.display = 'none';
            }
        }

        // 刷新任务列表
        async refreshTasks() {
            try {
                
                // 重新加载合约任务
                await this.loadContractTasks();
                
                // 重新加载代理商任务
                await this.loadAgentTasks();
                
                // 如果当前选中的任务不存在了，清空选择
                if (this.selectedContractTaskId && !this.contractTasks.find(t => t.task_id === this.selectedContractTaskId)) {
                    this.selectedContractTaskId = null;
                }
                
            } catch (error) {
                console.error('刷新任务列表失败:', error);
                this.showError('刷新任务列表失败: ' + error.message);
            }
        }

        // 处理任务选择变化
        onContractTaskChange(taskId) {
            this.selectedContractTaskId = taskId;
            this.updateAnalysisButtonState();
            
            // 更新状态提示
            if (this.selectedContractTaskId) {
                this.showTaskSelectionStatus('任务已选择完成，可以开始整合分析');
            } else {
                this.showTaskSelectionStatus('请选择合约分析任务');
            }
        }

        // 处理代理商任务选择变化
        onAgentTaskChange(taskId) {
            this.selectedAgentTaskId = taskId;
            // 代理商任务是可选的，不影响分析按钮状态
            // 只在状态提示中说明
            if (this.selectedContractTaskId) {
                if (this.selectedAgentTaskId) {
                    this.showTaskSelectionStatus('合约任务和代理商任务已选择，可以开始整合分析');
                } else {
                    this.showTaskSelectionStatus('合约任务已选择，可以开始整合分析（代理商任务可选）');
                }
            }
        }

        // 格式化日期时间显示
        formatDateTime(dateTimeStr) {
            try {
                const date = new Date(dateTimeStr);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return dateTimeStr; // 如果格式化失败，返回原始字符串
            }
        }
        
        // 处理任务选择
        handleTaskSelection(event) {
            const taskId = event.target.value;
            const startBtn = document.getElementById('startIntegrationBtn');
            
            if (startBtn) {
                startBtn.disabled = !taskId;
            }
        }
        
        // 开始整合分析
        async handleStartIntegration() {
            const taskId = document.getElementById('contractTaskSelector')?.value;
            
            if (!taskId) {
                this.showError('请选择一个合约分析任务');
                return;
            }
            
            try {
                this.showLoading(true);
                this.hideError();
                
                // 直接调用后端进行整合分析
                const result = await this.performIntegrationAnalysis({
                    taskId
                });
                
                this.currentAnalysisResult = result;
                this.renderAnalysisResults(result);
                
            } catch (error) {
                console.error('整合分析失败:', error);
                this.showError('整合分析失败: ' + error.message);
            } finally {
                this.showLoading(false);
            }
        }
        
        // 执行整合分析 - 确保接口不变
        async performIntegrationAnalysis({ taskId }) {
            try {
                // 构建请求参数
                const requestBody = {
                    contract_task_id: taskId // 使用新的参数名
                };
                
                // 如果选择了代理商任务，添加到请求中
                if (this.selectedAgentTaskId) {
                    requestBody.agent_task_id = this.selectedAgentTaskId;
                }
                
                // 调用后端的整合分析API - 接口路径和参数保持不变
                const response = await fetch('/api/link/integration-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(requestBody)
                });
                
                if (!response.ok) {
                    throw new Error(`后端分析失败: ${response.status} ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.status !== 'success') {
                    throw new Error(result.error || '后端分析返回错误');
                }
                
                return result.data;
                
            } catch (error) {
                console.error('❌ [合约整合] 后端分析失败:', error);
                throw error; // 不再回退到前端计算，直接抛出错误
            }
        }

        // 获取风险类型分布
        getRiskTypeDistribution(risks) {
            const distribution = {};
            
            risks.forEach(risk => {
                const type = risk.detection_type || risk.risk_type || risk.reason || '未知类型';
                distribution[type] = (distribution[type] || 0) + 1;
            });
            
            return distribution;
        }
        
        // 构建网络图数据
        buildNetworkData(contractRisks, bdData) {
            // 简化版本，返回基础网络结构
            const nodes = [];
            const links = [];
            const categories = [
                { name: 'BD团队', itemStyle: { color: '#5470c6' } },
                { name: '风险用户', itemStyle: { color: '#ff4d4f' } }
            ];
            
            if (bdData && bdData.bd_trees) {
                // 添加BD节点
                bdData.bd_trees.forEach(bdTree => {
                    const bdInfo = bdTree.user_info;
                    const bdName = bdInfo.bd_name || bdInfo.user_name;
                    
                    nodes.push({
                        id: `BD_${bdName}`,
                        name: bdName,
                        type: 'bd',
                        category: 0,
                        symbolSize: 50,
                        total_risks: 0, // 将在后面计算
                        high_risks: 0,
                        member_count: 0
                    });
                });
            }
            
            return { nodes, links, categories };
        }
        

        
        // 查找成员所属的BD
        findBDForMember(memberId, bdData) {
            if (!bdData || !bdData.bd_trees) return null;
            
            for (const bdTree of bdData.bd_trees) {
                const bdName = bdTree.user_info.bd_name || bdTree.user_info.user_name;
                const memberIds = new Set();
                this.extractMemberIdsFromTree(bdTree, memberIds);
                
                if (memberIds.has(memberId)) {
                    return bdName;
                }
            }
            
            return null;
        }
        
        // 模拟API调用已删除
        
        // 渲染分析结果
        renderAnalysisResults(result) {
            // 保存当前分析结果，供其他方法使用
            this.currentAnalysisResult = result;
            
            const { link_analysis, integration_info } = result;
            
            // 首先显示结果区域
            const resultsDiv = document.getElementById('analysisResults');
            if (resultsDiv) {
                resultsDiv.style.display = 'block';
        
            } else {
                return;
            }
            
            // 更新概览统计
            this.updateOverviewStats(link_analysis, integration_info);
            
            // 初始化BD筛选控制区域
            this.initializeBDFilterControls(link_analysis.bd_risk_stats);
            
            // 延迟一些时间确保DOM更新和显示动画完成
            setTimeout(() => {
        
                

                

                
                // 使用requestAnimationFrame确保在下一帧应用BD筛选
                requestAnimationFrame(() => {
                    requestAnimationFrame(() => {
                
                        // 应用BD筛选逻辑，这将自动渲染筛选后的内容
                        this.applyBDFilter();
                    });
                });
                
                // 滚动到结果区域
                setTimeout(() => {
                    const resultsDiv = document.getElementById('analysisResults');
                    if (resultsDiv) {
                        resultsDiv.scrollIntoView({ behavior: 'smooth', block: 'start' });
                
                    }
                }, 500);
                
        
            }, 300);
        }
        
        // 更新概览统计
        updateOverviewStats(linkAnalysis, integrationInfo) {
            const { summary } = linkAnalysis;
            
    
            
            // 更新统计数字
            const totalContractRisksEl = document.getElementById('totalContractRisks');
            const linkedMembersEl = document.getElementById('linkedMembers');
            const involvedBDsEl = document.getElementById('involvedBDs');
            const crossBDPatternsEl = document.getElementById('crossBDPatterns');
            
            if (totalContractRisksEl) {
                totalContractRisksEl.textContent = integrationInfo.contract_risks_count || 0;
            }
            
            if (linkedMembersEl) {
                linkedMembersEl.textContent = summary.linked_members || 0;
            }
            
            if (involvedBDsEl) {
                const bdCount = summary.bd_count || 0;
                involvedBDsEl.textContent = bdCount;
        
            } else {
                console.error('❌ [合约整合] 未找到 involvedBDs 元素');
            }
            
            if (crossBDPatternsEl) {
                // 优先使用统一分类结果中的跨BD对敲数量，如果没有则使用原有的统计
                let crossBDCount = summary.cross_bd_patterns || 0;
                
                // 检查是否有统一分类结果
                if (linkAnalysis && linkAnalysis.wash_trading_classification) {
                    const classification = linkAnalysis.wash_trading_classification;
                    const crossBDCategory = classification.summary?.categories?.['跨BD对敲'];
                    if (crossBDCategory) {
                        crossBDCount = crossBDCategory.count;
            
                    }
                }
                
                crossBDPatternsEl.textContent = crossBDCount;
            }
        }


        
        // 渲染网络关系图
        renderNetworkChart(networkData, retryCount = 0) {
            const chartDom = document.getElementById('contractBDNetworkChart');
            if (!chartDom) {
                console.error('❌ [合约整合] 未找到BD金字塔图表容器');
                return;
            }
            
            // 确保容器可见性和尺寸
            this.ensureContainerVisible(chartDom);
            
            // 检查容器尺寸，最多重试10次
            if ((chartDom.clientWidth === 0 || chartDom.clientHeight === 0) && retryCount < 10) {
                console.warn(`⚠️ [合约整合] 金字塔图容器尺寸为0，第${retryCount + 1}次重试`);
                setTimeout(() => this.renderNetworkChart(networkData, retryCount + 1), 300);
                return;
            }
            
            // 如果重试次数达到上限但仍然尺寸为0，强制设置最小尺寸并等待渲染
            if (chartDom.clientWidth === 0 || chartDom.clientHeight === 0) {
                console.warn('⚠️ [合约整合] 金字塔图容器尺寸仍为0，强制设置最小尺寸');
                chartDom.style.width = '800px';
                chartDom.style.height = '600px';
                chartDom.style.display = 'block';
                
                // 强制回流重绘
                chartDom.offsetHeight;
                
                // 再次检查，如果还是0就跳过渲染
                if (chartDom.clientWidth === 0 || chartDom.clientHeight === 0) {
                    console.error('❌ [合约整合] 无法获取有效的容器尺寸，跳过金字塔图渲染');
                    return;
                }
            }
            
            if (this.charts.networkChart) {
                this.charts.networkChart.dispose();
            }
            
            try {
                this.charts.networkChart = echarts.init(chartDom);
        
            } catch (error) {
                console.error('❌ [合约整合] BD金字塔图表初始化失败:', error);
                return;
            }
            
            // 检查数据结构
            if (!networkData.pyramid_trees || networkData.pyramid_trees.length === 0) {
                console.warn('⚠️ [合约整合] 没有BD金字塔数据');
                this.charts.networkChart.setOption({
                    title: {
                        text: 'BD金字塔风险链路图',
                        subtext: '暂无风险数据',
                        left: 'center',
                        top: 'center'
                    }
                });
                return;
            }
            
            // 准备ECharts的树形数据
            const echartsData = this.preparePyramidTreeData(networkData.pyramid_trees);
            
            // 计算总节点数用于自动缩放
            const totalNodes = echartsData.reduce((count, tree) => {
                return count + this.countTreeNodes(tree);
            }, 0);
            
            // 根据节点数量自动计算缩放比例
            let initialZoom = 1;
            if (totalNodes > 100) {
                initialZoom = 0.3;
            } else if (totalNodes > 50) {
                initialZoom = 0.5;
            } else if (totalNodes > 20) {
                initialZoom = 0.7;
            }
            
            const option = {
                title: {
                    text: echartsData.length === 1 ? 
                        `${echartsData[0].bd_name || echartsData[0].name} - BD金字塔风险链路图` : 
                        'BD金字塔风险链路图',
                    left: 'right',
                    top: 'top',
                    fontSize: echartsData.length === 1 ? 18 : 16,
                    fontWeight: 'bold',
                    subtext: echartsData.length === 1 ? 
                        '🔍 可缩放平移 | 📄 点击节点查看详情' : 
                        `共 ${echartsData.length} 个BD金字塔 | 总节点数: ${totalNodes}`,
                    subtextStyle: {
                        fontSize: 12,
                        color: '#666'
                    }
                },
                // 工具箱已隐藏，使用下方的自定义控制按钮
                toolbox: {
                    show: false // 隐藏默认工具栏
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (params.dataType === 'node') {
                            const data = params.data;
                            const riskCount = data.risk_count || 0;
                            const digitalId = data.digital_id || '未知';
                            const memberId = data.member_id || '未知';
                            
                            return `
                                <div style="max-width: 300px;">
                                    <strong style="font-size: 14px;">${data.name}</strong><br/>
                                    <span style="color: #666;">Digital ID: ${digitalId}</span><br/>
                                    <span style="color: #666;">Member ID: ${memberId.length > 10 ? memberId.substring(0, 10) + '...' : memberId}</span><br/>
                                    <span style="color: ${riskCount > 0 ? '#ff4d4f' : '#52c41a'};">
                                        风险数量: ${riskCount}
                                    </span><br/>
                                    ${data.has_risk ? '<span style="color: #ff4d4f;">⚠️ 存在风险</span>' : '<span style="color: #52c41a;">✓ 无风险</span>'}
                                    <div style="margin-top: 5px; color: #999; font-size: 12px;">
                                        💡 点击查看详细信息
                                    </div>
                                </div>
                            `;
                        }
                        return '';
                    }
                },
                legend: {
                    data: echartsData.map((treeData, index) => {
                        const totalTrees = echartsData.length;
                        return totalTrees === 1 ? 
                            `${treeData.bd_name || treeData.name} 金字塔风险链路图` : 
                            `BD金字塔-${index + 1}`;
                    }),
                    bottom: '5%',
                    show: echartsData.length > 1 // 只有多个BD时才显示图例
                },
                series: echartsData.map((treeData, index) => {
                    // 计算动态布局：根据数据量决定列数
                    const totalTrees = echartsData.length;
                    let cols, treeWidth, leftOffset, topOffset, height;
                    
                    if (totalTrees === 1) {
                        // 单个BD金字塔：居中显示，使用更多空间，调整位置让金字塔往下移动
                        treeWidth = 80;
                        leftOffset = 10;
                        topOffset = '20%';
                        height = '70%';
                    } else if (totalTrees <= 3) {
                        cols = totalTrees;
                        treeWidth = Math.floor(90 / cols);
                        leftOffset = 5 + (index * (90 / cols));
                        topOffset = '10%';
                        height = '70%';
                    } else if (totalTrees <= 6) {
                        cols = 3;
                        treeWidth = 28;
                        leftOffset = 5 + ((index % 3) * 30);
                        topOffset = '10%';
                        height = '70%';
                    } else {
                        cols = 4;
                        treeWidth = 22;
                        leftOffset = 2 + ((index % 4) * 24);
                        topOffset = `${10 + Math.floor(index / 4) * 45}%`;
                        height = '40%';
                    }
                    
                    return {
                        name: totalTrees === 1 ? `${treeData.bd_name || treeData.name} 金字塔风险链路图` : `BD金字塔-${index + 1}`,
                        type: 'tree',
                        data: [treeData],
                        left: `${leftOffset}%`,
                        top: topOffset,
                        width: `${treeWidth}%`,
                        height: height,
                        layout: 'orthogonal',
                        orient: 'vertical',
                        symbol: 'circle',
                        symbolSize: (value, params) => {
                            // 根据风险数量和节点层级动态调整大小
                            const riskCount = params.data.risk_count || 0;
                            const baseSize = totalTrees === 1 ? 25 : 20;
                            const riskBonus = Math.min(riskCount * 3, 15); // 风险越多节点越大
                            return Math.min(baseSize + riskBonus, totalTrees === 1 ? 50 : 40);
                        },
                        edgeShape: 'polyline',
                        edgeForkPosition: '63%',
                        initialTreeDepth: totalTrees === 1 ? -1 : 3, // 单个金字塔时展开所有层级
                        // 优化树形布局参数，减少连接线长度
                        nodeGap: totalTrees === 1 ? 30 : 20, // 同级节点间距（水平间距）
                        layerGap: totalTrees === 1 ? 60 : 40, // 层级间距（垂直间距）
                        lineStyle: {
                            width: totalTrees === 1 ? 3 : 2,
                            color: totalTrees === 1 ? '#1890ff' : '#999',
                            curveness: 0.1, // 添加一点弯曲度，让连接线更自然
                            type: 'solid' // 使用实线
                        },
                        // 启用缩放和拖拽
                        roam: true, // 允许平移和缩放
                        zoom: initialZoom, // 初始缩放比例
                        center: ['50%', '50%'], // 中心位置
                        // 确保节点可点击
                        silent: false, // 允许响应事件
                        animation: false, // 禁用动画避免事件冲突
                        label: {
                            backgroundColor: 'transparent',
                            position: 'bottom',
                            distance: totalTrees === 1 ? 12 : 8, // 减少标签与节点的距离
                            fontSize: totalTrees === 1 ? 12 : 10,
                            rich: {
                                name: {
                                    fontSize: totalTrees === 1 ? 14 : 12,
                                    fontWeight: 'bold'
                                },
                                level: {
                                    fontSize: totalTrees === 1 ? 12 : 10,
                                    color: '#666'
                                },
                                risk: {
                                    fontSize: totalTrees === 1 ? 11 : 9,
                                    color: '#ff4d4f'
                                }
                            },
                            formatter: function(params) {
                                const data = params.data;
                                const riskCount = data.risk_count || 0;
                                
                                if (totalTrees === 1) {
                                    // 单个金字塔时显示更详细信息
                                    if (riskCount > 0) {
                                        return `{name|${data.name}}\n{risk|⚠️ ${riskCount}个风险}`;
                                    } else {
                                        return `{name|${data.name}}\n✓ 无风险`;
                                    }
                                } else {
                                    // 多个金字塔时使用简洁显示
                                    if (riskCount > 0) {
                                        return `{name|${data.name}}\n⚠️${riskCount}`;
                                    } else {
                                        return `{name|${data.name}}`;
                                    }
                                }
                            }
                        },
                        leaves: {
                            label: {
                                position: 'bottom',
                                distance: 5
                            }
                        },
                        emphasis: {
                            focus: 'descendant',
                            scale: 1.15, // 稍微减少悬停放大倍数
                            itemStyle: {
                                shadowBlur: 15,
                                shadowColor: 'rgba(0, 0, 0, 0.25)'
                            },
                            lineStyle: {
                                width: totalTrees === 1 ? 4 : 3, // 悬停时连接线加粗
                                shadowBlur: 5,
                                shadowColor: 'rgba(24, 144, 255, 0.3)'
                            }
                        },
                        itemStyle: {
                            color: function(params) {
                                const riskCount = params.data.risk_count || 0;
                                if (riskCount === 0) return '#52c41a'; // 绿色：无风险
                                if (riskCount <= 2) return '#faad14'; // 黄色：低风险
                                if (riskCount <= 5) return '#fa8c16'; // 橙色：中风险
                                return '#ff4d4f'; // 红色：高风险
                            },
                            borderColor: '#fff',
                            borderWidth: 2
                        }
                    };
                })
            };
            
            this.charts.networkChart.setOption(option);
            
            // 绑定节点点击事件
            this.charts.networkChart.on('click', (params) => {
                // Tree图表的节点点击事件，检查是否有data属性且data包含节点信息
                if (params.componentType === 'series' && params.data && params.data.name) {
                    this.showNodeDetail(params.data);
                }
            });
            
            // 自适应容器大小变化
            const resizeObserver = new ResizeObserver(() => {
                if (this.charts.networkChart) {
                    this.charts.networkChart.resize();
                }
            });
            resizeObserver.observe(chartDom);
            
        }
        
        // 准备金字塔树数据供ECharts使用
        preparePyramidTreeData(pyramidTrees) {
            const convertNode = (node) => {
                // 使用统一的名称提取方法
                const displayName = this.extractNodeDisplayName(node);
                const user_info = node.user_info || {};
                
                const result = {
                    name: displayName,
                    value: node.risk_count || 0,
                    symbolSize: node.symbolSize || 20,
                    itemStyle: node.itemStyle,
                    user_info: user_info,
                    has_risk: node.has_risk,
                    risk_count: node.risk_count || 0,
                    max_risk_score: node.max_risk_score || 0,
                    // 保留原始节点的关键字段用于详情显示
                    digital_id: node.digital_id,
                    member_id: node.member_id,
                    user_name: node.user_name,
                    children: []
                };
                
                if (node.children && node.children.length > 0) {
                    result.children = node.children.map(child => convertNode(child));
                }
                
                return result;
            };
            
            return pyramidTrees.map(tree => convertNode(tree));
        }
        
        // 计算树中的节点总数（新增方法）
        countTreeNodes(node) {
            if (!node) return 0;
            let count = 1; // 当前节点
            if (node.children && node.children.length > 0) {
                count += node.children.reduce((sum, child) => sum + this.countTreeNodes(child), 0);
            }
            return count;
        }
        

        
        // 渲染BD详细统计表格
        renderBDDetailStatsTable(bdRiskStats) {
            const tbody = document.querySelector('#bdDetailStatsTable tbody');
            if (!tbody) return;
            
            tbody.innerHTML = '';
            
            bdRiskStats.forEach(bd => {
                const row = document.createElement('tr');
                
                // 获取主要风险类型
                const riskTypes = Object.entries(bd.risk_types || {});
                const mainRiskType = riskTypes.length > 0 ? 
                    riskTypes.sort((a, b) => b[1] - a[1])[0][0] : '未知';
                
                row.innerHTML = `
                    <td>${bd.bd_name}</td>
                    <td>${bd.total_risks}</td>
                    <td><span class="badge bg-danger">${bd.risk_levels?.high || 0}</span></td>
                    <td><span class="badge bg-warning">${bd.risk_levels?.medium || 0}</span></td>
                    <td><span class="badge bg-success">${bd.risk_levels?.low || 0}</span></td>
                    <td>${bd.member_count}</td>
                    <td>${bd.avg_risk_score.toFixed(2)}</td>
                    <td>${mainRiskType}</td>
                    <td>
                        <button class="btn btn-outline-info btn-sm" onclick="contractIntegrationService.showBDDetail('${bd.bd_name}')" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 渲染跨BD风险模式
        renderCrossBDPatterns(crossBDPatterns) {
            const container = document.getElementById('crossBDPatternsContainer');
            if (!container) return;
            
            // 检查是否有统一分类结果，优先显示分类统计
            if (this.currentAnalysisResult && this.currentAnalysisResult.link_analysis && 
                this.currentAnalysisResult.link_analysis.wash_trading_classification) {
                
                this.renderWashTradingClassification(this.currentAnalysisResult.link_analysis.wash_trading_classification);
                return;
            }
            
            // 如果没有统一分类结果，显示传统的跨BD模式
            if (crossBDPatterns.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-info-circle fs-1"></i>
                        <p class="mt-2">未发现跨BD风险关联模式</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = '';
            
            crossBDPatterns.forEach((pattern, index) => {
                const card = document.createElement('div');
                card.className = 'card mb-3';
                
                const correlationScore = (pattern.risk_correlation_score * 100).toFixed(1);
                const correlationClass = pattern.risk_correlation_score > 0.5 ? 'danger' : 'warning';
                
                card.innerHTML = `
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">跨BD模式 #${index + 1}</h6>
                        <span class="badge bg-${correlationClass}">关联度: ${correlationScore}%</span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>时间窗口:</strong><br>
                                <span class="text-muted">${pattern.time_window}</span>
                            </div>
                            <div class="col-md-4">
                                <strong>涉及BD:</strong><br>
                                <span class="text-muted">${pattern.involved_bds.join(', ')}</span>
                            </div>
                            <div class="col-md-2">
                                <strong>风险数量:</strong><br>
                                <span class="text-muted">${pattern.total_risks}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>模式类型:</strong><br>
                                <span class="badge bg-info">${pattern.pattern_type}</span>
                            </div>
                        </div>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }
        
        // 渲染对敲交易统一分类结果
        renderWashTradingClassification(classificationData) {
            const container = document.getElementById('crossBDPatternsContainer');
            if (!container) return;
            
            const classification = classificationData.classification || {};
            const summary = classificationData.summary || {};
            
            container.innerHTML = `
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> 对敲交易分类统计</h6>
                            <p class="mb-0">基于BD归属关系对所有对敲交易进行统一分类，总计 <strong>${summary.total_records || 0}</strong> 个对敲交易记录</p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    ${this.getSortedClassificationEntries(classification).map(([category, records]) => {
                        const categoryInfo = summary.categories?.[category] || {};
                        const count = categoryInfo.count || 0;
                        const percentage = categoryInfo.percentage || 0;
                        
                        let badgeClass = 'secondary';
                        let iconClass = 'bi-circle';
                        
                        if (category === '跨BD对敲') {
                            badgeClass = 'danger';
                            iconClass = 'bi-shuffle';
                        } else if (category === '同BD对敲') {
                            badgeClass = 'warning';
                            iconClass = 'bi-building';
                        } else if (category === '自成交对敲') {
                            badgeClass = 'info';
                            iconClass = 'bi-arrow-repeat';
                        } else if (category === '直客对敲') {
                            badgeClass = 'success';
                            iconClass = 'bi-person-check';
                        } else if (category === '未分类') {
                            badgeClass = 'light';
                            iconClass = 'bi-question-circle';
                        }
                        
                        return `
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="${iconClass}"></i> ${category}
                                        </h6>
                                        <span class="badge bg-${badgeClass}">${count}个</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>占比:</span>
                                            <strong>${percentage}%</strong>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-${badgeClass}" style="width: ${percentage}%"></div>
                                        </div>
                                        ${this.renderSubtypeBreakdown(categoryInfo.subtypes || {})}
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
        }
        
        // 获取排序后的分类条目（未分类放到最后）
        getSortedClassificationEntries(classification) {
            // 定义分类的显示优先级
            const categoryOrder = [
                '跨BD对敲',
                '同BD对敲', 
                '自成交对敲',
                '直客对敲',
                '未分类'  // 未分类放到最后
            ];
            
            const entries = Object.entries(classification);
            
            // 按照预定义的顺序排序
            return entries.sort(([categoryA], [categoryB]) => {
                const indexA = categoryOrder.indexOf(categoryA);
                const indexB = categoryOrder.indexOf(categoryB);
                
                // 如果分类不在预定义列表中，放到最后
                const orderA = indexA === -1 ? 999 : indexA;
                const orderB = indexB === -1 ? 999 : indexB;
                
                return orderA - orderB;
            });
        }
        
        // 渲染子类型分解
        renderSubtypeBreakdown(subtypes) {
            if (!subtypes || Object.keys(subtypes).length === 0) {
                return '<small class="text-muted">无子类型数据</small>';
            }
            
            return `
                <div class="mt-2">
                    <small class="text-muted d-block mb-1">子类型分布:</small>
                    ${Object.entries(subtypes).map(([subtype, count]) => 
                        `<small class="badge bg-light text-dark me-1">${subtype}: ${count}</small>`
                    ).join('')}
                </div>
            `;
        }
        

        
        // 处理标签页切换
        handleTabChange(event) {
            const targetId = event.target.getAttribute('data-bs-target').replace('#', '');

            // 如果需要在标签页切换时进行特殊处理，可以在这里添加逻辑
        }
        

        
        // 确保容器可见性和基本尺寸
        ensureContainerVisible(container) {
            if (!container) return false;
            
            try {
                // 确保容器本身可见
                container.style.display = 'block';
                container.style.visibility = 'visible';
                container.style.opacity = '1';
                
                // 确保所有父容器也可见
                let parent = container.parentElement;
                while (parent && parent !== document.body) {
                    const styles = window.getComputedStyle(parent);
                    if (styles.display === 'none') {
                        parent.style.display = 'block';
                    }
                    parent = parent.parentElement;
                }
                
                // 强制重新计算布局
                container.offsetHeight;
                
                // 最后检查是否成功获取尺寸
                return container.clientWidth > 0 && container.clientHeight > 0;
            } catch (error) {
                console.error('❌ [合约整合] 确保容器可见性时出错:', error);
                return false;
            }
        }
        
        // 初始化图表（优化版本）
        initializeCharts() {
            // 防抖的窗口大小变化处理
            const debouncedResize = this._debounce(() => {
                Object.values(this.charts).forEach(chart => {
                    if (chart && !chart.isDisposed()) {
                        try {
                            chart.resize();
                        } catch (error) {
                            console.warn('图表resize失败:', error);
                        }
                    }
                });
            }, 300);
            
            // 缓存处理函数用于后续清理
            this._resizeHandler = debouncedResize;
            window.addEventListener('resize', this._resizeHandler);
        }

        // 清理资源（新增方法）
        cleanup() {
            try {
                // 清理图表
                Object.values(this.charts).forEach(chart => {
                    if (chart && !chart.isDisposed()) {
                        chart.dispose();
                    }
                });
                this.charts = {};
                
                // 移除事件监听器
                this.unbindEvents();
                
                // 移除resize监听器
                if (this._resizeHandler) {
                    window.removeEventListener('resize', this._resizeHandler);
                    this._resizeHandler = null;
                }
                
                // 清理防抖函数缓存
                this._debouncedFunctions.clear();
                
                // 清理临时DOM元素
                const tempElements = ['riskDetailsLoading', 'nodeRiskDetailsModal', 'nodeDetailContainer'];
                tempElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.remove();
                    }
                });
                
                // 清理热力图和时间线相关的数据
                if (this.heatmapData) {
                    delete this.heatmapData;
                }
                if (this.timelineData) {
                    delete this.timelineData;
                }
                
    
            } catch (error) {
                console.error('❌ 资源清理时发生错误:', error);
            }
        }
        
        // 显示BD详情
        showBDDetail(bdName) {
            if (!this.currentAnalysisResult) return;
            
            const bdData = this.currentAnalysisResult.link_analysis.bd_risk_stats.find(bd => bd.bd_name === bdName);
            if (!bdData) return;
            
            const modal = new bootstrap.Modal(document.getElementById('bdDetailModal'));
            const content = document.getElementById('bdDetailContent');
            
            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>BD名称</td><td>${bdData.bd_name}</td></tr>
                            <tr><td>总风险数</td><td>${bdData.total_risks}</td></tr>
                            <tr><td>涉及用户数</td><td>${bdData.member_count}</td></tr>
                            <tr><td>平均风险评分</td><td>${bdData.avg_risk_score.toFixed(2)}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>风险分布</h6>
                        <div class="mb-2">
                            <span class="badge bg-danger me-2">高风险: ${bdData.risk_levels?.high || 0}</span>
                            <span class="badge bg-warning me-2">中风险: ${bdData.risk_levels?.medium || 0}</span>
                            <span class="badge bg-success">低风险: ${bdData.risk_levels?.low || 0}</span>
                        </div>
                        <h6 class="mt-3">风险类型分布</h6>
                        <div>
                            ${Object.entries(bdData.risk_types || {}).map(([type, count]) => 
                                `<span class="badge bg-secondary me-1">${type}: ${count}</span>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            `;
            
            modal.show();
        }
        
        // 显示加载状态
        showLoading(show) {
            const indicator = document.getElementById('loadingIndicator');
            const results = document.getElementById('analysisResults');
            
            if (indicator) {
                indicator.style.display = show ? 'block' : 'none';
            }
            
            if (!show && results) {
                results.style.display = 'none';
            }
        }
        
        // 显示错误信息
        showError(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            
            if (errorAlert && errorMessage) {
                errorMessage.textContent = message;
                errorAlert.style.display = 'block';
            }
        }
        
        // 隐藏错误信息
        hideError() {
            const errorAlert = document.getElementById('errorAlert');
            if (errorAlert) {
                errorAlert.style.display = 'none';
            }
        }
        
        // 更新数据源指示器
        updateDataSourceIndicator(type, text) {
            const indicator = document.getElementById('dataSourceIndicator');
            if (!indicator) return;
            
            // 清除之前的样式
            indicator.className = 'badge';
            
            if (type === 'real') {
                indicator.className += ' bg-success';
                indicator.innerHTML = '<i class="bi bi-check-circle"></i> ' + text;
                    } else if (type === 'none') {
            indicator.className += ' bg-secondary text-light';
            indicator.innerHTML = '<i class="bi bi-x-circle"></i> ' + text;
            } else {
                indicator.className += ' bg-secondary';
                indicator.innerHTML = '<i class="bi bi-database"></i> ' + text;
            }
        }
        
        // 获取风险等级对应的样式类
        getRiskBadgeClass(score) {
            if (score >= 70) return 'bg-danger';
            if (score >= 40) return 'bg-warning';
            return 'bg-success';
        }

        // ========== BD筛选功能相关方法 ==========

        // 初始化BD筛选控制区域
        initializeBDFilterControls(bdRiskStats) {
    
            
            // 显示BD筛选控制卡片
            const bdFilterCard = document.getElementById('bdFilterCard');
            if (bdFilterCard) {
                bdFilterCard.style.display = 'block';
            }

            // 填充BD选择下拉框
            this.populateBDSelectOptions(bdRiskStats);

            // 重新绑定BD筛选事件（确保事件监听器正常工作）
            this.bindBDFilterEvents();

            // 根据当前筛选状态更新显示
            this.applyBDFilter();
        }

        // 填充BD选择下拉框选项
        populateBDSelectOptions(bdRiskStats) {
            const bdSelectFilter = document.getElementById('bdSelectFilter');
            if (!bdSelectFilter || !bdRiskStats) return;

            // 清空现有选项（保留默认选项）
            bdSelectFilter.innerHTML = '<option value="">选择BD团队显示其金字塔风险链路图</option>';

            // 按风险评分排序BD
            const sortedBDs = [...bdRiskStats].sort((a, b) => b.avg_risk_score - a.avg_risk_score);

            // 添加BD选项
            sortedBDs.forEach((bd, index) => {
                const option = document.createElement('option');
                option.value = bd.bd_name;
                
                const riskLevel = bd.avg_risk_score >= 70 ? '高风险' : 
                                bd.avg_risk_score >= 40 ? '中风险' : '低风险';
                const rankText = index < 10 ? `[${index + 1}] ` : '';
                
                option.textContent = `${rankText}${bd.bd_name} - ${riskLevel} (${bd.total_risks}个风险)`;
                
                // 高风险BD使用红色标识
                if (bd.avg_risk_score >= 70) {
                    option.style.color = '#dc3545';
                    option.style.fontWeight = 'bold';
                } else if (bd.avg_risk_score >= 40) {
                    option.style.color = '#fd7e14';
                }
                
                bdSelectFilter.appendChild(option);
            });


        }

        // 处理BD筛选变化
        handleBDFilterChange(event) {
            const selectedBD = event.target.value;
            this.bdFilterState.selectedBD = selectedBD;
            


            this.applyBDFilter();
        }

        // 清除BD筛选
        clearBDFilter() {
            this.bdFilterState.selectedBD = '';
            
            const bdSelectFilter = document.getElementById('bdSelectFilter');
            if (bdSelectFilter) {
                bdSelectFilter.value = '';
            }

            this.applyBDFilter();
            this.hideBDFilterStatus();
        }

        // 重置BD筛选（显示默认BD）
        showAllBDs() {
            // 重置筛选状态
            this.bdFilterState.selectedBD = '';
            
            // 更新UI
            const bdSelectFilter = document.getElementById('bdSelectFilter');
            if (bdSelectFilter) bdSelectFilter.value = '';

            this.applyBDFilter();
        }

        // 查看选中BD
        showSelectedBD() {
            const selectedBD = this.bdFilterState.selectedBD;
            
            if (!selectedBD) {
                this.showError('请先选择一个BD团队');
                return;
            }

            this.applyBDFilter();
            this.showBDFilterStatus(`正在查看BD团队: ${selectedBD}`);
        }



        // 应用BD筛选
        applyBDFilter() {
            if (!this.currentAnalysisResult) return;
            
            const { bd_risk_stats } = this.currentAnalysisResult.link_analysis;
            let filteredBDStats = [...bd_risk_stats];

            // 应用筛选条件
            if (this.bdFilterState.selectedBD) {
                // 筛选特定BD
                filteredBDStats = filteredBDStats.filter(bd => bd.bd_name === this.bdFilterState.selectedBD);
                this.showBDFilterStatus(`筛选显示: ${this.bdFilterState.selectedBD}`);
            } else {
                // 默认显示第一个BD（风险最高的）
                const sortedBDs = [...bd_risk_stats].sort((a, b) => b.avg_risk_score - a.avg_risk_score);
                if (sortedBDs.length > 0) {
                    filteredBDStats = [sortedBDs[0]];
                    this.showBDFilterStatus(`当前显示: ${sortedBDs[0].bd_name} (风险最高的BD，可通过筛选控制查看其他BD)`);
                } else {
                    this.hideBDFilterStatus();
                }
            }

            // 重新渲染相关组件
            this.renderFilteredAnalysis(filteredBDStats);
        }

        // 渲染筛选后的分析结果
        renderFilteredAnalysis(filteredBDStats) {
            
            // BD详情统计表独立于筛选，始终显示全部BD用户
            if (this.currentAnalysisResult?.link_analysis?.bd_risk_stats) {
                this.renderBDDetailStatsTable(this.currentAnalysisResult.link_analysis.bd_risk_stats);
            }
            
            // 重新渲染跨BD风险模式（使用原始数据，因为跨BD模式不受单BD筛选影响）
            if (this.currentAnalysisResult?.link_analysis?.cross_bd_patterns) {
                this.renderCrossBDPatterns(this.currentAnalysisResult.link_analysis.cross_bd_patterns);
            }
            
            // 重新渲染网络图（只显示筛选的BD相关数据）
            const filteredNetworkData = this.buildFilteredNetworkData(filteredBDStats);
            this.renderNetworkChart(filteredNetworkData);
        }

        // 构建筛选后的网络数据
        buildFilteredNetworkData(filteredBDStats) {
            if (!this.currentAnalysisResult) return { pyramid_trees: [] };
            
            const bdNames = new Set(filteredBDStats.map(bd => bd.bd_name));
            const originalNetworkData = this.currentAnalysisResult.link_analysis.network_data;
            
            // 适应新的金字塔树数据结构
            if (!originalNetworkData || !originalNetworkData.pyramid_trees) {
                console.warn('⚠️ [合约整合] 网络数据缺少pyramid_trees结构');
                return { pyramid_trees: [] };
            }
            
            // 筛选金字塔树：只保留指定BD的树
            const filteredPyramidTrees = originalNetworkData.pyramid_trees.filter(tree => 
                bdNames.has(tree.bd_name)
            );
            
            if (filteredPyramidTrees.length > 0) {
                const sampleTree = filteredPyramidTrees[0];
                const childrenSummary = sampleTree.children ? 
                    sampleTree.children.map(child => ({
                        name: this.extractNodeDisplayName(child),
                        has_risk: child.has_risk,
                        children_count: child.children ? child.children.length : 0
                    })) : [];
                

            }
            
            return {
                pyramid_trees: filteredPyramidTrees
            };
                }

        // 提取节点显示名称（兼容不同的数据结构）
        extractNodeDisplayName(node) {
            // 优先使用已有的name字段
            if (node.name && node.name !== 'undefined') {
                return node.name;
            }
            
            // 如果没有name字段，从user_info中提取
            if (node.user_info) {
                const userInfo = node.user_info;
                
                // 对于BD节点，优先显示BD名称
                if (userInfo.agent_flag === 'BD' || userInfo.analyzed_level_type === 'BD') {
                    return userInfo.bd_name || userInfo.user_name || userInfo.digital_id || userInfo.member_id;
                }
                
                // 对于其他节点，显示digital_id的后5位数字
                let displayName = userInfo.digital_id || userInfo.user_name || userInfo.member_id;
                
                // 如果是8位数字，显示后5位
                if (displayName && /^\d{8}$/.test(displayName)) {
                    displayName = displayName.substr(-5);
                }
                
                return displayName || '未知用户';
            }
            
            // 兜底处理
            return node.id || '未知节点';
        }

        // 计算树的深度
        calculateTreeDepth(node) {
            if (!node.children || node.children.length === 0) {
                return 1;
            }
            
            let maxDepth = 0;
            for (const child of node.children) {
                maxDepth = Math.max(maxDepth, this.calculateTreeDepth(child));
            }
            
            return 1 + maxDepth;
        }



        // 显示BD筛选状态
        showBDFilterStatus(message) {
            const statusDiv = document.getElementById('bdFilterStatus');
            const statusText = document.getElementById('bdFilterStatusText');
            
            if (statusDiv && statusText) {
                statusText.textContent = message;
                statusDiv.style.display = 'block';
            }
        }

        // 隐藏BD筛选状态
        hideBDFilterStatus() {
            const statusDiv = document.getElementById('bdFilterStatus');
            if (statusDiv) {
                statusDiv.style.display = 'none';
            }
        }

        // 检查标签页是否活跃
        isTabActive(tabId) {
            const tab = document.getElementById(tabId);
            return tab && tab.classList.contains('active');
        }

        // BD金字塔风险链路分析
        async showBDPyramidRiskChain(bdName) {
            try {
                this.showLoading(true);
                
                // 调用后端API获取BD金字塔风险链路
                const response = await fetch(`/api/link-analysis/bd-pyramid-risk-chain/${encodeURIComponent(bdName)}`, {
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error(`获取BD金字塔风险链路失败: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || '获取BD金字塔风险链路失败');
                }
                
                // 显示BD金字塔风险链路模态框
                this.showBDPyramidRiskModal(result.pyramid_risk_chain);
                
            } catch (error) {
                console.error('❌ [合约整合] BD金字塔风险链路分析失败:', error);
                this.showError(`BD金字塔风险链路分析失败: ${error.message}`);
            } finally {
                this.showLoading(false);
            }
        }
        
        // 显示BD金字塔风险链路模态框
        showBDPyramidRiskModal(pyramidRiskData) {
            // 创建模态框HTML
            const modalHTML = `
                <div class="modal fade" id="bdPyramidRiskModal" tabindex="-1" aria-labelledby="bdPyramidRiskModalLabel" role="dialog" aria-modal="true">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="bdPyramidRiskModalLabel">
                                    <i class="bi bi-diagram-3"></i> BD金字塔风险链路分析 - ${pyramidRiskData.bd_name}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                            </div>
                            <div class="modal-body p-0">
                                <div class="container-fluid">
                                    <!-- 摘要统计 -->
                                    <div class="row bg-light p-3 mb-3">
                                        <div class="col-md-12">
                                            <h6 class="mb-3"><i class="bi bi-speedometer2"></i> 风险链路摘要</h6>
                                            <div class="row text-center">
                                                <div class="col-md-2">
                                                    <div class="card border-0 bg-transparent">
                                                        <div class="card-body p-2">
                                                            <h4 class="text-danger mb-1">${pyramidRiskData.summary.total_risk_nodes || 0}</h4>
                                                            <small class="text-muted">风险节点</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="card border-0 bg-transparent">
                                                        <div class="card-body p-2">
                                                            <h4 class="text-warning mb-1">${pyramidRiskData.summary.total_risks || 0}</h4>
                                                            <small class="text-muted">总风险数</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="card border-0 bg-transparent">
                                                        <div class="card-body p-2">
                                                            <h4 class="text-info mb-1">${pyramidRiskData.summary.total_risk_paths || 0}</h4>
                                                            <small class="text-muted">风险路径</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="card border-0 bg-transparent">
                                                        <div class="card-body p-2">
                                                            <h4 class="text-success mb-1">${(pyramidRiskData.summary.avg_path_length || 0).toFixed(1)}</h4>
                                                            <small class="text-muted">平均路径长度</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="card border-0 bg-transparent">
                                                        <div class="card-body p-2">
                                                            <h4 class="text-purple mb-1">${(pyramidRiskData.summary.max_path_risk || 0).toFixed(0)}</h4>
                                                            <small class="text-muted">最高风险分</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="card border-0 bg-transparent">
                                                        <div class="card-body p-2">
                                                            <h4 class="text-secondary mb-1">${(pyramidRiskData.summary.risk_concentration_score || 0).toFixed(2)}</h4>
                                                            <small class="text-muted">风险集中度</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 风险分布统计 -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0"><i class="bi bi-pie-chart"></i> 风险等级分布</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row text-center">
                                                        <div class="col-4">
                                                            <div class="text-danger">
                                                                <h5>${pyramidRiskData.summary.risk_level_distribution?.high || 0}</h5>
                                                                <small>高风险</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="text-warning">
                                                                <h5>${pyramidRiskData.summary.risk_level_distribution?.medium || 0}</h5>
                                                                <small>中风险</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="text-success">
                                                                <h5>${pyramidRiskData.summary.risk_level_distribution?.low || 0}</h5>
                                                                <small>低风险</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0"><i class="bi bi-layers"></i> 层级风险分布</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="level-risk-distribution">
                                                        ${this.buildLevelRiskDistributionHTML(pyramidRiskData.summary.level_risk_distribution || {})}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 标签页导航 -->
                                    <ul class="nav nav-tabs" id="pyramidRiskTabs" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="pyramid-chart-tab" data-bs-toggle="tab" data-bs-target="#pyramid-chart" type="button" role="tab">
                                                <i class="bi bi-diagram-3"></i> 金字塔链路图
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="risk-paths-tab" data-bs-toggle="tab" data-bs-target="#risk-paths" type="button" role="tab">
                                                <i class="bi bi-arrow-down-right"></i> 风险路径详情
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="risk-nodes-tab" data-bs-toggle="tab" data-bs-target="#risk-nodes" type="button" role="tab">
                                                <i class="bi bi-person-x"></i> 风险节点详情
                                            </button>
                                        </li>
                                    </ul>
                                    
                                    <!-- 标签页内容 -->
                                    <div class="tab-content" id="pyramidRiskTabContent">
                                        <!-- 金字塔链路图 -->
                                        <div class="tab-pane fade show active" id="pyramid-chart" role="tabpanel">
                                            <div class="p-3">
                                                <div class="mb-3">
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <button class="btn btn-outline-secondary active" data-pyramid-layout="tree">树形布局</button>
                                                        <button class="btn btn-outline-secondary" data-pyramid-layout="force">力导向</button>
                                                        <button class="btn btn-outline-secondary" data-pyramid-layout="radial">径向布局</button>
                                                    </div>
                                                    <span class="ms-3 text-muted">
                                                        <i class="bi bi-info-circle"></i> 节点大小表示风险数量，颜色表示风险等级
                                                    </span>
                                                </div>
                                                <div id="bdPyramidRiskChart" style="height: 600px;"></div>
                                            </div>
                                        </div>
                                        
                                        <!-- 风险路径详情 -->
                                        <div class="tab-pane fade" id="risk-paths" role="tabpanel">
                                            <div class="p-3">
                                                <div id="riskPathsTable">
                                                    ${this.buildRiskPathsTableHTML(pyramidRiskData.risk_paths || [])}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 风险节点详情 -->
                                        <div class="tab-pane fade" id="risk-nodes" role="tabpanel">
                                            <div class="p-3">
                                                <div id="riskNodesTable">
                                                    ${this.buildRiskNodesTableHTML(pyramidRiskData.risk_chain || [])}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-outline-primary" onclick="contractIntegrationService.exportBDPyramidRiskData('${pyramidRiskData.bd_name}')">
                                    <i class="bi bi-download"></i> 导出数据
                                </button>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 移除可能存在的旧模态框
            const existingModal = document.getElementById('bdPyramidRiskModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 添加新模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            
            // 显示模态框
            const modalElement = document.getElementById('bdPyramidRiskModal');
            const modal = new bootstrap.Modal(modalElement);
            
            // 添加事件监听器来处理无障碍性
            modalElement.addEventListener('shown.bs.modal', () => {
                // 模态框显示后，确保正确移除aria-hidden并设置焦点
                modalElement.removeAttribute('aria-hidden');
                modalElement.setAttribute('aria-modal', 'true');
                
                // 将焦点设置到第一个可聚焦元素（关闭按钮）
                const closeButton = modalElement.querySelector('.btn-close');
                if (closeButton) {
                    closeButton.focus();
                }
            });
            
            modalElement.addEventListener('hidden.bs.modal', () => {
                // 模态框隐藏后，重新设置aria-hidden
                modalElement.setAttribute('aria-hidden', 'true');
                modalElement.removeAttribute('aria-modal');
            });
            
            modal.show();
            
            // 渲染金字塔风险链路图
            setTimeout(() => {
                this.renderBDPyramidRiskChart(pyramidRiskData.visualization_data);
                this.bindPyramidModalEvents();
            }, 100);
        }
        
        // 构建层级风险分布HTML
        buildLevelRiskDistributionHTML(levelRiskDistribution) {
            let html = '';
            const levels = Object.keys(levelRiskDistribution).sort();
            
            for (const level of levels) {
                const count = levelRiskDistribution[level];
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">${level}</span>
                        <span class="badge bg-secondary">${count}</span>
                    </div>
                `;
            }
            
            return html || '<div class="text-muted">暂无数据</div>';
        }
        
        // 构建风险路径表格HTML
        buildRiskPathsTableHTML(riskPaths) {
            if (!riskPaths || riskPaths.length === 0) {
                return '<div class="text-center text-muted py-4">暂无风险路径数据</div>';
            }
            
            let html = `
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>路径ID</th>
                                <th>路径长度</th>
                                <th>总风险数</th>
                                <th>最高风险分</th>
                                <th>风险集中度</th>
                                <th>路径详情</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            for (const path of riskPaths) {
                const pathDetails = path.nodes.map(node => 
                    `${node.user_name}(${node.level_type})`
                ).join(' → ');
                
                html += `
                    <tr>
                        <td><code>${path.path_id}</code></td>
                        <td><span class="badge bg-info">${path.path_length}</span></td>
                        <td><span class="badge bg-warning">${path.total_risks}</span></td>
                        <td><span class="badge ${this.getRiskBadgeClass(path.max_path_risk)}">${path.max_path_risk.toFixed(0)}</span></td>
                        <td>${path.risk_concentration.toFixed(2)}</td>
                        <td class="text-truncate" style="max-width: 300px;" title="${pathDetails}">${pathDetails}</td>
                    </tr>
                `;
            }
            
            html += `
                        </tbody>
                    </table>
                </div>
            `;
            
            return html;
        }
        
        // 构建风险节点表格HTML
        buildRiskNodesTableHTML(riskChain) {
            if (!riskChain || riskChain.length === 0) {
                return '<div class="text-center text-muted py-4">暂无风险节点数据</div>';
            }
            
            // 展平风险链路节点
            const flattenNodes = (nodes) => {
                let result = [];
                for (const node of nodes) {
                    result.push(node);
                    if (node.children && node.children.length > 0) {
                        result = result.concat(flattenNodes(node.children));
                    }
                }
                return result;
            };
            
            const allNodes = flattenNodes(riskChain);
            
            let html = `
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>层级类型</th>
                                <th>风险数量</th>
                                <th>最高风险分</th>
                                <th>风险类型</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            for (const node of allNodes) {
                const riskTypes = node.risk_types.join(', ');
                
                html += `
                    <tr>
                        <td><strong>${node.user_name}</strong></td>
                        <td><span class="badge bg-secondary">${node.analyzed_level_type}</span></td>
                        <td><span class="badge bg-warning">${node.risk_count}</span></td>
                        <td><span class="badge ${this.getRiskBadgeClass(node.max_risk_score)}">${node.max_risk_score.toFixed(0)}</span></td>
                        <td class="text-truncate" style="max-width: 200px;" title="${riskTypes}">${riskTypes}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-info" onclick="contractIntegrationService.showRiskNodeDetail('${node.member_id}')">
                                <i class="bi bi-eye"></i> 详情
                            </button>
                        </td>
                    </tr>
                `;
            }
            
            html += `
                        </tbody>
                    </table>
                </div>
            `;
            
            return html;
        }
        
        // 渲染BD金字塔风险链路图
        renderBDPyramidRiskChart(visualizationData, retryCount = 0) {
            if (!window.echarts) {
                if (retryCount < 10) {
    
                    setTimeout(() => this.renderBDPyramidRiskChart(visualizationData, retryCount + 1), 200);
                    return;
                }
                console.error('ECharts库未加载');
                return;
            }
            
            const container = document.getElementById('bdPyramidRiskChart');
            if (!container) {
                console.error('BD金字塔风险链路图容器不存在');
                return;
            }
            
            // 确保容器可见
            this.ensureContainerVisible(container);
            
            // 初始化图表
            if (this.bdPyramidRiskChart) {
                this.bdPyramidRiskChart.dispose();
            }
            this.bdPyramidRiskChart = window.echarts.init(container);
            
            const option = {
                title: {
                    text: 'BD金字塔风险链路图',
                    left: 'center',
                    fontSize: 16,
                    fontWeight: 'bold'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (params.dataType === 'node') {
                            return `
                                <div>
                                    <strong>${params.name}</strong><br/>
                                    风险数量: ${params.data.risk_count}<br/>
                                    风险类型: ${params.data.risk_types.join(', ')}
                                </div>
                            `;
                        }
                        return params.name;
                    }
                },
                series: [{
                    type: 'graph',
                    layout: 'tree',
                    data: visualizationData.nodes,
                    links: visualizationData.links,
                    categories: visualizationData.categories,
                    roam: true,
                    draggable: true,
                    symbol: 'circle',
                    layoutAnimation: true,
                    tree: {
                        orient: 'TB',  // 从上到下
                        layerGap: 100,
                        nodeGap: 50
                    },
                    force: {
                        repulsion: 1000,
                        gravity: 0.1,
                        edgeLength: 100,
                        layoutAnimation: true
                    },
                    label: {
                        show: true,
                        position: 'bottom',
                        fontSize: 12,
                        color: '#333'
                    },
                    lineStyle: {
                        color: 'source',
                        curveness: 0.1
                    },
                    emphasis: {
                        focus: 'adjacency',
                        lineStyle: {
                            width: 3
                        }
                    }
                }]
            };
            
            this.bdPyramidRiskChart.setOption(option);
            
            // 绑定事件
            this.bdPyramidRiskChart.on('click', (params) => {
                if (params.dataType === 'node' && params.data.member_id) {
                    this.showRiskNodeDetail(params.data.member_id);
                }
            });
            
            
        }
        
        // 绑定金字塔模态框事件
        bindPyramidModalEvents() {
            // 布局切换事件
            document.querySelectorAll('[data-pyramid-layout]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    // 移除其他按钮的active类
                    document.querySelectorAll('[data-pyramid-layout]').forEach(b => b.classList.remove('active'));
                    // 添加当前按钮的active类
                    e.target.classList.add('active');
                    
                    const layout = e.target.getAttribute('data-pyramid-layout');
                    this.changePyramidLayout(layout);
                });
            });
        }
        
        // 切换金字塔布局
        changePyramidLayout(layout) {
            if (!this.bdPyramidRiskChart) return;
            
            const option = this.bdPyramidRiskChart.getOption();
            const series = option.series[0];
            
            switch (layout) {
                case 'tree':
                    series.layout = 'tree';
                    series.tree = {
                        orient: 'TB',
                        layerGap: 100,
                        nodeGap: 50
                    };
                    break;
                case 'force':
                    series.layout = 'force';
                    series.force = {
                        repulsion: 1000,
                        gravity: 0.1,
                        edgeLength: 100,
                        layoutAnimation: true
                    };
                    break;
                case 'radial':
                    series.layout = 'radial';
                    series.radial = {
                        layerGap: 80
                    };
                    break;
            }
            
            this.bdPyramidRiskChart.setOption(option);
        }
        
        // 显示风险节点详情
        showRiskNodeDetail(memberId) {
            // 这里可以调用现有的风险详情显示功能

            // 实际实现时可以调用已有的showRiskDetail方法
        }
        
        // 导出BD金字塔风险数据
        exportBDPyramidRiskData(bdName) {

            // 实际实现时可以调用后端API获取数据并下载
            this.showError('导出功能开发中...');
        }
        
        // 显示节点详情（新增方法）
        showNodeDetail(nodeData) {

            
            // 创建或更新节点详情栏
            let detailContainer = document.getElementById('nodeDetailContainer');
            
            if (!detailContainer) {
                // 创建详情栏容器
                detailContainer = document.createElement('div');
                detailContainer.id = 'nodeDetailContainer';
                detailContainer.className = 'card mt-4';
                
                // 寻找合适的插入位置
                const chartContainer = document.getElementById('contractBDNetworkChart');
                
                if (chartContainer) {
                    // 尝试找到图表容器的父级card
                    const parentCard = chartContainer.closest('.card');
                    
                    if (parentCard && parentCard.parentNode) {
                        // 在图表卡片下方插入
                        parentCard.parentNode.insertBefore(detailContainer, parentCard.nextSibling);
                    } else {
                        // 如果找不到card结构，直接在图表容器后插入
                        chartContainer.parentNode.insertBefore(detailContainer, chartContainer.nextSibling);
                    }
                } else {
                    // 如果找不到图表容器，插入到页面末尾
                    console.warn('⚠️ [合约整合] 未找到图表容器，将详情栏插入到页面末尾');
                    document.body.appendChild(detailContainer);
                }
            }
            
            // 构建详情内容
            const riskCount = nodeData.risk_count || 0;
            // 优化字段获取逻辑，支持多种数据源
            const digitalId = nodeData.digital_id || nodeData.user_info?.digital_id || nodeData.name || '未知';
            const memberId = nodeData.member_id || nodeData.user_info?.member_id || '未知';
            const userName = nodeData.user_name || nodeData.user_info?.user_name || nodeData.name;
            const hasRisk = nodeData.has_risk || false;
            
            // 获取风险等级和颜色
            let riskLevel = '无风险';
            let riskBadgeClass = 'bg-success';
            if (riskCount > 0) {
                if (riskCount <= 2) {
                    riskLevel = '低风险';
                    riskBadgeClass = 'bg-warning';
                } else if (riskCount <= 5) {
                    riskLevel = '中风险';
                    riskBadgeClass = 'bg-warning text-dark';
                } else {
                    riskLevel = '高风险';
                    riskBadgeClass = 'bg-danger';
                }
            }
            
            detailContainer.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="bi bi-person-circle"></i> 节点详情
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="this.closest('#nodeDetailContainer').style.display='none'">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                <div class="card-body">
                    <!-- 基本信息区域 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2 mb-3">
                                <i class="bi bi-info-circle"></i> 基本信息
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>用户名称:</strong></td>
                                            <td>${userName}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Digital ID:</strong></td>
                                            <td><code class="small">${digitalId}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Member ID:</strong></td>
                                            <td><code class="small text-truncate" style="max-width: 150px;" title="${memberId}">${memberId}</code></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-4">
                                    <!-- 风险状态移到这里 -->
                                    <div class="border-start ps-3">
                                        <label class="form-label text-muted"><strong>风险状态</strong></label>
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="badge ${riskBadgeClass} me-2">${riskLevel}</span>
                                            <span class="text-muted small">风险数量: ${riskCount}</span>
                                        </div>
                                        ${hasRisk ? 
                                            '<div class="alert alert-warning alert-sm py-1 px-2 mb-0"><i class="bi bi-exclamation-triangle me-1"></i>存在风险事件</div>' :
                                            '<div class="alert alert-success alert-sm py-1 px-2 mb-0"><i class="bi bi-check-circle me-1"></i>暂无风险事件</div>'
                                        }
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="border-start ps-3">
                                        <label class="form-label text-muted"><strong>节点统计</strong></label>
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="text-primary h5 mb-0">${nodeData.children ? nodeData.children.length : 0}</div>
                                                <small class="text-muted">下级节点</small>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-danger h5 mb-0">${riskCount}</div>
                                                <small class="text-muted">风险条目</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 风险条目展示区域 -->
                    <div class="row mb-4" id="riskItemsSection">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2 mb-3">
                                <i class="bi bi-exclamation-triangle"></i> 风险条目
                                <span class="badge bg-secondary ms-2" id="riskItemsCount">加载中...</span>
                            </h6>
                            <div id="riskItemsList">
                                <div class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <div class="text-muted mt-2">正在加载风险条目...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 详情展示区域 -->
                    <div class="row" id="nodeDetailsSection">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2 mb-3">
                                <i class="bi bi-list-ul"></i> 详细信息
                            </h6>
                            <div class="row">
                                <!-- 下级节点展示 -->
                                ${nodeData.children && nodeData.children.length > 0 ? `
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted"><strong>下级节点 (${nodeData.children.length}个)</strong></label>
                                    <div class="border rounded p-2" style="max-height: 200px; overflow-y: auto;">
                                        ${nodeData.children.slice(0, 8).map(child => `
                                            <div class="d-flex justify-content-between align-items-center py-1 px-2 mb-1 bg-light rounded">
                                                <div>
                                                    <div class="fw-bold small text-truncate" style="max-width: 120px;" title="${child.name}">
                                                        ${child.name}
                                                    </div>
                                                    <div class="text-muted small">风险: ${child.risk_count || 0}</div>
                                                </div>
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="contractIntegrationService.showNodeDetail(${JSON.stringify(child).replace(/"/g, '&quot;')})">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                            </div>
                                        `).join('')}
                                        ${nodeData.children.length > 8 ? `
                                            <div class="text-center py-2">
                                                <small class="text-muted">还有 ${nodeData.children.length - 8} 个下级节点</small>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                                ` : `
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted"><strong>下级节点</strong></label>
                                    <div class="alert alert-info py-2 mb-0">
                                        <i class="bi bi-info-circle me-1"></i>该节点无下级节点
                                    </div>
                                </div>
                                `}
                                
                                <!-- 扩展信息区域 -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted"><strong>扩展信息</strong></label>
                                    <div class="border rounded p-2">
                                        <div class="row small">
                                            <div class="col-6 mb-2">
                                                <div class="text-muted">节点类型</div>
                                                <div>${nodeData.node_type || nodeData.componentType || '用户节点'}</div>
                                            </div>
                                            <div class="col-6 mb-2">
                                                <div class="text-muted">节点层级</div>
                                                <div>${nodeData.level || nodeData.level_number || '未知'}</div>
                                            </div>
                                            <div class="col-6 mb-2">
                                                <div class="text-muted">BD归属</div>
                                                <div class="text-truncate" title="${nodeData.bd_name || '未知'}">${nodeData.bd_name || '未知'}</div>
                                            </div>
                                            <div class="col-6 mb-2">
                                                <div class="text-muted">代理标志</div>
                                                <div>${nodeData.agent_flag || '未知'}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 确保详情栏可见并滚动到位
            detailContainer.style.display = 'block';
            
            // 在DOM更新后加载风险详情
            setTimeout(() => {
                this.loadNodeRiskDetails(memberId);
            }, 50);
            
            // 滚动到详情栏
            setTimeout(() => {
                detailContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }, 100);
        }

        // 加载节点风险详情（优化版本 - 接口保持完全不变）
        async loadNodeRiskDetails(memberId) {
            // 输入验证
            if (!memberId || typeof memberId !== 'string') {
                console.warn('⚠️ 无效的memberId:', memberId);
                return;
            }

            try {
    
                
                // 先检查DOM元素是否存在
                const riskItemsList = document.getElementById('riskItemsList');
                const riskItemsCount = document.getElementById('riskItemsCount');
                
                if (!riskItemsList || !riskItemsCount) {
                    console.warn('⚠️ 风险条目容器不存在');
                    return;
                }
                
                // 获取当前任务ID
                const taskId = this.getCurrentTaskId();
                if (!taskId) {
                    console.warn('⚠️ 当前没有选择的任务ID');
                    riskItemsList.innerHTML = `
                        <div class="alert alert-warning py-2 mb-0">
                            <i class="bi bi-info-circle me-1"></i>请先选择合约分析任务
                        </div>
                    `;
                    return;
                }
                
                // 显示加载状态
                riskItemsList.innerHTML = `
                    <div class="text-center py-3">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="text-muted mt-2">正在加载风险条目...</div>
                    </div>
                `;
                
                // 调用后端API获取风险统计 - 接口路径保持完全不变
                const response = await fetch(`${this.baseUrl}/node-risk-statistics/${encodeURIComponent(memberId)}/${encodeURIComponent(taskId)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                
                // 验证响应数据结构
                if (!data || !data.statistics) {
                    throw new Error('响应数据格式异常');
                }
                
                const statistics = data.statistics;
                
                if (!statistics.total_count || statistics.total_count === 0) {
                    riskItemsCount.textContent = '0条';
                    riskItemsCount.className = 'badge bg-success ms-2';
                    riskItemsList.innerHTML = `
                        <div class="alert alert-success py-2 mb-0">
                            <i class="bi bi-check-circle me-1"></i>该用户暂无风险条目
                        </div>
                    `;
                    return;
                }
                
                // 更新风险数量显示
                riskItemsCount.textContent = `${statistics.total_count}条`;
                riskItemsCount.className = statistics.total_count > 5 ? 'badge bg-danger ms-2' : 
                                          statistics.total_count > 2 ? 'badge bg-warning ms-2' : 'badge bg-info ms-2';
                
                // 渲染风险统计列表
                this.renderRiskStatisticsList(statistics.risk_categories || [], riskItemsList, memberId, taskId);
                
            } catch (error) {
                console.error('❌ [合约整合] 加载节点风险详情失败:', error);
                
                // 安全地更新错误状态
                const riskItemsList = document.getElementById('riskItemsList');
                const riskItemsCount = document.getElementById('riskItemsCount');
                
                if (riskItemsCount) {
                    riskItemsCount.textContent = '错误';
                    riskItemsCount.className = 'badge bg-danger ms-2';
                }
                
                if (riskItemsList) {
                    riskItemsList.innerHTML = `
                        <div class="alert alert-danger py-2 mb-0">
                            <i class="bi bi-exclamation-triangle me-1"></i>加载风险详情时发生错误: ${error.message}
                        </div>
                    `;
                }
            }
        }

        // 获取当前任务ID
        getCurrentTaskId() {
            const selector = document.getElementById('contractTaskSelector');
            return selector ? selector.value : null;
        }

        // 渲染风险统计列表
        renderRiskStatisticsList(riskCategories, container, memberId, taskId) {
            if (riskCategories.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-info py-2 mb-0">
                        <i class="bi bi-info-circle me-1"></i>暂无风险条目
                    </div>
                `;
                return;
            }

            let html = '';
            for (const category of riskCategories) {
                const typeInfo = this.getRiskTypeInfo(category.risk_type);
                html += `
                    <div class="risk-category-item mb-3 border rounded p-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="flex-grow-1">
                                <h6 class="mb-1 text-${typeInfo.color}">
                                    <i class="bi bi-${typeInfo.icon}"></i> ${category.risk_type_name}
                                    <span class="badge bg-${typeInfo.color} ms-2">${category.count}条</span>
                                </h6>
                                <div class="text-muted small">
                                    总交易量: ${this.formatVolume(category.total_volume)} | 
                                    平均评分: ${category.avg_risk_score} | 
                                    最高评分: ${category.max_risk_score}
                                </div>
                                ${category.main_detection_method ? `
                                    <div class="text-info small mt-1">
                                        <i class="bi bi-gear-fill me-1"></i>主要方法: ${category.main_detection_method}
                                    </div>
                                ` : ''}
                                ${category.latest_time ? `
                                    <div class="text-secondary small mt-1">
                                        最新时间: ${this.formatTimeRange(category.latest_time)}
                                    </div>
                                ` : ''}
                            </div>
                            <button class="btn btn-sm btn-outline-${typeInfo.color}" 
                                    onclick="contractIntegrationService.showRiskTypeDetails('${memberId}', '${taskId}', '${category.risk_type}', '${category.risk_type_name}')">
                                <i class="bi bi-eye"></i> 详情
                            </button>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        // 显示风险类型详情（优化版本 - 接口保持完全不变）
        async showRiskTypeDetails(memberId, taskId, riskType, riskTypeName) {
            // 输入验证
            if (!memberId || !taskId || !riskType) {
                this.showError('参数不完整，无法加载风险详情');
                return;
            }

            try {
    
                
                // 显示加载状态
                this.showLoadingModal('正在加载风险详情...');
                
                // 调用后端API获取详细风险事件 - 接口路径保持完全不变
                const response = await fetch(`${this.baseUrl}/node-risk-details/${encodeURIComponent(memberId)}/${encodeURIComponent(taskId)}/${encodeURIComponent(riskType)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                
                // 验证数据格式
                if (!data || !Array.isArray(data.risk_events)) {
                    throw new Error('风险事件数据格式异常');
                }
                
                // 隐藏加载状态
                this.hideLoadingModal();
                
                // 显示风险详情模态框
                this.showRiskDetailsModal(data);
                
            } catch (error) {
                console.error('❌ [合约整合] 显示风险类型详情失败:', error);
                this.hideLoadingModal();
                this.showError(`加载风险详情失败: ${error.message}`);
            }
        }

        // 显示加载模态框
        showLoadingModal(message) {
            // 创建简单的加载提示
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'riskDetailsLoading';
            loadingDiv.className = 'position-fixed top-50 start-50 translate-middle bg-white border rounded p-4 shadow';
            loadingDiv.style.zIndex = '9999';
            loadingDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="text-muted">${message}</div>
                </div>
            `;
            document.body.appendChild(loadingDiv);
        }

        // 隐藏加载模态框
        hideLoadingModal() {
            const loadingDiv = document.getElementById('riskDetailsLoading');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }

        // 显示风险详情模态框
        showRiskDetailsModal(data) {
            // 检查是否已存在模态框
            let modal = document.getElementById('nodeRiskDetailsModal');
            if (!modal) {
                // 创建模态框
                modal = document.createElement('div');
                modal.id = 'nodeRiskDetailsModal';
                modal.className = 'modal fade';
                modal.setAttribute('tabindex', '-1');
                modal.setAttribute('aria-labelledby', 'nodeRiskDetailsModalLabel');
                modal.setAttribute('role', 'dialog');
                modal.setAttribute('aria-modal', 'true');
                document.body.appendChild(modal);
            }
            
            // 构建模态框内容
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="nodeRiskDetailsModalLabel">
                                <i class="bi bi-exclamation-triangle"></i> ${data.risk_type_name}详情 
                                <span class="badge bg-info ms-2">${data.risk_events.length}条</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                        </div>
                        <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                            ${this.renderRiskEventsDetails(data.risk_events)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            `;
            
            // 显示模态框
            const bootstrapModal = new bootstrap.Modal(modal);
            
            // 添加事件监听器来处理无障碍性
            modal.addEventListener('shown.bs.modal', () => {
                // 模态框显示后，确保正确移除aria-hidden并设置焦点
                modal.removeAttribute('aria-hidden');
                modal.setAttribute('aria-modal', 'true');
                
                // 将焦点设置到第一个可聚焦元素（关闭按钮）
                const closeButton = modal.querySelector('.btn-close');
                if (closeButton) {
                    closeButton.focus();
                }
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                // 模态框隐藏后，重新设置aria-hidden
                modal.setAttribute('aria-hidden', 'true');
                modal.removeAttribute('aria-modal');
            });
            
            bootstrapModal.show();
        }

        // 渲染风险事件详情
        renderRiskEventsDetails(riskEvents) {
            if (riskEvents.length === 0) {
                return `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-1"></i>暂无风险事件详情
                    </div>
                `;
            }

            return riskEvents.map((event, index) => {
                const severity = this.getRiskSeverity(event.total_risk_score || 0);
                return `
                    <div class="risk-event-card border rounded p-3 mb-3">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h6 class="mb-1">
                                    <span class="badge bg-${severity.color} me-2">${severity.level}</span>
                                    ${event.contract_name || '未知合约'}
                                </h6>
                                <div class="text-muted small">
                                    风险评分: ${event.total_risk_score?.toFixed(1) || '0.0'}
                                    ${event.time_range ? ` | 时间: ${this.formatTimeRange(event.time_range)}` : ''}
                                </div>
                            </div>
                            <span class="badge bg-secondary">#${index + 1}</span>
                        </div>
                        
                                                 <div class="row">
                             <div class="col-md-4">
                                 <div class="detail-group">
                                     <label class="text-muted small d-block">交易信息</label>
                                     <div class="detail-item">交易量: ${this.formatVolume(event.abnormal_volume || event.abnormalVolume || 0)}</div>
                                     <div class="detail-item">杠杆: ${this.getLeverageValue(event)}倍</div>
                                     <div class="detail-item">交易次数: ${this.getTradeCountValue(event)}</div>
                                     ${event.detection_method_chinese || event.detection_method ? `<div class="detail-item">检测方法: ${event.detection_method_chinese || this.formatDetectionMethod(event.detection_method)}</div>` : ''}
                                 </div>
                             </div>
                             <div class="col-md-4">
                                 <div class="detail-group">
                                     <label class="text-muted small d-block">风险指标</label>
                                     ${event.self_trade_ratio !== undefined && event.self_trade_ratio > 0 ? `<div class="detail-item">自成交比例: <span class="text-danger">${(event.self_trade_ratio * 100).toFixed(2)}%</span></div>` : ''}
                                     ${event.trading_frequency !== undefined && event.trading_frequency > 0 ? `<div class="detail-item">交易频率: ${(event.trading_frequency * 100).toFixed(1)}%</div>` : ''}
                                     ${event.indicators && event.indicators.trading_volume !== undefined ? `<div class="detail-item">交易强度: ${(event.indicators.trading_volume * 100).toFixed(1)}%</div>` : ''}
                                     <div class="detail-item">风险等级: <span class="badge bg-${this.getRiskLevelColor(event.risk_level || event.severity)}">${this.formatRiskLevel(event.risk_level || event.severity)}</span></div>
                                 </div>
                             </div>
                             <div class="col-md-4">
                                 <div class="detail-group">
                                     <label class="text-muted small d-block">评分构成</label>
                                     ${event.risk_details && event.risk_details.base_score !== undefined ? `<div class="detail-item">基础分: ${parseFloat(event.risk_details.base_score).toFixed(2)}</div>` : ''}
                                     ${event.risk_details && event.risk_details.leverage_bonus ? `<div class="detail-item">杠杆加分: +${event.risk_details.leverage_bonus}</div>` : ''}
                                     ${event.risk_details && event.risk_details.volume_bonus ? `<div class="detail-item">交易量加分: +${event.risk_details.volume_bonus}</div>` : ''}
                                     ${event.risk_details && event.risk_details.trade_count_bonus ? `<div class="detail-item">交易次数加分: +${event.risk_details.trade_count_bonus}</div>` : ''}
                                     <div class="detail-item"><strong>总评分: ${(event.total_risk_score || 0).toFixed(2)}</strong></div>
                                 </div>
                             </div>
                         </div>
                        
                        ${event.reason ? `
                            <div class="mt-3">
                                <label class="text-muted small d-block">风险原因</label>
                                <div class="bg-light rounded p-2 small">${event.reason}</div>
                            </div>
                        ` : ''}
                        
                        ${Object.keys(event.risk_details || {}).length > 0 ? `
                            <div class="mt-3">
                                <label class="text-muted small d-block">风险详情</label>
                                <div class="bg-light rounded p-2 small">
                                    <pre class="mb-0">${JSON.stringify(event.risk_details, null, 2)}</pre>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
        }

        // 获取杠杆值 - 统一处理不同数据来源（优化版本 - 数据格式保持不变）
        getLeverageValue(event) {
            // 安全检查事件对象
            if (!event || typeof event !== 'object') {
                return 1;
            }

            try {
                // 优先级: risk_details.key_indicators > 直接字段 > 默认值（因为key_indicators是经过验证的数据）
                if (event.risk_details && 
                    event.risk_details.key_indicators && 
                    typeof event.risk_details.key_indicators.leverage === 'number' && 
                    event.risk_details.key_indicators.leverage >= 0) {
                    const leverageValue = event.risk_details.key_indicators.leverage;
                    return leverageValue > 0 ? leverageValue : 1; // 0杠杆显示为1倍
                }
                
                if (typeof event.leverage === 'number' && event.leverage >= 0) {
                    return event.leverage || 1; // 如果是0也显示1
                }
                
                return 1; // 默认1倍杠杆
            } catch (error) {
                console.warn('获取杠杆值时发生错误:', error);
                return 1;
            }
        }

        // 获取交易次数值 - 统一处理不同数据来源（优化版本 - 数据格式保持不变）
        getTradeCountValue(event) {
            // 安全检查事件对象
            if (!event || typeof event !== 'object') {
                return 0;
            }

            try {
                // 优先级: risk_details.key_indicators.trades_count > trade_count > trades_count > indicators.pair_count > 默认值
                if (event.risk_details && 
                    event.risk_details.key_indicators && 
                    typeof event.risk_details.key_indicators.trades_count === 'number' && 
                    event.risk_details.key_indicators.trades_count > 0) {
                    return event.risk_details.key_indicators.trades_count;
                }
                
                if (typeof event.trade_count === 'number' && event.trade_count > 0) {
                    return event.trade_count;
                }
                
                if (typeof event.trades_count === 'number' && event.trades_count > 0) {
                    return event.trades_count;
                }
                
                // 对于对敲交易，尝试从indicators.pair_count获取
                if (event.indicators && 
                    typeof event.indicators.pair_count === 'number' && 
                    event.indicators.pair_count > 0) {
                    return event.indicators.pair_count;
                }
                
                return 0; // 默认值
            } catch (error) {
                console.warn('获取交易次数值时发生错误:', error);
                return 0;
            }
        }

        // 格式化检测方法
        formatDetectionMethod(method) {
            const methodMap = {
                // 自成交类
                'direct_self_trade': '直接自成交',
                'suspected_self_trade': '疑似自成交',
                
                // 同账户对敲类
                'same_account_wash_trading': '同账户对敲',
                'same_account_wash_trading_position_optimized': '同账户对敲（优化算法）',
                
                // 跨账户对敲类 (已迁移到统一的对敲交易类别)
                'cross_account_wash_trading': '跨账户对敲',
                
                // 网络分析类
                'enhanced_network_analysis': '增强网络分析',
                
                // 其他检测方法
                'open_close_pair': '开平仓配对',
                'pattern_matching': '模式匹配',
                'statistical_analysis': '统计分析',
                'time_correlation': '时间关联',
                'volume_correlation': '交易量关联',
                'high_frequency_pattern': '高频模式',
                'wash_trading_pattern': '对敲模式',
                'synchronized_trading': '同步交易'
            };
            
            // 处理组合方法，如 "method1+method2"
            if (method && method.includes('+')) {
                const methods = method.split('+');
                const translatedMethods = methods.map(m => methodMap[m.trim()] || m.trim());
                return translatedMethods.join(' + ');
            }
            
            return methodMap[method] || method || '未知方法';
        }

        // 格式化风险等级
        formatRiskLevel(level) {
            const levelMap = {
                'high': '高风险',
                'medium': '中风险', 
                'low': '低风险',
                'very_low': '极低风险'
            };
            return levelMap[level] || level;
        }

        // 获取风险等级颜色
        getRiskLevelColor(level) {
            const colorMap = {
                'high': 'danger',
                'medium': 'warning',
                'low': 'info', 
                'very_low': 'success'
            };
            return colorMap[level] || 'secondary';
        }

        // 从当前数据中筛选用户风险（保留作为备用方法）
        filterUserRisks(memberId) {
            if (!this.currentAnalysisResult?.link_analysis?.contract_risks) {
                return [];
            }
            
            const allRisks = this.currentAnalysisResult.link_analysis.contract_risks;
            return allRisks.filter(risk => risk.member_id === memberId);
        }

        // 按风险类型分组
        groupRisksByType(risks) {
            const groups = {};
            risks.forEach(risk => {
                const type = risk.detection_type || 'unknown';
                if (!groups[type]) {
                    groups[type] = [];
                }
                groups[type].push(risk);
            });
            return groups;
        }

        // 获取风险类型信息
        getRiskTypeInfo(riskType) {
            const typeMap = {
                'high_frequency_trading': { name: '高频交易', icon: 'clock-history', color: 'warning' },
                'wash_trading': { name: '对敲交易', icon: 'arrow-repeat', color: 'danger' },
                'large_volume_trading': { name: '大额交易', icon: 'bar-chart', color: 'info' },
                'abnormal_profit': { name: '异常盈利', icon: 'graph-up-arrow', color: 'success' },
                'market_manipulation': { name: '市场操纵', icon: 'shuffle', color: 'danger' },
                'unknown': { name: '未知风险', icon: 'question-circle', color: 'secondary' }
            };
            return typeMap[riskType] || typeMap['unknown'];
        }

        // 渲染风险条目
        renderRiskItems(riskItems) {
            return riskItems.map(risk => {
                const severity = this.getRiskSeverity(risk.total_risk_score || 0);
                return `
                    <div class="risk-item border rounded p-2 mb-2 bg-light">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="badge bg-${severity.color} me-2">${severity.level}</span>
                                    <span class="fw-bold small">${risk.contract_name || '未知合约'}</span>
                                </div>
                                <div class="text-muted small">
                                    风险评分: ${risk.total_risk_score?.toFixed(1) || '0.0'} | 
                                    交易量: ${this.formatVolume(risk.abnormal_volume)} |
                                    时间: ${this.formatTimeRange(risk.time_range)}
                                </div>
                                ${risk.reason ? `
                                    <div class="text-secondary small mt-1">
                                        ${risk.reason.length > 60 ? risk.reason.substring(0, 60) + '...' : risk.reason}
                                    </div>
                                ` : ''}
                            </div>
                            <button class="btn btn-sm btn-outline-info ms-2" 
                                    onclick="contractIntegrationService.showRiskDetail('${risk.member_id}', '${risk.detection_type}')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 获取风险严重程度
        getRiskSeverity(score) {
            if (score >= 80) return { level: '高风险', color: 'danger' };
            if (score >= 60) return { level: '中风险', color: 'warning' };
            if (score >= 40) return { level: '低风险', color: 'info' };
            return { level: '极低风险', color: 'success' };
        }

        // 格式化交易量
        formatVolume(volume) {
            if (!volume) return '0';
            if (volume >= 1000000) return (volume / 1000000).toFixed(1) + 'M';
            if (volume >= 1000) return (volume / 1000).toFixed(1) + 'K';
            return volume.toFixed(0);
        }

        // 格式化时间范围
        formatTimeRange(timeRange) {
            if (!timeRange) return '未知时间';
            if (timeRange.includes(' - ')) {
                const [start, end] = timeRange.split(' - ');
                const startTime = start.split(' ')[1] || '';
                const endTime = end.split(' ')[1] || '';
                return `${startTime}-${endTime}`;
            }
            return timeRange;
        }

        // 切换风险组显示状态
        toggleRiskGroup(riskType) {
            const container = document.getElementById(`group-${riskType}`);
            const icon = document.getElementById(`toggle-${riskType}`);
            
            if (container && icon) {
                if (container.style.display === 'none') {
                    container.style.display = 'block';
                    icon.className = 'bi bi-chevron-down';
                } else {
                    container.style.display = 'none';
                    icon.className = 'bi bi-chevron-right';
                }
            }
        }

        // 显示更多风险条目
        showMoreRiskItems(riskType) {
            // 这里可以实现分页加载或展开更多条目

            this.showError('展开更多功能开发中...');
        }

        // 重置图表缩放（新增方法）
        resetChartZoom() {
            if (!this.charts.networkChart) {
                this.showError('图表未初始化');
                return;
            }
            

            
            // 重置到初始缩放状态
            const action = {
                type: 'restore'
            };
            this.charts.networkChart.dispatchAction(action);
        }
        
        // 放大图表（新增方法）
        zoomIn() {
            if (!this.charts.networkChart) {
                this.showError('图表未初始化');
                return;
            }
            

            
            // 获取当前缩放比例并增加
            const option = this.charts.networkChart.getOption();
            option.series.forEach((series, index) => {
                if (series.zoom !== undefined) {
                    const currentZoom = series.zoom || 1;
                    const newZoom = Math.min(currentZoom * 1.2, 3); // 最大放大3倍
                    
                    this.charts.networkChart.dispatchAction({
                        type: 'geoRoam',
                        seriesIndex: index,
                        zoom: newZoom / currentZoom
                    });
                }
            });
        }
        
        // 缩小图表（新增方法）
        zoomOut() {
            if (!this.charts.networkChart) {
                this.showError('图表未初始化');
                return;
            }
            

            
            // 获取当前缩放比例并减少
            const option = this.charts.networkChart.getOption();
            option.series.forEach((series, index) => {
                if (series.zoom !== undefined) {
                    const currentZoom = series.zoom || 1;
                    const newZoom = Math.max(currentZoom * 0.8, 0.1); // 最小缩小到0.1倍
                    
                    this.charts.networkChart.dispatchAction({
                        type: 'geoRoam',
                        seriesIndex: index,
                        zoom: newZoom / currentZoom
                    });
                }
            });
        }
        
        // 适应内容（新增方法）
        fitToContent() {
            if (!this.charts.networkChart) {
                this.showError('图表未初始化');
                return;
            }
            

            
            // 重新渲染图表以适应容器大小
            this.charts.networkChart.resize();
            
            // 重置视图位置和缩放
            const action = {
                type: 'restore'
            };
            this.charts.networkChart.dispatchAction(action);
            
            // 延迟执行自适应逻辑
            setTimeout(() => {
                const option = this.charts.networkChart.getOption();
                
                // 计算合适的缩放比例
                const container = document.getElementById('contractBDNetworkChart');
                const containerWidth = container.clientWidth;
                const containerHeight = container.clientHeight;
                
                // 根据容器大小和数据量调整缩放
                let optimalZoom = 1;
                
                option.series.forEach((series, seriesIndex) => {
                    if (series.data && series.data[0]) {
                        const nodeCount = this.countTreeNodes(series.data[0]);
                        
                        // 根据节点数量动态调整间距
                        let nodeGap, layerGap;
                        if (nodeCount > 100) {
                            nodeGap = 15;
                            layerGap = 30;
                            optimalZoom = Math.min(containerWidth / 1200, containerHeight / 800) * 0.4;
                        } else if (nodeCount > 50) {
                            nodeGap = 20;
                            layerGap = 35;
                            optimalZoom = Math.min(containerWidth / 1000, containerHeight / 600) * 0.6;
                        } else if (nodeCount > 20) {
                            nodeGap = 25;
                            layerGap = 45;
                            optimalZoom = Math.min(containerWidth / 800, containerHeight / 500) * 0.8;
                        } else {
                            nodeGap = 30;
                            layerGap = 60;
                            optimalZoom = Math.min(containerWidth / 600, containerHeight / 400);
                        }
                        
                        // 应用动态间距
                        series.nodeGap = nodeGap;
                        series.layerGap = layerGap;
                        
                        optimalZoom = Math.max(0.1, Math.min(optimalZoom, 2)); // 限制在0.1-2倍之间
                    }
                });
                
                // 应用最佳缩放和间距
                option.series.forEach((series, index) => {
                    series.zoom = optimalZoom;
                    series.center = ['50%', '50%']; // 居中显示
                });
                
                this.charts.networkChart.setOption(option);

            }, 100);
        }
        
        // 调整连接线长度（新增方法）
        adjustLineLength(mode = 'compact') {
            if (!this.charts.networkChart) {
                this.showError('图表未初始化');
                return;
            }
            

            
            // 更新按钮状态
            this.updateLineControlButtons(mode);
            
            const option = this.charts.networkChart.getOption();
            
            option.series.forEach(series => {
                if (series.data && series.data[0]) {
                    const nodeCount = this.countTreeNodes(series.data[0]);
                    const isMultiTree = option.series.length > 1;
                    
                    let nodeGap, layerGap;
                    
                    switch (mode) {
                        case 'compact': // 紧凑模式
                            nodeGap = isMultiTree ? 15 : 25;
                            layerGap = isMultiTree ? 25 : 40;
                            break;
                        case 'normal': // 正常模式
                            nodeGap = isMultiTree ? 20 : 30;
                            layerGap = isMultiTree ? 40 : 60;
                            break;
                        case 'loose': // 宽松模式
                            nodeGap = isMultiTree ? 30 : 45;
                            layerGap = isMultiTree ? 60 : 80;
                            break;
                        case 'auto': // 自动模式
                        default:
                            if (nodeCount > 100) {
                                nodeGap = isMultiTree ? 12 : 20;
                                layerGap = isMultiTree ? 20 : 35;
                            } else if (nodeCount > 50) {
                                nodeGap = isMultiTree ? 15 : 25;
                                layerGap = isMultiTree ? 30 : 45;
                            } else if (nodeCount > 20) {
                                nodeGap = isMultiTree ? 20 : 30;
                                layerGap = isMultiTree ? 40 : 60;
                            } else {
                                nodeGap = isMultiTree ? 25 : 35;
                                layerGap = isMultiTree ? 50 : 70;
                            }
                            break;
                    }
                    
                    series.nodeGap = nodeGap;
                    series.layerGap = layerGap;
                }
            });
            
            this.charts.networkChart.setOption(option);

        }
        
        // 更新连接线控制按钮状态（新增方法）
        updateLineControlButtons(activeMode) {
            // 清除所有按钮的active状态
            const buttons = document.querySelectorAll('[aria-label="连接线控制"] .btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            
            // 根据模式添加active状态
            const modeMap = {
                'compact': 'compact',
                'normal': 'normal', 
                'loose': 'loose',
                'auto': 'normal' // auto模式显示为normal
            };
            
            const targetMode = modeMap[activeMode] || 'normal';
            const targetButton = document.querySelector(`[onclick*="adjustLineLength('${targetMode}')"]`);
            if (targetButton) {
                targetButton.classList.add('active');
            }
        }
    }
    
    return Service;
})();

// 创建并导出服务实例
const contractIntegrationService = new ContractIntegrationService();

// 将服务挂载到全局，供HTML中的事件处理使用
window.contractIntegrationService = contractIntegrationService;

// 全局错误隐藏函数
window.hideError = function() {
    if (window.contractIntegrationService) {
        window.contractIntegrationService.hideError();
    }
};

export default contractIntegrationService; 
/* 合约风险链路分析样式 */

/* 全局样式 */
.text-purple {
    color: #722ed1 !important;
}

/* 加载状态样式 */
#loadingIndicator {
    background: #ffffff;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#loadingIndicator .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 概览统计卡片 */
.card .border-end {
    border-right: 1px solid #e9ecef !important;
}

/* 图表容器样式 */
.card-body {
    position: relative;
}

#riskDistributionChart,
#contractBDNetworkChart,
#contractBDPyramidChart,
#riskHeatmapChart,
#riskTimelineChart {
    width: 100%;
    background: #ffffff;
    border-radius: 6px;
}

/* 金字塔图控制按钮组 */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.btn-group .btn.active {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;
}

/* 金字塔图专用样式 */
#contractBDPyramidChart,
#contractBDNetworkChart {
    cursor: pointer;
    position: relative;
}

#contractBDPyramidChart:hover,
#contractBDNetworkChart:hover {
    background-color: #fafafa;
}

/* 全宽BD金字塔图表特殊样式 */
#contractBDNetworkChart {
    width: 100%;
    min-height: 800px;
    border-radius: 6px;
    background: #ffffff;
}

/* 风险详情面板样式 */
#riskDetailPanel {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#riskDetailPanel .card {
    border: 2px solid #1890ff;
    box-shadow: 0 4px 15px rgba(24, 144, 255, 0.1);
}

#riskDetailPanel .card-header {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    color: white;
    border: none;
}

#riskDetailPanel .table td {
    border: none;
    padding: 0.5rem 0.75rem;
}

#riskDetailPanel .table tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* 金字塔节点风险状态指示器 */
.pyramid-node-risk-high {
    color: #ff4d4f !important;
    font-weight: bold;
}

.pyramid-node-risk-medium {
    color: #faad14 !important;
    font-weight: 600;
}

.pyramid-node-risk-low {
    color: #52c41a !important;
}

.pyramid-node-transmission {
    color: #ff9c6e !important;
    font-style: italic;
}

/* 表格样式增强 */
.table-hover > tbody > tr:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.table .badge {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

/* BD风险排名表格特殊样式 */
#bdRiskRankingTable tr:first-child {
    background: linear-gradient(90deg, #ffd700, #ffed4e);
    color: #333;
    font-weight: bold;
}

#bdRiskRankingTable tr:nth-child(2) {
    background: linear-gradient(90deg, #c0c0c0, #e8e8e8);
    color: #333;
    font-weight: 600;
}

#bdRiskRankingTable tr:nth-child(3) {
    background: linear-gradient(90deg, #cd7f32, #d4af37);
    color: #333;
    font-weight: 600;
}

/* 跨BD风险模式卡片 */
#crossBDPatternsContainer .card {
    border-left: 4px solid #1890ff;
    transition: all 0.3s ease;
}

#crossBDPatternsContainer .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 热力图维度选择器 */
.btn-check:checked + .btn {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;
}

/* 时间线筛选器 */
.input-group .input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    font-weight: 500;
}

/* 模态框样式 */
.modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 标签页样式 */
.nav-tabs .nav-link {
    border-radius: 6px 6px 0 0;
    font-weight: 500;
    color: #666;
}

.nav-tabs .nav-link.active {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    background-color: #f8f9fa;
}

/* 错误提示样式 */
#errorAlert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

/* 统计数字动画效果 */
.card h3 {
    font-weight: 700;
    font-size: 2.5rem;
    line-height: 1;
    animation: numberFadeIn 0.6s ease-out;
}

@keyframes numberFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* BD筛选状态提示 */
#bdFilterStatus {
    border-left: 4px solid #1890ff;
    background: linear-gradient(90deg, #e6f7ff, #f0f9ff);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 筛选控制卡片 */
#bdFilterCard {
    border: 1px solid #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

#bdFilterCard:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* 小屏幕适配 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    #contractBDPyramidChart {
        height: 400px !important;
    }
    
    #contractBDNetworkChart {
        height: 500px !important;
        min-height: 500px !important;
    }
    
    .btn-group {
        display: flex;
        flex-wrap: wrap;
    }
    
    .btn-group .btn {
        margin: 2px;
        border-radius: 4px !important;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .modal-dialog {
        margin: 10px;
    }
    
    /* 移动端BD筛选优化 */
    #bdFilterCard .row {
        flex-direction: column;
    }
    
    #bdFilterCard .col-md-4,
    #bdFilterCard .col-md-8 {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* 打印样式 */
@media print {
    .btn, .btn-group, .modal, .navbar, #bdFilterCard {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6;
        break-inside: avoid;
    }
    
    .container-fluid {
        max-width: 100%;
    }
    
    /* 打印时显示统计数据 */
    .card h3 {
        animation: none;
    }
}

/* 无障碍支持 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦点指示器 */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(24, 144, 255, 0.25);
}

/* 自定义滚动条 */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 高对比度模式支持 */
@media (forced-colors: active) {
    .btn {
        border: 2px solid;
    }
    
    .card {
        border: 2px solid;
    }
    
    .table th,
    .table td {
        border: 1px solid;
    }
    
    .modal-header {
        background: ButtonFace;
        color: ButtonText;
    }
}

/* 额外的交互增强样式 */
.card:hover {
    transition: transform 0.2s ease-in-out;
}

.card.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 徽章样式增强 */
.badge {
    font-weight: 500;
    font-size: 0.8em;
}

.badge.bg-success {
    background-color: #52c41a !important;
}

.badge.bg-warning {
    background-color: #faad14 !important;
    color: #fff !important;
}

.badge.bg-danger {
    background-color: #ff4d4f !important;
}

/* 加载状态增强 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* 数据为空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #8c8c8c;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* 节点详情栏样式 */
#nodeDetailContainer {
    animation: slideDown 0.3s ease-out;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 节点详情卡片内的操作按钮 */
#nodeDetailContainer .btn-group-vertical .btn {
    text-align: left;
    font-size: 0.875rem;
}

#nodeDetailContainer .btn-group-vertical .btn i {
    width: 16px;
    margin-right: 8px;
}

/* 下级节点卡片样式 */
#nodeDetailContainer .card.card-body {
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
    background: #f8f9fa;
}

#nodeDetailContainer .card.card-body:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
    background: #ffffff;
}

/* 风险等级徽章样式 */
.badge.bg-warning.text-dark {
    background-color: #fa8c16 !important;
    color: white !important;
}

/* 节点详情表格样式 */
#nodeDetailContainer .table-borderless td {
    padding: 0.25rem 0;
    vertical-align: top;
}

#nodeDetailContainer .table-borderless td:first-child {
    width: 120px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #nodeDetailContainer .col-md-4 {
        margin-bottom: 1rem;
    }
    
    #nodeDetailContainer .btn-group-vertical .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.5rem;
    }
    
    #nodeDetailContainer .col-md-2 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

/* 图表工具栏样式 */
.card-header .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.card-header .btn-group-sm .btn i {
    font-size: 0.875rem;
}

/* 工具栏按钮组合样式 */
.card-header .btn-toolbar {
    gap: 0.5rem;
}

.card-header .btn-toolbar .btn-group {
    flex-wrap: nowrap;
}

/* 连接线控制按钮特殊样式 */
.card-header .btn-group[aria-label="连接线控制"] .btn {
    min-width: 36px;
}

.card-header .btn-group[aria-label="连接线控制"] .btn.active {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.card-header .btn-group[aria-label="连接线控制"] .btn:hover:not(.active) {
    background-color: #f8f9fa;
    border-color: #6c757d;
}

/* 图表说明文字样式 */
.card-header small.text-muted {
    display: block;
    margin-top: 0.5rem;
    line-height: 1.4;
}

.card-header small.text-muted .badge {
    font-size: 0.75rem;
    padding: 0.25em 0.4em;
} 
// Main JavaScript entry point for Webpack


// Import Bootstrap CSS
import 'bootstrap/dist/css/bootstrap.min.css';
// Import Bootstrap Icons CSS
import 'bootstrap-icons/font/bootstrap-icons.css';

// Import custom styles for index.html
import '../../../shared/index-styles.css'; // Assuming src/shared/index-styles.css

// Import Bootstrap JS (all of it, or specific components if preferred)
// This makes `bootstrap` global object available if scripts depend on it that way,
// and also initializes Bootstrap components that rely on data attributes.
import * as bootstrap from 'bootstrap';
// 确保Bootstrap全局可用
window.bootstrap = bootstrap;

// Import navigation component and styles
import NavigationComponent from '../../../shared/components/navigation.js';
import '../../../shared/css/navigation.css';

// Import 独立的合约上传服务（避免重复绑定）
import contractUploadService from '../../../shared/services/contract-upload-service.js';

// Import page-specific scripts for index.html (initializes itself on DOMContentLoaded)
import '../services/agent-relationship-service.js';

// Example: Importing custom CSS (ensure you have a CSS file and loaders configured)
// import '../css/main.css'; // Corrected path assuming src/css/main.css

// Any other global setup or initialization for index.html can go here.
// For example, event listeners that were previously in inline scripts on index.html

document.addEventListener('DOMContentLoaded', async function() {
    // ========== 鉴权检查（必须第一个执行）==========
    try {
        // 导入鉴权工具
        const authUtilsModule = await import('../../../shared/utils/auth-utils.js');
        const AuthUtils = authUtilsModule.default || authUtilsModule.AuthUtils || window.AuthUtils;
        
        if (AuthUtils) {
            // 检查用户登录状态（如果未登录会自动跳转）
            const user = await AuthUtils.guardPage();
            if (!user) return; // 如果未登录，guardPage会自动跳转
            
            // 初始化页面权限控制（显示用户信息栏）
            AuthUtils.initPagePermissions();
            
            console.log('✅ 鉴权检查通过:', user.username, '角色:', user.role);
        } else {
            console.warn('⚠️ 鉴权工具未加载，跳过鉴权检查');
        }
    } catch (error) {
        console.error('❌ 鉴权检查失败:', error);
        // 如果鉴权检查失败，重定向到登录页面
        window.location.href = '/login.html';
        return;
    }
    
    // ========== 原有初始化逻辑 ==========
    // 初始化导航栏（在用户信息栏之后）
    NavigationComponent.init('navigationContainer');
    
    // 初始化合约上传服务（仅在包含合约上传Modal的页面）
    if (document.getElementById('contractAnalysisModal')) {
        contractUploadService.init();
    }
    
    // Any further global setup for index.html, not covered by imported scripts, can go here.
}); 
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理关系分析工具</title>
    <!-- Bootstrap CSS CDN link removed, Webpack will handle it -->
    <!-- Inline styles removed, Webpack will handle them from src/css/index-styles.css -->
    <style>
        /* 统一标题字体样式 - 与个人分析页面保持一致 */
        h1 {
            font-size: 1.8em !important;
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }
        h2 {
            font-size: 1.4em !important;
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }
        h4 {
            font-size: 1.2em !important;
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }
        h5 {
            font-size: 1.1em !important;
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }
        h6 {
            font-size: 1.0em !important;
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }
    </style>
</head>
<body>
    <!-- 导航栏容器 -->
    <div id="navigationContainer"></div>
    
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">代理关系分析工具</h1>
            <div class="text-muted">
                <i class="bi bi-diagram-3"></i> 分析用户代理关系，发现潜在风险
            </div>
        </div>
        
        <!-- 上传区域（保持原有虚线框样式） -->
        <div class="upload-container" id="uploadContainer">
            <h4>上传CSV文件</h4>
            <p>拖拽文件到这里，或者点击选择文件</p>
            <input type="file" id="fileUpload" accept=".csv" class="d-none">

            <!-- 数据过滤选项 -->
            <div class="row mt-3 mb-3">
                <div class="col-md-6">
                    <div class="card border-light">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0"><i class="bi bi-funnel"></i> 数据质量过滤</h6>
                        </div>
                        <div class="card-body py-2">
                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" id="excludeInvalidDevices" checked>
                                <label class="form-check-label" for="excludeInvalidDevices">
                                    <small>排除无效设备ID</small>
                                </label>
                                <div class="form-text">
                                    <small>过滤 unknowndeviceid、null 等无效设备标识</small>
                                </div>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="excludeInternalOperations" checked>
                                <label class="form-check-label" for="excludeInternalOperations">
                                    <small>排除内部运营数据</small>
                                </label>
                                <div class="form-text">
                                    <small>过滤内部运营IP (***********) 和相关用户数据</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-light">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0"><i class="bi bi-gear"></i> 分析选项</h6>
                        </div>
                        <div class="card-body py-2">
                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" id="enableLevelAnalysis" checked>
                                <label class="form-check-label" for="enableLevelAnalysis">
                                    <small>启用层级分析</small>
                                </label>
                                <div class="form-text">
                                    <small>分析用户代理层级关系</small>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <button class="btn btn-primary" id="selectFileBtn">选择文件</button>
        </div>

        <!-- 任务恢复功能 -->
        <div class="row mt-3 pt-3 border-top justify-content-center">
            <div class="col-md-8">
                <div class="text-center mb-3">
                    <label for="taskIdSelector" class="form-label">
                        <i class="bi bi-arrow-repeat"></i> 恢复分析任务
                    </label>
                    <div class="input-group justify-content-center">
                        <select class="form-select" id="taskIdSelector" style="max-width: 400px;">
                            <option value="">手动选择任务...</option>
                        </select>
                        <button class="btn btn-outline-secondary" type="button" id="loadTaskBtn" disabled>
                            <i class="bi bi-arrow-clockwise"></i> 加载
                        </button>
                        <button class="btn btn-outline-info" type="button" id="refreshTasksBtn">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="form-text mt-2">
                        <i class="bi bi-info-circle-fill me-1"></i>
                        选择之前的分析任务ID，点击加载按钮恢复结果（无需重新上传文件）
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增SQL展示区域（独立于上传区域） -->
        <div class="sql-display-container mt-4">
            <div class="text-center">
                <button id="showSqlBtn" class="btn btn-outline-primary mb-2">
                    <i class="bi bi-code me-2"></i>显示SQL查询语句
                </button>
                
                <div id="sqlContainer" class="d-none bg-light p-3 rounded border mx-auto" style="max-width: 800px; max-height: 500px; overflow-y: auto;">
                    <textarea id="sqlTextarea" class="form-control w-100 mb-2" style="font-family: monospace; font-size: 14px; white-space: pre; height: auto; min-height: 300px;" readonly></textarea>
                    <button id="copySqlBtn" class="btn btn-sm btn-success">
                        <i class="bi bi-clipboard me-1"></i>复制到剪贴板
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 加载中的显示 -->
        <div class="row" id="processing" style="display: none;">
            <div class="col-md-12">
                <div class="processing">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>正在处理数据，请稍候...</p>
                </div>
            </div>
        </div>
        
        <!-- 过滤状态显示区域 -->
        <!-- This div will be dynamically created by FilterManager.showFilterStatus if needed -->
        <!-- <div id="filterStatus" class="alert alert-info mb-3" style="display: none;">
            <span id="filterStatusText"></span>
            <button type="button" class="btn-close float-end" aria-label="Close" id="clearBdFilter"></button>
        </div> -->
        
        <!-- 结果展示区域 -->
        <div class="results-container" id="resultsContainer" style="display: none;">
            <!-- 数据摘要 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card summary-card">
                        <div class="card-body">
                            <h5 class="card-title">共享设备关系</h5>
                            <p class="card-text" id="deviceSharedCount">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card summary-card">
                        <div class="card-body">
                            <h5 class="card-title">共享IP关系</h5>
                            <p class="card-text" id="ipSharedCount">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card summary-card">
                        <div class="card-body">
                            <h5 class="card-title">两者都共享关系</h5>
                            <p class="card-text" id="bothSharedCount">0</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="filterUnassignedBD" checked>
                        <label class="form-check-label" for="filterUnassignedBD">不显示BD未分配的数据</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="filterOurbitInternal" checked>
                        <label class="form-check-label" for="filterOurbitInternal">不显示Ourbit_Internal的数据</label>
                    </div>
                </div>
                <div class="col-md-8 d-flex justify-content-end align-items-center gap-3">
                    <div class="input-group" style="max-width: 400px;">
                        <input type="text" class="form-control" placeholder="搜索用户ID..." id="searchInput">
                        <button class="btn btn-primary" id="searchBtn">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <button class="btn btn-outline-secondary" id="clearSearchBtn" style="display: none;">
                            <i class="bi bi-x-circle"></i> 清除搜索
                        </button>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-success" id="exportBtn" style="height: 38px;">
                            <i class="bi bi-download"></i> 导出CSV
                        </button>
                        <button class="btn btn-info" id="batchQueryModalBtn" data-bs-toggle="modal" data-bs-target="#batchQueryModal" style="height: 38px;">
                            <i class="bi bi-search"></i> 批量查询
                        </button>
                        <button class="btn btn-primary" id="batchQueryBtn" style="display: none; height: 38px;">
                            <i class="bi bi-file-earmark-excel"></i> 下载Excel
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Tab导航 -->
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysisTab" type="button" role="tab">数据分析</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="device-tab" data-bs-toggle="tab" data-bs-target="#deviceTab" type="button" role="tab">共享设备</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="ip-tab" data-bs-toggle="tab" data-bs-target="#ipTab" type="button" role="tab">共享IP</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="both-tab" data-bs-toggle="tab" data-bs-target="#bothTab" type="button" role="tab">两者都共享</button>
                </li>
            </ul>

            <!-- Tab内容 -->
            <div class="tab-content" id="myTabContent">
                <!-- 数据分析标签页 -->
                <div class="tab-pane fade show active" id="analysisTab" role="tabpanel">
                    <!-- 子Tab导航 -->
                    <ul class="nav nav-pills mt-3 mb-3" id="analysisSubTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="analysis-device-tab" data-bs-toggle="pill" data-bs-target="#analysisDeviceTab" type="button" role="tab">共享设备排名</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="analysis-ip-tab" data-bs-toggle="pill" data-bs-target="#analysisIpTab" type="button" role="tab">共享IP排名</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="analysis-both-tab" data-bs-toggle="pill" data-bs-target="#analysisBothTab" type="button" role="tab">两者都共享排名</button>
                        </li>
                    </ul>

                    <!-- 子Tab内容 -->
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="analysisDeviceTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">共享设备BD团队排名</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="deviceRankingTable">
                                            <thead>
                                                <tr>
                                                    <th>排名</th>
                                                    <th>BD团队</th>
                                                    <th>数量</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="analysisIpTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">共享IP BD团队排名</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="ipRankingTable">
                                            <thead>
                                                <tr>
                                                    <th>排名</th>
                                                    <th>BD团队</th>
                                                    <th>数量</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="analysisBothTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">两者都共享BD团队排名</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="bothRankingTable">
                                            <thead>
                                                <tr>
                                                    <th>排名</th>
                                                    <th>BD团队</th>
                                                    <th>数量</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 共享设备标签页 -->
                <div class="tab-pane fade" id="deviceTab" role="tabpanel">
                    <!-- 设备子Tab导航 -->
                    <ul class="nav nav-pills mt-3 mb-3" id="deviceSubTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="device-same-tab" data-bs-toggle="pill" data-bs-target="#deviceSameTab" type="button" role="tab">同一BD团队</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="device-diff-tab" data-bs-toggle="pill" data-bs-target="#deviceDiffTab" type="button" role="tab">不同BD团队</button>
                        </li>
                    </ul>

                    <!-- 设备子Tab内容 -->
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="deviceSameTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">同一BD团队 - 共享设备</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="deviceSameBdTable">
                                            <thead>
                                                <tr>
                                                    <th>BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享设备ID</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="deviceSameBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="deviceDiffTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">不同BD团队 - 共享设备</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="deviceDiffBdTable">
                                            <thead>
                                                <tr>
                                                    <th>1号BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号BD团队</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享设备ID</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="deviceDiffBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 共享IP标签页 -->
                <div class="tab-pane fade" id="ipTab" role="tabpanel">
                    <!-- IP子Tab导航 -->
                    <ul class="nav nav-pills mt-3 mb-3" id="ipSubTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="ip-same-tab" data-bs-toggle="pill" data-bs-target="#ipSameTab" type="button" role="tab">同一BD团队</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="ip-diff-tab" data-bs-toggle="pill" data-bs-target="#ipDiffTab" type="button" role="tab">不同BD团队</button>
                        </li>
                    </ul>

                    <!-- IP子Tab内容 -->
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="ipSameTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">同一BD团队 - 共享IP</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="ipSameBdTable">
                                            <thead>
                                                <tr>
                                                    <th>BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享IP地址</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="ipSameBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="ipDiffTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">不同BD团队 - 共享IP</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="ipDiffBdTable">
                                            <thead>
                                                <tr>
                                                    <th>1号BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号BD团队</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享IP地址</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="ipDiffBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 两者都共享标签页 -->
                <div class="tab-pane fade" id="bothTab" role="tabpanel">
                    <!-- 两者都共享子Tab导航 -->
                    <ul class="nav nav-pills mt-3 mb-3" id="bothSubTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="both-same-tab" data-bs-toggle="pill" data-bs-target="#bothSameTab" type="button" role="tab">同一BD团队</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="both-diff-tab" data-bs-toggle="pill" data-bs-target="#bothDiffTab" type="button" role="tab">不同BD团队</button>
                        </li>
                    </ul>

                    <!-- 两者都共享子Tab内容 -->
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="bothSameTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">同一BD团队 - 两者都共享</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="bothSameBdTable">
                                            <thead>
                                                <tr>
                                                    <th>BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享设备ID</th>
                                                    <th>共享IP地址</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="bothSameBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="bothDiffTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">不同BD团队 - 两者都共享</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="bothDiffBdTable">
                                            <thead>
                                                <tr>
                                                    <th>1号BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号BD团队</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享设备ID</th>
                                                    <th>共享IP地址</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="bothDiffBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- Bootstrap JS bundle (includes Popper) CDN link removed, Webpack will handle it -->
    <!-- Inline script block removed, Webpack will handle it from src/js/index-page-scripts.js -->
    
    <!-- 合约分析弹窗 (Modal for contract analysis) -->
    <div class="modal fade" id="contractAnalysisModal" tabindex="-1" aria-labelledby="contractAnalysisModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contractAnalysisModalLabel">合约分析上传</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="upload-container mb-3" id="contractUploadContainer">
                        <h5>上传合约交易数据</h5>
                        <p>支持.xlsx格式文件，拖拽文件到这里或点击选择文件</p>
                        <input type="file" id="contractFileUpload" accept=".xlsx" class="d-none">
                        <button class="btn btn-primary" id="contractSelectFileBtn">选择文件</button>
                    </div>
                    <div id="contractProcessing" style="display: none;">
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                        <p class="text-center mt-2">正在处理合约数据，请稍候...</p>
                        <div class="progress mt-2">
                            <div id="contractProgressBar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                    </div>
                    <div id="contractResults" style="display: none;">
                        <h5>分析结果</h5>
                        <div class="alert alert-info">
                            <p>检测到 <span id="contractAbnormalCount" class="fw-bold">0</span> 个异常交易行为</p>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="contractAnalysisTable">
                                <thead>
                                    <tr>
                                        <th>用户ID</th>
                                        <th>交易对</th>
                                        <th>时间</th>
                                        <th>异常交易量(USDT)</th>
                                        <th>总持仓量(USDT)</th>
                                        <th>异常原因</th>
                                        <th>风险等级</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success" id="contractExportBtn" style="display: none;">导出结果</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量查询弹窗 -->
    <div class="modal fade" id="batchQueryModal" tabindex="-1" aria-labelledby="batchQueryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchQueryModalLabel">批量查询用户关系</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="batchUserIds" class="form-label">用户ID列表</label>
                        <textarea class="form-control" id="batchUserIds" rows="8"
                                  placeholder="请输入用户ID，每行一个，例如：&#10;12345678&#10;87654321&#10;11223344&#10;&#10;支持最多5000个用户ID"></textarea>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            每行输入一个用户ID，系统将查询这些用户的设备共享和IP共享关系
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">查询范围</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeDeviceSharing" checked>
                                    <label class="form-check-label" for="includeDeviceSharing">
                                        设备共享关系
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeIpSharing" checked>
                                    <label class="form-check-label" for="includeIpSharing">
                                        IP共享关系
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">输出格式</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="outputFormat" id="separateSheets" value="separate" checked>
                                    <label class="form-check-label" for="separateSheets">
                                        分别输出（同BD/跨BD分开）
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="outputFormat" id="combinedSheet" value="combined">
                                    <label class="form-check-label" for="combinedSheet">
                                        合并输出（所有关系在一起）
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label">颜色标注选项</label>
                                <div class="form-text mb-2">
                                    <i class="bi bi-palette"></i>
                                    选择基于哪种关联类型进行颜色标注，相同关系群体将使用相同颜色
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="colorCoding" id="colorByDevice" value="device">
                                    <label class="form-check-label" for="colorByDevice">
                                        基于设备关系标注
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="colorCoding" id="colorByIp" value="ip">
                                    <label class="form-check-label" for="colorByIp">
                                        基于IP关系标注
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="colorCoding" id="colorByBoth" value="both" checked>
                                    <label class="form-check-label" for="colorByBoth">
                                        基于设备+IP关系标注（推荐）
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="colorCoding" id="colorByNone" value="none">
                                    <label class="form-check-label" for="colorByNone">
                                        不使用颜色标注
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-lightbulb"></i>
                        <strong>说明：</strong>
                        <ul class="mb-0 mt-2">
                            <li>系统将查询输入用户ID与其他用户的共享关系</li>
                            <li>同BD关系：只显示共享的设备ID或IP地址</li>
                            <li>跨BD关系：显示"BD名+设备ID"或"BD名+IP"格式</li>
                            <li>颜色标注：相同颜色的行表示用户之间存在关联关系</li>
                            <li>结果将以Excel文件形式下载</li>
                        </ul>
                    </div>

                    <div class="alert alert-secondary">
                        <i class="bi bi-info-circle"></i>
                        <strong>颜色标注说明：</strong>
                        <ul class="mb-0 mt-2">
                            <li><strong>基于设备关系：</strong>共享设备的用户使用相同颜色</li>
                            <li><strong>基于IP关系：</strong>共享IP的用户使用相同颜色</li>
                            <li><strong>基于设备+IP关系：</strong>同时共享设备和IP的用户使用相同颜色（推荐）</li>
                            <li><strong>不使用颜色标注：</strong>所有行保持默认颜色</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="startBatchQueryBtn">
                        <i class="bi bi-search"></i> 开始查询
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Script for contract-analysis.js removed, Webpack will handle it via main.js -->
    <!-- HtmlWebpackPlugin will inject bundled JS here -->
</body>
</html>
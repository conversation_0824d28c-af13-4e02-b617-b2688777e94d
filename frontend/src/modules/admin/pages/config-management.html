<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置管理 - 风险链接分析工具</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- js-yaml for YAML parsing -->
    <script src="https://cdn.jsdelivr.net/npm/js-yaml@4.1.0/dist/js-yaml.min.js"></script>
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="../styles/admin.css">
    <link rel="stylesheet" href="../../../shared/css/navigation.css">
    
    <style>
        .config-card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        
        .config-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .config-editor {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .config-status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        
        .backup-item {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-bottom: 10px;
        }
        
        .yaml-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- 导航栏容器 -->
    <div id="navigationContainer"></div>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-sliders text-primary"></i>
                            配置管理
                        </h1>
                        <p class="text-muted mb-0">管理系统YAML配置文件，支持在线编辑和备份</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-secondary me-2" onclick="window.location.href='/system-dashboard.html'">
                            <i class="bi bi-arrow-left"></i> 返回控制台
                        </button>
                        <button class="btn btn-outline-primary me-2" id="refreshConfigList">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                        <button class="btn btn-outline-info" onclick="showAllBackups()">
                            <i class="bi bi-clock-history"></i> 所有备份
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置文件列表 -->
        <div class="row mb-4">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-gear-fill"></i> 配置文件列表
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="configListContainer">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载配置文件...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle"></i> 使用说明
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="bi bi-1-circle text-primary"></i> 用户行为分析配置</h6>
                                <p class="small text-muted">
                                    控制用户交易行为分析的各种参数，包括：
                                    <br>• 最小交易量和交易笔数阈值
                                    <br>• 专业度评分权重配置
                                    <br>• 资金规模分类标准
                                    <br>• 币种胜率分析参数
                                </p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="bi bi-2-circle text-success"></i> 代理商数据过滤配置</h6>
                                <p class="small text-muted">
                                    管理代理商分析中的数据过滤规则：
                                    <br>• 无效设备ID过滤模式
                                    <br>• 内部运营数据排除列表
                                    <br>• 数据质量检查参数
                                    <br>• 过滤统计显示设置
                                </p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="bi bi-3-circle text-warning"></i> 对敲交易检测配置</h6>
                                <p class="small text-muted">
                                    调整对敲交易检测算法参数：
                                    <br>• 时间窗口和金额匹配阈值
                                    <br>• 风险等级分类标准
                                    <br>• 同账户和跨账户检测规则
                                    <br>• 业务规则和频率检查
                                </p>
                            </div>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-lightbulb"></i>
                            <strong>提示：</strong> 配置修改后将在下次执行相应分析时生效。系统会自动创建备份文件，确保配置安全。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置编辑器模态框 -->
    <div class="modal fade" id="configEditorModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="configEditorTitle">编辑配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="configEditor" class="form-label">
                                    <i class="bi bi-code-slash"></i> 配置内容 (YAML格式)
                                </label>
                                <textarea id="configEditor" class="form-control config-editor" 
                                         rows="25" placeholder="配置内容将在这里显示..."></textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="bi bi-eye"></i> 配置预览
                                </label>
                                <div id="configPreview" class="yaml-preview">
                                    选择配置文件后将显示预览...
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="bi bi-info-circle"></i> 配置信息
                                </label>
                                <div id="configInfo" class="small text-muted">
                                    配置文件信息将在这里显示...
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>注意：</strong> 
                        请确保YAML格式正确，错误的配置可能导致分析功能异常。建议先使用"验证格式"功能检查配置。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i> 取消
                    </button>
                    <button type="button" class="btn btn-info" onclick="previewConfig()">
                        <i class="bi bi-eye"></i> 预览
                    </button>
                    <button type="button" class="btn btn-warning" onclick="validateConfig()">
                        <i class="bi bi-check-circle"></i> 验证格式
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveConfig()">
                        <i class="bi bi-save"></i> 保存配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 备份列表模态框 -->
    <div class="modal fade" id="backupListModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="backupListTitle">配置备份</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="backupListContent">
                    <!-- 备份列表内容将动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notificationToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-info-circle text-primary me-2"></i>
                <strong class="me-auto">系统通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- 通知消息 -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 导航组件 -->
    <script src="../../../shared/components/NavigationComponent.js"></script>
</body>
</html>

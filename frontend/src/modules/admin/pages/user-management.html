<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 风险链接分析工具</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="../styles/admin.css">
    <link rel="stylesheet" href="../../../shared/css/navigation.css">
</head>
<body>


    <!-- 导航栏容器 -->
    <div id="navigationContainer"></div>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-people-fill text-primary"></i>
                            用户管理
                        </h1>
                        <p class="text-muted mb-0">管理系统用户账户和权限</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-secondary me-2" onclick="window.location.href='/system-dashboard.html'">
                            <i class="bi bi-arrow-left"></i> 返回控制台
                        </button>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                            <i class="bi bi-person-plus"></i> 创建用户
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选区域 -->
        <div class="row mb-4">
            <div class="col">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="searchInput" class="form-label">搜索用户</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="searchInput" 
                                           placeholder="输入用户名或邮箱...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="roleFilter" class="form-label">角色筛选</label>
                                <select class="form-select" id="roleFilter">
                                    <option value="">全部角色</option>
                                    <option value="admin">管理员</option>
                                    <option value="viewer">查看者</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="statusFilter" class="form-label">状态筛选</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-outline-secondary" id="clearFilters">
                                        <i class="bi bi-arrow-clockwise"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户列表区域 -->
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-table"></i> 用户列表
                            </h5>
                            <div class="d-flex align-items-center">
                                <span class="text-muted me-3" id="userCount">共 0 个用户</span>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-sm" id="refreshUsers">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" id="exportUsers">
                                        <i class="bi bi-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- 加载状态 -->
                        <div id="loadingState" class="text-center py-5" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在加载用户数据...</p>
                        </div>

                        <!-- 用户表格 -->
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="usersTable">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </th>
                                        <th scope="col">用户名</th>
                                        <th scope="col">邮箱</th>
                                        <th scope="col">角色</th>
                                        <th scope="col">状态</th>
                                        <th scope="col">创建时间</th>
                                        <th scope="col">最后登录</th>
                                        <th scope="col">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <!-- 用户数据将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 空状态 -->
                        <div id="emptyState" class="text-center py-5" style="display: none;">
                            <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">暂无用户数据</h5>
                            <p class="text-muted">点击上方"创建用户"按钮添加第一个用户</p>
                        </div>
                    </div>
                    
                    <!-- 分页区域 -->
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                显示第 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，
                                共 <span id="totalUsers">0</span> 条记录
                            </div>
                            <nav aria-label="用户列表分页">
                                <ul class="pagination pagination-sm mb-0" id="pagination">
                                    <!-- 分页按钮将在这里动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建用户模态框 -->
    <div class="modal fade" id="createUserModal" tabindex="-1" aria-labelledby="createUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createUserModalLabel">
                        <i class="bi bi-person-plus"></i> 创建新用户
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <form id="createUserForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="createUsername" class="form-label">用户名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="createUsername" name="username" required>
                            <div class="form-text">用户名必须唯一，建议使用英文字母和数字</div>
                        </div>
                        <div class="mb-3">
                            <label for="createPassword" class="form-label">密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="createPassword" name="password" required>
                            <div class="form-text">密码至少8位，包含大小写字母、数字和特殊字符</div>
                        </div>
                        <div class="mb-3">
                            <label for="createEmail" class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="createEmail" name="email">
                            <div class="form-text">用于接收系统通知和密码重置</div>
                        </div>
                        <div class="mb-3">
                            <label for="createRole" class="form-label">角色 <span class="text-danger">*</span></label>
                            <select class="form-select" id="createRole" name="role" required>
                                <option value="">请选择角色</option>
                                <option value="viewer">查看者 - 只能查看数据</option>
                                <option value="admin">管理员 - 拥有所有权限</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i> 创建用户
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">
                        <i class="bi bi-person-gear"></i> 编辑用户
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <form id="editUserForm">
                    <div class="modal-body">
                        <input type="hidden" id="editUserId" name="userId">
                        <div class="mb-3">
                            <label for="editUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="editUsername" readonly>
                            <div class="form-text">用户名创建后不可修改</div>
                        </div>
                        <div class="mb-3">
                            <label for="editEmail" class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="editEmail" name="email">
                        </div>
                        <div class="mb-3">
                            <label for="editRole" class="form-label">角色 <span class="text-danger">*</span></label>
                            <select class="form-select" id="editRole" name="role" required>
                                <option value="viewer">查看者 - 只能查看数据</option>
                                <option value="admin">管理员 - 拥有所有权限</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editStatus" class="form-label">状态</label>
                            <select class="form-select" id="editStatus" name="status">
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i> 保存更改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger" id="deleteConfirmModalLabel">
                        <i class="bi bi-exclamation-triangle"></i> 确认删除
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除用户 <strong id="deleteUserName"></strong> 吗？</p>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>警告：</strong>此操作不可撤销，用户的所有数据将被永久删除。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="bi bi-trash"></i> 确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastContainer">
        <!-- Toast 消息将在这里动态生成 -->
    </div>

    <!-- JavaScript 依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Webpack会自动注入打包后的JavaScript文件 -->
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理控制台 - 风险链接分析工具</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="../styles/admin.css">
    <link rel="stylesheet" href="../../../shared/css/navigation.css">
</head>
<body>


    <!-- 导航栏容器 -->
    <div id="navigationContainer"></div>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-speedometer2 text-primary"></i>
                            系统管理控制台
                        </h1>
                        <p class="text-muted mb-0">系统状态监控、配置管理和日志查看</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary" id="refreshDashboard">
                            <i class="bi bi-arrow-clockwise"></i> 刷新数据
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统状态卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="bi bi-people-fill text-primary me-2" style="font-size: 2rem;"></i>
                            <div>
                                <h3 class="mb-0" id="totalUsers">-</h3>
                                <small class="text-muted">总用户数</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="bi bi-person-check text-success me-2" style="font-size: 2rem;"></i>
                            <div>
                                <h3 class="mb-0" id="activeUsers">-</h3>
                                <small class="text-muted">活跃用户</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="bi bi-clock text-warning me-2" style="font-size: 2rem;"></i>
                            <div>
                                <h3 class="mb-0" id="onlineSessions">-</h3>
                                <small class="text-muted">在线会话</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="bi bi-shield-check text-info me-2" style="font-size: 2rem;"></i>
                            <div>
                                <h3 class="mb-0" id="systemStatus">-</h3>
                                <small class="text-muted">系统状态</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能导航卡片 -->
        <div class="row mb-4">
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card h-100 admin-function-card" onclick="location.href='user-management.html'">
                    <div class="card-body text-center">
                        <i class="bi bi-people-fill text-primary mb-3" style="font-size: 3rem;"></i>
                        <h5 class="card-title">用户管理</h5>
                        <p class="card-text text-muted">管理系统用户账户和权限</p>
                        <div class="mt-auto">
                            <span class="badge bg-primary" id="userCount">0 个用户</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card h-100 admin-function-card" onclick="location.href='config-management.html'">
                    <div class="card-body text-center">
                        <i class="bi bi-sliders text-success mb-3" style="font-size: 3rem;"></i>
                        <h5 class="card-title">配置管理</h5>
                        <p class="card-text text-muted">管理YAML配置文件和系统参数</p>
                        <div class="mt-auto">
                            <span class="badge bg-success">YAML配置</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card h-100 admin-function-card" onclick="showActivityLogs()">
                    <div class="card-body text-center">
                        <i class="bi bi-journal-text text-warning mb-3" style="font-size: 3rem;"></i>
                        <h5 class="card-title">操作日志</h5>
                        <p class="card-text text-muted">查看用户活动和系统事件</p>
                        <div class="mt-auto">
                            <span class="badge bg-warning" id="recentLogsCount">0 条记录</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card h-100 admin-function-card" onclick="showSessionManagement()">
                    <div class="card-body text-center">
                        <i class="bi bi-person-lines-fill text-info mb-3" style="font-size: 3rem;"></i>
                        <h5 class="card-title">会话管理</h5>
                        <p class="card-text text-muted">管理在线用户和会话</p>
                        <div class="mt-auto">
                            <span class="badge bg-info" id="sessionCount">0 个会话</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表和详细信息 -->
        <div class="row">
            <!-- 用户活动图表 -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up"></i> 用户活动趋势
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="activityChart" height="100"></canvas>
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history"></i> 最近活动
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="recentActivities" class="activity-list">
                            <!-- 活动列表将在这里动态生成 -->
                        </div>
                        <div class="text-center mt-3">
                            <button class="btn btn-outline-primary btn-sm" onclick="showActivityLogs()">
                                查看全部日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle"></i> 系统信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>系统版本:</strong><br>
                                <span class="text-muted">v1.0.0</span>
                            </div>
                            <div class="col-6">
                                <strong>运行时间:</strong><br>
                                <span class="text-muted" id="uptime">-</span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>数据库状态:</strong><br>
                                <span class="badge bg-success" id="dbStatus">正常</span>
                            </div>
                            <div class="col-6">
                                <strong>最后备份:</strong><br>
                                <span class="text-muted" id="lastBackup">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning"></i> 快速操作
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="location.href='user-management.html'">
                                <i class="bi bi-person-plus"></i> 创建新用户
                            </button>
                            <button class="btn btn-outline-success" onclick="exportSystemData()">
                                <i class="bi bi-download"></i> 导出系统数据
                            </button>
                            <button class="btn btn-outline-warning" onclick="clearOldLogs()">
                                <i class="bi bi-trash"></i> 清理旧日志
                            </button>
                            <button class="btn btn-outline-info" onclick="showSystemConfig()">
                                <i class="bi bi-gear"></i> 系统设置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastContainer">
        <!-- Toast 消息将在这里动态生成 -->
    </div>

    <!-- JavaScript 依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Webpack会自动注入打包后的JavaScript文件 -->

    <style>
        .admin-function-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .admin-function-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        
        .activity-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .activity-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
        }
    </style>
</body>
</html>

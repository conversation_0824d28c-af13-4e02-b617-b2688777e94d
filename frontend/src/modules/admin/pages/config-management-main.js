/**
 * 配置管理页面主入口
 * 负责页面初始化和组件协调
 */

// 导入配置管理组件
import '../components/ConfigManagement.js';

// DOM加载完成后的初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 配置管理页面开始加载...');

    // 防止重复初始化（热重载时可能发生）
    if (window.configManagementInitialized) {
        return;
    }

    try {
        // 初始化导航栏
        if (window.NavigationComponent) {
            window.NavigationComponent.init('navigationContainer');
        }

        // 配置管理组件已在HTML中通过script标签加载并自动初始化
        // 这里只需要等待组件完成初始化
        
        // 检查用户权限（但不阻止页面加载）
        checkAdminPermission().catch(error => {
            console.error('权限检查失败:', error);
            showPermissionError(error);
        });
        
        // 标记为已初始化
        window.configManagementInitialized = true;
        
        console.log('✅ 配置管理页面初始化完成');
        
    } catch (error) {
        console.error('❌ 配置管理页面初始化失败:', error);
        showInitializationError(error);
    }
});

/**
 * 检查管理员权限
 */
async function checkAdminPermission() {
    try {
        const response = await fetch('/api/auth/check', {
            method: 'GET',
            credentials: 'include'
        });
        
        if (!response.ok) {
            console.warn('权限检查请求失败:', response.status);
            showPermissionError(new Error(`HTTP ${response.status}: 权限检查失败`));
            return;
        }
        
        const result = await response.json();
        
        if (!result.authenticated) {
            // 未登录，显示登录提示
            showLoginRequired();
            return;
        }
        
        if (result.user.role !== 'admin') {
            // 非管理员，显示权限不足
            showPermissionDenied();
            return;
        }
        
        console.log('✅ 管理员权限验证通过');
        
    } catch (error) {
        console.error('权限检查异常:', error);
        showPermissionError(error);
    }
}

/**
 * 显示登录提示
 */
function showLoginRequired() {
    const container = document.getElementById('configListContainer');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-person-lock display-1 text-warning"></i>
                <h3 class="mt-3">需要登录</h3>
                <p class="text-muted">请先登录管理员账户才能访问配置管理功能</p>
                <button class="btn btn-primary" onclick="window.location.href='/login.html'">
                    <i class="bi bi-box-arrow-in-right"></i> 前往登录
                </button>
            </div>
        `;
    }
}

/**
 * 显示权限不足提示
 */
function showPermissionDenied() {
    const container = document.getElementById('configListContainer');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-shield-exclamation display-1 text-warning"></i>
                <h3 class="mt-3">权限不足</h3>
                <p class="text-muted">只有管理员才能访问配置管理功能</p>
                <button class="btn btn-primary" onclick="window.location.href='/system-dashboard.html'">
                    <i class="bi bi-arrow-left"></i> 返回控制台
                </button>
            </div>
        `;
    }
}

/**
 * 显示权限检查错误
 */
function showPermissionError(error) {
    const container = document.getElementById('configListContainer');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-exclamation-triangle display-1 text-danger"></i>
                <h3 class="mt-3">权限检查失败</h3>
                <p class="text-muted">${error.message}</p>
                <button class="btn btn-outline-primary" onclick="window.location.reload()">
                    <i class="bi bi-arrow-clockwise"></i> 重新加载
                </button>
            </div>
        `;
    }
}

/**
 * 显示初始化错误
 */
function showInitializationError(error) {
    const container = document.getElementById('configListContainer');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-exclamation-circle display-1 text-danger"></i>
                <h3 class="mt-3">页面初始化失败</h3>
                <p class="text-muted">${error.message}</p>
                <div class="mt-3">
                    <button class="btn btn-primary me-2" onclick="window.location.reload()">
                        <i class="bi bi-arrow-clockwise"></i> 重新加载
                    </button>
                    <button class="btn btn-outline-secondary" onclick="window.location.href='/system-dashboard.html'">
                        <i class="bi bi-arrow-left"></i> 返回控制台
                    </button>
                </div>
            </div>
        `;
    }
}

// 页面卸载时的清理
window.addEventListener('beforeunload', () => {
    // 清理定时器
    if (window.configManagement && window.configManagement.previewTimeout) {
        clearTimeout(window.configManagement.previewTimeout);
    }
    
    console.log('🧹 配置管理页面清理完成');
});

// 错误处理
window.addEventListener('error', (event) => {
    console.error('页面运行时错误:', event.error);
    
    // 可以在这里添加错误上报逻辑
    if (window.configManagement) {
        window.configManagement.showError('页面运行时错误: ' + event.error.message);
    }
});

// 未处理的Promise拒绝
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    
    if (window.configManagement) {
        window.configManagement.showError('系统错误: ' + event.reason);
    }
    
    // 阻止默认的错误处理
    event.preventDefault();
});

console.log('📄 配置管理页面脚本加载完成');

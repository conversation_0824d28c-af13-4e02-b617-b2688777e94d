<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限矩阵管理 - 风险链接分析工具</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="../styles/admin.css">
    <link rel="stylesheet" href="../../../shared/css/navigation.css">
    
    <style>
        .permission-matrix {
            overflow-x: auto;
            max-height: 70vh;
        }
        
        .matrix-table {
            min-width: 800px;
        }
        
        .matrix-table th {
            position: sticky;
            top: 0;
            background: #f8f9fa;
            z-index: 10;
            border-bottom: 2px solid #dee2e6;
        }
        
        .matrix-table th:first-child {
            position: sticky;
            left: 0;
            z-index: 11;
            background: #f8f9fa;
            border-right: 2px solid #dee2e6;
        }
        
        .matrix-table td:first-child {
            position: sticky;
            left: 0;
            background: white;
            border-right: 2px solid #dee2e6;
            font-weight: 500;
        }
        
        .permission-checkbox {
            transform: scale(1.2);
        }
        
        .permission-group {
            background-color: #f8f9fa;
            border-left: 4px solid #667eea;
        }
        
        .permission-item {
            padding-left: 1rem;
        }
        
        .action-header {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 60px;
            text-align: center;
        }
        
        .role-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .matrix-cell {
            text-align: center;
            vertical-align: middle;
            padding: 0.5rem;
        }
        
        .permission-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
    </style>
</head>
<body>


    <!-- 导航栏容器 -->
    <div id="navigationContainer"></div>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-grid-3x3-gap text-primary"></i>
                            权限矩阵管理
                        </h1>
                        <p class="text-muted mb-0">管理角色权限分配和细粒度权限控制</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-secondary me-2" onclick="window.location.href='/system-dashboard.html'">
                            <i class="bi bi-arrow-left"></i> 返回控制台
                        </button>
                        <button class="btn btn-outline-primary me-2" id="refreshMatrix">
                            <i class="bi bi-arrow-clockwise"></i> 刷新矩阵
                        </button>
                        <button class="btn btn-success" id="saveChanges" style="display: none;">
                            <i class="bi bi-check-lg"></i> 保存更改
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="row mb-4">
            <div class="col">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3 align-items-center">
                            <div class="col-md-3">
                                <label for="roleFilter" class="form-label">筛选角色</label>
                                <select class="form-select" id="roleFilter">
                                    <option value="">显示所有角色</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="groupFilter" class="form-label">筛选权限组</label>
                                <select class="form-select" id="groupFilter">
                                    <option value="">显示所有权限组</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="actionFilter" class="form-label">筛选操作</label>
                                <select class="form-select" id="actionFilter">
                                    <option value="">显示所有操作</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-outline-secondary" id="clearFilters">
                                        <i class="bi bi-arrow-clockwise"></i> 重置筛选
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限矩阵表格 -->
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-table"></i> 权限矩阵
                            </h5>
                            <div class="d-flex align-items-center">
                                <span class="text-muted me-3" id="matrixStats">-</span>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showInheritedPermissions">
                                    <label class="form-check-label" for="showInheritedPermissions">
                                        显示继承权限
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0 position-relative">
                        <!-- 加载状态 -->
                        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="text-muted mb-0">正在加载权限矩阵...</p>
                            </div>
                        </div>

                        <!-- 权限矩阵表格 -->
                        <div class="permission-matrix">
                            <table class="table table-bordered matrix-table mb-0" id="permissionMatrix">
                                <thead>
                                    <tr id="matrixHeader">
                                        <th scope="col" style="min-width: 200px;">权限 / 角色</th>
                                        <!-- 角色列头将在这里动态生成 -->
                                    </tr>
                                    <tr id="actionHeader">
                                        <th scope="col">操作</th>
                                        <!-- 操作列头将在这里动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="matrixBody">
                                    <!-- 权限矩阵内容将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 空状态 -->
                        <div id="emptyState" class="text-center py-5" style="display: none;">
                            <i class="bi bi-grid text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">暂无权限数据</h5>
                            <p class="text-muted">请先初始化权限系统</p>
                            <button class="btn btn-primary" onclick="initializePermissions()">
                                <i class="bi bi-gear"></i> 初始化权限系统
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限详情面板 -->
        <div class="row mt-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle"></i> 权限说明
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h6>操作类型</h6>
                                <ul class="list-unstyled">
                                    <li><span class="badge bg-primary me-2">读取</span>查看资源</li>
                                    <li><span class="badge bg-success me-2">创建</span>创建新资源</li>
                                    <li><span class="badge bg-warning me-2">编辑</span>修改资源</li>
                                    <li><span class="badge bg-danger me-2">删除</span>删除资源</li>
                                </ul>
                            </div>
                            <div class="col-6">
                                <h6>权限状态</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>已授权</li>
                                    <li><i class="bi bi-x-circle text-danger me-2"></i>未授权</li>
                                    <li><i class="bi bi-arrow-down-circle text-info me-2"></i>继承权限</li>
                                    <li><i class="bi bi-clock text-warning me-2"></i>临时权限</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning"></i> 快速操作
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="exportPermissionMatrix()">
                                <i class="bi bi-download"></i> 导出权限矩阵
                            </button>
                            <button class="btn btn-outline-success" onclick="showBulkAssignModal()">
                                <i class="bi bi-plus-circle"></i> 批量分配权限
                            </button>
                            <button class="btn btn-outline-warning" onclick="showPermissionAuditModal()">
                                <i class="bi bi-search"></i> 权限审计
                            </button>
                            <button class="btn btn-outline-info" onclick="showPermissionTemplateModal()">
                                <i class="bi bi-file-earmark-text"></i> 权限模板
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量分配权限模态框 -->
    <div class="modal fade" id="bulkAssignModal" tabindex="-1" aria-labelledby="bulkAssignModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkAssignModalLabel">
                        <i class="bi bi-plus-circle"></i> 批量分配权限
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="bulkAssignForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="bulkRole" class="form-label">选择角色</label>
                                <select class="form-select" id="bulkRole" required>
                                    <option value="">请选择角色</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="bulkPermissionGroup" class="form-label">权限组</label>
                                <select class="form-select" id="bulkPermissionGroup" required>
                                    <option value="">请选择权限组</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">选择操作</label>
                                <div id="bulkActions" class="d-flex flex-wrap gap-2">
                                    <!-- 操作选择将在这里动态生成 -->
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmBulkAssign">
                        <i class="bi bi-check-lg"></i> 确认分配
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastContainer">
        <!-- Toast 消息将在这里动态生成 -->
    </div>

    <!-- JavaScript 依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Webpack会自动注入打包后的JavaScript文件 -->

    <script>
        // 全局函数定义
        function initializePermissions() {
            if (window.permissionMatrix) {
                window.permissionMatrix.initializePermissions();
            }
        }

        function exportPermissionMatrix() {
            if (window.permissionMatrix) {
                window.permissionMatrix.exportMatrix();
            }
        }

        function showBulkAssignModal() {
            if (window.permissionMatrix) {
                window.permissionMatrix.showBulkAssignModal();
            }
        }

        function showPermissionAuditModal() {
            alert('权限审计功能开发中...\n\n将包括：\n- 权限使用统计\n- 异常权限检测\n- 权限变更历史\n- 合规性检查');
        }

        function showPermissionTemplateModal() {
            alert('权限模板功能开发中...\n\n将包括：\n- 预定义权限模板\n- 快速角色创建\n- 权限组合管理\n- 模板导入导出');
        }
    </script></script>
</body>
</html>

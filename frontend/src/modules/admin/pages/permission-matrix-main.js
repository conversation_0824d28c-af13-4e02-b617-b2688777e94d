// Permission Matrix Page Main Entry Point

// Import Bootstrap CSS and JavaScript
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap-icons/font/bootstrap-icons.css';
import * as bootstrap from 'bootstrap'; // 重要：导入Bootstrap JavaScript以支持下拉菜单等交互功能
// 确保Bootstrap全局可用
window.bootstrap = bootstrap;

// Import navigation component and styles
import NavigationComponent from '../../../shared/components/navigation.js';
import '../../../shared/css/navigation.css';

// Import admin styles
import '../styles/admin.css';

// Import permission matrix component
import PermissionMatrix from '../components/PermissionMatrix.js';

// DOM加载完成后的初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 权限矩阵页面开始加载...');

    // 初始化导航栏
    NavigationComponent.init('navigationContainer');

    // 初始化权限矩阵组件（组件内部会处理鉴权）
    const permissionMatrix = new PermissionMatrix();
    await permissionMatrix.init();

    // 确保Bootstrap下拉菜单正常工作 - 在所有组件初始化完成后
    setTimeout(() => {
        if (typeof window.bootstrap !== 'undefined') {
            // 手动初始化所有下拉菜单
            const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');

            dropdownElements.forEach((element) => {
                try {
                    // 检查是否已经初始化过，避免重复初始化
                    if (!element._bootstrap_dropdown_initialized) {
                        const dropdown = new window.bootstrap.Dropdown(element);
                        element._bootstrap_dropdown_initialized = true;

                        // 添加点击事件监听器确保下拉菜单工作
                        element.addEventListener('click', function() {
                            // 手动切换下拉菜单
                            try {
                                dropdown.toggle();
                            } catch (error) {
                                console.error('下拉菜单切换失败:', error);
                            }
                        });
                    }
                } catch (error) {
                    console.error('下拉菜单初始化失败:', error);
                }
            });
        }
    }, 1000); // 增加延迟时间，确保所有组件都已完全初始化

    console.log('✅ 权限矩阵页面初始化完成');
});

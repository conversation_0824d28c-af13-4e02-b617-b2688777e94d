// User Management Page Main Entry Point

// Import Bootstrap CSS and JavaScript
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap-icons/font/bootstrap-icons.css';
import * as bootstrap from 'bootstrap'; // 重要：导入Bootstrap JavaScript以支持下拉菜单等交互功能
// 确保Bootstrap全局可用
window.bootstrap = bootstrap;

// Import navigation component and styles
import NavigationComponent from '../../../shared/components/navigation.js';
import '../../../shared/css/navigation.css';

// Import admin styles
import '../styles/admin.css';

// Import user management component
import UserManagement from '../components/UserManagement.js';

// DOM加载完成后的初始化
document.addEventListener('DOMContentLoaded', async () => {
    
    // ========== 鉴权检查（必须第一个执行）==========
    try {
        // 导入鉴权工具
        const authUtilsModule = await import('../../../shared/utils/auth-utils.js');
        const AuthUtils = authUtilsModule.default || authUtilsModule.AuthUtils || window.AuthUtils;
        
        if (AuthUtils) {
            // 检查用户登录状态（如果未登录会自动跳转）
            const user = await AuthUtils.guardPage();
            if (!user) return; // 如果未登录，guardPage会自动跳转
            
            // 检查管理员权限
            if (user.role !== 'admin') {
                console.log('❌ 权限不足，重定向到主页');
                alert('权限不足，只有管理员可以访问此页面');
                window.location.href = '/';
                return;
            }
            
            // 初始化页面权限控制（不显示用户信息栏，只在导航栏显示）
            // AuthUtils.initPagePermissions(); // 暂时注释掉，避免重复显示用户信息

            console.log('✅ 鉴权检查通过:', user.username, '角色:', user.role);
        } else {
            console.warn('⚠️ 鉴权工具未加载，跳过鉴权检查');
        }
    } catch (error) {
        console.error('❌ 鉴权检查失败:', error);
        // 如果鉴权检查失败，重定向到登录页面
        window.location.href = '/login.html';
        return;
    }
    
    // ========== 原有初始化逻辑 ==========
    // 初始化导航栏（在用户信息栏之后）
    NavigationComponent.init('navigationContainer');

    // 初始化用户管理组件
    const userManagement = new UserManagement();
    await userManagement.init();

    // 确保Bootstrap下拉菜单正常工作 - 在所有组件初始化完成后
    setTimeout(() => {
        if (typeof window.bootstrap !== 'undefined') {
            // 手动初始化所有下拉菜单
            const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');

            dropdownElements.forEach((element) => {
                try {
                    // 检查是否已经初始化过，避免重复初始化
                    if (!element._bootstrap_dropdown_initialized) {
                        const dropdown = new window.bootstrap.Dropdown(element);
                        element._bootstrap_dropdown_initialized = true;

                        // 添加点击事件监听器确保下拉菜单工作
                        element.addEventListener('click', function() {
                            // 手动切换下拉菜单
                            try {
                                dropdown.toggle();
                            } catch (error) {
                                console.error('下拉菜单切换失败:', error);
                            }
                        });
                    }
                } catch (error) {
                    console.error('下拉菜单初始化失败:', error);
                }
            });
        }
    }, 1000); // 增加延迟时间，确保所有组件都已完全初始化
    

});

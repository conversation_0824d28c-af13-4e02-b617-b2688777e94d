/* 管理员模块样式 */

/* 用户信息栏样式 */
.user-info-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 0;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.user-info-bar .user-welcome {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-info-bar .user-welcome i {
    font-size: 18px;
}

.user-info-bar .badge {
    font-size: 11px;
}

.user-info-bar .btn {
    font-size: 12px;
    padding: 4px 8px;
    border-color: rgba(255,255,255,0.3);
    color: white;
}

.user-info-bar .btn:hover {
    background-color: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

/* 页面标题样式 */
.page-title {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.page-title h1 {
    color: #495057;
    font-weight: 600;
}

.page-title .text-muted {
    font-size: 1.1rem;
}

/* 卡片样式增强 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 用户状态徽章 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

.status-active {
    background-color: #d1edff;
    color: #0c63e4;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

/* 角色徽章 */
.role-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

.role-admin {
    background-color: #fff3cd;
    color: #856404;
}

.role-viewer {
    background-color: #d4edda;
    color: #155724;
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
}

/* 搜索和筛选区域 */
.search-filters {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.search-filters .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* 分页样式 */
.pagination-sm .page-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.pagination .page-link {
    color: #667eea;
    border-color: #dee2e6;
}

.pagination .page-link:hover {
    color: #5a6fd8;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    color: #495057;
    font-weight: 600;
}

.modal-body .form-label {
    font-weight: 600;
    color: #495057;
}

.modal-body .form-text {
    font-size: 0.8rem;
    color: #6c757d;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.empty-state h5 {
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #adb5bd;
    margin-bottom: 0;
}

/* 加载状态 */
.loading-state {
    text-align: center;
    padding: 3rem 1rem;
}

.loading-state .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Toast 通知样式 */
.toast {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.toast-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-info-bar {
        font-size: 12px;
    }
    
    .user-info-bar .user-actions {
        display: flex;
        gap: 0.25rem;
    }
    
    .user-info-bar .btn {
        font-size: 11px;
        padding: 2px 6px;
    }
    
    .page-title h1 {
        font-size: 1.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.125rem;
    }
    
    .search-filters .row > div {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .user-info-bar .user-welcome span:not(.badge) {
        display: none;
    }
    
    .user-info-bar .btn span {
        display: none;
    }
    
    .card-header .d-flex {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start !important;
    }
    
    .pagination-sm {
        font-size: 0.75rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 表格行悬停效果 */
.table tbody tr {
    transition: background-color 0.15s ease-in-out;
}

/* 按钮悬停效果 */
.btn {
    transition: all 0.15s ease-in-out;
}

/* 表单控件焦点效果 */
.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 自定义滚动条 */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.75rem;
}

/* 确认对话框样式 */
.modal-content .text-danger {
    color: #dc3545 !important;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #856404;
}

/* 批量操作栏 */
.bulk-actions {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    display: none;
}

.bulk-actions.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
    to {
        opacity: 1;
        max-height: 100px;
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }
}

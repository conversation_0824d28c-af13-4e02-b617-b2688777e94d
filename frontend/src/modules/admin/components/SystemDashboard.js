/**
 * 系统管理控制台主组件
 * 负责系统状态监控、数据统计和快速操作
 */

// 导入依赖
import NavigationComponent from '../../../shared/components/navigation.js';

class SystemDashboard {
    constructor() {
        this.refreshInterval = null;
        this.activityChart = null;
        this.initialized = false; // 添加初始化状态标记

        // 绑定方法上下文
        this.init = this.init.bind(this);
        this.loadDashboardData = this.loadDashboardData.bind(this);
        this.refreshDashboard = this.refreshDashboard.bind(this);
    }

    /**
     * 初始化系统控制台
     */
    async init() {
        // 防止重复初始化
        if (this.initialized) {
            return true;
        }

        try {
            console.log('🚀 系统控制台开始加载，进行鉴权检查...');

            // 导入鉴权工具
            let AuthUtils;
            try {
                const authUtilsModule = await import('../../../shared/utils/auth-utils.js');
                AuthUtils = authUtilsModule.default || authUtilsModule.AuthUtils || window.AuthUtils;
            } catch (error) {
                console.error('❌ 无法导入AuthUtils:', error);
                AuthUtils = window.AuthUtils; // 尝试从全局获取
            }

            if (!AuthUtils) {
                console.error('❌ AuthUtils未定义，重定向到登录页面');
                window.location.href = '/login.html';
                return false;
            }

            // 1. 检查用户登录状态（必须）
            const user = await AuthUtils.guardPage();
            if (!user) {
                console.log('❌ 用户未登录，已跳转到登录页面');
                return false;
            }

            // 2. 检查管理员权限
            if (user.role !== 'admin') {
                console.log('❌ 权限不足，重定向到主页');
                this.showError('权限不足，只有管理员可以访问此页面');
                setTimeout(() => {
                    window.location.href = '/frontend/src/shared/index.html';
                }, 2000);
                return false;
            }
            
            // 3. 初始化页面权限控制（显示用户信息栏）
            AuthUtils.initPagePermissions();
            
            console.log('✅ 用户已登录:', user.username, '角色:', user.role);
            
            // 4. 初始化导航栏
            NavigationComponent.init('navigationContainer');
            
            // 5. 初始化事件监听器
            this.initializeEventListeners();
            
            // 6. 加载控制台数据
            await this.loadDashboardData();
            
            // 7. 初始化图表
            this.initializeCharts();
            
            // 8. 设置自动刷新
            this.startAutoRefresh();

            // 标记为已初始化
            this.initialized = true;

            console.log('✅ 系统控制台初始化完成');
            return true;
            
        } catch (error) {
            console.error('❌ 系统控制台初始化失败:', error);
            this.showError('页面初始化失败: ' + error.message);
            return false;
        }
    }

    /**
     * 销毁组件，清理资源
     */
    destroy() {
        // 清理定时器
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }

        // 彻底销毁图表
        const ctx = document.getElementById('activityChart');
        if (ctx) {
            this.destroyExistingChart(ctx);
        }
        this.activityChart = null;

        // 重置初始化状态
        this.initialized = false;
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', this.refreshDashboard);
        }

        // 全局函数绑定
        window.showSystemConfig = this.showSystemConfig.bind(this);
        window.showActivityLogs = this.showActivityLogs.bind(this);
        window.showSessionManagement = this.showSessionManagement.bind(this);
        window.exportSystemData = this.exportSystemData.bind(this);
        window.clearOldLogs = this.clearOldLogs.bind(this);
    }

    /**
     * 加载控制台数据
     */
    async loadDashboardData() {
        try {
            // 并行加载各种数据
            const [
                systemStats,
                recentActivities,
                systemInfo
            ] = await Promise.all([
                this.loadSystemStats(),
                this.loadRecentActivities(),
                this.loadSystemInfo()
            ]);

            // 更新统计卡片
            this.updateStatsCards(systemStats);
            
            // 更新最近活动
            this.updateRecentActivities(recentActivities);
            
            // 更新系统信息
            this.updateSystemInfo(systemInfo);
            
            // 更新图表数据
            this.updateActivityChart();

        } catch (error) {
            console.error('加载控制台数据失败:', error);
            this.showError('加载数据失败: ' + error.message);
        }
    }

    /**
     * 加载系统统计数据
     */
    async loadSystemStats() {
        try {
            const response = await fetch('/api/auth/users', {
                method: 'GET',
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.success) {
                const users = data.data.users || [];
                const activeUsers = users.filter(user => user.is_active).length;
                
                return {
                    totalUsers: users.length,
                    activeUsers: activeUsers,
                    onlineSessions: 0, // 待实现
                    systemStatus: 'normal'
                };
            } else {
                throw new Error(data.error || '获取统计数据失败');
            }

        } catch (error) {
            console.error('加载系统统计失败:', error);
            return {
                totalUsers: 0,
                activeUsers: 0,
                onlineSessions: 0,
                systemStatus: 'error'
            };
        }
    }

    /**
     * 加载最近活动
     */
    async loadRecentActivities() {
        try {
            const response = await fetch('/api/auth/activity-logs?page=1&page_size=10', {
                method: 'GET',
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.success) {
                return data.data.logs || [];
            } else {
                throw new Error(data.error || '获取活动日志失败');
            }

        } catch (error) {
            console.error('加载最近活动失败:', error);
            return [];
        }
    }

    /**
     * 加载系统信息
     */
    async loadSystemInfo() {
        // 模拟系统信息，实际应该从后端API获取
        return {
            version: 'v1.0.0',
            uptime: this.formatUptime(Date.now() - (24 * 60 * 60 * 1000)), // 模拟24小时运行时间
            dbStatus: 'normal',
            lastBackup: new Date(Date.now() - (2 * 60 * 60 * 1000)).toLocaleString('zh-CN') // 模拟2小时前备份
        };
    }

    /**
     * 更新统计卡片
     */
    updateStatsCards(stats) {
        const elements = {
            totalUsers: document.getElementById('totalUsers'),
            activeUsers: document.getElementById('activeUsers'),
            onlineSessions: document.getElementById('onlineSessions'),
            systemStatus: document.getElementById('systemStatus'),
            userCount: document.getElementById('userCount'),
            sessionCount: document.getElementById('sessionCount')
        };

        if (elements.totalUsers) elements.totalUsers.textContent = stats.totalUsers;
        if (elements.activeUsers) elements.activeUsers.textContent = stats.activeUsers;
        if (elements.onlineSessions) elements.onlineSessions.textContent = stats.onlineSessions;
        if (elements.userCount) elements.userCount.textContent = `${stats.totalUsers} 个用户`;
        if (elements.sessionCount) elements.sessionCount.textContent = `${stats.onlineSessions} 个会话`;
        
        if (elements.systemStatus) {
            elements.systemStatus.textContent = stats.systemStatus === 'normal' ? '正常' : '异常';
            elements.systemStatus.className = stats.systemStatus === 'normal' ? 'text-success' : 'text-danger';
        }
    }

    /**
     * 更新最近活动列表
     */
    updateRecentActivities(activities) {
        const container = document.getElementById('recentActivities');
        const countElement = document.getElementById('recentLogsCount');
        
        if (!container) return;

        if (activities.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="bi bi-inbox"></i>
                    <p class="mb-0">暂无最近活动</p>
                </div>
            `;
            if (countElement) countElement.textContent = '0 条记录';
            return;
        }

        const html = activities.map(activity => `
            <div class="activity-item d-flex align-items-center">
                <div class="activity-icon bg-${this.getActivityColor(activity.action)} text-white me-3">
                    <i class="bi bi-${this.getActivityIcon(activity.action)}"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-medium">${this.getActivityDescription(activity)}</div>
                    <small class="text-muted">
                        ${activity.username} • ${this.formatTime(activity.created_at)}
                    </small>
                </div>
                <div class="text-end">
                    <span class="badge bg-${activity.success ? 'success' : 'danger'}">
                        ${activity.success ? '成功' : '失败'}
                    </span>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
        if (countElement) countElement.textContent = `${activities.length} 条记录`;
    }

    /**
     * 更新系统信息
     */
    updateSystemInfo(info) {
        const elements = {
            uptime: document.getElementById('uptime'),
            dbStatus: document.getElementById('dbStatus'),
            lastBackup: document.getElementById('lastBackup')
        };

        if (elements.uptime) elements.uptime.textContent = info.uptime;
        if (elements.lastBackup) elements.lastBackup.textContent = info.lastBackup;
        
        if (elements.dbStatus) {
            elements.dbStatus.textContent = info.dbStatus === 'normal' ? '正常' : '异常';
            elements.dbStatus.className = `badge bg-${info.dbStatus === 'normal' ? 'success' : 'danger'}`;
        }
    }

    /**
     * 彻底销毁已存在的图表
     */
    destroyExistingChart(ctx) {
        // 方法1: 销毁实例图表
        if (this.activityChart) {
            this.activityChart.destroy();
            this.activityChart = null;
        }

        // 方法2: 从Chart.js全局注册表中查找并销毁
        if (typeof Chart !== 'undefined' && Chart.getChart) {
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }
        }

        // 方法3: 清理canvas元素
        if (ctx && ctx.getContext) {
            const context = ctx.getContext('2d');
            if (context) {
                context.clearRect(0, 0, ctx.width, ctx.height);
            }
        }

        // 方法4: 重新创建canvas元素（最彻底的方法）
        if (ctx && ctx.parentNode) {
            const newCanvas = document.createElement('canvas');
            newCanvas.id = ctx.id;
            newCanvas.className = ctx.className;
            newCanvas.style.cssText = ctx.style.cssText;
            ctx.parentNode.replaceChild(newCanvas, ctx);
        }
    }

    /**
     * 初始化图表
     */
    initializeCharts() {
        let ctx = document.getElementById('activityChart');
        if (!ctx) return;

        // 彻底清理已存在的图表
        this.destroyExistingChart(ctx);

        // 重新获取canvas元素（可能已被替换）
        ctx = document.getElementById('activityChart');
        if (!ctx) return;

        this.activityChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '用户活动',
                    data: [],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    /**
     * 更新活动图表
     */
    updateActivityChart() {
        if (!this.activityChart) return;

        // 生成模拟数据（实际应该从API获取）
        const labels = [];
        const data = [];
        
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
            data.push(Math.floor(Math.random() * 20) + 5);
        }

        this.activityChart.data.labels = labels;
        this.activityChart.data.datasets[0].data = data;
        this.activityChart.update();
    }

    /**
     * 刷新控制台数据
     */
    async refreshDashboard() {
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spinner-border spinner-border-sm"></i> 刷新中...';
        }

        try {
            await this.loadDashboardData();
            this.showSuccess('数据刷新成功');
        } catch (error) {
            this.showError('数据刷新失败: ' + error.message);
        } finally {
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新数据';
            }
        }
    }

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        // 每5分钟自动刷新一次
        this.refreshInterval = setInterval(() => {
            this.loadDashboardData();
        }, 5 * 60 * 1000);
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * 显示系统配置
     */
    showSystemConfig() {
        alert('系统配置功能开发中...\n\n将包括：\n- 密码策略设置\n- 会话超时配置\n- 安全选项\n- 系统参数');
    }

    /**
     * 显示活动日志
     */
    showActivityLogs() {
        alert('操作日志功能开发中...\n\n将包括：\n- 用户活动记录\n- 系统事件日志\n- 日志筛选和搜索\n- 日志导出');
    }

    /**
     * 显示会话管理
     */
    showSessionManagement() {
        alert('会话管理功能开发中...\n\n将包括：\n- 在线用户列表\n- 会话详情查看\n- 强制下线功能\n- 会话统计');
    }

    /**
     * 导出系统数据
     */
    exportSystemData() {
        alert('数据导出功能开发中...\n\n将支持导出：\n- 用户数据\n- 操作日志\n- 系统配置\n- 统计报告');
    }

    /**
     * 清理旧日志
     */
    clearOldLogs() {
        if (confirm('确定要清理30天前的旧日志吗？\n此操作不可撤销。')) {
            alert('日志清理功能开发中...');
        }
    }

    // 工具方法
    getActivityColor(action) {
        const colorMap = {
            'login_success': 'success',
            'login_failed': 'danger',
            'create_user': 'primary',
            'update_user': 'info',
            'delete_user': 'warning',
            'reset_password': 'secondary'
        };
        return colorMap[action] || 'secondary';
    }

    getActivityIcon(action) {
        const iconMap = {
            'login_success': 'box-arrow-in-right',
            'login_failed': 'x-circle',
            'create_user': 'person-plus',
            'update_user': 'person-gear',
            'delete_user': 'person-dash',
            'reset_password': 'key'
        };
        return iconMap[action] || 'activity';
    }

    getActivityDescription(activity) {
        const descMap = {
            'login_success': '用户登录',
            'login_failed': '登录失败',
            'create_user': '创建用户',
            'update_user': '更新用户',
            'delete_user': '删除用户',
            'reset_password': '重置密码'
        };
        return descMap[activity.action] || activity.resource || '系统操作';
    }

    formatTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
        return date.toLocaleDateString('zh-CN');
    }

    formatUptime(ms) {
        const days = Math.floor(ms / (24 * 60 * 60 * 1000));
        const hours = Math.floor((ms % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
        return `${days}天 ${hours}小时`;
    }

    showSuccess(message) {
        this.showToast('成功', message, 'success');
    }

    showError(message) {
        this.showToast('错误', message, 'danger');
    }

    showToast(title, message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;

        const toastId = 'toast_' + Date.now();
        const toastHTML = `
            <div class="toast" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} text-${type} me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="关闭"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
        toast.show();

        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
}

// 创建全局实例
const systemDashboard = new SystemDashboard();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    systemDashboard.init();
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    systemDashboard.stopAutoRefresh();
});

// 导出到全局作用域
window.systemDashboard = systemDashboard;

export default SystemDashboard;

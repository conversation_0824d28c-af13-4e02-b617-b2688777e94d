/**
 * 用户管理主组件
 * 负责用户列表显示、创建、编辑、删除等功能
 */

// 导入依赖
import NavigationComponent from '../../../shared/components/navigation.js';

class UserManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalUsers = 0;
        this.users = [];
        this.filteredUsers = [];
        this.selectedUsers = new Set();
        this.isInitialized = false;

        // 绑定方法上下文
        this.init = this.init.bind(this);
        this.loadUsers = this.loadUsers.bind(this);
        this.renderUsers = this.renderUsers.bind(this);
        this.handleSearch = this.handleSearch.bind(this);
        this.handleFilter = this.handleFilter.bind(this);
        this.handleCreateUser = this.handleCreateUser.bind(this);
        this.handleEditUser = this.handleEditUser.bind(this);
        this.deleteUser = this.deleteUser.bind(this);
        this.confirmDelete = this.confirmDelete.bind(this);
    }

    /**
     * 初始化用户管理组件
     */
    async init() {
        try {
            // 注意：鉴权检查已在主页面文件中完成，这里不需要重复检查

            // 1. 初始化事件监听器
            this.initializeEventListeners();

            // 2. 加载用户数据
            await this.loadUsers();

            return true;

        } catch (error) {
            console.error('❌ 用户管理组件初始化失败:', error);
            this.showError('组件初始化失败: ' + error.message);
            return false;
        }
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(this.handleSearch, 300));
        }

        // 筛选功能
        const roleFilter = document.getElementById('roleFilter');
        const statusFilter = document.getElementById('statusFilter');
        if (roleFilter) roleFilter.addEventListener('change', this.handleFilter);
        if (statusFilter) statusFilter.addEventListener('change', this.handleFilter);

        // 重置筛选
        const clearFilters = document.getElementById('clearFilters');
        if (clearFilters) {
            clearFilters.addEventListener('click', () => {
                searchInput.value = '';
                roleFilter.value = '';
                statusFilter.value = '';
                this.handleFilter();
            });
        }

        // 刷新用户列表
        const refreshUsers = document.getElementById('refreshUsers');
        if (refreshUsers) {
            refreshUsers.addEventListener('click', () => this.loadUsers());
        }

        // 导出用户数据
        const exportUsers = document.getElementById('exportUsers');
        if (exportUsers) {
            exportUsers.addEventListener('click', () => this.exportUsers());
        }

        // 全选功能
        const selectAll = document.getElementById('selectAll');
        if (selectAll) {
            selectAll.addEventListener('change', (e) => {
                const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
                checkboxes.forEach(cb => {
                    cb.checked = e.target.checked;
                    if (e.target.checked) {
                        this.selectedUsers.add(cb.dataset.userId);
                    } else {
                        this.selectedUsers.delete(cb.dataset.userId);
                    }
                });
                this.updateBulkActions();
            });
        }

        // 只在第一次初始化时添加事件监听器
        if (!this.isInitialized) {
            // 创建用户表单
            const createUserForm = document.getElementById('createUserForm');
            if (createUserForm) {
                createUserForm.addEventListener('submit', this.handleCreateUser);
            }

            // 编辑用户表单
            const editUserForm = document.getElementById('editUserForm');
            if (editUserForm) {
                editUserForm.addEventListener('submit', this.handleEditUser);
            }

            // 确认删除按钮
            const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
            if (confirmDeleteBtn) {
                confirmDeleteBtn.addEventListener('click', this.confirmDelete);
            }

            this.isInitialized = true;
        }
    }

    /**
     * 加载用户数据
     */
    async loadUsers() {
        try {
            this.showLoading(true);

            const response = await fetch('/api/auth/users', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.users = data.data.users || [];
                this.totalUsers = data.data.total || 0;
                this.filteredUsers = [...this.users];

                this.renderUsers();
                this.updateUserCount();
            } else {
                throw new Error(data.error || '获取用户列表失败');
            }

        } catch (error) {
            console.error('❌ 加载用户数据失败:', error);
            this.showError('加载用户数据失败: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 渲染用户列表
     */
    renderUsers() {
        const tbody = document.getElementById('usersTableBody');
        const emptyState = document.getElementById('emptyState');

        if (!tbody) {
            console.error('❌ 找不到usersTableBody元素');
            return;
        }

        // 计算分页
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageUsers = this.filteredUsers.slice(startIndex, endIndex);

        if (pageUsers.length === 0) {
            tbody.innerHTML = '';
            if (emptyState) emptyState.style.display = 'block';
            return;
        }

        if (emptyState) emptyState.style.display = 'none';

        tbody.innerHTML = pageUsers.map(user => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input" 
                           data-user-id="${user.id}" 
                           onchange="userManagement.handleUserSelect(this)">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-person-circle text-muted me-2"></i>
                        <strong>${this.escapeHtml(user.username)}</strong>
                    </div>
                </td>
                <td>${user.email ? this.escapeHtml(user.email) : '<span class="text-muted">未设置</span>'}</td>
                <td>
                    <span class="badge role-${user.role}">
                        ${user.role === 'admin' ? '管理员' : '查看者'}
                    </span>
                </td>
                <td>
                    <span class="badge status-${user.is_active ? 'active' : 'inactive'}">
                        ${user.is_active ? '启用' : '禁用'}
                    </span>
                </td>
                <td>
                    <small class="text-muted">
                        ${user.created_at ? this.formatDate(user.created_at) : '-'}
                    </small>
                </td>
                <td>
                    <small class="text-muted">
                        ${user.last_login ? this.formatDate(user.last_login) : '从未登录'}
                    </small>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-outline-primary btn-sm" 
                                onclick="userManagement.editUser(${user.id})"
                                title="编辑用户">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-warning btn-sm" 
                                onclick="userManagement.resetPassword(${user.id})"
                                title="重置密码">
                            <i class="bi bi-key"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm" 
                                onclick="userManagement.deleteUser(${user.id}, '${this.escapeHtml(user.username)}')"
                                title="删除用户"
                                ${user.id === AuthUtils.getCurrentUser()?.id ? 'disabled' : ''}>
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        this.renderPagination();
    }

    /**
     * 渲染分页组件
     */
    renderPagination() {
        const pagination = document.getElementById('pagination');
        const pageStart = document.getElementById('pageStart');
        const pageEnd = document.getElementById('pageEnd');
        const totalUsers = document.getElementById('totalUsers');
        
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredUsers.length / this.pageSize);
        const startIndex = (this.currentPage - 1) * this.pageSize + 1;
        const endIndex = Math.min(this.currentPage * this.pageSize, this.filteredUsers.length);

        // 更新统计信息
        if (pageStart) pageStart.textContent = startIndex;
        if (pageEnd) pageEnd.textContent = endIndex;
        if (totalUsers) totalUsers.textContent = this.filteredUsers.length;

        // 生成分页按钮
        let paginationHTML = '';
        
        // 上一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="userManagement.goToPage(${this.currentPage - 1})">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="userManagement.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        // 下一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="userManagement.goToPage(${this.currentPage + 1})">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    /**
     * 跳转到指定页面
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredUsers.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderUsers();
        }
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
        this.applyFilters();
    }

    /**
     * 处理筛选
     */
    handleFilter() {
        this.applyFilters();
    }

    /**
     * 应用筛选条件
     */
    applyFilters() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
        const roleFilter = document.getElementById('roleFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        this.filteredUsers = this.users.filter(user => {
            // 搜索条件
            const matchesSearch = !searchTerm || 
                user.username.toLowerCase().includes(searchTerm) ||
                (user.email && user.email.toLowerCase().includes(searchTerm));

            // 角色筛选
            const matchesRole = !roleFilter || user.role === roleFilter;

            // 状态筛选
            const matchesStatus = !statusFilter || 
                (statusFilter === 'active' && user.is_active) ||
                (statusFilter === 'inactive' && !user.is_active);

            return matchesSearch && matchesRole && matchesStatus;
        });

        this.currentPage = 1; // 重置到第一页
        this.renderUsers();
        this.updateUserCount();
    }

    /**
     * 更新用户数量显示
     */
    updateUserCount() {
        const userCount = document.getElementById('userCount');
        if (userCount) {
            userCount.textContent = `共 ${this.filteredUsers.length} 个用户`;
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        const loadingState = document.getElementById('loadingState');
        const usersTable = document.getElementById('usersTable');
        
        if (loadingState) {
            loadingState.style.display = show ? 'block' : 'none';
        }
        if (usersTable) {
            usersTable.style.display = show ? 'none' : 'table';
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        this.showToast('错误', message, 'danger');
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.showToast('成功', message, 'success');
    }

    /**
     * 显示Toast通知
     */
    showToast(title, message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;

        const toastId = 'toast_' + Date.now();
        const toastHTML = `
            <div class="toast" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} text-${type} me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="关闭"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
        toast.show();

        // 自动清理
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }

    /**
     * 格式化日期
     */
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * 处理创建用户
     */
    async handleCreateUser(event) {
        event.preventDefault();

        // 防止重复提交
        const submitBtn = event.target.querySelector('button[type="submit"]');
        if (submitBtn.disabled) {
            return;
        }

        // 禁用提交按钮
        submitBtn.disabled = true;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 创建中...';

        const formData = new FormData(event.target);
        const userData = {
            username: formData.get('username'),
            password: formData.get('password'),
            email: formData.get('email'),
            role: formData.get('role')
        };

        // 表单验证
        if (!userData.username || !userData.password || !userData.role) {
            this.showError('请填写所有必填字段');
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
            return;
        }

        try {
            // 获取CSRF token
            const csrfResponse = await fetch('/api/csrf-token', {
                credentials: 'include'
            });
            const csrfData = await csrfResponse.json();

            if (!csrfData.csrf_token) {
                this.showError('无法获取CSRF token，请刷新页面后重试');
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
                return;
            }

            const response = await fetch('/api/auth/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfData.csrf_token
                },
                credentials: 'include',
                body: JSON.stringify(userData)
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('用户创建成功');

                // 如果有2FA设置数据，显示二维码
                if (data['2fa_setup'] && data['2fa_setup'].qr_code) {
                    this.show2FASetupModal(data.user_id, data['2fa_setup']);
                }

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('createUserModal'));
                modal.hide();

                // 重置表单
                event.target.reset();

                // 重新加载用户列表
                await this.loadUsers();
            } else {
                this.showError(data.error || '创建用户失败');
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }

        } catch (error) {
            console.error('创建用户失败:', error);
            this.showError('创建用户失败: ' + error.message);
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    }

    /**
     * 编辑用户
     */
    editUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showError('用户不存在');
            return;
        }

        // 填充编辑表单
        document.getElementById('editUserId').value = user.id;
        document.getElementById('editUsername').value = user.username;
        document.getElementById('editEmail').value = user.email || '';
        document.getElementById('editRole').value = user.role;
        document.getElementById('editStatus').value = user.is_active.toString();

        // 显示编辑模态框
        const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
        modal.show();
    }

    /**
     * 处理编辑用户
     */
    async handleEditUser(event) {
        event.preventDefault();

        // 防止重复提交
        const submitBtn = event.target.querySelector('button[type="submit"]');
        if (submitBtn.disabled) {
            return;
        }

        // 禁用提交按钮
        submitBtn.disabled = true;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 更新中...';

        const formData = new FormData(event.target);
        const userId = formData.get('userId');
        const userData = {
            email: formData.get('email'),
            role: formData.get('role'),
            is_active: formData.get('status') === 'true'
        };

        try {
            // 获取CSRF token
            const csrfResponse = await fetch('/api/csrf-token', {
                credentials: 'include'
            });
            const csrfData = await csrfResponse.json();

            if (!csrfData.csrf_token) {
                this.showError('无法获取CSRF token，请刷新页面后重试');
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
                return;
            }

            const response = await fetch(`/api/auth/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfData.csrf_token
                },
                credentials: 'include',
                body: JSON.stringify(userData)
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('用户信息更新成功');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
                modal.hide();

                // 重新加载用户列表
                await this.loadUsers();
            } else {
                this.showError(data.error || '更新用户信息失败');
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }

        } catch (error) {
            console.error('更新用户信息失败:', error);
            this.showError('更新用户信息失败: ' + error.message);
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    }

    /**
     * 删除用户
     */
    deleteUser(userId, username) {
        // 防止删除自己
        const currentUser = AuthUtils.getCurrentUser();
        if (currentUser && currentUser.id === userId) {
            this.showError('不能删除自己的账户');
            return;
        }

        // 设置删除确认信息
        document.getElementById('deleteUserName').textContent = username;
        document.getElementById('confirmDeleteBtn').dataset.userId = userId;

        // 显示确认删除模态框
        const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        modal.show();
    }

    /**
     * 确认删除用户
     */
    async confirmDelete() {
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        const userId = confirmBtn.dataset.userId;
        if (!userId) return;

        // 防止重复提交
        if (confirmBtn.disabled) {
            return;
        }

        // 禁用确认按钮
        confirmBtn.disabled = true;
        const originalText = confirmBtn.innerHTML;
        confirmBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 删除中...';

        try {
            // 获取CSRF token
            const csrfResponse = await fetch('/api/csrf-token', {
                credentials: 'include'
            });
            const csrfData = await csrfResponse.json();

            if (!csrfData.csrf_token) {
                this.showError('无法获取CSRF token，请刷新页面后重试');
                // 恢复按钮状态
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = originalText;
                return;
            }

            const response = await fetch(`/api/auth/users/${userId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': csrfData.csrf_token
                },
                credentials: 'include'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('用户删除成功');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
                modal.hide();

                // 重新加载用户列表
                await this.loadUsers();

                // 重置按钮状态（模态框关闭后会自动重置，但为了保险起见）
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = originalText;
            } else {
                this.showError(data.error || '删除用户失败');
                // 恢复按钮状态
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = originalText;
            }

        } catch (error) {
            console.error('删除用户失败:', error);
            this.showError('删除用户失败: ' + error.message);
            // 恢复按钮状态
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = originalText;
        }
    }

    /**
     * 重置用户密码
     */
    async resetPassword(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showError('用户不存在');
            return;
        }

        if (!confirm(`确定要重置用户 "${user.username}" 的密码吗？\n新密码将通过邮件发送给用户。`)) {
            return;
        }

        try {
            // 获取CSRF token
            const csrfResponse = await fetch('/api/csrf-token', {
                credentials: 'include'
            });
            const csrfData = await csrfResponse.json();

            if (!csrfData.csrf_token) {
                this.showError('无法获取CSRF token，请刷新页面后重试');
                return;
            }

            const response = await fetch(`/api/auth/users/${userId}/reset-password`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfData.csrf_token
                },
                credentials: 'include'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess(`密码重置成功，新密码：${data.new_password}`);
            } else {
                this.showError(data.error || '重置密码失败');
            }

        } catch (error) {
            console.error('重置密码失败:', error);
            this.showError('重置密码失败: ' + error.message);
        }
    }

    /**
     * 处理用户选择
     */
    handleUserSelect(checkbox) {
        const userId = checkbox.dataset.userId;
        if (checkbox.checked) {
            this.selectedUsers.add(userId);
        } else {
            this.selectedUsers.delete(userId);
        }
        this.updateBulkActions();
    }

    /**
     * 更新批量操作栏
     */
    updateBulkActions() {
        const bulkActions = document.querySelector('.bulk-actions');
        const selectAll = document.getElementById('selectAll');

        if (this.selectedUsers.size > 0) {
            if (bulkActions) bulkActions.classList.add('show');
        } else {
            if (bulkActions) bulkActions.classList.remove('show');
        }

        // 更新全选状态
        if (selectAll) {
            const totalCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]').length;
            selectAll.checked = this.selectedUsers.size === totalCheckboxes && totalCheckboxes > 0;
            selectAll.indeterminate = this.selectedUsers.size > 0 && this.selectedUsers.size < totalCheckboxes;
        }
    }

    /**
     * 导出用户数据
     */
    exportUsers() {
        const csvContent = this.generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `users_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    /**
     * 生成CSV内容
     */
    generateCSV() {
        const headers = ['用户名', '邮箱', '角色', '状态', '创建时间', '最后登录'];
        const rows = this.filteredUsers.map(user => [
            user.username,
            user.email || '',
            user.role === 'admin' ? '管理员' : '查看者',
            user.is_active ? '启用' : '禁用',
            user.created_at ? this.formatDate(user.created_at) : '',
            user.last_login ? this.formatDate(user.last_login) : '从未登录'
        ]);

        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        return '\uFEFF' + csvContent; // 添加BOM以支持中文
    }

    /**
     * 显示2FA设置模态框
     */
    show2FASetupModal(userId, setupData) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="twoFASetupModal" tabindex="-1" aria-labelledby="twoFASetupModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="twoFASetupModalLabel">
                                <i class="bi bi-shield-lock"></i> 双因子认证设置
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                <strong>重要提示：</strong>请将以下信息提供给用户，用于首次登录时设置双因子认证。
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6>扫描二维码</h6>
                                    <div class="text-center">
                                        <img src="${setupData.qr_code}" alt="2FA二维码" class="img-fluid border rounded" style="max-width: 200px;">
                                    </div>
                                    <p class="text-muted small mt-2">用户可使用Google Authenticator、Microsoft Authenticator等应用扫描此二维码</p>
                                </div>

                                <div class="col-md-6">
                                    <h6>备用恢复码</h6>
                                    <div class="alert alert-warning">
                                        <small><strong>请妥善保存这些恢复码：</strong></small>
                                        <div class="mt-2" style="font-family: monospace; font-size: 12px;">
                                            ${setupData.backup_codes.map(code => `<span class="badge bg-secondary me-1 mb-1">${code}</span>`).join('')}
                                        </div>
                                    </div>
                                    <p class="text-muted small">当用户无法使用认证应用时，可以使用这些恢复码登录</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" onclick="userManagement.print2FAInfo()">
                                <i class="bi bi-printer"></i> 打印
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('twoFASetupModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('twoFASetupModal'));
        modal.show();

        // 保存设置数据以供打印
        this.currentSetupData = setupData;
    }

    /**
     * 打印2FA信息
     */
    print2FAInfo() {
        if (!this.currentSetupData) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>双因子认证设置信息</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .qr-section, .codes-section { margin: 20px 0; }
                        .backup-code {
                            display: inline-block;
                            margin: 5px;
                            padding: 5px 10px;
                            border: 1px solid #ccc;
                            font-family: monospace;
                        }
                        .warning {
                            background: #fff3cd;
                            border: 1px solid #ffeaa7;
                            padding: 10px;
                            margin: 10px 0;
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>双因子认证设置信息</h2>
                        <p>风险分析系统</p>
                    </div>

                    <div class="qr-section">
                        <h3>1. 扫描二维码</h3>
                        <img src="${this.currentSetupData.qr_code}" alt="2FA二维码" style="max-width: 200px;">
                        <p>使用Google Authenticator、Microsoft Authenticator等应用扫描此二维码</p>
                    </div>

                    <div class="codes-section">
                        <h3>2. 备用恢复码</h3>
                        <div class="warning">
                            <strong>重要：请妥善保存这些恢复码，当无法使用认证应用时可以使用</strong>
                        </div>
                        <div>
                            ${this.currentSetupData.backup_codes.map(code => `<span class="backup-code">${code}</span>`).join('')}
                        </div>
                    </div>

                    <div style="margin-top: 30px; font-size: 12px; color: #666;">
                        <p>打印时间：${new Date().toLocaleString()}</p>
                    </div>
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// 创建全局实例
const userManagement = new UserManagement();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    userManagement.init();
});

// 导出到全局作用域，供HTML中的onclick使用
window.userManagement = userManagement;

export default UserManagement;

/**
 * 配置管理组件
 * 提供YAML配置文件的可视化编辑界面
 */

class ConfigManagement {
    constructor() {
        this.currentConfig = null;
        this.currentConfigType = null;
        this.isEditing = false;
        this.originalConfig = null;
        
        this.init();
    }
    
    async init() {
        console.log('🔧 配置管理组件初始化...');
        await this.loadConfigList();
        this.bindEvents();
    }
    
    /**
     * 加载配置文件列表
     */
    async loadConfigList() {
        try {
            const response = await fetch('/api/config/configs', {
                method: 'GET',
                credentials: 'include'
            });

            // 特殊处理401未授权
            if (response.status === 401) {
                this.showLoginRequired();
                return;
            }

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.status === 'success') {
                this.renderConfigList(result.data);
            } else {
                throw new Error(result.error || '获取配置列表失败');
            }

        } catch (error) {
            console.error('加载配置列表失败:', error);
            this.showError('加载配置列表失败: ' + error.message);
            this.showErrorInContainer();
        }
    }
    
    /**
     * 渲染配置文件列表
     */
    renderConfigList(configs) {
        const container = document.getElementById('configListContainer');
        if (!container) return;
        
        const html = `
            <div class="row">
                ${configs.map(config => `
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 config-card position-relative" 
                             onclick="configManagement.editConfig('${config.config_type}')">
                            <span class="config-status-badge">
                                ${config.exists ? 
                                    '<span class="badge bg-success">正常</span>' : 
                                    '<span class="badge bg-danger">缺失</span>'
                                }
                            </span>
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-gear-fill text-primary"></i>
                                    ${config.description}
                                </h5>
                                <p class="card-text">
                                    <small class="text-muted">
                                        <strong>类型:</strong> ${config.config_type}<br>
                                        <strong>路径:</strong> <code class="small">${config.file_path.split('/').pop()}</code><br>
                                        ${config.last_modified ? 
                                            `<strong>修改时间:</strong> ${new Date(config.last_modified).toLocaleString()}` : 
                                            '<strong>状态:</strong> 文件不存在'
                                        }
                                    </small>
                                </p>
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-primary btn-sm" 
                                        onclick="event.stopPropagation(); configManagement.editConfig('${config.config_type}')"
                                        ${!config.exists ? 'disabled' : ''}>
                                    <i class="bi bi-pencil"></i> 编辑配置
                                </button>
                                <button class="btn btn-outline-secondary btn-sm ms-2" 
                                        onclick="event.stopPropagation(); configManagement.viewBackups('${config.config_type}')">
                                    <i class="bi bi-clock-history"></i> 备份
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
        
        container.innerHTML = html;
    }
    
    /**
     * 编辑配置文件
     */
    async editConfig(configType) {
        try {
            this.currentConfigType = configType;
            
            // 显示加载状态
            this.showLoading('正在加载配置文件...');
            
            // 加载配置数据
            const response = await fetch(`/api/config/configs/${configType}`, {
                method: 'GET',
                credentials: 'include'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            if (result.status === 'success') {
                this.currentConfig = result.data;
                this.originalConfig = JSON.parse(JSON.stringify(result.data.data));
                this.showConfigEditor();
            } else {
                throw new Error(result.error || '加载配置失败');
            }
            
        } catch (error) {
            console.error('加载配置失败:', error);
            this.showError('加载配置失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 显示配置编辑器
     */
    showConfigEditor() {
        // 设置模态框标题
        document.getElementById('configEditorTitle').textContent = 
            `编辑配置: ${this.currentConfig.description}`;
        
        // 设置配置内容到编辑器（带中文注释）
        const editor = document.getElementById('configEditor');
        editor.value = this.formatYAMLWithComments(this.currentConfig.data);
        
        // 更新配置信息
        this.updateConfigInfo();
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('configEditorModal'));
        modal.show();
        
        this.isEditing = true;
    }
    
    /**
     * 更新配置信息显示
     */
    updateConfigInfo() {
        const infoContainer = document.getElementById('configInfo');
        if (!infoContainer || !this.currentConfig) return;
        
        const info = `
            <div class="mb-2">
                <strong>配置类型:</strong> ${this.currentConfig.config_type}
            </div>
            <div class="mb-2">
                <strong>描述:</strong> ${this.currentConfig.description}
            </div>
            <div class="mb-2">
                <strong>文件路径:</strong><br>
                <code class="small">${this.currentConfig.file_path}</code>
            </div>
            <div class="mb-2">
                <strong>最后修改:</strong><br>
                ${new Date(this.currentConfig.last_modified).toLocaleString()}
            </div>
        `;
        
        infoContainer.innerHTML = info;
    }
    
    /**
     * 预览配置
     */
    previewConfig() {
        try {
            const editor = document.getElementById('configEditor');
            const configText = editor.value;
            
            // 解析YAML
            const configData = jsyaml.load(configText);
            
            // 显示预览
            const previewContainer = document.getElementById('configPreview');
            previewContainer.innerHTML = `<pre class="small">${JSON.stringify(configData, null, 2)}</pre>`;
            
            this.showSuccess('配置预览已更新');
            
        } catch (error) {
            console.error('配置预览失败:', error);
            this.showError('配置预览失败: ' + error.message);
            
            // 显示错误信息
            const previewContainer = document.getElementById('configPreview');
            previewContainer.innerHTML = `
                <div class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    YAML格式错误:<br>
                    <small>${error.message}</small>
                </div>
            `;
        }
    }
    
    /**
     * 验证配置格式
     */
    async validateConfig() {
        try {
            const editor = document.getElementById('configEditor');
            const configText = editor.value;
            
            // 先进行本地YAML解析验证
            let configData;
            try {
                configData = jsyaml.load(configText);
            } catch (yamlError) {
                throw new Error('YAML格式错误: ' + yamlError.message);
            }
            
            // 发送到后端验证
            const response = await fetch(`/api/config/configs/${this.currentConfigType}/validate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    config_data: configData
                })
            });
            
            const result = await response.json();
            if (result.status === 'success') {
                this.showSuccess('✅ 配置格式验证通过！');
            } else {
                throw new Error(result.error || '配置验证失败');
            }
            
        } catch (error) {
            console.error('配置验证失败:', error);
            this.showError('配置验证失败: ' + error.message);
        }
    }
    
    /**
     * 保存配置
     */
    async saveConfig() {
        try {
            const editor = document.getElementById('configEditor');
            const configText = editor.value;
            
            // 清理中文注释后解析YAML
            let configData;
            try {
                const cleanedYaml = this._removeChineseComments(configText);
                configData = jsyaml.load(cleanedYaml);
            } catch (yamlError) {
                throw new Error('YAML格式错误: ' + yamlError.message);
            }
            
            // 显示保存状态
            this.showLoading('正在保存配置...');
            
            // 发送到后端保存
            const response = await fetch(`/api/config/configs/${this.currentConfigType}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    config_data: configData,
                    create_backup: true
                })
            });
            
            const result = await response.json();
            if (result.status === 'success') {
                this.showSuccess('✅ 配置保存成功！下次分析时将使用新配置。');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('configEditorModal'));
                modal.hide();
                
                // 重新加载配置列表
                await this.loadConfigList();
                
            } else {
                throw new Error(result.error || '配置保存失败');
            }
            
        } catch (error) {
            console.error('配置保存失败:', error);
            this.showError('配置保存失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 查看备份文件
     */
    async viewBackups(configType) {
        try {
            const response = await fetch(`/api/config/configs/${configType}/backups`, {
                method: 'GET',
                credentials: 'include'
            });
            
            const result = await response.json();
            if (result.status === 'success') {
                this.showBackupList(configType, result.data);
            } else {
                throw new Error(result.error || '获取备份列表失败');
            }
            
        } catch (error) {
            console.error('获取备份列表失败:', error);
            this.showError('获取备份列表失败: ' + error.message);
        }
    }
    
    /**
     * 显示备份列表
     */
    showBackupList(configType, backups) {
        const title = document.getElementById('backupListTitle');
        const content = document.getElementById('backupListContent');
        
        title.textContent = `配置备份 - ${configType}`;
        
        if (backups.length === 0) {
            content.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-inbox display-4 text-muted"></i>
                    <p class="text-muted mt-2">暂无备份文件</p>
                </div>
            `;
        } else {
            content.innerHTML = `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th><i class="bi bi-file-earmark"></i> 文件名</th>
                                <th><i class="bi bi-calendar"></i> 创建时间</th>
                                <th><i class="bi bi-hdd"></i> 大小</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${backups.map(backup => `
                                <tr>
                                    <td>
                                        <code class="small">${backup.filename}</code>
                                    </td>
                                    <td>${new Date(backup.created_at).toLocaleString()}</td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            ${this.formatFileSize(backup.size)}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }
        
        const modal = new bootstrap.Modal(document.getElementById('backupListModal'));
        modal.show();
    }
    
    /**
     * 格式化YAML
     */
    formatYAML(data) {
        return jsyaml.dump(data, {
            indent: 2,
            lineWidth: 120,
            noRefs: true
        });
    }

    /**
     * 生成带中文注释的YAML内容
     */
    formatYAMLWithComments(data) {
        // 先生成基础YAML
        let yamlContent = this.formatYAML(this._removeDisplayFields(data));

        // 添加中文注释
        yamlContent = this._addChineseComments(yamlContent, data);

        return yamlContent;
    }

    /**
     * 移除显示字段（_display结尾的字段）
     */
    _removeDisplayFields(obj) {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }

        if (Array.isArray(obj)) {
            return obj.map(item => this._removeDisplayFields(item));
        }

        const cleaned = {};
        for (const [key, value] of Object.entries(obj)) {
            if (!key.endsWith('_display')) {
                cleaned[key] = this._removeDisplayFields(value);
            }
        }
        return cleaned;
    }

    /**
     * 为YAML内容添加中文注释
     */
    _addChineseComments(yamlContent, originalData) {
        const lines = yamlContent.split('\n');
        const commentedLines = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            commentedLines.push(line);

            // 检查是否是键值对行
            const match = line.match(/^(\s*)([^:\s]+):/);
            if (match) {
                const indent = match[1];
                const key = match[2];

                // 查找对应的中文显示名
                const displayKey = `${key}_display`;
                const chineseName = this._findDisplayValue(originalData, displayKey);

                if (chineseName && chineseName !== key) {
                    // 在下一行添加中文注释
                    commentedLines.push(`${indent}# ${chineseName}`);
                }
            }
        }

        return commentedLines.join('\n');
    }

    /**
     * 在嵌套对象中查找显示值
     */
    _findDisplayValue(obj, displayKey) {
        if (typeof obj !== 'object' || obj === null) {
            return null;
        }

        if (obj.hasOwnProperty(displayKey)) {
            return obj[displayKey];
        }

        // 递归查找
        for (const value of Object.values(obj)) {
            if (typeof value === 'object' && value !== null) {
                const found = this._findDisplayValue(value, displayKey);
                if (found) return found;
            }
        }

        return null;
    }

    /**
     * 移除YAML中的中文注释
     */
    _removeChineseComments(yamlContent) {
        const lines = yamlContent.split('\n');
        const cleanedLines = [];

        for (const line of lines) {
            // 跳过纯中文注释行（以#开头且包含中文字符）
            if (line.trim().startsWith('#') && /[\u4e00-\u9fff]/.test(line)) {
                continue;
            }
            cleanedLines.push(line);
        }

        return cleanedLines.join('\n');
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshConfigList');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadConfigList();
            });
        }
        
        // 配置编辑器实时预览
        const editor = document.getElementById('configEditor');
        if (editor) {
            editor.addEventListener('input', () => {
                // 防抖处理
                clearTimeout(this.previewTimeout);
                this.previewTimeout = setTimeout(() => {
                    this.previewConfig();
                }, 1000);
            });
        }
    }
    
    /**
     * 显示加载状态
     */
    showLoading(message = '加载中...') {
        // 可以实现一个全局的加载指示器
        console.log('Loading:', message);
    }
    
    /**
     * 隐藏加载状态
     */
    hideLoading() {
        console.log('Loading finished');
    }
    
    /**
     * 显示登录要求
     */
    showLoginRequired() {
        const container = document.getElementById('configListContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-person-lock display-1 text-warning"></i>
                    <h3 class="mt-3">需要登录</h3>
                    <p class="text-muted">请先登录管理员账户才能访问配置管理功能</p>
                    <div class="mt-3">
                        <button class="btn btn-primary me-2" onclick="window.location.href='/login.html'">
                            <i class="bi bi-box-arrow-in-right"></i> 前往登录
                        </button>
                        <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新页面
                        </button>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 在容器中显示错误信息
     */
    showErrorInContainer() {
        const container = document.getElementById('configListContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-exclamation-triangle display-1 text-danger"></i>
                    <h3 class="mt-3">加载失败</h3>
                    <p class="text-muted">配置文件加载失败，请检查网络连接或稍后重试</p>
                    <button class="btn btn-outline-primary" onclick="window.configManagement.loadConfigList()">
                        <i class="bi bi-arrow-clockwise"></i> 重新加载
                    </button>
                </div>
            `;
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    }
    
    /**
     * 显示错误消息
     */
    showError(message) {
        this.showToast(message, 'error');
    }
    
    /**
     * 显示Toast通知
     */
    showToast(message, type = 'info') {
        const toastElement = document.getElementById('notificationToast');
        const toastMessage = document.getElementById('toastMessage');
        
        if (toastElement && toastMessage) {
            // 设置消息内容
            toastMessage.textContent = message;
            
            // 设置样式
            toastElement.className = `toast ${type === 'error' ? 'bg-danger text-white' : 
                                              type === 'success' ? 'bg-success text-white' : 
                                              'bg-info text-white'}`;
            
            // 显示Toast
            const toast = new bootstrap.Toast(toastElement);
            toast.show();
        } else {
            // 降级到alert
            alert((type === 'error' ? '❌ ' : type === 'success' ? '✅ ' : 'ℹ️ ') + message);
        }
    }
}

// 全局函数，供HTML调用
window.showAllBackups = async function() {
    try {
        const response = await fetch('/api/config/configs/backups', {
            method: 'GET',
            credentials: 'include'
        });
        
        const result = await response.json();
        if (result.status === 'success') {
            window.configManagement.showBackupList('所有配置', result.data);
        } else {
            throw new Error(result.error || '获取备份列表失败');
        }
        
    } catch (error) {
        console.error('获取所有备份列表失败:', error);
        window.configManagement.showError('获取备份列表失败: ' + error.message);
    }
};

// 全局函数，供HTML调用
window.previewConfig = function() {
    window.configManagement.previewConfig();
};

window.validateConfig = function() {
    window.configManagement.validateConfig();
};

window.saveConfig = function() {
    window.configManagement.saveConfig();
};

// 创建全局实例
window.configManagement = new ConfigManagement();

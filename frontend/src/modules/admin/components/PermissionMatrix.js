/**
 * 权限矩阵管理组件
 * 负责权限矩阵的显示、编辑和管理
 */

// 导入依赖
import NavigationComponent from '../../../shared/components/navigation.js';

class PermissionMatrix {
    constructor() {
        this.matrixData = null;
        this.filteredData = null;
        this.pendingChanges = new Map();
        
        // 绑定方法上下文
        this.init = this.init.bind(this);
        this.loadMatrix = this.loadMatrix.bind(this);
        this.renderMatrix = this.renderMatrix.bind(this);
        this.handlePermissionChange = this.handlePermissionChange.bind(this);
    }

    /**
     * 初始化权限矩阵组件
     */
    async init() {
        try {
            console.log('🚀 权限矩阵页面开始加载，进行鉴权检查...');

            // 导入鉴权工具
            let AuthUtils;
            try {
                const authUtilsModule = await import('../../../shared/utils/auth-utils.js');
                AuthUtils = authUtilsModule.default || authUtilsModule.AuthUtils || window.AuthUtils;
            } catch (error) {
                console.error('❌ 无法导入AuthUtils:', error);
                AuthUtils = window.AuthUtils; // 尝试从全局获取
            }

            if (!AuthUtils) {
                console.error('❌ AuthUtils未定义，重定向到登录页面');
                window.location.href = '/login.html';
                return false;
            }

            // 1. 检查用户登录状态（必须）
            const user = await AuthUtils.guardPage();
            if (!user) {
                console.log('❌ 用户未登录，已跳转到登录页面');
                return false;
            }
            
            // 2. 检查管理员权限
            if (user.role !== 'admin') {
                console.log('❌ 权限不足，重定向到主页');
                this.showError('权限不足，只有管理员可以访问此页面');
                setTimeout(() => {
                    window.location.href = '/frontend/src/shared/index.html';
                }, 2000);
                return false;
            }
            
            // 3. 初始化页面权限控制（显示用户信息栏）
            AuthUtils.initPagePermissions();
            
            console.log('✅ 用户已登录:', user.username, '角色:', user.role);
            
            // 4. 初始化导航栏
            NavigationComponent.init('navigationContainer');
            
            // 5. 初始化事件监听器
            this.initializeEventListeners();
            
            // 6. 加载权限矩阵数据
            await this.loadMatrix();
            
            console.log('✅ 权限矩阵页面初始化完成');
            return true;
            
        } catch (error) {
            console.error('❌ 权限矩阵页面初始化失败:', error);
            this.showError('页面初始化失败: ' + error.message);
            return false;
        }
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 刷新矩阵按钮
        const refreshBtn = document.getElementById('refreshMatrix');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadMatrix());
        }

        // 保存更改按钮
        const saveBtn = document.getElementById('saveChanges');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveChanges());
        }

        // 筛选器
        const filters = ['roleFilter', 'groupFilter', 'actionFilter'];
        filters.forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', () => this.applyFilters());
            }
        });

        // 清除筛选器
        const clearFilters = document.getElementById('clearFilters');
        if (clearFilters) {
            clearFilters.addEventListener('click', () => this.clearFilters());
        }

        // 显示继承权限开关
        const showInherited = document.getElementById('showInheritedPermissions');
        if (showInherited) {
            showInherited.addEventListener('change', () => this.renderMatrix());
        }

        // 批量分配确认按钮
        const confirmBulkAssign = document.getElementById('confirmBulkAssign');
        if (confirmBulkAssign) {
            confirmBulkAssign.addEventListener('click', () => this.confirmBulkAssign());
        }
    }

    /**
     * 加载权限矩阵数据
     */
    async loadMatrix() {
        try {
            this.showLoading(true);
            
            const response = await fetch('/api/permissions/matrix', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.success) {
                this.matrixData = data.data;
                this.filteredData = JSON.parse(JSON.stringify(this.matrixData)); // 深拷贝
                
                this.populateFilters();
                this.renderMatrix();
                this.updateMatrixStats();
                
                // 隐藏空状态
                const emptyState = document.getElementById('emptyState');
                if (emptyState) emptyState.style.display = 'none';
                
            } else {
                throw new Error(data.error || '获取权限矩阵失败');
            }

        } catch (error) {
            console.error('加载权限矩阵失败:', error);
            this.showError('加载权限矩阵失败: ' + error.message);
            
            // 显示空状态
            const emptyState = document.getElementById('emptyState');
            if (emptyState) emptyState.style.display = 'block';
            
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 填充筛选器选项
     */
    populateFilters() {
        if (!this.matrixData) return;

        // 填充角色筛选器
        const roleFilter = document.getElementById('roleFilter');
        if (roleFilter) {
            roleFilter.innerHTML = '<option value="">显示所有角色</option>';
            this.matrixData.roles.forEach(role => {
                const option = document.createElement('option');
                option.value = role.id;
                option.textContent = role.display_name;
                roleFilter.appendChild(option);
            });
        }

        // 填充权限组筛选器
        const groupFilter = document.getElementById('groupFilter');
        if (groupFilter) {
            groupFilter.innerHTML = '<option value="">显示所有权限组</option>';
            this.matrixData.groups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.id;
                option.textContent = group.display_name;
                groupFilter.appendChild(option);
            });
        }

        // 填充操作筛选器
        const actionFilter = document.getElementById('actionFilter');
        if (actionFilter) {
            actionFilter.innerHTML = '<option value="">显示所有操作</option>';
            this.matrixData.actions.forEach(action => {
                const option = document.createElement('option');
                option.value = action.id;
                option.textContent = action.display_name;
                actionFilter.appendChild(option);
            });
        }

        // 填充批量分配模态框
        this.populateBulkAssignModal();
    }

    /**
     * 填充批量分配模态框
     */
    populateBulkAssignModal() {
        if (!this.matrixData) return;

        // 填充角色选择
        const bulkRole = document.getElementById('bulkRole');
        if (bulkRole) {
            bulkRole.innerHTML = '<option value="">请选择角色</option>';
            this.matrixData.roles.forEach(role => {
                const option = document.createElement('option');
                option.value = role.id;
                option.textContent = role.display_name;
                bulkRole.appendChild(option);
            });
        }

        // 填充权限组选择
        const bulkPermissionGroup = document.getElementById('bulkPermissionGroup');
        if (bulkPermissionGroup) {
            bulkPermissionGroup.innerHTML = '<option value="">请选择权限组</option>';
            this.matrixData.groups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.id;
                option.textContent = group.display_name;
                bulkPermissionGroup.appendChild(option);
            });
        }

        // 填充操作选择
        const bulkActions = document.getElementById('bulkActions');
        if (bulkActions) {
            bulkActions.innerHTML = '';
            this.matrixData.actions.forEach(action => {
                const checkDiv = document.createElement('div');
                checkDiv.className = 'form-check';
                checkDiv.innerHTML = `
                    <input class="form-check-input" type="checkbox" value="${action.id}" id="bulkAction${action.id}">
                    <label class="form-check-label" for="bulkAction${action.id}">
                        ${action.display_name}
                    </label>
                `;
                bulkActions.appendChild(checkDiv);
            });
        }
    }

    /**
     * 渲染权限矩阵
     */
    renderMatrix() {
        if (!this.filteredData) return;

        const matrixHeader = document.getElementById('matrixHeader');
        const actionHeader = document.getElementById('actionHeader');
        const matrixBody = document.getElementById('matrixBody');
        
        if (!matrixHeader || !actionHeader || !matrixBody) return;

        // 清空现有内容
        matrixHeader.innerHTML = '<th scope="col" style="min-width: 200px;">权限 / 角色</th>';
        actionHeader.innerHTML = '<th scope="col">操作</th>';
        matrixBody.innerHTML = '';

        // 生成角色列头
        this.filteredData.roles.forEach(role => {
            const th = document.createElement('th');
            th.className = 'role-header';
            th.style.minWidth = '120px';
            th.innerHTML = `
                <div class="d-flex flex-column align-items-center">
                    <strong>${role.display_name}</strong>
                    <small>${role.role_name}</small>
                </div>
            `;
            matrixHeader.appendChild(th);
        });

        // 生成操作列头
        this.filteredData.actions.forEach(action => {
            const th = document.createElement('th');
            th.className = 'action-header';
            th.textContent = action.display_name;
            actionHeader.appendChild(th);
        });

        // 生成权限矩阵内容
        this.filteredData.groups.forEach(group => {
            // 权限组标题行
            const groupRow = document.createElement('tr');
            groupRow.className = 'permission-group';
            
            const groupCell = document.createElement('td');
            groupCell.colSpan = 1 + this.filteredData.roles.length * this.filteredData.actions.length;
            groupCell.innerHTML = `
                <strong><i class="bi bi-folder"></i> ${group.display_name}</strong>
                <small class="text-muted ms-2">${group.description || ''}</small>
            `;
            groupRow.appendChild(groupCell);
            matrixBody.appendChild(groupRow);

            // 权限项行
            group.permissions.forEach(permission => {
                const permissionRow = document.createElement('tr');
                
                // 权限名称列
                const nameCell = document.createElement('td');
                nameCell.className = 'permission-item';
                nameCell.innerHTML = `
                    <div>
                        <strong>${permission.display_name}</strong>
                        <br>
                        <small class="text-muted">${permission.resource_name}</small>
                        <span class="badge permission-badge bg-secondary ms-1">${permission.resource_type}</span>
                    </div>
                `;
                permissionRow.appendChild(nameCell);

                // 为每个角色和操作组合生成单元格
                this.filteredData.roles.forEach(role => {
                    this.filteredData.actions.forEach(action => {
                        const cell = document.createElement('td');
                        cell.className = 'matrix-cell';
                        
                        const hasPermission = group.role_permissions[role.id] && 
                                            group.role_permissions[role.id][permission.id] && 
                                            group.role_permissions[role.id][permission.id][action.id];
                        
                        const checkboxId = `perm_${role.id}_${permission.id}_${action.id}`;
                        
                        cell.innerHTML = `
                            <input type="checkbox" 
                                   class="form-check-input permission-checkbox" 
                                   id="${checkboxId}"
                                   data-role-id="${role.id}"
                                   data-permission-id="${permission.id}"
                                   data-action-id="${action.id}"
                                   ${hasPermission ? 'checked' : ''}
                                   onchange="permissionMatrix.handlePermissionChange(this)">
                        `;
                        
                        permissionRow.appendChild(cell);
                    });
                });

                matrixBody.appendChild(permissionRow);
            });
        });
    }

    /**
     * 处理权限变更
     */
    handlePermissionChange(checkbox) {
        const roleId = parseInt(checkbox.dataset.roleId);
        const permissionId = parseInt(checkbox.dataset.permissionId);
        const actionId = parseInt(checkbox.dataset.actionId);
        const isGranted = checkbox.checked;

        // 记录待保存的更改
        const changeKey = `${roleId}_${permissionId}_${actionId}`;
        this.pendingChanges.set(changeKey, {
            roleId,
            permissionId,
            actionId,
            isGranted
        });

        // 显示保存按钮
        const saveBtn = document.getElementById('saveChanges');
        if (saveBtn) {
            saveBtn.style.display = this.pendingChanges.size > 0 ? 'inline-block' : 'none';
        }

        console.log(`权限变更: 角色${roleId}, 权限${permissionId}, 操作${actionId}, 授权${isGranted}`);
    }

    /**
     * 保存权限更改
     */
    async saveChanges() {
        if (this.pendingChanges.size === 0) {
            this.showInfo('没有需要保存的更改');
            return;
        }

        try {
            const saveBtn = document.getElementById('saveChanges');
            if (saveBtn) {
                saveBtn.disabled = true;
                saveBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';
            }

            // 批量保存所有更改
            const promises = Array.from(this.pendingChanges.values()).map(change => 
                this.saveRolePermission(change)
            );

            const results = await Promise.allSettled(promises);
            
            const successCount = results.filter(r => r.status === 'fulfilled').length;
            const failCount = results.filter(r => r.status === 'rejected').length;

            if (failCount === 0) {
                this.showSuccess(`成功保存 ${successCount} 项权限更改`);
                this.pendingChanges.clear();
            } else {
                this.showError(`保存完成：${successCount} 项成功，${failCount} 项失败`);
            }

        } catch (error) {
            console.error('保存权限更改失败:', error);
            this.showError('保存权限更改失败: ' + error.message);
        } finally {
            const saveBtn = document.getElementById('saveChanges');
            if (saveBtn) {
                saveBtn.disabled = false;
                saveBtn.innerHTML = '<i class="bi bi-check-lg"></i> 保存更改';
                saveBtn.style.display = this.pendingChanges.size > 0 ? 'inline-block' : 'none';
            }
        }
    }

    /**
     * 保存单个角色权限
     */
    async saveRolePermission(change) {
        const response = await fetch(`/api/permissions/roles/${change.roleId}/permissions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                permission_id: change.permissionId,
                action_id: change.actionId,
                is_granted: change.isGranted
            })
        });

        if (!response.ok) {
            throw new Error(`保存权限失败: HTTP ${response.status}`);
        }

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.error || '保存权限失败');
        }

        return data;
    }

    /**
     * 应用筛选器
     */
    applyFilters() {
        if (!this.matrixData) return;

        const roleFilter = document.getElementById('roleFilter').value;
        const groupFilter = document.getElementById('groupFilter').value;
        const actionFilter = document.getElementById('actionFilter').value;

        // 深拷贝原始数据
        this.filteredData = JSON.parse(JSON.stringify(this.matrixData));

        // 筛选角色
        if (roleFilter) {
            this.filteredData.roles = this.filteredData.roles.filter(
                role => role.id == roleFilter
            );
        }

        // 筛选权限组
        if (groupFilter) {
            this.filteredData.groups = this.filteredData.groups.filter(
                group => group.id == groupFilter
            );
        }

        // 筛选操作
        if (actionFilter) {
            this.filteredData.actions = this.filteredData.actions.filter(
                action => action.id == actionFilter
            );
        }

        this.renderMatrix();
        this.updateMatrixStats();
    }

    /**
     * 清除筛选器
     */
    clearFilters() {
        document.getElementById('roleFilter').value = '';
        document.getElementById('groupFilter').value = '';
        document.getElementById('actionFilter').value = '';
        
        this.applyFilters();
    }

    /**
     * 更新矩阵统计信息
     */
    updateMatrixStats() {
        if (!this.filteredData) return;

        const statsElement = document.getElementById('matrixStats');
        if (statsElement) {
            const roleCount = this.filteredData.roles.length;
            const groupCount = this.filteredData.groups.length;
            const actionCount = this.filteredData.actions.length;
            
            statsElement.textContent = `${roleCount} 个角色 • ${groupCount} 个权限组 • ${actionCount} 个操作`;
        }
    }

    /**
     * 显示批量分配模态框
     */
    showBulkAssignModal() {
        const modal = new bootstrap.Modal(document.getElementById('bulkAssignModal'));
        modal.show();
    }

    /**
     * 确认批量分配
     */
    async confirmBulkAssign() {
        const roleId = document.getElementById('bulkRole').value;
        const groupId = document.getElementById('bulkPermissionGroup').value;
        const selectedActions = Array.from(document.querySelectorAll('#bulkActions input:checked'))
            .map(cb => parseInt(cb.value));

        if (!roleId || !groupId || selectedActions.length === 0) {
            this.showError('请选择角色、权限组和操作');
            return;
        }

        try {
            // 获取权限组中的所有权限
            const group = this.matrixData.groups.find(g => g.id == groupId);
            if (!group) {
                this.showError('权限组不存在');
                return;
            }

            // 批量分配权限
            const promises = [];
            group.permissions.forEach(permission => {
                selectedActions.forEach(actionId => {
                    promises.push(this.saveRolePermission({
                        roleId: parseInt(roleId),
                        permissionId: permission.id,
                        actionId: actionId,
                        isGranted: true
                    }));
                });
            });

            await Promise.all(promises);
            
            this.showSuccess('批量权限分配成功');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('bulkAssignModal'));
            modal.hide();
            
            // 重新加载矩阵
            await this.loadMatrix();

        } catch (error) {
            console.error('批量分配权限失败:', error);
            this.showError('批量分配权限失败: ' + error.message);
        }
    }

    /**
     * 导出权限矩阵
     */
    exportMatrix() {
        if (!this.matrixData) {
            this.showError('没有可导出的数据');
            return;
        }

        try {
            // 生成CSV内容
            const csvContent = this.generateMatrixCSV();
            
            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `permission_matrix_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                this.showSuccess('权限矩阵导出成功');
            }

        } catch (error) {
            console.error('导出权限矩阵失败:', error);
            this.showError('导出权限矩阵失败: ' + error.message);
        }
    }

    /**
     * 生成权限矩阵CSV内容
     */
    generateMatrixCSV() {
        const headers = ['权限组', '权限名称', '资源名称', '资源类型'];
        
        // 添加角色-操作组合列头
        this.matrixData.roles.forEach(role => {
            this.matrixData.actions.forEach(action => {
                headers.push(`${role.display_name}-${action.display_name}`);
            });
        });

        const rows = [headers];

        // 生成数据行
        this.matrixData.groups.forEach(group => {
            group.permissions.forEach(permission => {
                const row = [
                    group.display_name,
                    permission.display_name,
                    permission.resource_name,
                    permission.resource_type
                ];

                // 添加权限状态
                this.matrixData.roles.forEach(role => {
                    this.matrixData.actions.forEach(action => {
                        const hasPermission = group.role_permissions[role.id] && 
                                            group.role_permissions[role.id][permission.id] && 
                                            group.role_permissions[role.id][permission.id][action.id];
                        row.push(hasPermission ? '是' : '否');
                    });
                });

                rows.push(row);
            });
        });

        // 转换为CSV格式
        return '\uFEFF' + rows.map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    /**
     * 初始化权限系统
     */
    async initializePermissions() {
        try {
            const response = await fetch('/api/permissions/initialize', {
                method: 'POST',
                credentials: 'include'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('权限系统初始化成功');
                await this.loadMatrix();
            } else {
                this.showError(data.error || '权限系统初始化失败');
            }

        } catch (error) {
            console.error('初始化权限系统失败:', error);
            this.showError('初始化权限系统失败: ' + error.message);
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = show ? 'flex' : 'none';
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.showToast('成功', message, 'success');
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        this.showToast('错误', message, 'danger');
    }

    /**
     * 显示信息消息
     */
    showInfo(message) {
        this.showToast('提示', message, 'info');
    }

    /**
     * 显示Toast通知
     */
    showToast(title, message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;

        const toastId = 'toast_' + Date.now();
        const toastHTML = `
            <div class="toast" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} text-${type} me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="关闭"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
        toast.show();

        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
}

// 创建全局实例
const permissionMatrix = new PermissionMatrix();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    permissionMatrix.init();
});

// 导出到全局作用域，供HTML中的onclick使用
window.permissionMatrix = permissionMatrix;

export default PermissionMatrix;

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合约分析工具</title>
    <!-- Webpack will inject necessary CSS here (e.g., through MiniCssExtractPlugin or style-loader) -->
    <!-- All CDN links for CSS (Bootstrap, Element Plus, Bootstrap Icons) removed -->
    <style>
        /* 统一标题字体样式 - 与个人分析页面保持一致 */
        h1 {
            font-size: 1.8em !important;
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }
        h2 {
            font-size: 1.4em !important;
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }
        h5 {
            font-size: 1.1em !important;
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }
        h6 {
            font-size: 1.0em !important;
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }

        /* 详情模态框样式 */
        .anomaly-details .detail-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .anomaly-details .detail-section h5 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1em;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }

        .anomaly-details .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .anomaly-details .detail-item:last-child {
            border-bottom: none;
        }

        .anomaly-details .detail-item label {
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0;
            min-width: 120px;
        }

        .anomaly-details .detail-item span {
            text-align: right;
            flex: 1;
        }

        .anomaly-details .copyable {
            cursor: pointer;
            color: #007bff;
            transition: color 0.2s;
        }

        .anomaly-details .copyable:hover {
            color: #0056b3;
            text-decoration: underline;
        }

        .anomaly-details .badge {
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <!-- 导航栏容器 -->
    <div id="navigationContainer"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">
                <i class="bi bi-bar-chart-line"></i> 合约分析工具
            </h1>
            <div class="text-muted">
                <i class="bi bi-file-earmark-bar-graph"></i> 检测合约交易异常行为
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">数据上传与分析</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contractFile" class="form-label">
                                <i class="bi bi-file-earmark-excel"></i> 上传合约数据
                            </label>
                            <input class="form-control" type="file" id="contractFile" accept=".xlsx,.xls,.csv">
                            <div class="form-text">支持Excel或CSV格式，需包含交易记录数据</div>
                        </div>

                        <!-- 新增：处理模式选择 -->
                        <div class="mb-3">
                            <label for="processingMode" class="form-label">
                                <i class="bi bi-gear"></i> 处理模式
                            </label>
                            <select class="form-select" id="processingMode">
                                <option value="normal">普通模式 - 完整数据分析</option>
                                <option value="incremental">增量模式 - 智能补全不完整订单</option>
                            </select>
                            <div class="form-text">
                                <small>
                                    <strong>普通模式：</strong>适用于完整的交易数据，直接进行风险分析<br>
                                    <strong>增量模式：</strong>适用于不完整的订单数据，先进行智能补全再分析
                                </small>
                            </div>
                        </div>

                        <button id="uploadContractBtn" class="btn btn-primary">
                            <i class="bi bi-cloud-arrow-up"></i> 上传并分析
                        </button>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="taskIdSelector" class="form-label">
                                <i class="bi bi-arrow-repeat"></i> 恢复分析任务
                            </label>
                            <div class="input-group">
                                <select class="form-select" id="taskIdSelector">
                                    <option value="">请选择要恢复的任务...</option>
                                </select>
                                <button class="btn btn-outline-secondary" type="button" id="refreshTasksBtn" title="刷新任务列表">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                                <button class="btn btn-outline-primary" type="button" id="loadTaskBtn" disabled>
                                    <i class="bi bi-play-fill"></i> 加载
                                </button>
                            </div>
                            <div class="form-text">选择之前的分析任务进行恢复，无需重新上传文件</div>
                        </div>

                    </div>
                </div>
                <div id="contractAlert" class="alert d-none" role="alert"></div>
            </div>
        </div>

        <!-- 批量查询区域 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-check"></i> 批量查询异常金额
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="batchMemberIds" class="form-label">
                                <i class="bi bi-person-lines-fill"></i> 用户ID列表
                            </label>
                            <textarea
                                class="form-control"
                                id="batchMemberIds"
                                rows="6"
                                placeholder="请输入用户ID，每行一个&#10;例如：&#10;12345&#10;67890&#10;11111&#10;&#10;支持最多5000个用户ID"></textarea>
                            <div class="form-text">每行输入一个用户ID，支持批量查询各类算法的异常金额总和</div>
                        </div>
                        <div class="d-flex gap-2">
                            <button id="batchQueryBtn" class="btn btn-success">
                                <i class="bi bi-search"></i> 批量查询
                            </button>
                            <button id="clearBatchInputBtn" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> 清空
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-info-circle"></i> 查询说明
                                </h6>
                                <ul class="list-unstyled small">
                                    <li><i class="bi bi-check-circle text-success"></i> 对敲交易异常金额</li>
                                    <li><i class="bi bi-check-circle text-success"></i> 高频交易异常金额</li>
                                    <li><i class="bi bi-check-circle text-success"></i> 资金费率套利金额</li>
                                    <li><i class="bi bi-check-circle text-success"></i> 常规刷量异常金额</li>
                                </ul>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-file-earmark-excel"></i>
                                        结果将以Excel格式下载
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="batchQueryAlert" class="alert d-none mt-3" role="alert"></div>
            </div>
        </div>

        <div id="resultTabs" class="d-none">
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary" type="button" role="tab">
                        <i class="bi bi-pie-chart"></i> 统计概览
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contract-tab" data-bs-toggle="tab" data-bs-target="#contract" type="button" role="tab">
                        <i class="bi bi-currency-exchange"></i> 合约异常
                    </button>
                </li>
            </ul>
            <div class="tab-content" id="myTabContent">
                <!-- 统计概览 -->
                <div class="tab-pane fade show active" id="summary" role="tabpanel">
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">合约交易分布</h6>
                                </div>
                                <div class="card-body">
                                    <div id="contractChart" class="chart-container"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">异常类型分布</h6>
                                </div>
                                <div class="card-body">
                                    <div id="anomalyChart" class="chart-container"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">风险等级分布</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 text-center">
                                            <div class="p-3 bg-light rounded">
                                                <h1 id="highRiskCount" class="text-danger">0</h1>
                                                <p>高风险用户</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <div class="p-3 bg-light rounded">
                                                <h1 id="mediumRiskCount" class="text-warning">0</h1>
                                                <p>中风险用户</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <div class="p-3 bg-light rounded">
                                                <h1 id="lowRiskCount" class="text-success">0</h1>
                                                <p>低风险用户</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 合约异常 -->
                <div class="tab-pane fade" id="contract" role="tabpanel">
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>合约异常检测结果</h5>
                            <div>
                                <input type="text" id="contractSearchInput" class="form-control form-control-sm" placeholder="搜索用户ID或合约名称">
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table id="contractTable" class="table table-striped table-hover table-sm">
                                <thead>
                                    <tr>
                                        <th>用户ID</th>
                                        <th>合约名称</th>
                                        <th>异常类型</th>
                                        <th>时间范围</th>
                                        <th>异常交易量(USDT)</th>
                                        <th>风险等级</th>
                                        <th>异常原因</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="contractTableBody">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                

            </div>
        </div>
    </div>

    <!-- 异常详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">异常详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="detailModalBody">
                    <!-- Detail content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加分页控件 -->
    <div id="paginationControls" class="d-flex justify-content-center mt-3 d-none">
        <div class="btn-group">
            <button id="prevPageBtn" class="btn btn-outline-primary btn-sm" disabled>
                <i class="bi bi-chevron-left"></i> 上一页
            </button>
            <button class="btn btn-outline-secondary btn-sm" disabled>
                第 <span id="currentPage">1</span> 页 / 共 <span id="totalPages">1</span> 页
            </button>
            <button id="nextPageBtn" class="btn btn-outline-primary btn-sm" disabled>
                下一页 <i class="bi bi-chevron-right"></i>
            </button>
        </div>
    </div>

    <!-- HtmlWebpackPlugin will inject bundled JS here -->
</body>
</html> 
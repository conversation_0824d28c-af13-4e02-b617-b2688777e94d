/**
 * DOM元素管理器
 * 负责用户分析页面所有DOM元素的获取、验证和管理
 */

class DOMManager {
    constructor() {
        this.elements = {};
        this.initialized = false;
    }

    /**
     * 初始化所有DOM元素
     */
    initialize() {
        if (this.initialized) {
            return this.elements;
        }

        this.elements = {
            // 搜索相关
            userIdInput: document.getElementById('userIdInput'),
            userIdLabel: document.getElementById('userIdLabel'),
            searchBtn: document.getElementById('searchBtn'),
            searchHint: document.getElementById('searchHint'),
            contractTaskSelect: document.getElementById('contractTaskSelect'),
            agentTaskSelect: document.getElementById('agentTaskSelect'),
            refreshTasksBtn: document.getElementById('refreshTasksBtn'),
            
            // 状态指示器
            loadingIndicator: document.getElementById('loadingIndicator'),
            errorMessage: document.getElementById('errorMessage'),
            analysisResults: document.getElementById('analysisResults'),
            
            // 用户档案
            profileMemberId: document.getElementById('profileMemberId'),
            profileDigitalId: document.getElementById('profileDigitalId'),
            profileUserType: document.getElementById('profileUserType'),
            profileBdName: document.getElementById('profileBdName'),
            profileTotalRisks: document.getElementById('profileTotalRisks'),
            profileLastActivity: document.getElementById('profileLastActivity'),
            profileSearchMethod: document.getElementById('profileSearchMethod'),
            profileFundScale: document.getElementById('profileFundScale'),
            
            // 风险摘要
            summaryTotalRisks: document.getElementById('summaryTotalRisks'),
            summaryMaxScore: document.getElementById('summaryMaxScore'),
            summaryTotalVolume: document.getElementById('summaryTotalVolume'),
            summaryRiskTypes: document.getElementById('summaryRiskTypes'),
            riskCategoriesCanvas: document.getElementById('riskCategoriesCanvas'),
            
            // 关联分析
            associationTabs: document.querySelectorAll('.tab-btn'),
            associationTabContent: document.querySelectorAll('.tab-content'),
            sameIpCount: document.getElementById('sameIpCount'),
            sameDeviceCount: document.getElementById('sameDeviceCount'),
            bothSharedCount: document.getElementById('bothSharedCount'),
            sameIpUsers: document.getElementById('sameIpUsers'),
            sameDeviceUsers: document.getElementById('sameDeviceUsers'),
            bothSharedUsers: document.getElementById('bothSharedUsers'),
            
            // 交易详情
            riskTypeFilter: document.getElementById('riskTypeFilter'),
            searchTransactions: document.getElementById('searchTransactions'),
            sortBy: document.getElementById('sortBy'),
            transactionsList: document.getElementById('transactionsList'),
            prevPage: document.getElementById('prevPage'),
            nextPage: document.getElementById('nextPage'),
            pageInfo: document.getElementById('pageInfo'),
            
            // 模态框
            counterpartyModal: document.getElementById('counterpartyModal'),
            counterpartyDetails: document.getElementById('counterpartyDetails'),
            
            // 用户行为分析相关元素
            behaviorAnalysisTabs: document.querySelectorAll('.behavior-tab-btn'),
            behaviorTabContents: document.querySelectorAll('.behavior-tab-content'),
            
            // 基础数据指标Tab相关元素
            metricTabs: document.querySelectorAll('.metric-tab'),
            metricPanels: document.querySelectorAll('.metric-panel'),
            
            // 专业度评分
            totalScoreValue: document.getElementById('totalScoreValue'),
            totalScoreLevel: document.getElementById('totalScoreLevel'),
            traderTypeValue: document.getElementById('traderTypeValue'),
            confidenceLevel: document.getElementById('confidenceLevel'),
            profitabilityScore: document.getElementById('profitabilityScore'),
            riskControlScore: document.getElementById('riskControlScore'),
            tradingBehaviorScore: document.getElementById('tradingBehaviorScore'),
            marketUnderstandingScore: document.getElementById('marketUnderstandingScore'),
            totalScore: document.getElementById('totalScore'),
            traderType: document.getElementById('traderType'),
            fundScale: document.getElementById('fundScale'),
            analysisDate: document.getElementById('analysisDate'),
            professionalScoreGauge: document.getElementById('professionalScoreGauge'),
            
            // 币种分析
            totalCoinsAnalyzed: document.getElementById('totalCoinsAnalyzed'),
            avgCoinWinRate: document.getElementById('avgCoinWinRate'),
            advantageCoinsCount: document.getElementById('advantageCoinsCount'),
            advantageCoinsList: document.getElementById('advantageCoinsList'),
            coinInsightsCanvas: document.getElementById('coinInsightsCanvas'),
            analysisCoinsCount: document.getElementById('analysisCoinsCount'),
            avgCoinWinRateDisplay: document.getElementById('avgCoinWinRateDisplay'),
            expertCoinsCount: document.getElementById('expertCoinsCount'),
            advantageCoinsCountDisplay: document.getElementById('advantageCoinsCountDisplay'),
            coinRankingTableBody: document.getElementById('coinRankingTableBody'),
            advantageCoinsGrid: document.getElementById('advantageCoinsGrid'),
            expertiseLevelsGrid: document.getElementById('expertiseLevelsGrid'),
            tradingInsightsGrid: document.getElementById('tradingInsightsGrid'),
            
            // 交易指标
            totalVolumeMetric: document.getElementById('totalVolumeMetric'),
            totalTradesMetric: document.getElementById('totalTradesMetric'),
            avgTradeSizeMetric: document.getElementById('avgTradeSizeMetric'),
            tradingDaysMetric: document.getElementById('tradingDaysMetric'),
            winRateMetric: document.getElementById('winRateMetric'),
            profitLossRatioMetric: document.getElementById('profitLossRatioMetric'),
            avgLeverageMetric: document.getElementById('avgLeverageMetric'),
            fundScaleMetric: document.getElementById('fundScaleMetric'),
            metricsCanvas: document.getElementById('metricsCanvas'),
            
            // 风险画像
            dataQualityScore: document.getElementById('dataQualityScore'),
            analysisConfidence: document.getElementById('analysisConfidence'),
            analysisReportContainer: document.getElementById('analysisReportContainer'),
            
            // 对冲分析
            hedgePositionsCount: document.getElementById('hedgePositionsCount'),
            maxConcurrentPositions: document.getElementById('maxConcurrentPositions'),
            hedgeContractsCount: document.getElementById('hedgeContractsCount'),
            avgHedgeDuration: document.getElementById('avgHedgeDuration'),
            hedgeTableBody: document.getElementById('hedgeTableBody'),
            
            // 行为风险摘要
            behaviorHighRiskCount: document.getElementById('behaviorHighRiskCount'),
            behaviorHighRiskPercentage: document.getElementById('behaviorHighRiskPercentage'),
            behaviorMediumRiskCount: document.getElementById('behaviorMediumRiskCount'),
            behaviorMediumRiskPercentage: document.getElementById('behaviorMediumRiskPercentage'),
            behaviorLowRiskCount: document.getElementById('behaviorLowRiskCount'),
            behaviorLowRiskPercentage: document.getElementById('behaviorLowRiskPercentage'),
            behaviorRiskTotalVolume: document.getElementById('behaviorRiskTotalVolume'),
            behaviorRiskMaxScore: document.getElementById('behaviorRiskMaxScore')
        };

        // 验证关键元素
        this.validateElements();
        
        this.initialized = true;

        return this.elements;
    }

    /**
     * 验证关键DOM元素是否存在
     */
    validateElements() {
        const keyElements = [
            'userIdInput', 'userIdLabel', 'contractTaskSelect', 'agentTaskSelect', 
            'searchBtn', 'refreshTasksBtn', 'loadingIndicator', 'errorMessage', 'analysisResults'
        ];
        
        const missingElements = [];
        
        for (const key of keyElements) {
            if (!this.elements[key]) {
                missingElements.push(key);
                console.error(`❌ 关键DOM元素不存在: ${key}`);
            }
        }
        
        if (missingElements.length > 0) {
            console.warn(`⚠️ 发现 ${missingElements.length} 个缺失的关键DOM元素:`, missingElements);
        }
        
        // 检查用户档案相关元素
        const profileElements = [
            'profileMemberId', 'profileDigitalId', 'profileUserType', 'profileBdName', 
            'profileTotalRisks', 'profileLastActivity'
        ];
        
        const missingProfileElements = [];
        for (const key of profileElements) {
            if (!this.elements[key]) {
                missingProfileElements.push(key);
            }
        }

        // 用户档案元素缺失是正常的，因为某些元素可能已被移除
    }

    /**
     * 获取所有DOM元素
     */
    getElements() {
        if (!this.initialized) {
            return this.initialize();
        }
        return this.elements;
    }

    /**
     * 获取特定DOM元素
     */
    getElement(key) {
        if (!this.initialized) {
            this.initialize();
        }
        return this.elements[key];
    }

    /**
     * 检查元素是否存在
     */
    hasElement(key) {
        if (!this.initialized) {
            this.initialize();
        }
        return !!this.elements[key];
    }

    /**
     * 更新元素文本内容
     */
    updateElementText(elementKey, text) {
        const element = this.getElement(elementKey);
        if (element) {
            element.textContent = text;
            return true;
        } else {
            return false;
        }
    }

    /**
     * 显示/隐藏元素
     */
    toggleElement(elementKey, show) {
        const element = this.getElement(elementKey);
        if (element) {
            element.style.display = show ? 'block' : 'none';
            return true;
        }
        return false;
    }

    /**
     * 添加/移除元素的CSS类
     */
    toggleElementClass(elementKey, className, add = true) {
        const element = this.getElement(elementKey);
        if (element) {
            if (add) {
                element.classList.add(className);
            } else {
                element.classList.remove(className);
            }
            return true;
        }
        return false;
    }

    /**
     * 重置DOM管理器
     */
    reset() {
        this.elements = {};
        this.initialized = false;
    }
}

// 创建单例实例
const domManager = new DOMManager();

export default domManager; 
/**
 * 专业度评分圆形仪表盘组件
 * 显示用户专业度总分和各维度评分
 */

import { getTraderTypeColor } from './utils.js';

class ProfessionalScoreGauge {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.canvas = null;
        this.ctx = null;
        this.animationId = null;
        this.currentScore = 0;
        this.targetScore = 0;
        this.animationSpeed = 2;
        
        this.init();
    }

    /**
     * 初始化仪表盘
     */
    init() {
        if (!this.container) {
            console.error('专业度评分仪表盘容器未找到');
            return;
        }

        // 创建Canvas元素
        this.canvas = document.createElement('canvas');
        this.canvas.width = 200;
        this.canvas.height = 200;
        this.canvas.style.maxWidth = '100%';
        this.canvas.style.height = 'auto';
        
        this.ctx = this.canvas.getContext('2d');
        this.container.appendChild(this.canvas);

        // 绑定事件
        this.bindEvents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 窗口大小变化时重绘
        window.addEventListener('resize', () => {
            this.draw();
        });
    }

    /**
     * 更新评分数据
     * @param {Object} scoreData - 评分数据
     */
    updateScore(scoreData) {
        if (!scoreData) return;

        this.targetScore = scoreData.total_score || 0;
        this.traderType = scoreData.trader_type || '数据不足，无法计算';
        this.dimensions = {
            profitability: scoreData.profitability_score || 0,
            riskControl: scoreData.risk_control_score || 0,
            tradingBehavior: scoreData.trading_behavior_score || 0,
            marketUnderstanding: scoreData.market_understanding_score || 0
        };

        // 启动动画
        this.animateToTarget();
    }

    /**
     * 动画到目标分数
     */
    animateToTarget() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }

        const animate = () => {
            if (Math.abs(this.currentScore - this.targetScore) < 0.1) {
                this.currentScore = this.targetScore;
                this.draw();
                return;
            }

            this.currentScore += (this.targetScore - this.currentScore) * 0.1;
            this.draw();
            this.animationId = requestAnimationFrame(animate);
        };

        animate();
    }

    /**
     * 绘制仪表盘
     */
    draw() {
        if (!this.ctx) return;

        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const radius = 70;
        const lineWidth = 12;

        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制背景圆环
        this.drawBackgroundRing(centerX, centerY, radius, lineWidth);

        // 绘制评分弧线
        this.drawScoreArc(centerX, centerY, radius, lineWidth);

        // 绘制中心文字
        this.drawCenterText(centerX, centerY);

        // 绘制刻度
        this.drawScaleMarks(centerX, centerY, radius + 20);
    }

    /**
     * 绘制背景圆环
     */
    drawBackgroundRing(centerX, centerY, radius, lineWidth) {
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        this.ctx.strokeStyle = '#e9ecef';
        this.ctx.lineWidth = lineWidth;
        this.ctx.stroke();
    }

    /**
     * 绘制评分弧线
     */
    drawScoreArc(centerX, centerY, radius, lineWidth) {
        const angle = (this.currentScore / 100) * 2 * Math.PI;
        const startAngle = -Math.PI / 2; // 从12点钟方向开始

        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius, startAngle, startAngle + angle);
        
        // 根据评分设置颜色
        this.ctx.strokeStyle = this.getScoreColor(this.currentScore);
        this.ctx.lineWidth = lineWidth;
        this.ctx.lineCap = 'round';
        this.ctx.stroke();

        // 绘制发光效果
        if (this.currentScore > 0) {
            this.ctx.shadowBlur = 10;
            this.ctx.shadowColor = this.getScoreColor(this.currentScore);
            this.ctx.stroke();
            this.ctx.shadowBlur = 0;
        }
    }

    /**
     * 绘制中心文字
     */
    drawCenterText(centerX, centerY) {
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        // 绘制分数
        this.ctx.font = 'bold 24px Arial';
        this.ctx.fillStyle = '#2c3e50';
        // 直接使用后端格式化数据，不做前端格式化
        const scoreText = this.scoreData?.score_display || this.currentScore || '0';
        this.ctx.fillText(scoreText, centerX, centerY - 10);

        // 绘制"专业度评分"文字
        this.ctx.font = '12px Arial';
        this.ctx.fillStyle = '#6c757d';
        this.ctx.fillText('专业度评分', centerX, centerY + 8);

        // 绘制交易者类型
        if (this.traderType) {
            this.ctx.font = 'bold 14px Arial';
            this.ctx.fillStyle = getTraderTypeColor(this.traderType);
            this.ctx.fillText(this.traderType, centerX, centerY + 25);
        }
    }

    /**
     * 绘制刻度标记
     */
    drawScaleMarks(centerX, centerY, radius) {
        const marks = [0, 25, 50, 75, 100];
        
        marks.forEach(mark => {
            const angle = (mark / 100) * 2 * Math.PI - Math.PI / 2;
            const x1 = centerX + Math.cos(angle) * (radius - 5);
            const y1 = centerY + Math.sin(angle) * (radius - 5);
            const x2 = centerX + Math.cos(angle) * (radius + 5);
            const y2 = centerY + Math.sin(angle) * (radius + 5);

            this.ctx.beginPath();
            this.ctx.moveTo(x1, y1);
            this.ctx.lineTo(x2, y2);
            this.ctx.strokeStyle = '#6c757d';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();

            // 绘制刻度数字
            const textX = centerX + Math.cos(angle) * (radius + 15);
            const textY = centerY + Math.sin(angle) * (radius + 15);
            
            this.ctx.font = '10px Arial';
            this.ctx.fillStyle = '#6c757d';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(mark.toString(), textX, textY);
        });
    }

    /**
     * 根据评分获取颜色
     * @param {number} score - 评分
     * @returns {string} 颜色值
     */
    getScoreColor(score) {
        if (score >= 75) return '#28a745'; // 绿色 - 优秀
        if (score >= 60) return '#ffc107'; // 黄色 - 良好
        if (score >= 40) return '#fd7e14'; // 橙色 - 一般
        return '#dc3545'; // 红色 - 较差
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        if (this.canvas && this.container) {
            this.container.removeChild(this.canvas);
        }
    }

    /**
     * 获取当前评分
     * @returns {number} 当前评分
     */
    getCurrentScore() {
        return this.currentScore;
    }

    /**
     * 设置动画速度
     * @param {number} speed - 动画速度 (0-1)
     */
    setAnimationSpeed(speed) {
        this.animationSpeed = Math.max(0.1, Math.min(1, speed));
    }
}

/**
 * 创建专业度评分仪表盘
 * @param {string} containerId - 容器ID
 * @param {Object} scoreData - 评分数据
 * @returns {ProfessionalScoreGauge} 仪表盘实例
 */
export function createProfessionalScoreGauge(containerId, scoreData = null) {
    const gauge = new ProfessionalScoreGauge(containerId);
    
    if (scoreData) {
        gauge.updateScore(scoreData);
    }
    
    return gauge;
}

// 默认导出类
export default ProfessionalScoreGauge; 
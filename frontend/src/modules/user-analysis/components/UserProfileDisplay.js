/**
 * 用户档案显示组件
 * 负责显示用户基本信息、专业度评分预览、资金规模标签等
 */

import { formatNumber, getScoreLevel, getScoreBadgeClass, getTraderType, getTraderTypeClass, getFundScale, getFundScaleClass } from './utils.js';

class UserProfileDisplay {
    constructor(domManager) {
        this.elements = domManager.elements;
        this.currentProfile = null;
    }

    /**
     * 显示用户档案
     */
    display(profile) {
        if (!profile) {
            console.warn('用户档案数据为空');
            return;
        }


        this.currentProfile = profile;

        // 显示基本信息
        this.displayBasicInfo(profile);
        
        // 显示统计信息
        this.displayStatistics(profile);
        
        // 搜索方式显示已从用户档案中移除
    }

    /**
     * 显示基本信息
     */
    displayBasicInfo(profile) {
        const elements = this.elements;

        // 数字ID显示已从用户档案中移除

        // 用户ID标签 (显示member_id的值)
        if (elements.profileMemberId) {
            elements.profileMemberId.textContent = profile.member_id || '-';
        }

        // 用户类型
        if (elements.profileUserType) {
            const userType = profile.user_type || '-';
            elements.profileUserType.textContent = userType;
            elements.profileUserType.className = `user-type-badge ${this.getUserTypeClass(userType)}`;
        }

        // BD归属显示已从用户档案中移除

        // 最近活动时间
        if (elements.profileLastActivity) {
            elements.profileLastActivity.textContent = this.formatLastActivity(profile.last_activity);
        }
    }

    /**
     * 显示统计信息
     */
    displayStatistics(profile) {
        const elements = this.elements;

        // 风险事件总数
        if (elements.profileTotalRisks) {
            const totalRisks = profile.total_risks || profile.total_risk_events || profile.risk_events_count || 0;
            elements.profileTotalRisks.textContent = totalRisks;
            elements.profileTotalRisks.className = `risk-count ${this.getRiskCountClass(totalRisks)}`;
        }
    }

    /**
     * 显示搜索方式 - 已从用户档案中移除
     */

    /**
     * 更新专业度评分预览
     */
    updateProfessionalScorePreview(behaviorData) {
        if (!behaviorData || !behaviorData.professional_scores) {
            this.showDefaultProfessionalScore();
            return;
        }

        const scores = behaviorData.professional_scores;
        const elements = this.elements;

        // 交易者类型已从用户档案中移除，只在专业度评分仪表盘中显示

        // 更新专业度评分
        if (elements.profileProfessionalScore) {
            // 直接使用后端格式化数据，不做前端格式化
        const totalScore = scores.total_score_display || scores.total_score || '0';
            elements.profileProfessionalScore.textContent = `${totalScore}/100`;
            elements.profileProfessionalScore.className = `score-badge ${getScoreBadgeClass(totalScore)}`;
        }

        // 资金规模显示已从用户档案中移除
    }

    /**
     * 显示默认专业度评分
     */
    showDefaultProfessionalScore() {
        const elements = this.elements;

        // 专业度评分已从用户档案中移除，只在专业度评分仪表盘中显示

        // 资金规模显示已从用户档案中移除
    }

    /**
     * 获取用户类型样式类
     */
    getUserTypeClass(userType) {
        const typeMap = {
            'VIP': 'vip',
            '普通用户': 'normal',
            '新用户': 'new',
            '活跃用户': 'active',
            '沉默用户': 'inactive'
        };
        return typeMap[userType] || 'default';
    }

    /**
     * 获取风险数量样式类
     */
    getRiskCountClass(count) {
        if (count === 0) return 'safe';
        if (count <= 5) return 'low';
        if (count <= 15) return 'medium';
        return 'high';
    }

    /**
     * 获取搜索方式样式类 - 已从用户档案中移除
     */

    /**
     * 格式化最近活动时间
     */
    formatLastActivity(lastActivity) {
        if (!lastActivity) return '-';
        
        try {
            const date = new Date(lastActivity);
            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays === 1) return '今天';
            if (diffDays === 2) return '昨天';
            if (diffDays <= 7) return `${diffDays}天前`;
            if (diffDays <= 30) return `${Math.floor(diffDays / 7)}周前`;
            if (diffDays <= 365) return `${Math.floor(diffDays / 30)}个月前`;
            return `${Math.floor(diffDays / 365)}年前`;
        } catch (error) {
            console.warn('格式化最近活动时间失败:', error);
            return lastActivity;
        }
    }

    /**
     * 清空显示
     */
    clear() {
        const elements = this.elements;
        const profileElements = [
            'profileMemberId', 'profileUserType',
            'profileTotalRisks', 'profileLastActivity',
            'profileTraderType', 'profileProfessionalScore'
        ];

        profileElements.forEach(elementKey => {
            const element = elements[elementKey];
            if (element) {
                element.textContent = '-';
                element.className = element.className.split(' ')[0]; // 保留基础类名
            }
        });

        this.currentProfile = null;
    }

    /**
     * 获取当前档案数据
     */
    getCurrentProfile() {
        return this.currentProfile;
    }

    /**
     * 检查是否有有效的档案数据
     */
    hasValidProfile() {
        return this.currentProfile && this.currentProfile.member_id;
    }

    /**
     * 显示用户资金规模
     */
    displayFundScale(fundScale) {
        const element = document.getElementById('fundScale');
        if (element) {
            // 🚀 修复：确保数据不足时显示正确信息
            if (!fundScale || fundScale === '散户' || fundScale === '普通散户') {
                // 检查是否有实际交易数据支撑
                const hasValidData = this.currentUserData && 
                                   this.currentUserData.trading_volume && 
                                   this.currentUserData.trading_volume > 1000;
                if (!hasValidData) {
                    element.textContent = '--';
                    element.className = 'fund-scale insufficient-data';
                    return;
                }
            }

            element.textContent = fundScale || '--';
            element.className = `fund-scale ${this.getFundScaleClass(fundScale)}`;
        }
    }

    /**
     * 🔧 已删除：displayTraderType方法
     * 原因：交易者类型已从用户档案中移除，只在专业度评分仪表盘中显示
     */
}

export default UserProfileDisplay; 
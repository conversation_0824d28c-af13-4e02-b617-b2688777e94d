/**
 * 搜索管理器
 * 负责用户搜索、任务加载、ID类型识别等功能
 */

import domManager from './DOMManager.js';
import pageManager from './PageManager.js';

class SearchManager {
    constructor() {
        this.initialized = false;
        this._searchInProgress = false; // 🚀 新增：搜索进行中标志
    }

    /**
     * 初始化搜索管理器
     */
    initialize() {
        if (this.initialized) {
            console.warn('搜索管理器已经初始化过了');
            return;
        }

        this.setupEventListeners();
        this.initialized = true;

    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        const elements = domManager.getElements();

        // 搜索按钮点击事件
        if (elements.searchBtn) {
            elements.searchBtn.addEventListener('click', () => this.handleSearch());
        }

        // 输入框回车事件
        if (elements.userIdInput) {
            elements.userIdInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleSearch();
                }
            });

            // 输入提示
            elements.userIdInput.addEventListener('input', () => this.handleInputChange());
        }

        // 任务刷新按钮
        if (elements.refreshTasksBtn) {
            elements.refreshTasksBtn.addEventListener('click', () => this.loadAvailableTasks());
        }
    }

    /**
     * 处理输入变化，提供实时提示
     */
    handleInputChange() {
        const elements = domManager.getElements();
        const userId = elements.userIdInput?.value?.trim();
        const searchHint = elements.searchHint;
        
        if (!userId || !searchHint) {
            return;
        }
        
        const idType = this.detectIdType(userId);
        let hintText = '';
        
        switch (idType) {
            case 'digital_id':
                hintText = '已识别为数字ID，将通过数字ID搜索';
                searchHint.style.display = 'block';
                break;
            case 'member_id':
                hintText = '已识别为用户ID，将通过用户ID搜索';
                searchHint.style.display = 'block';
                break;
            default:
                hintText = '';
                searchHint.style.display = 'none';
        }
        
        if (hintText) {
            searchHint.innerHTML = `<small style="color: #007bff;">${hintText}</small>`;
        }
    }

    /**
     * 智能检测ID类型
     * @param {string} userId - 输入的用户ID
     * @returns {string} - 'digital_id', 'member_id', 或 'unknown'
     */
    detectIdType(userId) {
        if (!userId || typeof userId !== 'string') {
            return 'unknown';
        }
        
        const trimmedId = userId.trim();
        
        // 数字ID特征：纯数字，通常7-12位
        if (/^\d+$/.test(trimmedId) && trimmedId.length >= 7 && trimmedId.length <= 15) {
            return 'digital_id';
        }
        
        // 用户ID特征：32位16进制字符串
        if (/^[a-f0-9]{32}$/i.test(trimmedId)) {
            return 'member_id';
        }
        
        // 如果长度较短且包含数字和字母，可能是较短的digital_id (排除纯数字的情况)
        if (trimmedId.length >= 7 && trimmedId.length <= 10 && /^[a-zA-Z0-9]+$/.test(trimmedId) && !/^\d+$/.test(trimmedId)) {
            return 'digital_id';
        }
        
        // 如果长度较长且包含16进制字符，可能是member_id
        if (trimmedId.length > 20 && /^[a-f0-9-]+$/i.test(trimmedId)) {
            return 'member_id';
        }
        
        return 'unknown';
    }

    /**
     * 处理搜索 - 修复重复调用问题
     */
    async handleSearch() {
        // 🚀 修复：防止重复调用
        if (this._searchInProgress) {
            console.warn('搜索正在进行中，忽略重复调用');
            return;
        }
        
        const elements = domManager.getElements();
        const userId = elements.userIdInput?.value?.trim();
        
        if (!userId) {
            this.showError('请输入用户ID或数字ID');
            return;
        }
        
        this._searchInProgress = true;
        
        try {
            this.showLoading(true);
            this.hideError();
            this.hideResults();
            
            // 获取选中的任务ID
            const selectedTaskId = this.getSelectedTaskId();
            
            // 智能ID类型检测
            const idType = this.detectIdType(userId);
            let data, searchMethod;
            
            const userBehaviorService = pageManager.getUserBehaviorService();
            const userAnalysisService = pageManager.getUserAnalysisService();
            
            // 🚀 修复：智能数据获取，避免数据丢失
            let finalUserId = userId;
            let baseUserData = null; // 存储用户基础信息
            
            if (idType === 'digital_id') {
                // 对于数字ID，先获取完整的用户信息（包含用户画像和关联分析）
                try {
                    console.log(`🔍 获取数字ID用户信息: ${userId}`);
                    baseUserData = await userAnalysisService.searchUserAnalysisByDigitalId(userId, selectedTaskId, '');
                    const memberId = baseUserData.user_profile?.member_id;
                    if (memberId) {
                        finalUserId = memberId;
                        searchMethod = '数字ID搜索';
                        console.log(`✅ 数字ID转换成功: ${userId} → ${memberId}`);

                        // 🚀 保存关联分析数据，稍后合并到完整数据中
                        if (baseUserData.associations) {
                            console.log('🔗 保存关联分析数据用于后续合并:', baseUserData.associations);
                            this._savedAssociations = baseUserData.associations;
                        }
                    } else {
                        searchMethod = '数字ID搜索（直接）';
                        console.warn('⚠️ 数字ID转换未获取到member_id，使用原始ID');
                    }
                } catch (error) {
                    console.warn('⚠️ 数字ID转换失败，使用原始ID:', error.message);
                    searchMethod = '数字ID搜索（直接）';
                }
            } else {
                searchMethod = idType === 'member_id' ? '用户ID搜索' : '自动识别搜索';
            }
            
            // 🚀 修复：获取完整的行为分析数据（强制实时分析）
            console.log(`🔍 调用完整分析API: ${finalUserId}, taskId: ${selectedTaskId}, 强制实时分析: true`);
            const behaviorData = await userBehaviorService.getUserCompleteAnalysis(finalUserId, selectedTaskId, true);
            
            // 🚀 修复：合并数据，确保用户画像等信息不丢失
            if (baseUserData && baseUserData.user_profile) {
                // 将数字ID API的用户画像数据合并到完整分析数据中
                if (behaviorData.complete_data) {
                    // 合并用户画像信息
                    behaviorData.complete_data.user_profile = {
                        ...baseUserData.user_profile,
                        ...behaviorData.complete_data.user_profile,
                        search_method: searchMethod
                    };
                    
                    // 合并关联信息
                    if (baseUserData.associations) {
                        behaviorData.complete_data.user_associations = baseUserData.associations;
                    }
                    
                    // 合并交易摘要
                    if (baseUserData.transaction_summary) {
                        behaviorData.complete_data.transaction_summary = baseUserData.transaction_summary;
                    }
                    
                    // 合并风险分析
                    if (baseUserData.risk_analysis) {
                        behaviorData.complete_data.risk_analysis = baseUserData.risk_analysis;
                    }
                    
                    console.log('✅ 数据合并完成，用户画像信息已保留');
                }
            }
            
            data = behaviorData;
            
            // 验证数据完整性
            if (data && data.complete_data) {
                // 添加搜索方法标识
                if (data.user_id || data.complete_data.user_profile) {
                    const userProfile = data.complete_data.user_profile || {};
                    userProfile.search_method = searchMethod;
                }

                // 🚀 合并保存的关联分析数据
                if (this._savedAssociations && data.complete_data) {
                    console.log('🔗 合并关联分析数据到完整数据中');
                    data.complete_data.associations = this._savedAssociations;
                    console.log('✅ 关联分析数据合并完成:', this._savedAssociations);

                    // 清理临时数据
                    delete this._savedAssociations;
                }

                // 触发搜索成功事件
                this.onSearchSuccess(data);
                this.showResults();
            } else {
                throw new Error('返回数据格式不完整');
            }
            
        } catch (error) {
            console.error('搜索失败:', error);
            this.showError('搜索失败: ' + error.message);
            this.onSearchError(error);
            
        } finally {
            this.showLoading(false);
            this._searchInProgress = false; // 🚀 确保标志被重置
        }
    }

    /**
     * 获取选中的任务ID
     */
    getSelectedTaskId() {
        const elements = domManager.getElements();
        const contractTaskId = elements.contractTaskSelect?.value || '';
        const agentTaskId = elements.agentTaskSelect?.value || '';
        
        // 优先返回合约任务ID，如果没有则返回代理任务ID
        return contractTaskId || agentTaskId || '';
    }

    /**
     * 处理旧的分步搜索逻辑（回退方案）
     */
    async handleLegacySearch(userId, idType) {
        const elements = domManager.getElements();
        const contractTaskId = elements.contractTaskSelect?.value || '';
        const agentTaskId = elements.agentTaskSelect?.value || '';
        
        let data;
        let searchMethod;
        const userAnalysisService = pageManager.getUserAnalysisService();
        
        if (idType === 'digital_id') {
            data = await userAnalysisService.searchUserAnalysisByDigitalId(userId, contractTaskId, agentTaskId);
            searchMethod = '数字ID搜索（兼容模式）';
        } else if (idType === 'member_id') {
            data = await userAnalysisService.searchUserAnalysis(userId, contractTaskId, agentTaskId);
            searchMethod = '用户ID搜索（兼容模式）';
        } else {
            // 未知类型，先尝试数字ID搜索，如果失败则尝试用户ID搜索
            try {
                data = await userAnalysisService.searchUserAnalysisByDigitalId(userId, contractTaskId, agentTaskId);
                searchMethod = '数字ID搜索（自动尝试）';
            } catch (digitalIdError) {
                data = await userAnalysisService.searchUserAnalysis(userId, contractTaskId, agentTaskId);
                searchMethod = '用户ID搜索（自动尝试）';
            }
        }
        
        // 添加搜索方法标识
        if (data.user_profile) {
            data.user_profile.search_method = searchMethod;
        }
        
        // 触发搜索成功事件
        this.onSearchSuccess(data);
    }

    /**
     * 搜索成功回调（由外部设置）
     */
    onSearchSuccess(data) {
        // 这个方法会被外部设置具体的处理逻辑
        console.log('搜索成功，数据:', data);
    }

    /**
     * 搜索错误回调（由外部设置）
     */
    onSearchError(error) {
        // 这个方法会被外部设置具体的处理逻辑
        console.error('搜索错误:', error);
    }

    /**
     * 设置搜索成功回调
     */
    setSearchSuccessCallback(callback) {
        this.onSearchSuccess = callback;
    }

    /**
     * 加载可用的分析任务
     */
    async loadAvailableTasks() {
        try {
            const elements = domManager.getElements();
            
            // 显示加载状态
            this.showLoading(true);
            
            // 检查服务对象
            const userAnalysisService = pageManager.getUserAnalysisService();
            if (!userAnalysisService) {
                throw new Error('用户分析服务对象未初始化');
            }
            
            // 检查DOM元素
            if (!elements.contractTaskSelect || !elements.agentTaskSelect) {
                throw new Error('DOM元素未找到');
            }
            
            // 调用API
            const data = await userAnalysisService.getAvailableTasks();
            
            // 验证数据格式
            if (!data || typeof data !== 'object') {
                throw new Error('API返回数据格式不正确');
            }
            
            // 更新合约任务选择器
            this.updateTaskSelect(elements.contractTaskSelect, data.contract_tasks, '选择合约风险分析任务');
            
            // 更新代理任务选择器
            this.updateTaskSelect(elements.agentTaskSelect, data.agent_tasks, '选择代理关系分析任务');
            
            // 隐藏加载状态
            this.showLoading(false);
            
        } catch (error) {
            console.error('=== 任务列表加载失败 ===');
            console.error('错误详情:', error);
            
            this.showLoading(false);
            this.showError('加载任务列表失败: ' + error.message);
        }
    }

    /**
     * 更新任务选择器
     */
    updateTaskSelect(selectElement, tasks, defaultText) {
        if (!selectElement) {
            console.error('选择器元素不存在');
            return;
        }
        
        // 清空现有选项
        selectElement.innerHTML = `<option value="">${defaultText}...</option>`;
        
        // 添加任务选项
        if (tasks && Array.isArray(tasks)) {
            tasks.forEach(task => {
                const option = document.createElement('option');
                option.value = task.task_id;
                // 修复：使用正确的字段名，优先使用name，回退到filename
                const taskName = task.name || task.filename || task.task_name || `任务_${task.task_id?.substring(0, 8)}`;
                option.textContent = `${taskName} (${new Date(task.created_at).toLocaleDateString()})`;
                selectElement.appendChild(option);
            });
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        domManager.toggleElement('loadingIndicator', show);
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        console.error('显示错误:', message);
        const elements = domManager.getElements();
        if (elements.errorMessage) {
            elements.errorMessage.textContent = message;
            elements.errorMessage.style.display = 'block';
            
            // 3秒后自动隐藏错误信息
            setTimeout(() => {
                elements.errorMessage.style.display = 'none';
            }, 3000);
        }
    }

    /**
     * 隐藏错误信息
     */
    hideError() {
        domManager.toggleElement('errorMessage', false);
    }

    /**
     * 显示分析结果
     */
    showResults() {
        const elements = domManager.getElements();
        if (elements.analysisResults) {
            elements.analysisResults.style.display = 'block';
            // 强制重新渲染
            elements.analysisResults.offsetHeight;
        }
    }

    /**
     * 隐藏分析结果
     */
    hideResults() {
        domManager.toggleElement('analysisResults', false);
    }

    /**
     * 获取当前搜索的任务ID
     */
    getCurrentTaskIds() {
        const elements = domManager.getElements();
        return {
            contractTaskId: elements.contractTaskSelect?.value || '',
            agentTaskId: elements.agentTaskSelect?.value || ''
        };
    }

    /**
     * 重置搜索管理器
     */
    reset() {
        // 🚀 修复：重置搜索状态标志
        this._searchInProgress = false;
        
        // 重置初始化状态
        this.initialized = false;
        
        // 清理UI状态
        this.hideError();
        this.hideResults();
    }
}

// 创建单例实例
const searchManager = new SearchManager();

export default searchManager; 
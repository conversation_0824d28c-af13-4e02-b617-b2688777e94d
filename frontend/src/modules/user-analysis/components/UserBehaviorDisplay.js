/**
 * 用户行为分析显示组件
 * 负责显示专业度评分、交易指标、币种分析、风险画像等
 */

import { 
    formatNumber, 
    formatVolume, 
    getScoreLevel, 
    getScoreBadgeClass,
    getRiskLevel,
    getRiskLevelClass,
    getScoreLevelClass,
    getTraderType,
    getTraderTypeClass,
    getFundScale,
    getFundScaleClass,
    formatTime,
    updateProgressBar,
    updateScoreBar
} from './utils.js';
import ProfessionalScoreGauge from './ProfessionalScoreGauge.js';
import CoinWinRateAnalysis from './CoinWinRateAnalysis.js';

class UserBehaviorDisplay {
    constructor(domManager) {
        this.elements = domManager.elements;
        this.scoreGauge = null;
        this.coinAnalysis = null;
        this.currentBehaviorData = null;
        
        this.initializeComponents();
    }

    /**
     * 初始化组件
     */
    initializeComponents() {
        // 初始化专业度评分仪表盘
        if (this.elements.professionalScoreGauge) {
            this.scoreGauge = new ProfessionalScoreGauge('professionalScoreGauge');
        }

        // 初始化币种分析组件
        if (document.getElementById('coinAnalysisContainer')) {
            this.coinAnalysis = new CoinWinRateAnalysis('coinAnalysisContainer');
        }
    }

    /**
     * 🔧 废弃：显示用户行为分析结果 - 复杂的数据映射逻辑
     * 注释原因：改为使用DirectFieldMapper直接映射，避免复杂的数据处理
     */
    // display(behaviorData) {
    //     if (!behaviorData) {
    //         console.warn('用户行为分析数据为空');
    //         this.showError('暂无用户行为分析数据');
    //         return;
    //     }

    //     this.currentBehaviorData = behaviorData;

    //     try {
    //         // 显示专业度评分
    //         if (behaviorData.professional_scores) {
    //             this.displayProfessionalScores(behaviorData.professional_scores);
    //         }

    //         // 显示交易指标
    //         if (behaviorData.basic_metrics) {
    //             this.displayTradingMetrics(behaviorData.basic_metrics);
    //         }

    //         // 显示币种分析摘要
    //         if (behaviorData.coin_analysis_summary) {
    //             this.displayCoinAnalysisSummary(behaviorData.coin_analysis_summary);
    //         }

    //         // 显示数据质量信息
    //         this.displayDataQualityInfo(behaviorData);

    //         // 显示分析报告
    //         if (behaviorData.detailed_report) {
    //             this.displayAnalysisReport(behaviorData.detailed_report);
    //         }

    //         // 显示衍生指标
    //         if (behaviorData.derived_analysis || behaviorData.derived_metrics) {
    //             this.displayDerivedMetrics(behaviorData.derived_analysis || behaviorData.derived_metrics);
    //         }

    //         // 显示交易偏好
    //         if (behaviorData.trading_preferences) {
    //             this.displayTradingPreferences(behaviorData.trading_preferences);
    //         }

    //         // 显示异常交易分析
    //         if (behaviorData.abnormal_analysis) {
    //             this.displayAbnormalAnalysis(behaviorData.abnormal_analysis);
    //         }

    //         // 显示对冲分析（已移除）
    //         // if (behaviorData.hedge_data) {
    //         //     this.displayHedgeAnalysis(behaviorData.hedge_data);
    //         // }

    //         // 填充基础指标的详细数据
    //         this.fillAdditionalBasicMetrics(behaviorData);

    //         // 填充订单类型指标
    //         this.fillOrderTypeMetrics(behaviorData);

    //         // 填充杠杆指标
    //         this.fillLeverageMetrics(behaviorData);

    //         // 填充资金规模分布
    //         this.fillFundScaleDistribution(behaviorData);

    //         // 填充其他缺失元素
    //         this.fillOtherMissingElements(behaviorData);

    //     } catch (error) {
    //         console.error('显示用户行为分析结果时出错:', error);
    //         this.showError('显示分析结果时出错: ' + error.message);
    //     }
    // }

    /**
     * 🔧 废弃：填充基础指标的详细数据
     * 注释原因：改为使用DirectFieldMapper直接映射，避免重复的字段更新
     */
    // fillAdditionalBasicMetrics(behaviorData) {
    //     const metrics = behaviorData.basic_metrics || {};
    //     const derivedMetrics = behaviorData.derived_metrics || {};
    //     const extendedMetrics = behaviorData.basic_metrics_extended || {};

    //     // 🚀 直接使用后端user_trading_profiles表的数据，不进行前端计算
    //     // 最大最小交易规模 - 直接从后端数据获取
    //     this.updateElementText('maxTradeSize', this.formatNumber(metrics.max_trade_size || 0) + ' USDT');
    //     this.updateElementText('maxTradeContract', metrics.max_trade_contract || '暂无数据');
    //     this.updateElementText('minTradeSize', this.formatNumber(metrics.min_trade_size || 0) + ' USDT');
    //     this.updateElementText('minTradeContract', metrics.min_trade_contract || '暂无数据');

    //     // 盈亏统计详细数据 - 直接使用后端计算好的数据
    //     this.updateElementText('profitTrades', formatNumber(metrics.profitable_count || 0) + ' 笔');
    //     this.updateElementText('lossTrades', formatNumber(metrics.loss_count || 0) + ' 笔');
    //     this.updateElementText('totalProfitAmount', formatVolume(metrics.total_profit || 0) + ' USDT');
    //     this.updateElementText('totalLossAmount', formatVolume(Math.abs(metrics.total_loss || 0)) + ' USDT');
    //     this.updateElementText('totalPnL', formatVolume(metrics.total_pnl || 0) + ' USDT');
    //     this.updateElementText('totalFees', formatVolume(metrics.total_commission || 0) + ' USDT');

    //     // 🚀 直接使用后端计算好的比例数据
    //     this.updateElementText('profitTradesRatio', this.formatPercentage(metrics.profit_trades_ratio || 0));
    //     this.updateElementText('lossTradesRatio', this.formatPercentage(metrics.loss_trades_ratio || 0));
    //     this.updateElementText('avgProfitPerTrade', formatVolume(metrics.avg_profit_per_trade || 0) + ' USDT');
    //     this.updateElementText('avgLossPerTrade', formatVolume(Math.abs(metrics.avg_loss_per_trade || 0)) + ' USDT');
    //     this.updateElementText('returnRate', this.formatPercentage(metrics.return_rate || 0));
    //     this.updateElementText('feeRatio', this.formatPercentage(metrics.fee_ratio || 0));

    //     // 🚀 持仓时间分析 - 直接使用后端数据
    //     this.updateElementText('avgProfitDuration', this.formatDuration(metrics.avg_profit_duration_minutes || 0));
    //     this.updateElementText('avgProfitDurationMinutes', (metrics.avg_profit_duration_minutes || 0) + ' 分钟');
    //     this.updateElementText('avgLossDuration', this.formatDuration(metrics.avg_loss_duration_minutes || 0));
    //     this.updateElementText('avgLossDurationMinutes', (metrics.avg_loss_duration_minutes || 0) + ' 分钟');

    //     // 盈亏时长比 - 直接使用后端数据
    //     this.updateElementText('durationRatio', this.formatRatio(metrics.profit_loss_duration_ratio || 0));
    //     this.updateElementText('durationRatioDescription', this.getDurationRatioDescription(metrics.profit_loss_duration_ratio || 0));

    //     // 最长最短持仓时间 - 直接使用后端数据
    //     this.updateElementText('maxHoldingTime', this.formatDuration(metrics.max_holding_time || 0));
    //     this.updateElementText('maxHoldingContract', metrics.max_holding_contract || '暂无数据');
    //     this.updateElementText('minHoldingTime', this.formatDuration(metrics.min_holding_time || 0));
    //     this.updateElementText('minHoldingDescription', this.getMinHoldingDescription(metrics.min_holding_time || 0));

    //     // 交易频率 - 直接使用后端数据
    //     this.updateElementText('tradingFrequency', this.formatFrequency(metrics.trading_frequency || 0));
    //     this.updateElementText('frequencyDescription', this.getFrequencyDescription(metrics.trading_frequency || 0));
    // }

    /**
     * 🔧 废弃：填充订单类型指标 - 直接使用后端数据
     * 注释原因：改为使用DirectFieldMapper直接映射，避免重复的字段更新
     */
    // fillOrderTypeMetrics(behaviorData) {
    //     const metrics = behaviorData.basic_metrics || {};

    //     // 🚀 直接使用后端计算好的订单类型数据 - 修正元素ID匹配HTML
    //     this.updateElementText('marketOrderRatio', this.formatPercentage(metrics.market_orders_ratio || 0));
    //     this.updateElementText('limitOrderRatio', this.formatPercentage(metrics.limit_orders_ratio || 0));

    //     // 详细的开仓平仓订单类型分布 - 直接使用后端数据
    //     this.updateElementText('openMarketOrders', formatNumber(metrics.open_market_orders || 0) + ' 笔');
    //     this.updateElementText('openLimitOrders', formatNumber(metrics.open_limit_orders || 0) + ' 笔');
    //     this.updateElementText('closeMarketOrders', formatNumber(metrics.close_market_orders || 0) + ' 笔');
    //     this.updateElementText('closeLimitOrders', formatNumber(metrics.close_limit_orders || 0) + ' 笔');

    //     // 计算详细比例 - 基于后端数据进行简单格式化
    //     const totalOpenOrders = (metrics.open_market_orders || 0) + (metrics.open_limit_orders || 0);
    //     const totalCloseOrders = (metrics.close_market_orders || 0) + (metrics.close_limit_orders || 0);

    //     this.updateElementText('openMarketRatio', totalOpenOrders > 0 ?
    //         this.formatPercentage((metrics.open_market_orders || 0) / totalOpenOrders) : '0%');
    //     this.updateElementText('openLimitRatio', totalOpenOrders > 0 ?
    //         this.formatPercentage((metrics.open_limit_orders || 0) / totalOpenOrders) : '0%');
    //     this.updateElementText('closeMarketRatio', totalCloseOrders > 0 ?
    //         this.formatPercentage((metrics.close_market_orders || 0) / totalCloseOrders) : '0%');
    //     this.updateElementText('closeLimitRatio', totalCloseOrders > 0 ?
    //         this.formatPercentage((metrics.close_limit_orders || 0) / totalCloseOrders) : '0%');

    //     // 订单类型描述 - 基于后端数据
    //     this.updateElementText('marketOrderDescription', this.getMarketOrderDescription(metrics.market_orders_ratio || 0));
    //     this.updateElementText('limitOrderDescription', this.getLimitOrderDescription(metrics.limit_orders_ratio || 0));
    // }

    /**
     * 🔧 废弃：填充杠杆指标 - 直接使用后端数据
     * 注释原因：改为使用DirectFieldMapper直接映射，避免重复的字段更新
     */
    // fillLeverageMetrics(behaviorData) {
    //     const metrics = behaviorData.basic_metrics || {};

    //     // 🚀 杠杆指标 - 直接使用后端数据，修正元素ID匹配HTML
    //     this.updateElementText('avgLeverageMetric', this.formatNumber(metrics.avg_leverage || 0) + 'x');
    //     this.updateElementText('maxLeverage', this.formatNumber(metrics.max_leverage || 0) + 'x');
    //     this.updateElementText('leverageStability', this.formatPercentage(metrics.leverage_stability || 0));
    //     this.updateElementText('maxSingleLoss', formatVolume(Math.abs(metrics.max_single_loss || 0)) + ' USDT');

    //     // 杠杆描述 - 基于后端数据
    //     this.updateElementText('avgLeverageDescription', this.getAvgLeverageDescription(metrics.avg_leverage || 0));
    //     this.updateElementText('maxLeverageDescription', this.getMaxLeverageDescription(metrics.max_leverage || 0));
    //     this.updateElementText('leverageStabilityDescription', this.getLeverageStabilityDescription(metrics.leverage_stability || 0));
    //     this.updateElementText('maxLossDescription', this.getMaxLossDescription(Math.abs(metrics.max_single_loss || 0)));

    //     // 🚀 杠杆分布 - 直接使用后端数据，HTML中只有低、中杠杆，没有高杠杆元素
    //     this.updateElementText('lowLeverageTrades', formatNumber(metrics.low_leverage_trades || 0) + ' 笔');
    //     this.updateElementText('mediumLeverageTrades', formatNumber(metrics.medium_leverage_trades || 0) + ' 笔');
    //     // 注意：HTML中没有highLeverageTrades元素，跳过

    //     // 杠杆分布比例 - 基于后端数据计算
    //     const totalLeverageTrades = (metrics.low_leverage_trades || 0) +
    //                                (metrics.medium_leverage_trades || 0) +
    //                                (metrics.high_leverage_trades || 0);

    //     this.updateElementText('lowLeverageRatio', totalLeverageTrades > 0 ?
    //         this.formatPercentage((metrics.low_leverage_trades || 0) / totalLeverageTrades) : '0%');
    //     this.updateElementText('mediumLeverageRatio', totalLeverageTrades > 0 ?
    //         this.formatPercentage((metrics.medium_leverage_trades || 0) / totalLeverageTrades) : '0%');
    //     // 注意：HTML中没有highLeverageRatio元素，跳过
    // }

    /**
     * 🔧 废弃：填充资金规模分布 - 直接使用后端数据
     * 注释原因：改为使用DirectFieldMapper直接映射，避免重复的字段更新
     */
    // fillFundScaleDistribution(behaviorData) {
    //     const metrics = behaviorData.basic_metrics || {};

    //     // 🚀 资金规模分析 - 直接使用后端数据，修正元素ID匹配HTML
    //     // 🔧 注释：fundScaleMetric 和 realTradingVolume 由DirectFieldMapper处理，避免冲突
    //     // this.updateElementText('fundScaleMetric', metrics.fund_scale_category || '数据不足');
    //     // this.updateElementText('realTradingVolume', formatVolume(metrics.real_trading_volume || 0) + ' USDT');
    //     this.updateElementText('fundScaleRange', this.getFundScaleRange(metrics.fund_scale_category || ''));

    //     // 🚀 交易规模分布 - 直接使用后端数据
    //     this.updateElementText('smallTrades', formatNumber(metrics.small_trades || 0) + ' 笔');
    //     this.updateElementText('mediumTrades', formatNumber(metrics.medium_trades || 0) + ' 笔');
    //     this.updateElementText('largeTrades', formatNumber(metrics.large_trades || 0) + ' 笔');

    //     // 计算交易规模分布比例 - 基于后端数据
    //     const totalTrades = (metrics.small_trades || 0) + (metrics.medium_trades || 0) + (metrics.large_trades || 0);

    //     this.updateElementText('smallTradesRatio', totalTrades > 0 ?
    //         this.formatPercentage((metrics.small_trades || 0) / totalTrades) : '0%');
    //     this.updateElementText('mediumTradesRatio', totalTrades > 0 ?
    //         this.formatPercentage((metrics.medium_trades || 0) / totalTrades) : '0%');
    //     this.updateElementText('largeTradesRatio', totalTrades > 0 ?
    //         this.formatPercentage((metrics.large_trades || 0) / totalTrades) : '0%');

    //     // 持仓一致性 - 直接使用后端数据
    //     this.updateElementText('positionConsistency', this.formatPercentage(metrics.position_consistency || 0));
    //     this.updateElementText('positionConsistencyDescription', this.getPositionConsistencyDescription(metrics.position_consistency || 0));
    // }

    /**
     * 🔧 废弃：填充其他缺失元素 - 直接使用后端数据
     * 注释原因：改为使用DirectFieldMapper直接映射，避免重复的字段更新
     */
    // fillOtherMissingElements(behaviorData) {
    //     const metrics = behaviorData.basic_metrics || {};

    //     // 🚀 注意：这些高级分析指标在基础指标区域没有对应的HTML元素，跳过
    //     // timingAbility, riskDiscipline, executionEfficiency 在衍生指标中已处理

    //     // 总交易天数和频率 - 注意HTML中可能没有totalTradingDays元素
    //     // this.updateElementText('totalTradingDays', formatNumber(metrics.total_trading_days || 0) + ' 天');

    //     // 🔧 注释：资金规模类别由DirectFieldMapper处理，避免冲突
    //     // this.updateElementText('fundScale', metrics.fund_scale_category || '数据不足');

    //     // 交易频率在交易指标中显示
    //     this.updateElementText('tradingFrequency', this.formatFrequency(metrics.trading_frequency || 0));
    //     this.updateElementText('frequencyDescription', this.getFrequencyDescription(metrics.trading_frequency || 0));
    // }

    /**
     * 显示衍生分析指标 - 直接使用后端数据
     */
    displayDerivedMetrics(derivedData) {
        if (!derivedData) {
            this.showDataNotAvailable();
            return;
        }

        // 🚀 盈利能力指标 - 直接使用后端数据
        this.fillProfitabilityMetrics(derivedData.profitability || {});
        
        // 🚀 风险控制指标 - 直接使用后端数据
        this.fillRiskControlMetrics(derivedData.risk_control || {});
        
        // 🚀 交易行为指标 - 直接使用后端数据
        this.fillTradingBehaviorMetrics(derivedData.trading_behavior || {});
        
        // 🚀 市场理解指标 - 直接使用后端数据
        this.fillMarketUnderstandingMetrics(derivedData.market_understanding || {});
    }

    /**
     * 填充盈利能力指标 - 直接使用后端数据
     */
    fillProfitabilityMetrics(profitability) {
        // 🚀 直接使用后端计算好的评分数据 - 修正元素ID匹配HTML
        this.updateElementText('derivedWinRateScore', '+' + (profitability.win_rate_score || 0) + '分');
        this.updateElementText('derivedProfitLossRatioScore', '+' + (profitability.profit_loss_ratio_score || 0) + '分');
        this.updateElementText('derivedProfitFactorScore', '+' + (profitability.profit_factor_score || 0) + '分');
        this.updateElementText('derivedProfitConsistencyScore', '+' + (profitability.profit_consistency_score || 0) + '分');
        
        // 🚀 同时填充对应的数值显示
        this.updateElementText('derivedWinRate', this.formatPercentage(profitability.win_rate || 0));
        this.updateElementText('derivedProfitLossRatio', this.formatRatio(profitability.profit_loss_ratio || 0));
        this.updateElementText('derivedProfitFactor', this.formatRatio(profitability.profit_factor || 0));
        this.updateElementText('derivedProfitConsistency', this.formatPercentage(profitability.profit_consistency || 0));
    }

    /**
     * 填充风险控制指标 - 直接使用后端数据
     */
    fillRiskControlMetrics(riskControl) {
        // 🚀 直接使用后端计算好的评分数据 - 修正元素ID匹配HTML
        this.updateElementText('derivedAvgLeverageScore', '+' + (riskControl.avg_leverage_score || 0) + '分');
        this.updateElementText('derivedMaxLeverageScore', '+' + (riskControl.max_leverage_score || 0) + '分');
        this.updateElementText('derivedLeverageStabilityScore', '+' + (riskControl.leverage_stability_score || 0) + '分');
        this.updateElementText('derivedMaxSingleLossScore', '+' + (riskControl.max_single_loss_score || 0) + '分');
        
        // 🚀 同时填充对应的数值显示
        this.updateElementText('derivedAvgLeverage', this.formatNumber(riskControl.avg_leverage || 0) + 'x');
        this.updateElementText('derivedMaxLeverage', this.formatNumber(riskControl.max_leverage || 0) + 'x');
        this.updateElementText('derivedLeverageStability', this.formatPercentage(riskControl.leverage_stability || 0));
        this.updateElementText('derivedMaxSingleLoss', this.formatPercentage(riskControl.max_single_loss_ratio || 0));
    }

    /**
     * 填充交易行为指标 - 直接使用后端数据
     */
    fillTradingBehaviorMetrics(tradingBehavior) {
        // 🚀 直接使用后端计算好的评分数据 - 修正元素ID匹配HTML
        this.updateElementText('derivedTradingFrequencyScore', '+' + (tradingBehavior.trading_frequency_score || 0) + '分');
        this.updateElementText('derivedMarketOrderRatioScore', '+' + (tradingBehavior.market_order_ratio_score || 0) + '分');
        this.updateElementText('derivedDurationRatioScore', '+' + (tradingBehavior.duration_ratio_score || 0) + '分');
        this.updateElementText('derivedPositionConsistencyScore', '+' + (tradingBehavior.position_consistency_score || 0) + '分');
        
        // 🚀 同时填充对应的数值显示
        this.updateElementText('derivedTradingFrequency', this.formatFrequency(tradingBehavior.trading_frequency || 0));
        this.updateElementText('derivedMarketOrderRatio', this.formatPercentage(tradingBehavior.market_order_ratio || 0));
        this.updateElementText('derivedDurationRatio', this.formatRatio(tradingBehavior.duration_ratio || 0));
        this.updateElementText('derivedPositionConsistency', this.formatPercentage(tradingBehavior.position_consistency || 0));
    }

    /**
     * 填充市场理解指标 - 直接使用后端数据
     */
    fillMarketUnderstandingMetrics(marketUnderstanding) {
        // 🚀 直接使用后端计算好的评分数据 - 修正元素ID匹配HTML
        this.updateElementText('derivedTimingAbilityScore', '+' + (marketUnderstanding.timing_ability_score || 0) + '分');
        this.updateElementText('derivedRiskDisciplineScore', '+' + (marketUnderstanding.risk_discipline_score || 0) + '分');
        this.updateElementText('derivedExecutionEfficiencyScore', '+' + (marketUnderstanding.execution_efficiency_score || 0) + '分');
        
        // 🚀 同时填充对应的数值显示
        this.updateElementText('derivedTimingAbility', this.formatPercentage(marketUnderstanding.timing_ability || 0));
        this.updateElementText('derivedRiskDiscipline', this.formatPercentage(marketUnderstanding.risk_discipline || 0));
        this.updateElementText('derivedExecutionEfficiency', this.formatPercentage(marketUnderstanding.execution_efficiency || 0));
    }

    /**
     * 显示异常交易分析 - 直接使用后端数据
     */
    displayAbnormalAnalysis(abnormalData) {
        if (!abnormalData) {
            console.warn('异常交易分析数据为空');
            return;
        }

        // 🚀 异常交易总量 - 直接使用后端数据
        this.updateElementText('totalAbnormalVolume', formatVolume(abnormalData.abnormal_volume || 0) + ' USDT');
        
        // 🚀 异常交易比例 - 直接使用后端数据
        this.updateElementText('totalAbnormalRatio', this.formatPercentage(abnormalData.abnormal_ratio || 0));

        // 🚀 各类异常交易详情 - 直接使用后端数据，修正元素ID匹配HTML
        this.updateElementText('washTradingVolume', formatVolume(abnormalData.wash_trading_volume || 0) + ' USDT');
        this.updateElementText('highFreqVolume', formatVolume(abnormalData.high_frequency_volume || 0) + ' USDT');
        this.updateElementText('fundingArbitrageVolume', formatVolume(abnormalData.funding_arbitrage_volume || 0) + ' USDT');

        // 🚀 风险事件数量 - 直接使用后端数据
        this.updateElementText('riskEventsCount', formatNumber(abnormalData.risk_events_count || 0) + ' 个');
        
        // 🚀 补充其他异常数据
        this.updateElementText('washTradingCount', formatNumber(abnormalData.wash_trading_count || 0) + ' 笔');
        this.updateElementText('highFreqCount', formatNumber(abnormalData.high_frequency_count || 0) + ' 笔');
        this.updateElementText('fundingArbitrageCount', formatNumber(abnormalData.funding_arbitrage_count || 0) + ' 笔');
        
        // 异常交易比例 - 使用详细格式化（保留2位小数）
        this.updateElementText('washTradingRatio', this.formatPercentageDetailed(abnormalData.wash_trading_ratio || 0));
        this.updateElementText('highFreqRatio', this.formatPercentageDetailed(abnormalData.high_frequency_ratio || 0));
        this.updateElementText('fundingArbitrageRatio', this.formatPercentageDetailed(abnormalData.funding_arbitrage_ratio || 0));
    }

    /**
     * 显示交易偏好 - 直接使用后端数据
     */
    displayTradingPreferences(preferences) {


        if (!preferences) {
            console.warn('❌ 交易偏好数据为空');
            return;
        }

        // 🚀 币种偏好 - 修复字段名匹配和百分比格式化
        this.updateElementText('mainstreamPercentage', this.formatDirectPercentage(preferences.mainstream_percentage || 0));
        this.updateElementText('altcoinPercentage', this.formatDirectPercentage(preferences.altcoin_percentage || 0));
        this.updateElementText('defiPercentage', this.formatDirectPercentage(preferences.defi_percentage || 0));
        this.updateElementText('othersPercentage', this.formatDirectPercentage(preferences.others_percentage || 0));

        // 🚀 风险偏好 - 直接使用后端数据
        this.updateElementText('riskAppetiteLevel', preferences.risk_appetite_level || '中等');
        this.updateElementText('volatilityPreference', this.formatPercentage(preferences.volatility_preference || 0));

        // 🚀 分散化评分 - 直接使用后端数据
        this.updateElementText('diversificationScore', this.formatPercentage(preferences.diversification_score || 0));

        // 🚀 偏好合约 - 直接使用后端数据
        const favoriteContracts = preferences.favorite_contracts;
        if (favoriteContracts && favoriteContracts.length > 0) {
            this.updateElementText('favoriteContracts', favoriteContracts.join(', '));
        } else {
            this.updateElementText('favoriteContracts', '暂无偏好合约');
        }

        // 🚀 交易时间偏好 - 使用新的24小时热力图格式，支持多种字段名
        const peakHours = preferences.peak_trading_hours || preferences.peak_hours;


        if (peakHours) {
            this.generateTimeHeatmapFromPeakHours(peakHours);
            this.updatePeakHoursText(peakHours);
        } else {

            this.updateElementText('peakHours', '暂无数据');
        }

        // 更新币种偏好图表
        this.updateCoinPreferences(preferences);
    }

    /**
     * 显示对冲分析 - 已移除
     * 对冲功能已被完全移除，如需恢复请参考版本控制历史
     */
    // displayHedgeAnalysis(hedgeData) {
    //     // 对冲分析功能已移除
    // }

    /**
     * 生成时间热力图
     */
    generateTimeHeatmap(timeDistribution) {
        const container = document.getElementById('timeHeatmapGrid');
        if (!container) return;

        container.innerHTML = '';
        
        // 如果没有传入时间分布数据，尝试从扩展指标中获取
        if (!timeDistribution || Object.keys(timeDistribution).length === 0) {
            const behaviorData = this.getCurrentBehaviorData();
            timeDistribution = behaviorData?.basic_metrics_extended?.time_distribution;
        }
        
        if (!timeDistribution || Object.keys(timeDistribution).length === 0) {
            container.innerHTML = '<div class="no-data-placeholder">数据不足，无法计算</div>';
            return;
        }
        
        // 检查是否有有效数据
        const hasData = Object.values(timeDistribution).some(value => value > 0);
        if (!hasData) {
            container.innerHTML = '<div class="no-data-placeholder">[数据为空] 交易时间分布数据</div>';
            return;
        }
        
        // 生成24小时的热力图格子
        for (let hour = 0; hour < 24; hour++) {
            const cell = document.createElement('div');
            cell.className = 'time-cell';
            
            // 获取该小时的交易强度（0-1）
            const intensity = timeDistribution[hour];
            
            if (intensity !== undefined) {
                // 根据强度设置背景色深度
                const opacity = Math.min(1, intensity);
                cell.style.backgroundColor = `rgba(74, 144, 226, ${opacity})`;
                cell.title = `${hour}:00 - 交易强度: ${(intensity * 100).toFixed(1)}%`;
            } else {
                // 没有数据的时间段
                cell.style.backgroundColor = '#f0f0f0';
                cell.style.border = '1px dashed #ccc';
                cell.title = `${hour}:00 - 无数据`;
                cell.textContent = '--';
                cell.classList.add('no-data');
            }
            
            container.appendChild(cell);
        }
    }

    /**
     * 🚀 新增：根据 peak_trading_hours 数据生成热力图（demo样式）
     */
    generateTimeHeatmapFromPeakHours(peakHoursData) {


        const container = document.getElementById('timeHeatmapGrid');
        if (!container) {
            console.error('❌ 找不到 timeHeatmapGrid 容器元素');
            return;
        }

        container.innerHTML = '';

        // 解析 peak_trading_hours 数据
        let hourlyData = {};
        try {
            if (typeof peakHoursData === 'string') {
                hourlyData = JSON.parse(peakHoursData);

            } else if (typeof peakHoursData === 'object') {
                hourlyData = peakHoursData;

            }
        } catch (e) {
            console.error('❌ 解析 peak_trading_hours 数据失败:', e);
            container.innerHTML = '<div class="no-data-placeholder">数据格式错误</div>';
            return;
        }

        // 检查是否有有效数据
        const totalTrades = Object.values(hourlyData).reduce((sum, count) => sum + parseInt(count || 0), 0);
        if (totalTrades === 0) {
            container.innerHTML = '<div class="no-data-placeholder">暂无交易时间数据</div>';
            return;
        }

        // 计算交易强度等级
        const maxTrades = Math.max(...Object.values(hourlyData).map(count => parseInt(count || 0)));

        // 生成24小时的热力图格子（demo样式）
        for (let hour = 0; hour < 24; hour++) {
            const cell = document.createElement('div');
            const tradeCount = parseInt(hourlyData[hour.toString()] || 0);

            // 根据交易次数设置样式等级（参考demo页面）
            let cellClass = 'hour-cell';
            if (tradeCount === 0) {
                cellClass += ' empty';
            } else if (tradeCount <= maxTrades * 0.3) {
                cellClass += ' low';
            } else if (tradeCount <= maxTrades * 0.7) {
                cellClass += ' medium';
            } else {
                cellClass += ' high';
            }

            cell.className = cellClass;
            cell.title = `${hour.toString().padStart(2, '0')}:00-${((hour + 1) % 24).toString().padStart(2, '0')}:00 (${tradeCount}笔)`;

            container.appendChild(cell);
        }
    }

    /**
     * 🚀 新增：更新主要交易时段文本
     */
    updatePeakHoursText(peakHoursData) {
        try {
            let hourlyData = {};
            if (typeof peakHoursData === 'string') {
                hourlyData = JSON.parse(peakHoursData);
            } else if (typeof peakHoursData === 'object') {
                hourlyData = peakHoursData;
            }

            // 找出交易次数最多的时段
            const sortedHours = Object.entries(hourlyData)
                .map(([hour, count]) => ({ hour: parseInt(hour), count: parseInt(count || 0) }))
                .filter(item => item.count > 0)
                .sort((a, b) => b.count - a.count);

            if (sortedHours.length === 0) {
                this.updateElementText('peakHours', '暂无交易数据');
                return;
            }

            // 取前3个最活跃时段
            const topHours = sortedHours.slice(0, 3);
            const peakHoursText = topHours.map(item => {
                const startHour = item.hour.toString().padStart(2, '0');
                const endHour = ((item.hour + 1) % 24).toString().padStart(2, '0');
                return `${startHour}:00-${endHour}:00`;
            }).join(', ');

            this.updateElementText('peakHours', peakHoursText);

        } catch (e) {
            console.error('更新主要交易时段失败:', e);
            this.updateElementText('peakHours', '数据解析失败');
        }
    }

    /**
     * 生成对冲详情表格 - 已移除
     * 对冲功能已被完全移除，如需恢复请参考版本控制历史
     */
    // generateHedgeDetailsTable(hedgeDetails) {
    //     // 对冲详情表格功能已移除
    // }

    /**
     * 安全更新元素文本内容
     */
    updateElementText(elementId, text) {
        try {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = text;
            } else {
                // 元素不存在（可能已被移除）
            }
        } catch (error) {
            console.error(`更新元素 ${elementId} 时出错:`, error);
        }
    }

    /**
     * 获取盈亏时长比描述
     */
    getDurationRatioDescription(ratio) {
        if (ratio > 1.5) return '盈利持仓时间过长';
        if (ratio > 1.2) return '盈利持仓时间较长';
        if (ratio > 0.8) return '盈亏持仓时间均衡';
        if (ratio > 0.5) return '亏损持仓时间较长';
        return '亏损持仓时间过长';
    }

    /**
     * 获取最短持仓时间描述
     */
    getMinHoldingDescription(time) {
        if (!time) return '--';
        return time.includes('分钟') ? '超短线交易' : '短线交易';
    }

    /**
     * 获取交易频率描述
     */
    getFrequencyDescription(frequency) {
        if (frequency > 10) return '高频交易者';
        if (frequency > 5) return '活跃交易者';
        if (frequency > 2) return '中等频率';
        if (frequency > 0.5) return '低频交易';
        return '偶尔交易';
    }

    /**
     * 获取市价单偏好描述
     */
    getMarketOrderDescription(ratio) {
        if (ratio > 0.8) return '极度偏好市价单';
        if (ratio > 0.6) return '偏好市价单';
        if (ratio > 0.4) return '市价单使用适中';
        return '较少使用市价单';
    }

    /**
     * 获取限价单偏好描述
     */
    getLimitOrderDescription(ratio) {
        if (ratio > 0.8) return '极度偏好限价单';
        if (ratio > 0.6) return '偏好限价单';
        if (ratio > 0.4) return '限价单使用适中';
        return '较少使用限价单';
    }

    /**
     * 获取平均杠杆描述
     */
    getAvgLeverageDescription(leverage) {
        if (leverage > 20) return '极高杠杆';
        if (leverage > 10) return '高杠杆';
        if (leverage > 5) return '中等杠杆';
        if (leverage > 2) return '低杠杆';
        return '保守杠杆';
    }

    /**
     * 获取最大杠杆描述
     */
    getMaxLeverageDescription(leverage) {
        if (leverage > 50) return '极端风险';
        if (leverage > 20) return '高风险';
        if (leverage > 10) return '中等风险';
        if (leverage > 5) return '低风险';
        return '保守策略';
    }

    /**
     * 获取杠杆稳定性描述
     */
    getLeverageStabilityDescription(stability) {
        if (stability > 0.8) return '杠杆使用非常稳定';
        if (stability > 0.6) return '杠杆使用较稳定';
        if (stability > 0.4) return '杠杆使用一般';
        return '杠杆使用不稳定';
    }

    /**
     * 获取最大亏损描述
     */
    getMaxLossDescription(loss) {
        if (loss > 0.5) return '风险控制极差';
        if (loss > 0.3) return '风险控制较差';
        if (loss > 0.1) return '风险控制一般';
        return '风险控制良好';
    }

    /**
     * 获取资金规模范围描述
     */
    getFundScaleRange(scale) {
        const scaleMap = {
            '散户': '< 1万 USDT',
            '小户': '1万 - 10万 USDT',
            '中户': '10万 - 100万 USDT',
            '大户': '100万 - 1000万 USDT',
            '超大户': '> 1000万 USDT'
        };
        return scaleMap[scale] || '未知';
    }

    /**
     * 获取仓位一致性描述
     */
    getPositionConsistencyDescription(consistency) {
        if (consistency > 0.8) return '仓位规模非常一致';
        if (consistency > 0.6) return '仓位规模较一致';
        if (consistency > 0.4) return '仓位规模一般';
        return '仓位规模变化较大';
    }

    /**
     * 显示专业度评分
     */
    displayProfessionalScores(scores) {

        
        const elements = this.elements;

        // 更新总分和等级
        if (elements.totalScoreValue) {
            const totalScore = Math.round(scores.total_score || 0);
            elements.totalScoreValue.textContent = totalScore;
        }

        if (elements.totalScoreLevel) {
            const level = getScoreLevel(scores.total_score || 0);
            elements.totalScoreLevel.textContent = level;
        }

        // 更新交易者类型
        if (elements.traderTypeValue) {
            const traderType = getTraderType(scores.total_score || 0);
            elements.traderTypeValue.textContent = traderType;
        }

        // 更新各维度评分
        this.updateDimensionScore('profitabilityScore', scores.profitability_score);
        this.updateDimensionScore('riskControlScore', scores.risk_control_score);
        this.updateDimensionScore('tradingBehaviorScore', scores.trading_behavior_score);
        this.updateDimensionScore('marketUnderstandingScore', scores.market_understanding_score);

        // 更新专业度评分仪表盘
        if (this.scoreGauge) {
            this.scoreGauge.updateScore(scores.total_score || 0);
        }
    }

    /**
     * 更新维度评分
     */
    updateDimensionScore(elementKey, score) {
        const element = this.elements[elementKey];
        if (element) {
            const roundedScore = Math.round(score || 0);
            element.textContent = roundedScore;
            
            // 更新对应的进度条
            const barId = elementKey.replace('Score', 'Bar');
            updateScoreBar(barId, roundedScore);
        }
    }

    // 删除重复的updateScoreBar方法，使用utils.js中的统一实现

    /**
     * 🔧 废弃：显示交易指标
     * 注释原因：改为使用DirectFieldMapper直接映射，避免重复的字段更新
     */
    // displayTradingMetrics(metrics) {


    //     const elements = this.elements;

    //     // 🔧 注释：基础交易指标由DirectFieldMapper处理，避免冲突
    //     // if (elements.totalVolumeMetric) {
    //     //     elements.totalVolumeMetric.textContent = formatVolume(metrics.total_volume || 0);
    //     // }

    //     if (elements.totalTradesMetric) {
    //         elements.totalTradesMetric.textContent = formatNumber(metrics.total_trades || 0);
    //     }

    //     if (elements.avgTradeSizeMetric) {
    //         const avgSize = metrics.total_volume && metrics.total_trades
    //             ? metrics.total_volume / metrics.total_trades
    //             : 0;
    //         elements.avgTradeSizeMetric.textContent = formatVolume(avgSize);
    //     }

    //     if (elements.tradingDaysMetric) {
    //         elements.tradingDaysMetric.textContent = metrics.trading_days || 0;
    //     }

    //     if (elements.winRateMetric) {
    //         const winRate = metrics.win_rate || 0;
    //         elements.winRateMetric.textContent = `${(winRate * 100).toFixed(1)}%`;
    //     }

    //     if (elements.profitLossRatioMetric) {
    //         elements.profitLossRatioMetric.textContent = (metrics.profit_loss_ratio || 0).toFixed(2);
    //     }

    //     if (elements.avgLeverageMetric) {
    //         elements.avgLeverageMetric.textContent = `${(metrics.avg_leverage || 0).toFixed(1)}x`;
    //     }

    //     // 🔧 注释：资金规模指标由DirectFieldMapper处理，避免冲突
    //     // if (elements.fundScaleMetric) {
    //     //     elements.fundScaleMetric.textContent = getFundScale(metrics);
    //     // }
    // }

    /**
     * 显示币种分析摘要
     */
    displayCoinAnalysisSummary(coinSummary) {

        
        if (!coinSummary) {
            // 显示占位符
            this.updateElementText('analysisCoinsCount', '数据不足，无法计算');
            this.updateElementText('avgCoinWinRateDisplay', '数据不足，无法计算');
            this.updateElementText('expertCoinsCount', '数据不足，无法计算');
            this.updateElementText('advantageCoinsCountDisplay', '数据不足，无法计算');
            
            // 显示空的表格和网格占位符
            this.generateCoinRankingTable([]);
            this.generateAdvantageCoinsGrid([]);
            return;
        }
        
        const elements = this.elements;

        // 基础币种分析数据
        if (elements.totalCoinsAnalyzed) {
            elements.totalCoinsAnalyzed.textContent = coinSummary.total_analyzed_coins !== undefined ? coinSummary.total_analyzed_coins : '数据不足，无法计算';
        }

        if (elements.avgCoinWinRate) {
            const avgWinRate = coinSummary.avg_coin_win_rate;
            if (avgWinRate !== undefined) {
                elements.avgCoinWinRate.textContent = `${(avgWinRate * 100).toFixed(1)}%`;
            } else {
                elements.avgCoinWinRate.textContent = '数据不足，无法计算';
            }
        }

        if (elements.advantageCoinsCount) {
            elements.advantageCoinsCount.textContent = coinSummary.advantage_coins_count !== undefined ? coinSummary.advantage_coins_count : '数据不足，无法计算';
        }

        // 填充币种分析的详细元素
        this.updateElementText('analysisCoinsCount', coinSummary.total_analyzed_coins !== undefined ? `${coinSummary.total_analyzed_coins} 个` : '数据不足，无法分析币种');
        this.updateElementText('avgCoinWinRateDisplay', coinSummary.avg_coin_win_rate !== undefined ? `${(coinSummary.avg_coin_win_rate * 100).toFixed(1)}%` : '数据不足，无法计算平均胜率');
        this.updateElementText('expertCoinsCount', coinSummary.expert_coins_count !== undefined ? `${coinSummary.expert_coins_count} 个` : '数据不足，无法统计专家级币种');
        this.updateElementText('advantageCoinsCountDisplay', coinSummary.advantage_coins_count !== undefined ? `${coinSummary.advantage_coins_count} 个` : '数据不足，无法统计优势币种');

        // 显示优势币种列表
        if (elements.advantageCoinsList && coinSummary.advantage_coins) {
            this.displayAdvantageCoins(coinSummary.advantage_coins);
        } else if (elements.advantageCoinsList) {
            elements.advantageCoinsList.innerHTML = '<p class="no-data">数据不足，无法计算</p>';
        }

        // 生成币种排行榜表格
        if (coinSummary.coin_performance_ranking) {
            this.generateCoinRankingTable(coinSummary.coin_performance_ranking);
        } else {
            this.generateCoinRankingTable([]);
        }

        // 生成优势币种网格
        if (coinSummary.advantage_coins) {
            this.generateAdvantageCoinsGrid(coinSummary.advantage_coins);
        } else {
            this.generateAdvantageCoinsGrid([]);
        }

        // 更新币种分析组件
        if (this.coinAnalysis) {
            // 传递完整的币种分析数据给CoinWinRateAnalysis组件
            this.coinAnalysis.updateData(coinSummary);
        }
    }

    /**
     * 生成币种排行榜表格
     */
    generateCoinRankingTable(coinRanking) {
        const tbody = document.getElementById('coinRankingTableBody');
        if (!tbody) return;

        // 🔧 修复：确保 coinRanking 是数组类型
        let rankingArray = [];
        if (Array.isArray(coinRanking)) {
            rankingArray = coinRanking;
        } else if (coinRanking && typeof coinRanking === 'object') {
            // 如果是对象，尝试转换为数组
            rankingArray = Object.values(coinRanking);
        }

        if (!rankingArray || rankingArray.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center">数据不足，无法计算</td></tr>';
            return;
        }

        tbody.innerHTML = rankingArray.slice(0, 10).map((coin, index) => `
            <tr>
                <td>${index + 1}</td>
                <td><strong>${coin.contract || coin.symbol || '--'}</strong></td>
                <td class="${this.getWinRateClass(coin.win_rate || 0)}">${((coin.win_rate || 0) * 100).toFixed(1)}%</td>
                <td>${formatNumber(coin.total_trades || coin.trade_count || 0)} 笔</td>
                <td class="${(coin.net_pnl || coin.total_pnl || 0) >= 0 ? 'profit' : 'loss'}">
                    ${formatVolume(coin.net_pnl || coin.total_pnl || 0)} USDT
                </td>
                <td>${(coin.profit_factor || 0).toFixed(2)}</td>
                <td class="expertise-level">${coin.expertise_level || this.getCoinExpertiseLevel(coin.win_rate || 0, coin.total_trades || coin.trade_count || 0)}</td>
                <td class="performance-score">${Math.round(coin.performance_score || 0)}</td>
            </tr>
        `).join('');
    }

    /**
     * 生成优势币种网格
     */
    generateAdvantageCoinsGrid(advantageCoins) {
        const grid = document.getElementById('advantageCoinsGrid');
        if (!grid) return;

        // 🔧 修复：确保 advantageCoins 是数组类型
        let coinsArray = [];
        if (Array.isArray(advantageCoins)) {
            coinsArray = advantageCoins;
        } else if (advantageCoins && typeof advantageCoins === 'object') {
            // 如果是对象，尝试转换为数组
            coinsArray = Object.values(advantageCoins);
        }

        if (!coinsArray || coinsArray.length === 0) {
            grid.innerHTML = '<p class="no-data">数据不足，无法计算</p>';
            return;
        }

        grid.innerHTML = coinsArray.slice(0, 8).map(coin => `
            <div class="advantage-coin-card">
                <div class="coin-header">
                    <span class="coin-symbol">${coin.symbol || '--'}</span>
                    <span class="coin-rank">#${coin.rank || '--'}</span>
                </div>
                <div class="coin-metrics">
                    <div class="metric">
                        <label>胜率</label>
                        <span class="value ${this.getWinRateClass(coin.win_rate || 0)}">${((coin.win_rate || 0) * 100).toFixed(1)}%</span>
                    </div>
                    <div class="metric">
                        <label>交易笔数</label>
                        <span class="value">${formatNumber(coin.trade_count || 0)}</span>
                    </div>
                    <div class="metric">
                        <label>总盈亏</label>
                        <span class="value ${(coin.total_pnl || 0) >= 0 ? 'profit' : 'loss'}">
                            ${formatVolume(coin.total_pnl || 0)} USDT
                        </span>
                    </div>
                    <div class="metric">
                        <label>专业度</label>
                        <span class="value expertise">${this.getCoinExpertiseLevel(coin.win_rate || 0, coin.trade_count || 0)}</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 显示优势币种
     */
    displayAdvantageCoins(advantageCoins) {
        // 🔧 修复：确保 advantageCoins 是数组类型
        let coinsArray = [];
        if (Array.isArray(advantageCoins)) {
            coinsArray = advantageCoins;
        } else if (advantageCoins && typeof advantageCoins === 'object') {
            // 如果是对象，尝试转换为数组
            coinsArray = Object.values(advantageCoins);
        }

        if (!coinsArray || coinsArray.length === 0) {
            if (this.elements.advantageCoinsList) {
                this.elements.advantageCoinsList.innerHTML = '<p class="no-data">暂无优势币种</p>';
            }
            return;
        }

        const coinsHTML = coinsArray.slice(0, 5).map(coin => `
            <div class="advantage-coin-item">
                <div class="coin-symbol">${coin.symbol || '--'}</div>
                <div class="coin-metrics">
                    <span class="win-rate ${this.getWinRateClass(coin.win_rate || 0)}">${((coin.win_rate || 0) * 100).toFixed(1)}%</span>
                    <span class="trade-count">${coin.trade_count || 0}笔</span>
                </div>
            </div>
        `).join('');

        if (this.elements.advantageCoinsList) {
            this.elements.advantageCoinsList.innerHTML = coinsHTML;
        }
    }

    /**
     * 获取币种专业度等级
     */
    getCoinExpertiseLevel(winRate, tradeCount) {
        if (tradeCount < 5) return '数据不足';
        if (winRate >= 0.8 && tradeCount >= 20) return '专家级';
        if (winRate >= 0.7 && tradeCount >= 10) return '熟练';
        if (winRate >= 0.6 && tradeCount >= 5) return '一般';
        if (winRate >= 0.5) return '新手';
        return '待提升';
    }

    /**
     * 显示数据质量信息
     */
    displayDataQualityInfo(behaviorData) {
        const elements = this.elements;

        if (elements.dataQualityScore) {
            const quality = behaviorData.data_quality || {};
            const score = quality.score || behaviorData.data_quality_score || 0;
            elements.dataQualityScore.textContent = `${(score * 100).toFixed(0)}%`;
            elements.dataQualityScore.className = `quality-score ${this.getQualityClass(score)}`;
        }

        if (elements.analysisConfidence) {
            const confidence = behaviorData.analysis_confidence || 0;
            elements.analysisConfidence.textContent = `${(confidence * 100).toFixed(0)}%`;
            elements.analysisConfidence.className = `confidence-level ${this.getConfidenceClass(confidence)}`;
        }
    }

    /**
     * 显示分析报告
     */
    displayAnalysisReport(report) {
        if (!this.elements.analysisReportContainer || !report) {
            return;
        }

        let reportHtml = '<div class="analysis-report-content">';

        if (report.summary) {
            reportHtml += `
                <div class="report-section">
                    <h4><i class="bi bi-file-text"></i> 分析摘要</h4>
                    <p>${report.summary}</p>
                </div>
            `;
        }

        if (report.strengths && report.strengths.length > 0) {
            reportHtml += `
                <div class="report-section">
                    <h4><i class="bi bi-check-circle"></i> 主要优势</h4>
                    <ul class="strengths-list">
                        ${report.strengths.map(strength => `<li>${strength}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        if (report.weaknesses && report.weaknesses.length > 0) {
            reportHtml += `
                <div class="report-section">
                    <h4><i class="bi bi-exclamation-circle"></i> 需要改进</h4>
                    <ul class="weaknesses-list">
                        ${report.weaknesses.map(weakness => `<li>${weakness}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        if (report.recommendations && report.recommendations.length > 0) {
            reportHtml += `
                <div class="report-section">
                    <h4><i class="bi bi-lightbulb"></i> 建议</h4>
                    <ul class="recommendations-list">
                        ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        reportHtml += '</div>';
        this.elements.analysisReportContainer.innerHTML = reportHtml;
    }

    /**
     * 切换行为分析标签
     */
    switchBehaviorTab(tabName) {
        const elements = this.elements;
        
        // 更新标签按钮状态
        if (elements.behaviorAnalysisTabs) {
            elements.behaviorAnalysisTabs.forEach(tab => {
                tab.classList.remove('active');
                if (tab.dataset.tab === tabName) {
                    tab.classList.add('active');
                }
            });
        }
        
        // 更新内容显示
        if (elements.behaviorTabContents) {
            elements.behaviorTabContents.forEach(content => {
                content.classList.remove('active');
                if (content.id === tabName) {
                    content.classList.add('active');
                }
            });
        }
    }

    /**
     * 切换指标标签
     */
    switchMetricTab(tabName) {
        const elements = this.elements;
        
        // 更新Tab按钮状态
        if (elements.metricTabs) {
            elements.metricTabs.forEach(tab => {
                tab.classList.remove('active');
                if (tab.dataset.tab === tabName) {
                    tab.classList.add('active');
                }
            });
        }

        // 更新内容面板显示
        if (elements.metricPanels) {
            elements.metricPanels.forEach(panel => {
                panel.classList.remove('active');
                if (panel.id === tabName) {
                    panel.classList.add('active');
                }
            });
        }
    }


    getWinRateClass(winRate) {
        const rate = winRate * 100;
        if (rate >= 70) return 'high';
        if (rate >= 50) return 'medium';
        return 'low';
    }

    getQualityClass(score) {
        if (score >= 0.8) return 'high';
        if (score >= 0.6) return 'medium';
        return 'low';
    }

    getConfidenceClass(confidence) {
        if (confidence >= 0.8) return 'high';
        if (confidence >= 0.6) return 'medium';
        return 'low';
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        console.warn('用户行为分析错误:', message);
        
        // 重置所有行为分析相关的显示元素
        const behaviorElements = [
            'totalScoreValue', 'totalScoreLevel', 'traderTypeValue',
            'profitabilityScore', 'riskControlScore', 'tradingBehaviorScore', 'marketUnderstandingScore',
            'totalCoinsAnalyzed', 'avgCoinWinRate', 'advantageCoinsCount',
            'totalVolumeMetric', 'totalTradesMetric', 'avgTradeSizeMetric', 'tradingDaysMetric',
            'winRateMetric', 'profitLossRatioMetric', 'avgLeverageMetric', 'fundScaleMetric',
            'dataQualityScore', 'analysisConfidence'
        ];

        behaviorElements.forEach(elementKey => {
            const element = this.elements[elementKey];
            if (element) {
                element.textContent = '暂无数据';
            }
        });

        // 在分析报告容器中显示错误信息
        if (this.elements.analysisReportContainer) {
            this.elements.analysisReportContainer.innerHTML = `
                <div class="error-message">
                    <div class="error-icon">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="error-content">
                        <h4>用户行为分析不可用</h4>
                        <p>${message}</p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 显示数据不可用状态
     */
    showDataNotAvailable() {
        this.showError('暂无用户行为分析数据，可能是数据正在处理中或用户交易数据不足');
    }

    /**
     * 清空显示
     */
    clear() {
        // 清空所有显示元素
        const allElements = [
            'totalScoreValue', 'totalScoreLevel', 'traderTypeValue',
            'profitabilityScore', 'riskControlScore', 'tradingBehaviorScore', 'marketUnderstandingScore',
            'totalCoinsAnalyzed', 'avgCoinWinRate', 'advantageCoinsCount',
            'totalVolumeMetric', 'totalTradesMetric', 'avgTradeSizeMetric', 'tradingDaysMetric',
            'winRateMetric', 'profitLossRatioMetric', 'avgLeverageMetric', 'fundScaleMetric',
            'dataQualityScore', 'analysisConfidence'
        ];

        allElements.forEach(elementKey => {
            const element = this.elements[elementKey];
            if (element) {
                element.textContent = '-';
            }
        });

        // 清空列表和容器
        if (this.elements.advantageCoinsList) {
            this.elements.advantageCoinsList.innerHTML = '<p class="no-data">暂无数据</p>';
        }

        if (this.elements.analysisReportContainer) {
            this.elements.analysisReportContainer.innerHTML = '<p class="no-data">暂无数据</p>';
        }

        // 重置组件
        if (this.scoreGauge) {
            this.scoreGauge.updateScore(0);
        }

        if (this.coinAnalysis) {
            this.coinAnalysis.clear();
        }

        this.currentBehaviorData = null;
    }

    /**
     * 获取当前数据
     */
    getCurrentBehaviorData() {
        return this.currentBehaviorData;
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.scoreGauge) {
            this.scoreGauge.destroy();
        }

        if (this.coinAnalysis) {
            this.coinAnalysis.destroy();
        }
    }

    /**
     * 更新币种偏好分布 - 直接使用后端数据
     */
    updateCoinPreferences(preferences = null) {
        if (!preferences) {
            // 没有数据时显示默认值
            this.updateElementText('mainstreamPercentage', '0%');
            this.updateElementText('altcoinPercentage', '0%');
            this.updateElementText('defiPercentage', '0%');
            this.updateElementText('othersPercentage', '0%');
            return;
        }

        // 🚀 修复字段名匹配和百分比格式化
        const mainstream = preferences.mainstream_percentage || 0;
        const altcoin = preferences.altcoin_percentage || 0;
        const defi = preferences.defi_percentage || 0;
        const others = preferences.others_percentage || 0;

        // 进度条直接使用百分比数值（updateProgressBar已优化处理）
        updateProgressBar('mainstreamFill', mainstream);
        updateProgressBar('altcoinFill', altcoin);
        updateProgressBar('defiFill', defi);
        updateProgressBar('othersFill', others);

        this.updateElementText('mainstreamPercentage', this.formatDirectPercentage(mainstream));
        this.updateElementText('altcoinPercentage', this.formatDirectPercentage(altcoin));
        this.updateElementText('defiPercentage', this.formatDirectPercentage(defi));
        this.updateElementText('othersPercentage', this.formatDirectPercentage(others));
    }

    // 🚀 新增格式化方法 - 直接格式化后端数据
    /**
     * 格式化百分比（从小数转换）
     */
    formatPercentage(value) {
        if (value === null || value === undefined) return '0%';
        return (value * 100).toFixed(1) + '%';
    }

    /**
     * 格式化百分比（从小数转换，保留2位小数）
     */
    formatPercentageDetailed(value) {
        if (value === null || value === undefined) return '0%';
        return (value * 100).toFixed(2) + '%';
    }

    /**
     * 格式化已经是百分比数值的数据（后端已经乘以100）
     */
    formatDirectPercentage(value) {
        if (value === null || value === undefined) return '0%';
        return value.toFixed(1) + '%';
    }

    /**
     * 格式化数值
     */
    formatNumber(value) {
        if (value === null || value === undefined) return '0';
        return formatNumber(value);
    }

    /**
     * 格式化时长（分钟转为可读格式）
     */
    formatDuration(minutes) {
        if (!minutes || minutes === 0) return '0分钟';
        
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        
        if (hours > 0) {
            return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
        }
        return `${mins}分钟`;
    }

    /**
     * 格式化比例
     */
    formatRatio(value) {
        if (value === null || value === undefined) return '0.0';
        return value.toFixed(2);
    }

    /**
     * 格式化频率
     */
    formatFrequency(value) {
        if (value === null || value === undefined) return '0笔/天';
        return value.toFixed(1) + '笔/天';
    }
}

export default UserBehaviorDisplay;
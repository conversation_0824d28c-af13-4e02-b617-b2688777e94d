/**
 * 用户分析服务
 * 提供与后端用户分析API的交互功能
 */

class UserAnalysisService {
    constructor() {
        // 动态获取API基础URL，支持不同环境
        this.baseURL = this.getApiBaseUrl() + '/api/user-analysis';
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    }

    /**
     * 动态获取API基础URL，支持不同环境
     */
    getApiBaseUrl() {
        // 如果是通过webpack dev server访问（3000端口），使用代理
        if (window.location.port === '3000') {
            return '';  // 使用相对路径，通过webpack代理转发
        }
        // 如果是直接访问后端（5005端口）或其他情况
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        return `${protocol}//${hostname}:5005`;
    }

    /**
     * 获取可用的分析任务列表
     */
    async getAvailableTasks() {
        try {
            const response = await fetch(`${this.baseURL}/available-tasks`, {
                credentials: 'include'
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            if (data.status === 'error') {
                throw new Error(data.error);
            }
            
            return data;
        } catch (error) {
            console.error('获取可用任务失败:', error);
            throw error;
        }
    }

    /**
     * 搜索用户分析数据
     * @param {string} memberId - 用户ID
     * @param {string} contractTaskId - 合约风险分析任务ID（可选）
     * @param {string} agentTaskId - 代理关系分析任务ID（可选）
     */
    async searchUserAnalysis(memberId, contractTaskId = '', agentTaskId = '') {
        try {
            if (!memberId || memberId.trim() === '') {
                throw new Error('用户ID不能为空');
            }

            // 构建查询参数
            const params = new URLSearchParams();
            if (contractTaskId) {
                params.append('contract_task_id', contractTaskId);
            }
            if (agentTaskId) {
                params.append('agent_task_id', agentTaskId);
            }

            const url = `${this.baseURL}/search/${encodeURIComponent(memberId.trim())}?${params.toString()}`;

            const response = await fetch(url, {
                credentials: 'include'
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            if (data.status === 'error') {
                throw new Error(data.error);
            }

            // 缓存结果
            const cacheKey = `${memberId}_${contractTaskId}_${agentTaskId}`;
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });

            return data;
        } catch (error) {
            console.error('用户分析查询失败:', error);
            throw error;
        }
    }

    /**
     * 批量搜索用户
     * @param {string[]} memberIds - 用户ID列表
     * @param {string} contractTaskId - 合约风险分析任务ID
     * @param {string} agentTaskId - 代理关系分析任务ID
     */
    async batchSearchUsers(memberIds, contractTaskId = '', agentTaskId = '') {
        try {
            if (!memberIds || memberIds.length === 0) {
                throw new Error('用户ID列表不能为空');
            }

            const requestData = {
                member_ids: memberIds,
                contract_task_id: contractTaskId,
                agent_task_id: agentTaskId
            };

            const response = await fetch(`${this.baseURL}/batch-search`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            if (data.status === 'error') {
                throw new Error(data.error);
            }

            return data;
        } catch (error) {
            console.error('批量用户查询失败:', error);
            throw error;
        }
    }

    /**
     * 从缓存获取用户分析数据
     * @param {string} memberId - 用户ID
     * @param {string} contractTaskId - 合约风险分析任务ID
     * @param {string} agentTaskId - 代理关系分析任务ID
     */
    getCachedUserAnalysis(memberId, contractTaskId = '', agentTaskId = '') {
        const cacheKey = `${memberId}_${contractTaskId}_${agentTaskId}`;
        const cached = this.cache.get(cacheKey);
        
        if (!cached) {
            return null;
        }

        // 检查缓存是否过期
        if (Date.now() - cached.timestamp > this.cacheTimeout) {
            this.cache.delete(cacheKey);
            return null;
        }

        return cached.data;
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * 格式化用户类型
     */
    formatUserType(userType, bdName, agentFlag) {
        // 直客用户优先判断
        if (agentFlag === '直客') {
            return '直客用户';
        }
        
        // 直接返回后端提供的用户类型，不要根据bdName来推断
        return userType || '未知用户';
    }

    /**
     * 格式化风险类型名称
     */
    formatRiskType(riskType) {
        const riskTypeMapping = {
            'high_frequency_trading': '高频交易',
            'wash_trading': '对敲交易',
            'suspected_wash_trading': '疑似对敲交易',
            'large_volume_trading': '大额交易',
            'abnormal_profit': '异常盈利',
            'market_manipulation': '市场操纵',
            'abnormal_trading_pattern': '异常交易模式',
            'cross_platform_arbitrage': '跨平台套利',
            'unknown': '未知风险'
        };
        
        return riskTypeMapping[riskType] || riskType;
    }

    /**
     * 格式化检测方法名称
     */
    formatDetectionMethod(method) {
        const methodMapping = {
            'direct_self_trade': '直接自成交',
            'suspected_self_trade': '疑似自成交',
            'same_account_wash_trading': '同账户对敲',
            'cross_account_wash_trading': '跨账户对敲',
            'enhanced_network_analysis': '增强网络分析',
            'open_close_pair': '开平仓配对',
            'pattern_matching': '模式匹配',
            'statistical_analysis': '统计分析',
            'time_correlation': '时间关联',
            'volume_correlation': '交易量关联'
        };
        
        return methodMapping[method] || method;
    }

    /**
     * 格式化数字 - 直接使用后端格式化数据
     */
    formatNumber(num, decimals = 2) {
        // 直接使用后端格式化数据，不做前端格式化
        if (typeof num === 'object' && num.formatted_display) {
            return num.formatted_display;
        }
        
        // 如果是字符串且已经格式化过，直接返回
        if (typeof num === 'string') {
            return num;
        }
        
        // 兜底：直接返回数值或默认值
        return num || '0';
    }

    /**
     * 格式化时间
     */
    formatTime(timeStr) {
        if (!timeStr || timeStr === '') {
            return '-';
        }
        
        // 处理时间范围格式，如 "2024-01-01 to 2024-01-02"
        if (timeStr.includes(' to ')) {
            const [start, end] = timeStr.split(' to ');
            return `${start.trim()} 至 ${end.trim()}`;
        }
        
        return timeStr;
    }

    /**
     * 获取风险等级颜色
     */
    getRiskLevelColor(riskScore) {
        if (riskScore >= 80) {
            return '#ff4757'; // 高风险 - 红色
        } else if (riskScore >= 60) {
            return '#ff6b35'; // 中高风险 - 橙色
        } else if (riskScore >= 40) {
            return '#ffa502'; // 中风险 - 黄色
        } else if (riskScore >= 20) {
            return '#3742fa'; // 低风险 - 蓝色
        } else {
            return '#2ed573'; // 很低风险 - 绿色
        }
    }

    /**
     * 获取风险等级文本
     */
    getRiskLevelText(riskScore) {
        if (riskScore >= 80) {
            return '高风险';
        } else if (riskScore >= 60) {
            return '中高风险';
        } else if (riskScore >= 40) {
            return '中风险';
        } else if (riskScore >= 20) {
            return '低风险';
        } else {
            return '很低风险';
        }
    }

    /**
     * 基于digital_id搜索用户分析数据
     * @param {string} digitalId - 数字ID
     * @param {string} contractTaskId - 合约风险分析任务ID（可选）
     * @param {string} agentTaskId - 代理关系分析任务ID（可选）
     */
    async searchUserAnalysisByDigitalId(digitalId, contractTaskId = '', agentTaskId = '') {
        try {
            if (!digitalId || digitalId.trim() === '') {
                throw new Error('数字ID不能为空');
            }

            // 构建查询参数
            const params = new URLSearchParams();
            if (contractTaskId) {
                params.append('contract_task_id', contractTaskId);
            }
            if (agentTaskId) {
                params.append('agent_task_id', agentTaskId);
            }

            const url = `${this.baseURL}/search-by-digital/${encodeURIComponent(digitalId.trim())}?${params.toString()}`;

            const response = await fetch(url, {
                credentials: 'include'
            });
            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error(`未找到数字ID ${digitalId} 对应的用户信息`);
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            if (data.status === 'error') {
                throw new Error(data.error);
            }

            // 缓存结果（使用digital_id作为前缀）
            const cacheKey = `digital_${digitalId}_${contractTaskId}_${agentTaskId}`;
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });

            return data;
        } catch (error) {
            console.error('基于digital_id的用户分析查询失败:', error);
            throw error;
        }
    }
}

// 创建全局实例
window.userAnalysisService = new UserAnalysisService();

// 导出服务类
export default UserAnalysisService; 
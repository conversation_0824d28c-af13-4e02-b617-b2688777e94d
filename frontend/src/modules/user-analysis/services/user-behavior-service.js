/**
 * 用户行为分析服务
 * 统一使用 complete-analysis 接口，避免API混乱
 */
class UserBehaviorService {
    constructor() {
        // 动态获取API基础URL，支持不同环境
        this.baseURL = this.getApiBaseUrl() + '/api/user-analysis';
        this.timeout = 30000; // 30秒超时
    }

    /**
     * 动态获取API基础URL，支持不同环境
     */
    getApiBaseUrl() {
        // 如果是通过webpack dev server访问（3000端口），使用代理
        if (window.location.port === '3000') {
            return '';  // 使用相对路径，通过webpack代理转发
        }
        // 如果是直接访问后端（5005端口）或其他情况
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        return `${protocol}//${hostname}:5005`;
    }

    /**
     * 获取用户完整分析数据 - 主要接口
     * @param {string} userId 用户ID
     * @param {string} taskId 可选的任务ID
     * @param {boolean} forceRealtime 是否强制实时分析（默认true，确保数据最新）
     * @returns {Promise<Object>} 完整分析数据
     */
    async getUserCompleteAnalysis(userId, taskId = '', forceRealtime = true) {
        try {
            const url = `${this.baseURL}/complete-analysis/${userId}`;
            const params = new URLSearchParams();

            if (taskId) {
                params.append('task_id', taskId);
            }

            // 🚀 新增：强制实时分析参数，确保每次都获取最新数据
            if (forceRealtime) {
                params.append('force_realtime', 'true');
            }

            const fullUrl = params.toString() ? `${url}?${params.toString()}` : url;
            

            
            const response = await fetch(fullUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include', // 🚀 修复：包含鉴权信息
                timeout: this.timeout
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.status !== 'success') {
                throw new Error(data.message || '分析失败');
            }



            return data;
            
        } catch (error) {
            console.error('❌ 获取用户完整分析失败:', error);
            throw new Error(`获取用户分析失败: ${error.message}`);
        }
    }

    /**
     * 获取用户行为分析数据 - 简化版（保持兼容性）
     * @param {string} userId 用户ID
     * @param {string} taskId 可选的任务ID
     * @param {boolean} forceRealtime 是否强制实时分析（默认true）
     * @returns {Promise<Object>} 行为分析数据
     */
    async getUserBehaviorAnalysis(userId, taskId = '', forceRealtime = true) {
        try {
            // 直接调用完整分析，然后提取行为分析部分
            const completeData = await this.getUserCompleteAnalysis(userId, taskId, forceRealtime);
            
            // 构建兼容旧版的行为分析响应
            return {
                status: 'success',
                user_id: userId,
                analysis_timestamp: completeData.analysis_timestamp,
                data_quality_score: completeData.data_quality_score,
                analysis_confidence: completeData.analysis_confidence,
                
                // 基础指标
                basic_metrics: completeData.complete_data.trading_scale_metrics,
                pnl_metrics: completeData.complete_data.pnl_metrics,
                
                // 专业度评分
                professional_scores: completeData.complete_data.professional_dashboard,
                
                // 衍生指标
                derived_metrics: completeData.complete_data.derived_analysis,
                
                // 独立指标对象（前端期望的格式）
                holding_time_metrics: completeData.complete_data.holding_time_metrics,
                order_type_metrics: completeData.complete_data.order_type_metrics,
                risk_control_metrics: completeData.complete_data.risk_control_metrics,
                fund_scale_metrics: completeData.complete_data.fund_scale_metrics,
                
                // 其他分析
                coin_analysis_summary: completeData.complete_data.coin_analysis,
                trading_preferences: completeData.complete_data.trading_preferences,
                abnormal_analysis: completeData.complete_data.abnormal_analysis
            };
            
        } catch (error) {
            console.error('❌ 获取用户行为分析失败:', error);
            throw error;
        }
    }

    /**
     * 批量分析用户
     * @param {Array<string>} userIds 用户ID列表
     * @param {Object} options 选项
     * @returns {Promise<Object>} 批量分析结果
     */
    async batchAnalyzeUsers(userIds, options = {}) {
        try {
            const url = `${this.baseURL}/batch-analysis`;
            
            const requestBody = {
                user_ids: userIds,
                task_id: options.taskId || '',
                days: options.days || 30,
                include_details: options.includeDetails || false
            };

            console.log(`🔍 批量分析用户: ${userIds.length} 个用户`);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include', // 🚀 修复：包含鉴权信息
                body: JSON.stringify(requestBody),
                timeout: this.timeout * 2 // 批量分析需要更长时间
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.status !== 'success') {
                throw new Error(data.message || '批量分析失败');
            }

            console.log('✅ 批量分析完成:', {
                requestedUsers: data.requested_users,
                analyzedUsers: data.analyzed_users,
                successfulAnalyses: data.successful_analyses
            });

            return data;
            
        } catch (error) {
            console.error('❌ 批量分析失败:', error);
            throw new Error(`批量分析失败: ${error.message}`);
        }
    }

    /**
     * 获取用户币种分析
     * @param {string} userId 用户ID
     * @param {Object} options 选项
     * @returns {Promise<Object>} 币种分析结果
     */
    async getUserCoinAnalysis(userId, options = {}) {
        try {
            const url = `${this.baseURL}/coin-analysis/${userId}`;
            const params = new URLSearchParams();
            
            if (options.taskId) params.append('task_id', options.taskId);
            if (options.days) params.append('days', options.days);
            if (options.minTrades) params.append('min_trades', options.minTrades);
            
            const fullUrl = params.toString() ? `${url}?${params.toString()}` : url;

            const response = await fetch(fullUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include', // 🚀 修复：包含鉴权信息
                timeout: this.timeout
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.status !== 'success') {
                throw new Error(data.message || '币种分析失败');
            }

            return data;
            
        } catch (error) {
            console.error('❌ 获取币种分析失败:', error);
            throw new Error(`币种分析失败: ${error.message}`);
        }
    }

    /**
     * 生成用户分析报告
     * @param {string} userId 用户ID
     * @param {Object} options 选项
     * @returns {Promise<Object>} 分析报告
     */
    async generateUserReport(userId, options = {}) {
        try {
            const url = `${this.baseURL}/analysis-report/${userId}`;
            const params = new URLSearchParams();
            
            if (options.taskId) params.append('task_id', options.taskId);
            if (options.days) params.append('days', options.days);
            if (options.format) params.append('format', options.format);
            
            const fullUrl = params.toString() ? `${url}?${params.toString()}` : url;

            const response = await fetch(fullUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include', // 🚀 修复：包含鉴权信息
                timeout: this.timeout
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.status && data.status !== 'success') {
                throw new Error(data.message || '报告生成失败');
            }

            return data;
            
        } catch (error) {
            console.error('❌ 生成用户报告失败:', error);
            throw new Error(`报告生成失败: ${error.message}`);
        }
    }

    /**
     * 获取分析器状态
     * @returns {Promise<Object>} 分析器状态
     */
    async getAnalyzerStatus() {
        try {
            const url = `${this.baseURL}/analyzer-status`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include', // 🚀 修复：包含鉴权信息
                timeout: 5000
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return data;
            
        } catch (error) {
            console.error('❌ 获取分析器状态失败:', error);
            throw new Error(`获取状态失败: ${error.message}`);
        }
    }

    /**
     * 兼容性方法：搜索用户（重定向到完整分析）
     * @param {string} memberId 用户ID
     * @param {Object} options 选项
     * @returns {Promise<Object>} 用户分析数据
     */
    async searchUser(memberId, options = {}) {
        console.warn('⚠️ searchUser 方法已废弃，请使用 getUserCompleteAnalysis');
        return this.getUserCompleteAnalysis(memberId, options.taskId, true); // 强制实时分析
    }


}

// 导出服务实例
window.UserBehaviorService = UserBehaviorService;

// 兼容性：保持旧的全局变量
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserBehaviorService; 
} 
export default UserBehaviorService; 
/* ===================================
   用户分析模块 - 统一样式文件
   整合日期: 2024-12-19
   说明: 整合了所有用户分析相关的CSS样式
   包含: user-analysis-base.css, user-behavior-analysis.css, user-analysis.css, user-behavior-demo.css
   ================================== */

/* ===================================
   1. 基础重置和通用样式
   ================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.user-behavior-analysis {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #2c3e50;
    line-height: 1.6;
}

.behavior-analysis-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 通用卡片样式 */
.analysis-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 通用标题样式 - 统一标题层级 */
.section-title {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 标题层级统一规范 */
h1 {
    font-size: 1.8em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5em;
}

h2 {
    font-size: 1.4em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75em;
}

h3 {
    font-size: 1.2em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75em;
}

h4 {
    font-size: 1.1em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5em;
}

h5 {
    font-size: 1.0em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5em;
}

/* 通用网格布局 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

/* 通用统计项样式 */
.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e5e7eb;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9em;
}

.stat-value {
    font-weight: bold;
    color: #2c3e50;
}

/* 通用评分条样式 */
.score-bar-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
}

.score-bar {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.score-fill {
    height: 100%;
    border-radius: inherit;
    transition: width 0.3s ease;
}

.score-number {
    font-weight: bold;
    color: #2c3e50;
    min-width: 40px;
    text-align: right;
}

/* 通用徽章样式 */
.badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: bold;
    color: white;
}

.badge.success { background: #28a745; }
.badge.warning { background: #ffc107; color: #333; }
.badge.info { background: #17a2b8; }
.badge.danger { background: #dc3545; }

/* 通用颜色主题 */
.positive { color: #28a745; }
.negative { color: #dc3545; }
.warning { color: #ffc107; }
.info { color: #17a2b8; }

/* 通用按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

/* 通用表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid #d1d5db;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #d1d5db;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

/* 通用动画 */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

/* ===================================
   2. 页面标题和搜索区域
   ================================== */

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-header h1 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 1.8em;
    font-weight: 600;
}

.page-description {
    color: #7f8c8d;
    font-size: 1.1em;
    margin: 0;
}

.search-section {
    background: white;
    padding: 25px;
    margin-bottom: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-input-group {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 15px;
    align-items: center;
}

.search-input-group input {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
}

/* 搜索提示样式 */
.search-hint {
    grid-column: 1 / -1;
    margin: 0;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    margin-bottom: 15px;
}

.search-hint small {
    color: #007bff;
    font-weight: 500;
    font-size: 13px;
}

/* 搜索方法标识样式 */
.search-method-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.search-method-badge.digital-id-search {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.search-method-badge.member-id-search {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
}

.data-source-selection {
    display: grid;
    grid-template-columns: auto 1fr 1fr auto;
    gap: 15px;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.data-source-selection label {
    font-weight: 600;
    color: #2c3e50;
    white-space: nowrap;
}

.data-source-selection select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    transition: all 0.2s ease;
}

.data-source-selection select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 加载和错误状态 */
.loading-indicator {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 8px;
    margin-bottom: 25px;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #f5c6cb;
    margin-bottom: 25px;
}

.analysis-results {
    display: none;
}

/* ===================================
   3. 用户档案部分
   ================================== */

.user-profile-section {
    background: white;
    padding: 25px;
    margin-bottom: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.user-profile-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    font-size: 1.4em;
    font-weight: 600;
}

.profile-basic-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.info-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item span {
    font-weight: bold;
    color: #2c3e50;
    font-size: 1.1em;
}

/* 用户ID特殊样式 - 适应长ID显示 */
#profileMemberId {
    font-size: 0.9em !important;
    font-weight: bold !important;
    word-break: break-all;
    line-height: 1.3;
    font-family: 'Courier New', monospace;
}

/* 专业度评分概览样式 */
.professional-score-preview {
    border-left-color: #28a745 !important;
}

.trader-type-container {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.trader-type-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: bold;
}

.trader-type-badge.专业交易员 { background: #d4edda; color: #155724; }
.trader-type-badge.半专业交易员 { background: #fff3cd; color: #856404; }
.trader-type-badge.普通散户 { background: #f8d7da; color: #721c24; }
.trader-type-badge.新用户 { background: #cff4fc; color: #055160; }

.score-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.score-badge.high { background: #d4edda; color: #155724; }
.score-badge.medium { background: #fff3cd; color: #856404; }
.score-badge.low { background: #f8d7da; color: #721c24; }

.fund-scale-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9em;
    background: #e7f3ff;
    color: #0c5aa6;
}

/* 展开/收起按钮 */
.behavior-toggle-section {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn-toggle {
    background: linear-gradient(45deg, #007bff, #28a745);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,123,255,0.3);
}

.btn-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,123,255,0.4);
}

.btn-toggle i {
    margin-right: 8px;
    transition: transform 0.3s ease;
}

.btn-toggle.expanded i {
    transform: rotate(180deg);
}

/* ===================================
   4. 专业度评分仪表盘
   ================================== */

.professional-score-dashboard {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    align-items: center;
    /* margin-bottom 已在统一规则中设置 */
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
}

.score-main-display {
    text-align: center;
}

.score-gauge-container {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.score-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.score-value {
    font-size: 2.5em;
    font-weight: bold;
    color: #2c3e50;
}

.score-label {
    font-size: 0.9em;
    color: #6c757d;
    margin-top: 5px;
}

.trader-type {
    font-size: 1.1em;
    font-weight: bold;
    margin-top: 8px;
    padding: 4px 12px;
    border-radius: 20px;
    display: inline-block;
}

.classification-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}

.fund-scale {
    padding: 8px 16px;
    background: #e7f3ff;
    color: #0c5aa6;
    border-radius: 20px;
    font-weight: bold;
}

.analysis-date {
    font-size: 0.9em;
    color: #6c757d;
}

/* 评分维度条形图 */
.score-dimensions {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.dimension-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.dimension-item label {
    min-width: 140px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95em;
}

.score-bar-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
}

.score-bar {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.8s ease;
}

.score-fill.profitability { background: linear-gradient(90deg, #28a745, #20c997); }
.score-fill.risk-control { background: linear-gradient(90deg, #ffc107, #fd7e14); }
.score-fill.trading-behavior { background: linear-gradient(90deg, #17a2b8, #6f42c1); }
.score-fill.market-understanding { background: linear-gradient(90deg, #dc3545, #e83e8c); }

.score-number {
    min-width: 45px;
    font-weight: bold;
    color: #2c3e50;
    text-align: center;
}

.trader-classification {
    text-align: center;
    margin-top: 20px;
}

.trader-type-professional { background: #27ae60; }
.trader-type-semi-professional { background: #f39c12; }
.trader-type-retail { background: #e74c3c; }
.trader-type-new { background: #3498db; }

.fund-scale-info {
    margin-top: 10px;
    font-size: 13px;
    opacity: 0.9;
}

/* ===================================
   5. 交易行为分析部分
   ================================== */

.trading-behavior-section {
    background: white;
    margin-bottom: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.trading-behavior-section.collapsed {
    max-height: 0;
    margin-bottom: 0;
    opacity: 0;
    transform: translateY(-10px);
}

.trading-behavior-section:not(.collapsed) {
    max-height: none;
    opacity: 1;
    transform: translateY(0);
    padding: 25px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.section-header h2 {
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.4em;
    font-weight: 600;
}

.btn-collapse {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.btn-collapse:hover {
    background: #c82333;
}

/* 基础数据指标详细展示 */
.basic-metrics-section {
    /* margin-top 和 margin-bottom 已在统一规则中设置 */
}

/* 交易行为画像内部子模块统一间距 */
.trading-behavior-section .professional-score-dashboard,
.trading-behavior-section .basic-metrics-section,
.trading-behavior-section .derived-metrics-section,
.trading-behavior-section .preferences-section,
.trading-behavior-section .coin-win-rate-section,
/* .trading-behavior-section .hedge-analysis-section, */ /* 对冲分析样式已移除 */
.trading-behavior-section .abnormal-analysis-section {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-bottom: 15px !important;
}

/* 第一个子模块不需要上边距 */
.trading-behavior-section .professional-score-dashboard {
    margin-top: 0 !important;
}

/* 基础数据指标需要上边距来与专业度评分仪表盘分开 */
.trading-behavior-section .basic-metrics-section {
    margin-top: 15px !important;
}

.basic-metrics-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2em;
    font-weight: 600;
}

.metrics-tabs {
    display: flex;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 20px;
    overflow-x: auto;
}

.metric-tab {
    padding: 12px 24px;
    border: none;
    background: none;
    color: #6b7280;
    cursor: pointer;
    font-weight: 500;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.metric-tab:hover {
    color: #3b82f6;
}

.metric-tab.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
}

.metric-content {
    position: relative;
}

.metric-panel {
    display: none;
}

.metric-panel.active {
    display: block;
}

.stat-box {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-box:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.stat-box.positive {
    border-left: 4px solid #10b981;
}

.stat-box.negative {
    border-left: 4px solid #ef4444;
}

.stat-box.warning {
    border-left: 4px solid #f59e0b;
}

.stat-box label {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-box .value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #374151;
    margin-bottom: 4px;
}

.stat-box small {
    font-size: 11px;
    color: #9ca3af;
}

/* 交易统计卡片 */
.trading-statistics-section {
    margin-bottom: 30px;
}

.stats-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stats-card {
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stats-card.primary { border-color: #007bff; }
.stats-card.success { border-color: #28a745; }
.stats-card.warning { border-color: #ffc107; }
.stats-card.info { border-color: #17a2b8; }

.stats-card h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1em;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 5px 0;
}

.stat-item label {
    color: #6c757d;
    font-size: 0.9em;
}

.stat-item span {
    font-weight: bold;
    color: #2c3e50;
}

.pnl-value.positive { color: #28a745; }
.pnl-value.negative { color: #dc3545; }

/* ===================================
   6. 衍生分析指标
   ================================== */

.derived-metrics-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    /* margin-bottom 已在统一规则中设置 */
}

.derived-metrics-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2em;
    font-weight: 600;
}

.derived-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.derived-card {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.derived-card.profitability {
    border-left: 4px solid #10b981;
}

.derived-card.risk-control {
    border-left: 4px solid #f59e0b;
}

.derived-card.trading-behavior {
    border-left: 4px solid #6366f1;
}

.derived-card.market-understanding {
    border-left: 4px solid #ef4444;
}

.derived-card h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.derived-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.derived-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.derived-item:last-child {
    border-bottom: none;
}

.derived-item label {
    color: #6b7280;
    font-size: 14px;
}

.derived-item .value {
    font-weight: 600;
    color: #2c3e50;
}

.derived-item .score {
    font-size: 12px;
    color: #10b981;
    font-weight: 500;
}

.dimension-total {
    text-align: center;
    padding: 10px;
    background: #f9fafb;
    border-radius: 6px;
    font-weight: bold;
    color: #2c3e50;
}

/* ===================================
   7. 交易偏好分析
   ================================== */

.preferences-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    /* margin-bottom 已在统一规则中设置 */
}

.preferences-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2em;
    font-weight: 600;
}

.preferences-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.preference-card {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 20px;
}

.preference-card h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
    font-weight: 600;
}

.preference-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.pref-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pref-item label {
    min-width: 120px;
    color: #6b7280;
    font-size: 14px;
}

.pref-bar {
    flex: 1;
    height: 16px;
    background: #f3f4f6;
    border-radius: 8px;
    overflow: hidden;
}

.pref-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

.pref-item span {
    min-width: 50px;
    text-align: right;
    font-weight: 600;
    color: #2c3e50;
}

.favorite-contracts {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 14px;
    color: #2c3e50;
}

/* 🚀 更新：时间热力图样式（匹配demo页面） */
.time-heatmap {
    margin-bottom: 16px;
}

.time-grid {
    display: grid;
    grid-template-columns: repeat(24, 1fr);
    gap: 2px;
    margin-bottom: 8px;
}

.hour-cell {
    height: 20px;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
}

/* 🚀 新增：空数据状态 */
.hour-cell.empty {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
}

/* 🚀 更新：低活跃度（匹配demo颜色） */
.hour-cell.low {
    background: #dbeafe;
}

/* 🚀 更新：中等活跃度（匹配demo颜色） */
.hour-cell.medium {
    background: #60a5fa;
}

/* 🚀 更新：高活跃度（匹配demo颜色） */
.hour-cell.high {
    background: #1d4ed8;
}

/* 🚀 新增：悬停效果 */
.hour-cell:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1;
    position: relative;
}

.time-labels {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: #6b7280;
}

/* 🚀 新增：主要交易时段样式 */
.peak-hours {
    font-size: 13px;
    color: #6b7280;
    background: #f8fafc;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #3b82f6;
}

.peak-hours strong {
    color: #2c3e50;
}

.peak-hours {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 14px;
    color: #2c3e50;
}

.risk-appetite {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.appetite-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.appetite-item label {
    color: #6b7280;
    font-size: 14px;
}

.appetite-level.moderate {
    background: #fff3cd;
    color: #856404;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.volatility-pref.medium {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.diversification-score {
    font-weight: bold;
    color: #2c3e50;
}

/* ===================================
   8. 币种胜率分析
   ================================== */

.coin-win-rate-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    /* margin-bottom 已在统一规则中设置 */
}

.coin-win-rate-section h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 25px;
    color: #2c3e50;
    font-size: 1.2em;
    font-weight: 600;
}

.feature-badge.new {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7em;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 概览统计卡片 */
.coin-overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 10px;
}

.overview-stat-card {
    padding: 20px;
    background: white;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-top: 4px solid;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.overview-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.overview-stat-card.primary { border-color: #007bff; }
.overview-stat-card.success { border-color: #28a745; }
.overview-stat-card.warning { border-color: #ffc107; }
.overview-stat-card.info { border-color: #17a2b8; }

.overview-stat-card label {
    display: block;
    color: #6c757d;
    font-size: 0.9em;
    margin-bottom: 8px;
    font-weight: 500;
}

.overview-stat-card span {
    display: block;
    font-size: 1.8em;
    font-weight: bold;
    color: #2c3e50;
}

/* 币种表现排行榜 */
.coin-performance-ranking {
    margin-bottom: 10px;
}

.coin-performance-ranking h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
    font-weight: 600;
}

.ranking-table-container {
    overflow-x: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.coin-ranking-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
}

.coin-ranking-table th,
.coin-ranking-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.coin-ranking-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
}

.coin-ranking-table tr:hover {
    background: #f8f9fa;
}

.rank-badge {
    display: inline-block;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    text-align: center;
    line-height: 24px;
    font-weight: bold;
    font-size: 0.8em;
    color: white;
}

.rank-badge.gold { background: linear-gradient(45deg, #ffd700, #ffed4e); color: #333; }
.rank-badge.silver { background: linear-gradient(45deg, #c0c0c0, #e6e6e6); color: #333; }
.rank-badge.bronze { background: linear-gradient(45deg, #cd7f32, #daa520); color: white; }
.rank-badge:not(.gold):not(.silver):not(.bronze) { background: #6c757d; }

.win-rate.high { color: #28a745; font-weight: bold; }
.win-rate.medium { color: #ffc107; font-weight: bold; }
.win-rate.low { color: #dc3545; font-weight: bold; }

.pnl.positive { color: #28a745; font-weight: bold; }
.pnl.negative { color: #dc3545; font-weight: bold; }

.expertise.expert { background: #d4edda; color: #155724; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; }
.expertise.skilled { background: #fff3cd; color: #856404; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; }
.expertise.average { background: #f8d7da; color: #721c24; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; }
.expertise.weak { background: #f5f5f5; color: #6c757d; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; }

.performance-score.high { color: #28a745; font-weight: bold; }
.performance-score.medium { color: #ffc107; font-weight: bold; }
.performance-score.low { color: #dc3545; font-weight: bold; }

/* 优势币种识别 */
.advantage-coins-section {
    margin-bottom: 10px;
}

.advantage-coins-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.advantage-coins-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
}

.advantage-coin-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 5px solid;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.advantage-coin-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.advantage-coin-card.high-win-rate { border-color: #28a745; }
.advantage-coin-card.high-profit { border-color: #007bff; }
.advantage-coin-card.comprehensive { border-color: #ffc107; }

.advantage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.coin-name {
    font-size: 1.1em;
    font-weight: bold;
    color: #2c3e50;
}

.advantage-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: bold;
    color: white;
}

.advantage-coin-card.high-win-rate .advantage-badge { background: #28a745; }
.advantage-coin-card.high-profit .advantage-badge { background: #007bff; }
.advantage-coin-card.comprehensive .advantage-badge { background: #ffc107; }

.advantage-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-item label {
    color: #6c757d;
    font-size: 0.9em;
    font-weight: 500;
}

.metric-value {
    font-weight: bold;
    color: #2c3e50;
}

.metric-value.positive { color: #28a745; }

.win-rate.high { color: #28a745; font-weight: bold; }
.win-rate.medium { color: #ffc107; font-weight: bold; }
.win-rate.low { color: #dc3545; font-weight: bold; }

.pnl.positive { color: #28a745; font-weight: bold; }
.pnl.negative { color: #dc3545; font-weight: bold; }

.expertise.expert { background: #d4edda; color: #155724; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; }
.expertise.skilled { background: #fff3cd; color: #856404; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; }
.expertise.average { background: #f8d7da; color: #721c24; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; }
.expertise.weak { background: #f5f5f5; color: #6c757d; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; }

.performance-score.high { color: #28a745; font-weight: bold; }
.performance-score.medium { color: #ffc107; font-weight: bold; }
.performance-score.low { color: #dc3545; font-weight: bold; }

.advantage-coins-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
}

.advantage-coin-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border-left: 5px solid;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.advantage-coin-card.high-win-rate { border-color: #28a745; }
.advantage-coin-card.high-profit { border-color: #007bff; }
.advantage-coin-card.comprehensive { border-color: #ffc107; }

.advantage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.coin-name {
    font-size: 1.1em;
    font-weight: bold;
    color: #2c3e50;
}

.advantage-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: bold;
    color: white;
}

.advantage-coin-card.high-win-rate .advantage-badge { background: #28a745; }
.advantage-coin-card.high-profit .advantage-badge { background: #007bff; }
.advantage-coin-card.comprehensive .advantage-badge { background: #ffc107; }

.advantage-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e5e7eb;
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-item label {
    color: #6c757d;
    font-size: 0.9em;
}

.metric-value {
    font-weight: bold;
    color: #2c3e50;
}

.metric-value.positive { color: #28a745; }

/* ===================================
   9. 对冲分析（已移除）
   ================================== */

/* 对冲分析相关样式已被移除 */
/* 如需恢复对冲功能，请参考版本控制历史记录 */

.details-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 6px;
    overflow: hidden;
}

.details-table th,
.details-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #d1d5db;
    font-size: 14px;
}

.details-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.details-table .no-data {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

/* ===================================
   10. 异常交易分析
   ================================== */

.abnormal-analysis-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    /* margin-bottom 已在统一规则中设置 */
}

.abnormal-analysis-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2em;
    font-weight: 600;
}

.abnormal-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
}

.abnormal-card {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid;
}

.abnormal-card.wash-trading { border-left: 4px solid #ef4444; }
.abnormal-card.high-frequency { border-left: 4px solid #f59e0b; }
.abnormal-card.funding-arbitrage { border-left: 4px solid #6366f1; }

.abnormal-card h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
    font-weight: 600;
}

.abnormal-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.abnormal-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.abnormal-item label {
    color: #6b7280;
    font-size: 14px;
}

.abnormal-item .value {
    font-weight: 600;
    color: #2c3e50;
}

.abnormal-summary {
    grid-column: 1 / -1;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.abnormal-summary h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.summary-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-item {
    text-align: center;
}

.summary-item label {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 5px;
}

.summary-item .value {
    font-size: 1.2em;
    font-weight: bold;
    color: #2c3e50;
}

.summary-item .value.warning {
    color: #f59e0b;
}

.summary-item .value.positive {
    color: #10b981;
}

/* ===================================
   11. 关联分析部分
   ================================== */

.association-section {
    background: white;
    padding: 25px;
    margin-bottom: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.association-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    font-size: 1.4em;
    font-weight: 600;
}

.association-tabs {
    display: flex;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 20px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.tab-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.tab-btn:hover {
    background: #e9ecef;
}

.association-content {
    min-height: 200px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.association-summary {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 15px;
    font-weight: 500;
    color: #2c3e50;
}

.users-list {
    display: grid;
    gap: 10px;
}

.associated-user {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.user-id {
    font-weight: 600;
    color: #2c3e50;
}

.user-details {
    font-size: 0.9em;
    color: #6c757d;
}

.shared-info {
    display: flex;
    gap: 10px;
    font-size: 0.8em;
}

/* 关联用户项样式 */
.associated-user-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.associated-user-item:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.associated-user-item .user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.associated-user-item .user-header {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.associated-user-item .user-id {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1em;
}

.associated-user-item .bd-name {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

.associated-user-item .risk-score {
    background: #fff3e0;
    color: #f57c00;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

.associated-user-item .shared-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.associated-user-item .shared-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.85em;
    color: #495057;
}

.associated-user-item .shared-info i {
    color: #6c757d;
    width: 14px;
}

.associated-user-item .shared-info code {
    background: #f8f9fa;
    color: #495057;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8em;
    border: 1px solid #e9ecef;
}

.associated-user-item .ip-info code {
    color: #0d6efd;
    background: #e7f1ff;
    border-color: #b6d7ff;
}

.associated-user-item .device-info code {
    color: #198754;
    background: #d1e7dd;
    border-color: #a3cfbb;
}

.associated-user-item .activity-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8em;
    color: #6c757d;
    margin-top: 3px;
}

.associated-user-item .user-actions {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-left: 15px;
}

.associated-user-item .user-actions .btn {
    white-space: nowrap;
    font-size: 0.85em;
}

/* ===================================
   12. 交易详情部分
   ================================== */

.transaction-details-section {
    background: white;
    padding: 25px;
    margin-bottom: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.transaction-details-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    font-size: 1.4em;
    font-weight: 600;
}

.details-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 20px;
}

.filter-controls, .sort-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.filter-controls select,
.sort-controls select,
.filter-controls input {
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 14px;
}

.transactions-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.transaction-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.transaction-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.transaction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f8f9fa;
}

.transaction-index {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.risk-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.transaction-time {
    color: #6c757d;
    font-size: 14px;
}

.expand-btn {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    font-size: 14px;
}

.expand-btn:hover {
    text-decoration: underline;
}

.transaction-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-label {
    color: #6c757d;
    font-size: 14px;
}

.summary-value {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.summary-value.risk-type {
    color: #dc3545;
}

.summary-value.volume {
    color: #007bff;
}

.summary-value.leverage {
    color: #ffc107;
}

.transaction-details {
    display: none;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f8f9fa;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.detail-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
}

.detail-section h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 16px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 5px;
}

.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
}

.pagination-controls button:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.6;
}

/* ===================================
   13. 模态框样式
   ================================== */

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 1200px;
    max-height: 85vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.5em;
}

.modal-body {
    color: #2c3e50;
    line-height: 1.6;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    line-height: 1;
}

.close:hover {
    color: #000;
    text-decoration: none;
}

.no-data {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px;
}

/* ===================================
   14. 响应式设计
   ================================== */

/* 中等屏幕尺寸 - 平板设备 */
@media (max-width: 1024px) and (min-width: 769px) {
    .advantage-coins-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .abnormal-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .professional-score-dashboard {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .profile-basic-info {
        grid-template-columns: 1fr;
    }

    .stats-cards-grid {
        grid-template-columns: 1fr;
    }

    .container {
        padding: 10px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .derived-stats-grid {
        grid-template-columns: 1fr;
    }

    .preferences-grid {
        grid-template-columns: 1fr;
    }

    .abnormal-stats-grid {
        grid-template-columns: 1fr;
    }

    .metrics-tabs {
        overflow-x: auto;
    }

    .coin-overview-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .advantage-coins-grid {
        grid-template-columns: 1fr;
    }

    /* .hedge-summary-cards {  对冲样式已移除
        grid-template-columns: repeat(2, 1fr);
    } */

    .advantage-metrics {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .coin-overview-stats {
        grid-template-columns: 1fr;
    }

    /* .hedge-summary-cards {  对冲样式已移除
        grid-template-columns: 1fr;
    } */

    .ranking-table-container {
        overflow-x: auto;
    }

    .coin-ranking-table th,
    .coin-ranking-table td {
        padding: 8px 4px;
        font-size: 11px;
    }
}

/* ===================================
   15. 动画和过渡效果
   ================================== */

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* ===================================
   16. 风险摘要部分
   ================================== */

.risk-summary-section {
    background: white;
    padding: 25px;
    margin-bottom: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.risk-summary-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-card {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-top: 4px solid #dc3545;
}

.summary-card h3 {
    color: #6c757d;
    font-size: 0.9em;
    margin-bottom: 10px;
}

.summary-value {
    font-size: 2em;
    font-weight: bold;
    color: #dc3545;
}

/* ===================================
   17. 额外的功能性样式
   ================================== */

.expertise-classification {
    margin-bottom: 25px;
}

.expertise-classification h4 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.expertise-levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.expertise-level {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-top: 4px solid;
}

.expertise-level.expert { border-color: #28a745; }
.expertise-level.skilled { border-color: #007bff; }
.expertise-level.average { border-color: #ffc107; }
.expertise-level.weak { border-color: #6c757d; }

.expertise-level h5 {
    color: #2c3e50;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.coins-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.coin-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.coin-item .coin-name {
    font-weight: bold;
    color: #2c3e50;
}

.coin-item .coin-stats {
    font-size: 0.9em;
    color: #6c757d;
}

/* 交易洞察与建议 */
.trading-insights {
    margin-bottom: 25px;
}

.trading-insights h4 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.insight-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.insight-card h5 {
    color: #2c3e50;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.insight-text {
    color: #495057;
    line-height: 1.6;
    margin: 0;
}

.top-performer-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.performer-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.performer-item:last-child {
    border-bottom: none;
}

.performer-item .label {
    color: #6c757d;
    font-size: 0.9em;
}

.performer-item .value {
    font-weight: bold;
    color: #2c3e50;
}

.suggestions-list {
    margin: 0;
    padding-left: 20px;
    color: #495057;
}

.suggestions-list li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* ===================================
   14. 对敲交易详情样式
   ================================== */

/* 对敲交易详情样式 */
.wash-trading-details {
    padding: 20px;
    max-width: 100%;
    overflow-x: auto;
}

.wash-trading-details h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.wash-trading-details h4 {
    color: #34495e;
    margin: 20px 0 15px 0;
    font-size: 1.1em;
}

.basic-info-section,
.trading-pairs-section,
.time-analysis-section,
.profit-analysis-section {
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.info-item label {
    font-weight: 600;
    color: #495057;
    margin-right: 10px;
}

.info-item span {
    color: #212529;
    font-weight: 500;
}

/* 交易对容器样式 */
.trading-pair-container {
    margin-top: 15px;
}

.user-trading-info {
    display: flex;
    align-items: stretch;
    gap: 25px;
    margin-top: 15px;
    min-height: 300px;
}

.user-section {
    flex: 1;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    position: relative;
}

.user-section.user-a {
    border-left-color: #28a745;
}

.user-section.user-b {
    border-left-color: #dc3545;
}

.user-section h5 {
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.trading-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #f8f9fa;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9em;
}

.detail-item span {
    font-weight: 500;
    color: #212529;
}

.vs-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 10px;
    color: #6c757d;
    font-weight: bold;
}

.vs-divider i {
    font-size: 1.5em;
    margin-bottom: 5px;
}

/* 特殊字段样式 */
.user-id {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.85em;
}

.position-id {
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.85em;
}

.side {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 600;
    background: #17a2b8;
    color: white;
}

.amount {
    font-weight: 600;
    color: #495057;
}

.profit.positive {
    color: #28a745;
    font-weight: 600;
}

.profit.negative {
    color: #dc3545;
    font-weight: 600;
}

.detection-type {
    background: #6f42c1;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 600;
}

.risk-score {
    background: #fd7e14;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 600;
}

/* 时间和利润分析样式 */
.time-metrics,
.profit-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.time-gap {
    font-weight: 600;
    color: #495057;
    background: #e3f2fd;
    padding: 4px 8px;
    border-radius: 4px;
}

.net-profit.positive {
    color: #28a745;
    font-weight: 600;
}

.net-profit.negative {
    color: #dc3545;
    font-weight: 600;
}

.total-amount {
    font-weight: 600;
    color: #495057;
}

.profit-rate.positive {
    color: #28a745;
    font-weight: 600;
}

.profit-rate.negative {
    color: #dc3545;
    font-weight: 600;
}

/* 简单交易详情样式 */
.simple-transaction-details {
    padding: 20px;
}

.simple-transaction-details h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.note {
    margin-top: 20px;
    padding: 15px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 10px;
}

.note i {
    color: #f39c12;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-trading-info {
        flex-direction: column;
    }

    .vs-divider {
        flex-direction: row;
        padding: 10px;
    }

    .vs-divider i {
        margin-bottom: 0;
        margin-right: 5px;
    }

    .info-grid,
    .time-metrics,
    .profit-metrics {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 2% auto;
    }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
    .modal-content {
        width: 85%;
        max-width: 1400px;
    }

    .info-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }

    .time-metrics,
    .profit-metrics {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .user-trading-info {
        gap: 30px;
    }
}

/* ===================================
   15. 风险交易列表样式
   ================================== */

.risk-transaction-item {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1.5fr 1.5fr 1.5fr 1fr;
    gap: 15px;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.risk-transaction-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #3498db;
}

.risk-transaction-item .tx-id {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #495057;
    font-weight: 600;
}

.risk-transaction-item .risk-type {
    background: #6f42c1;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 600;
    text-align: center;
}



.risk-transaction-item .tx-volume {
    font-weight: 600;
    color: #495057;
    text-align: right;
}

.risk-transaction-item .detection-method {
    color: #6c757d;
    font-size: 0.9em;
}

.risk-transaction-item .tx-time {
    color: #6c757d;
    font-size: 0.85em;
}

.risk-transaction-item .tx-actions {
    text-align: center;
}

.risk-transaction-item .tx-actions .btn {
    padding: 4px 8px;
    font-size: 0.8em;
}

/* 风险交易列表头部 */
.transactions-list-header {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1.5fr 1.5fr 1.5fr 1fr;
    gap: 15px;
    padding: 10px 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
}

.transactions-list {
    border-radius: 0 0 8px 8px;
}

.no-data {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .risk-transaction-item {
        grid-template-columns: 1fr;
        gap: 10px;
        text-align: left;
    }

    .transactions-list-header {
        display: none;
    }

    .risk-transaction-item > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .risk-transaction-item > div:before {
        content: attr(data-label);
        font-weight: 600;
        color: #6c757d;
        min-width: 100px;
    }
}
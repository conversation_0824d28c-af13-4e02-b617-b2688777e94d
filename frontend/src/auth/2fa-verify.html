<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双因子认证 - 风险分析系统</title>
    <link rel="stylesheet" href="/src/auth/login.css">
    <style>
        .verification-code-input {
            font-size: 24px;
            text-align: center;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            padding: 15px;
            margin: 20px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            width: 100%;
            max-width: 300px;
        }
        
        .verification-code-input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .qr-code-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .qr-code-image {
            max-width: 200px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
        }
        
        .backup-codes {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .backup-codes h4 {
            margin-top: 0;
            color: #495057;
        }
        
        .backup-code {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 8px;
            background: white;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .step.active .step-number {
            background: #667eea;
            color: white;
        }
        
        .step.completed .step-number {
            background: #28a745;
            color: white;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #0056b3;
        }
        
        .app-recommendations {
            display: flex;
            justify-content: space-around;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .app-recommendation {
            text-align: center;
            margin: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            min-width: 120px;
        }
        
        .app-icon {
            width: 40px;
            height: 40px;
            margin: 0 auto 8px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>双因子认证</h1>
                <p id="pageDescription">请完成双因子认证设置</p>
            </div>
            
            <!-- 步骤指示器 -->
            <div class="step-indicator" id="stepIndicator">
                <div class="step" id="step1">
                    <div class="step-number">1</div>
                    <span>扫描二维码</span>
                </div>
                <div class="step" id="step2">
                    <div class="step-number">2</div>
                    <span>输入验证码</span>
                </div>
                <div class="step" id="step3">
                    <div class="step-number">3</div>
                    <span>完成设置</span>
                </div>
            </div>
            
            <!-- 首次设置界面 -->
            <div id="setupInterface" style="display: none;">
                <div class="instructions">
                    <h4>设置双因子认证</h4>
                    <p>1. 在手机上安装认证应用</p>
                    <div class="app-recommendations">
                        <div class="app-recommendation">
                            <div class="app-icon">🔐</div>
                            <div>Google Authenticator</div>
                        </div>
                        <div class="app-recommendation">
                            <div class="app-icon">🛡️</div>
                            <div>Microsoft Authenticator</div>
                        </div>
                        <div class="app-recommendation">
                            <div class="app-icon">🔑</div>
                            <div>Authy</div>
                        </div>
                    </div>
                    <p>2. 使用认证应用扫描下方二维码</p>
                </div>
                
                <div class="qr-code-container">
                    <img id="qrCodeImage" class="qr-code-image" alt="2FA二维码" style="display: none;">
                    <div id="qrCodeLoading">正在生成二维码...</div>
                </div>
                
                <div class="backup-codes" id="backupCodes" style="display: none;">
                    <h4>备用恢复码</h4>
                    <p>请妥善保存这些恢复码，当您无法使用认证应用时可以使用：</p>
                    <div id="backupCodesList"></div>
                </div>
            </div>
            
            <!-- 验证界面 -->
            <div id="verifyInterface">
                <div class="instructions">
                    <h4 id="verifyTitle">请输入验证码</h4>
                    <p id="verifyDescription">请打开您的认证应用，输入6位验证码</p>
                </div>
                
                <form id="verifyForm" class="login-form">
                    <div class="form-group">
                        <label for="verificationCode">验证码</label>
                        <input type="text" 
                               id="verificationCode" 
                               name="verificationCode" 
                               class="verification-code-input"
                               placeholder="000000" 
                               maxlength="6" 
                               pattern="[0-9]{6}"
                               autocomplete="off"
                               required>
                    </div>
                    
                    <button type="submit" class="login-btn" id="verifyBtn">
                        <span class="btn-text">验证</span>
                        <span class="btn-loading" style="display: none;">验证中...</span>
                    </button>
                    
                    <div class="error-message" id="errorMessage" style="display: none;"></div>
                    <div class="success-message" id="successMessage" style="display: none;"></div>
                </form>
                
                <div class="form-footer">
                    <p><a href="#" id="useBackupCode" style="display: none;">使用备用恢复码</a></p>
                    <p><a href="/login">返回登录</a></p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/src/auth/2fa-verify.js"></script>
</body>
</html>

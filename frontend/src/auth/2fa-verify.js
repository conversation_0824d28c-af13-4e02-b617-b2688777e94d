/**
 * 2FA验证页面逻辑
 */

class TwoFAManager {
    constructor() {
        this.tempToken = null;
        this.username = null;
        this.isSetupMode = false;
        this.setupData = null;

        this.init();
    }
    
    init() {
        // 从URL参数或localStorage获取用户信息
        this.getUserInfo();
        
        // 绑定事件
        this.bindEvents();
        
        // 初始化界面
        this.initInterface();
        
        // 自动聚焦验证码输入框
        document.getElementById('verificationCode').focus();
    }
    
    getUserInfo() {
        // 优先使用安全传递的数据（POST方式），然后是URL参数（向后兼容），最后是localStorage
        if (window.secureAuthData) {
            // 使用POST方式安全传递的数据
            this.tempToken = window.secureAuthData.tempToken;
            this.username = window.secureAuthData.username;
            this.isSetupMode = window.secureAuthData.isSetup;

            // 清除敏感数据，防止内存泄露
            delete window.secureAuthData;

        } else {
            // 降级处理：从URL参数或localStorage获取（向后兼容）
            const urlParams = new URLSearchParams(window.location.search);
            this.tempToken = urlParams.get('temp_token') || localStorage.getItem('2fa_temp_token');
            this.username = urlParams.get('username') || localStorage.getItem('2fa_username');
            this.isSetupMode = urlParams.get('setup') === 'true' || localStorage.getItem('2fa_setup_mode') === 'true';
        }

        if (!this.tempToken) {
            this.showError('临时令牌缺失，请重新登录');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
            return;
        }

        // 保存到localStorage以防页面刷新（但不保存敏感的temp_token）
        localStorage.setItem('2fa_username', this.username);
        localStorage.setItem('2fa_setup_mode', this.isSetupMode.toString());

        // 注意：不再将temp_token保存到localStorage，提高安全性
        // localStorage.setItem('2fa_temp_token', this.tempToken);
    }
    
    initInterface() {
        const pageDescription = document.getElementById('pageDescription');
        const setupInterface = document.getElementById('setupInterface');
        const verifyInterface = document.getElementById('verifyInterface');
        const verifyTitle = document.getElementById('verifyTitle');
        const verifyDescription = document.getElementById('verifyDescription');
        const useBackupCode = document.getElementById('useBackupCode');
        
        if (this.isSetupMode) {
            // 首次设置模式
            pageDescription.textContent = `欢迎 ${this.username}，请完成双因子认证设置`;
            setupInterface.style.display = 'block';
            verifyTitle.textContent = '验证设置';
            verifyDescription.textContent = '请输入认证应用中显示的6位验证码以完成设置';
            this.updateStepIndicator(1);
            
            // 获取2FA设置数据
            this.loadSetupData();
        } else {
            // 登录验证模式
            pageDescription.textContent = `${this.username}，请输入双因子认证码`;
            setupInterface.style.display = 'none';
            verifyTitle.textContent = '双因子认证';
            verifyDescription.textContent = '请打开您的认证应用，输入6位验证码';
            useBackupCode.style.display = 'inline';
            this.hideStepIndicator();
        }
    }
    
    async loadSetupData() {
        try {
            // 从localStorage获取设置数据（应该在登录时保存）
            const setupDataStr = localStorage.getItem('2fa_setup_data');
            if (setupDataStr) {
                this.setupData = JSON.parse(setupDataStr);
                this.displaySetupData();
            } else {
                this.showError('2FA设置数据缺失，请联系管理员');
            }
        } catch (error) {
            console.error('加载2FA设置数据失败:', error);
            this.showError('加载设置数据失败');
        }
    }
    
    displaySetupData() {
        if (!this.setupData) return;
        
        const qrCodeImage = document.getElementById('qrCodeImage');
        const qrCodeLoading = document.getElementById('qrCodeLoading');
        const backupCodes = document.getElementById('backupCodes');
        const backupCodesList = document.getElementById('backupCodesList');
        
        // 显示二维码
        if (this.setupData.qr_code) {
            qrCodeImage.src = this.setupData.qr_code;
            qrCodeImage.style.display = 'block';
            qrCodeLoading.style.display = 'none';
            this.updateStepIndicator(2);
        }
        
        // 显示备用码
        if (this.setupData.backup_codes) {
            backupCodesList.innerHTML = this.setupData.backup_codes
                .map(code => `<span class="backup-code">${code}</span>`)
                .join('');
            backupCodes.style.display = 'block';
        }
    }
    
    bindEvents() {
        const verifyForm = document.getElementById('verifyForm');
        const verificationCode = document.getElementById('verificationCode');
        const useBackupCode = document.getElementById('useBackupCode');
        
        verifyForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleVerify();
        });
        
        // 验证码输入格式化
        verificationCode.addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, ''); // 只保留数字
            if (value.length > 6) {
                value = value.substring(0, 6);
            }
            e.target.value = value;
            
            // 自动提交
            if (value.length === 6) {
                setTimeout(() => this.handleVerify(), 500);
            }
        });
        
        // 备用码链接
        useBackupCode.addEventListener('click', (e) => {
            e.preventDefault();
            this.showBackupCodeInput();
        });
    }
    
    async handleVerify() {
        const code = document.getElementById('verificationCode').value.trim();
        
        if (!code || code.length !== 6) {
            this.showError('请输入6位验证码');
            return;
        }
        
        this.setLoading(true);
        this.hideError();
        
        try {
            const endpoint = this.isSetupMode ? '/api/auth/2fa/setup-verify' : '/api/auth/2fa/verify';
            
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    temp_token: this.tempToken,
                    totp_code: code
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.handleVerifySuccess(data);
            } else {
                this.showError(data.error || '验证失败，请重试');
                this.clearCode();
            }
        } catch (error) {
            console.error('验证请求失败:', error);
            this.showError('网络连接失败，请重试');
            this.clearCode();
        } finally {
            this.setLoading(false);
        }
    }
    
    handleVerifySuccess(data) {
        if (this.isSetupMode) {
            // 设置模式成功
            this.updateStepIndicator(3);
            this.showSuccess('2FA设置成功！正在跳转...');
            
            // 清除设置数据
            localStorage.removeItem('2fa_setup_data');
            localStorage.removeItem('2fa_setup_mode');
            localStorage.removeItem('2fa_username');
            // 注意：temp_token不再存储在localStorage中，无需清理
            
            setTimeout(() => {
                window.location.href = '/agent_relationship.html';
            }, 2000);
        } else {
            // 登录验证成功
            this.showSuccess('验证成功！正在跳转...');
            
            // 保存用户信息
            localStorage.setItem('userAuth', JSON.stringify(data.user));
            
            // 清除2FA临时数据
            localStorage.removeItem('2fa_username');
            // 注意：temp_token不再存储在localStorage中，无需清理
            
            setTimeout(() => {
                window.location.href = '/agent_relationship.html';
            }, 1000);
        }
    }
    
    showBackupCodeInput() {
        const verificationCode = document.getElementById('verificationCode');
        const verifyDescription = document.getElementById('verifyDescription');
        
        verificationCode.placeholder = '输入8位恢复码';
        verificationCode.maxLength = 8;
        verificationCode.value = '';
        verifyDescription.textContent = '请输入8位备用恢复码';
        
        document.getElementById('useBackupCode').style.display = 'none';
    }
    
    updateStepIndicator(currentStep) {
        for (let i = 1; i <= 3; i++) {
            const step = document.getElementById(`step${i}`);
            if (i < currentStep) {
                step.className = 'step completed';
            } else if (i === currentStep) {
                step.className = 'step active';
            } else {
                step.className = 'step';
            }
        }
    }
    
    hideStepIndicator() {
        document.getElementById('stepIndicator').style.display = 'none';
    }
    
    setLoading(loading) {
        const verifyBtn = document.getElementById('verifyBtn');
        const btnText = verifyBtn.querySelector('.btn-text');
        const btnLoading = verifyBtn.querySelector('.btn-loading');
        
        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline';
            verifyBtn.disabled = true;
        } else {
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            verifyBtn.disabled = false;
        }
    }
    
    showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            this.hideError();
        }, 3000);
    }
    
    hideError() {
        document.getElementById('errorMessage').style.display = 'none';
    }
    
    showSuccess(message) {
        const successMessage = document.getElementById('successMessage');
        successMessage.textContent = message;
        successMessage.style.display = 'block';
    }
    
    clearCode() {
        document.getElementById('verificationCode').value = '';
        document.getElementById('verificationCode').focus();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new TwoFAManager();
});

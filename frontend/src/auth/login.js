/**
 * 登录页面逻辑
 */
import './login.css';
class LoginManager {
    constructor() {
        this.loginForm = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.loginBtn = document.getElementById('loginBtn');
        this.errorMessage = document.getElementById('errorMessage');
        
        this.init();
    }
    
    init() {
        // 检查是否已经登录
        this.checkExistingAuth();
        
        // 绑定事件
        this.bindEvents();
        
        // 自动聚焦用户名输入框
        this.usernameInput.focus();
    }
    
    async checkExistingAuth() {
        try {
            const response = await fetch('/api/auth/check', {
                method: 'GET',
                credentials: 'include'
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.authenticated) {
                    // 已经登录，重定向到主页
                    this.redirectToHome();
                }
            }
        } catch (error) {
            console.log('检查登录状态失败，继续显示登录页面');
        }
    }
    
    bindEvents() {
        this.loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
        
        // 回车键登录
        this.passwordInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleLogin();
            }
        });
        
        // 清除错误信息
        [this.usernameInput, this.passwordInput].forEach(input => {
            input.addEventListener('input', () => {
                this.hideError();
            });
        });
    }
    
    async handleLogin() {
        const username = this.usernameInput.value.trim();
        const password = this.passwordInput.value;
        
        // 验证输入
        if (!username) {
            this.showError('请输入用户名');
            this.usernameInput.focus();
            return;
        }
        
        if (!password) {
            this.showError('请输入密码');
            this.passwordInput.focus();
            return;
        }
        
        // 显示加载状态
        this.setLoading(true);
        this.hideError();
        
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                if (data.require_2fa) {
                    // 需要2FA验证
                    this.showSuccess('密码验证成功，正在跳转到2FA验证...');

                    // 安全跳转到2FA验证页面，使用POST传递敏感数据
                    setTimeout(() => {
                        this.secureRedirectTo2FA(data.temp_token, data.username, false);
                    }, 1000);

                } else if (data.require_2fa_setup) {
                    // 需要首次设置2FA
                    this.showSuccess('密码验证成功，正在跳转到2FA设置...');

                    // 保存2FA设置数据到localStorage
                    if (data['2fa_setup']) {
                        localStorage.setItem('2fa_setup_data', JSON.stringify(data['2fa_setup']));
                    } else if (data['2fa_setup_data']) {
                        localStorage.setItem('2fa_setup_data', JSON.stringify(data['2fa_setup_data']));
                    }

                    // 安全跳转到2FA设置页面，使用POST传递敏感数据
                    setTimeout(() => {
                        this.secureRedirectTo2FA(data.temp_token, data.username, true);
                    }, 1000);

                } else {
                    // 直接登录成功（仅在不强制2FA的情况下）
                    // 注意：在当前强制2FA的配置下，这种情况不应该发生
                    console.warn('用户直接登录成功，未经过2FA验证');
                    this.showSuccess('登录成功，正在跳转...');

                    // 保存用户信息到localStorage
                    localStorage.setItem('userAuth', JSON.stringify(data.user));

                    // 延迟跳转，让用户看到成功信息
                    setTimeout(() => {
                        this.redirectToHome();
                    }, 1000);
                }

            } else {
                // 登录失败
                this.showError(data.error || '登录失败，请重试');
                this.passwordInput.value = '';
                this.passwordInput.focus();
            }
            
        } catch (error) {
            console.error('登录请求失败:', error);
            this.showError('网络连接失败，请检查网络后重试');
        } finally {
            this.setLoading(false);
        }
    }
    
    setLoading(loading) {
        this.loginBtn.disabled = loading;
        if (loading) {
            this.loginBtn.classList.add('loading');
        } else {
            this.loginBtn.classList.remove('loading');
        }
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        this.errorMessage.className = 'error-message';
    }
    
    showSuccess(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        this.errorMessage.className = 'success-message';
        this.errorMessage.style.backgroundColor = '#efe';
        this.errorMessage.style.borderColor = '#cfc';
        this.errorMessage.style.color = '#363';
    }
    
    hideError() {
        this.errorMessage.style.display = 'none';
    }
    
    /**
     * 安全地重定向到2FA验证页面
     * 使用POST方式传递敏感数据，避免在URL中暴露temp_token
     */
    secureRedirectTo2FA(tempToken, username, isSetup = false) {
        try {
            // 创建一个隐藏的表单来POST数据
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/2fa-verify';
            form.style.display = 'none';

            // 添加temp_token字段
            const tokenField = document.createElement('input');
            tokenField.type = 'hidden';
            tokenField.name = 'temp_token';
            tokenField.value = tempToken;
            form.appendChild(tokenField);

            // 添加username字段
            const usernameField = document.createElement('input');
            usernameField.type = 'hidden';
            usernameField.name = 'username';
            usernameField.value = username;
            form.appendChild(usernameField);

            // 如果是设置模式，添加setup字段
            if (isSetup) {
                const setupField = document.createElement('input');
                setupField.type = 'hidden';
                setupField.name = 'setup';
                setupField.value = 'true';
                form.appendChild(setupField);
            }

            // 将表单添加到页面并提交
            document.body.appendChild(form);
            form.submit();

        } catch (error) {
            console.error('安全重定向失败:', error);
            // 降级处理：显示错误信息
            this.showError('页面跳转失败，请刷新页面重试');
        }
    }

    redirectToHome() {
        // 登录成功后跳转到代理分析页面
        const homePage = '/agent_relationship.html';

        // 跳转到代理分析页面
        if (window.location.origin) {
            window.location.href = window.location.origin + homePage;
        } else {
            window.location.href = homePage;
        }
    }
}

// 页面加载完成后初始化登录管理器
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();
    
    // 添加一些额外的用户体验优化
    addLoginEnhancements();
});

function addLoginEnhancements() {
    // 添加密码可见性切换功能
    addPasswordToggle();
    
    // 添加表单验证反馈
    addFormValidation();
    
    // 添加键盘快捷键
    addKeyboardShortcuts();
}

function addPasswordToggle() {
    const passwordInput = document.getElementById('password');
    const passwordGroup = passwordInput.parentElement;
    
    // 创建眼睛图标
    const toggleBtn = document.createElement('button');
    toggleBtn.type = 'button';
    toggleBtn.className = 'password-toggle';
    toggleBtn.innerHTML = '👁️';
    toggleBtn.title = '显示/隐藏密码';
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .form-group {
            position: relative;
        }
        .password-toggle {
            position: absolute;
            right: 12px;
            top: 32px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            opacity: 0.6;
            transition: opacity 0.3s;
        }
        .password-toggle:hover {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
    
    // 添加到DOM
    passwordGroup.style.position = 'relative';
    passwordGroup.appendChild(toggleBtn);
    
    // 绑定点击事件
    toggleBtn.addEventListener('click', () => {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleBtn.innerHTML = '🙈';
        } else {
            passwordInput.type = 'password';
            toggleBtn.innerHTML = '👁️';
        }
    });
}

function addFormValidation() {
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    // 实时验证用户名
    usernameInput.addEventListener('blur', () => {
        if (usernameInput.value.trim().length < 2) {
            usernameInput.style.borderColor = '#e74c3c';
        } else {
            usernameInput.style.borderColor = '#27ae60';
        }
    });
    
    // 实时验证密码
    passwordInput.addEventListener('blur', () => {
        if (passwordInput.value.length < 6) {
            passwordInput.style.borderColor = '#e74c3c';
        } else {
            passwordInput.style.borderColor = '#27ae60';
        }
    });
    
    // 聚焦时重置边框颜色
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('focus', () => {
            input.style.borderColor = '#667eea';
        });
    });
}

function addKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + Enter 快速登录
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            document.getElementById('loginBtn').click();
        }
        
        // Escape 清空表单
        if (e.key === 'Escape') {
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            document.getElementById('username').focus();
        }
    });
} 
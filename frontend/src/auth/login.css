/* 登录页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-box {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 40px;
    text-align: center;
}

.logo h1 {
    color: #333;
    font-size: 24px;
    margin-bottom: 8px;
    font-weight: 600;
}

.logo p {
    color: #666;
    font-size: 14px;
    margin-bottom: 40px;
}

.login-form {
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #333;
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group input[type="text"],
.form-group input[type="password"] {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="password"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 8px;
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.login-btn.loading {
    background: #999;
}

.btn-loading {
    display: none;
}

.login-btn.loading .btn-text {
    display: none;
}

.login-btn.loading .btn-loading {
    display: inline;
}

.error-message {
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
    padding: 12px;
    border-radius: 6px;
    margin-top: 15px;
    font-size: 14px;
    text-align: center;
}

.success-message {
    background: #efe !important;
    border: 1px solid #cfc !important;
    color: #363 !important;
    padding: 12px;
    border-radius: 6px;
    margin-top: 15px;
    font-size: 14px;
    text-align: center;
}

.login-footer {
    margin-top: 30px;
    text-align: center;
}

.login-footer p {
    color: #999;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        padding: 10px;
    }
    
    .login-box {
        padding: 30px 20px;
    }
    
    .logo h1 {
        font-size: 20px;
    }
}

/* 密码可见性切换按钮 */
.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 14px;
}

.password-group {
    position: relative;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-box {
    animation: fadeIn 0.5s ease-out;
}

/* 输入框验证状态 */
.form-group input.invalid {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.form-group input.valid {
    border-color: #27ae60;
    box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    .login-box {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .logo h1 {
        color: #ecf0f1;
    }
    
    .form-group label {
        color: #bdc3c7;
    }
    
    .form-group input {
        background-color: #34495e;
        border-color: #4a5c6a;
        color: #ecf0f1;
    }
    
    .form-group input:focus {
        background-color: #3d4f60;
        border-color: #667eea;
    }
    
    .checkbox-label {
        color: #bdc3c7;
    }
    
    .login-footer {
        border-top-color: #4a5c6a;
    }
    
    .login-footer p {
        color: #95a5a6;
    }
} 
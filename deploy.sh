#!/bin/bash

# =============================================================================
# 风险链路分析工具 - 自动化部署脚本
# 用于在Linux服务器上自动部署应用程序
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# =============================================================================
# 配置参数
# =============================================================================

# 部署目录
DEPLOY_DIR="/opt/risk-link-analysis-tool"
SERVICE_NAME="risk-analysis"
SERVICE_USER="www-data"
SERVICE_GROUP="www-data"

# 备份目录
BACKUP_DIR="/opt/backups/risk-analysis"
BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# =============================================================================
# 工具函数
# =============================================================================

# 检查是否以root权限运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请以root权限运行此脚本"
        log_info "使用: sudo $0"
        exit 1
    fi
}

# 检查系统依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    local deps=("python3" "python3-venv" "python3-pip" "nodejs" "npm" "redis-server" "git")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v $dep &> /dev/null && ! dpkg -l | grep -q "^ii  $dep "; then
            missing_deps+=($dep)
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_warn "缺少以下依赖: ${missing_deps[*]}"
        log_info "正在安装缺少的依赖..."
        
        apt update
        apt install -y "${missing_deps[@]}"
    fi
    
    log_info "系统依赖检查完成"
}

# 创建系统用户
create_service_user() {
    log_step "创建服务用户..."
    
    if ! id "$SERVICE_USER" &>/dev/null; then
        log_info "创建用户: $SERVICE_USER"
        useradd --system --shell /bin/bash --home-dir $DEPLOY_DIR --create-home $SERVICE_USER
    else
        log_info "用户 $SERVICE_USER 已存在"
    fi
}

# 创建部署目录
create_deploy_directory() {
    log_step "创建部署目录..."
    
    # 创建主目录
    mkdir -p $DEPLOY_DIR
    
    # 创建子目录
    mkdir -p $DEPLOY_DIR/{data,logs,temp,uploads,venv}
    mkdir -p $BACKUP_DIR
    
    # 设置权限
    chown -R $SERVICE_USER:$SERVICE_GROUP $DEPLOY_DIR
    chmod -R 755 $DEPLOY_DIR
    
    log_info "部署目录创建完成: $DEPLOY_DIR"
}

# 备份现有部署
backup_existing_deployment() {
    if [ -d "$DEPLOY_DIR" ] && [ "$(ls -A $DEPLOY_DIR)" ]; then
        log_step "备份现有部署..."
        
        local backup_path="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
        mkdir -p "$backup_path"
        
        # 备份应用文件（排除虚拟环境和临时文件）
        rsync -av --exclude='venv/' --exclude='node_modules/' --exclude='temp/' \
              --exclude='logs/' --exclude='*.pyc' --exclude='__pycache__/' \
              "$DEPLOY_DIR/" "$backup_path/"
        
        log_info "备份完成: $backup_path"
    fi
}

# 部署应用文件
deploy_application() {
    log_step "部署应用文件..."
    
    local current_dir=$(pwd)
    
    # 复制应用文件到部署目录
    rsync -av --exclude='venv/' --exclude='node_modules/' --exclude='temp/' \
          --exclude='logs/' --exclude='*.pyc' --exclude='__pycache__/' \
          --exclude='.git/' --exclude='*.log' \
          "$current_dir/" "$DEPLOY_DIR/"
    
    # 设置权限
    chown -R $SERVICE_USER:$SERVICE_GROUP $DEPLOY_DIR
    chmod +x $DEPLOY_DIR/start_production.sh
    
    log_info "应用文件部署完成"
}

# 配置环境
configure_environment() {
    log_step "配置环境..."
    
    # 复制环境配置文件
    if [ ! -f "$DEPLOY_DIR/production.local.env" ]; then
        cp "$DEPLOY_DIR/production.env" "$DEPLOY_DIR/production.local.env"
        log_info "创建本地环境配置文件: production.local.env"
        log_warn "请编辑 $DEPLOY_DIR/production.local.env 以配置生产环境参数"
    fi
    
    # 设置环境配置文件权限
    chmod 600 "$DEPLOY_DIR/production.local.env"
    chown $SERVICE_USER:$SERVICE_GROUP "$DEPLOY_DIR/production.local.env"
}

# 安装Python依赖
install_python_dependencies() {
    log_step "安装Python依赖..."
    
    cd $DEPLOY_DIR
    
    # 以服务用户身份创建虚拟环境
    sudo -u $SERVICE_USER python3 -m venv venv
    
    # 激活虚拟环境并安装依赖
    sudo -u $SERVICE_USER bash -c "
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r backend/requirements.txt
    "
    
    log_info "Python依赖安装完成"
}

# 安装Node.js依赖并构建前端
build_frontend() {
    log_step "构建前端应用..."
    
    cd $DEPLOY_DIR
    
    # 以服务用户身份安装依赖和构建
    sudo -u $SERVICE_USER bash -c "
        npm install
        npm run build
    "
    
    log_info "前端构建完成"
}

# 配置Redis
configure_redis() {
    log_step "配置Redis..."
    
    # 启动Redis服务
    systemctl enable redis-server
    systemctl start redis-server
    
    # 检查Redis状态
    if systemctl is-active --quiet redis-server; then
        log_info "Redis服务运行正常"
    else
        log_error "Redis服务启动失败"
        exit 1
    fi
}

# 安装系统服务
install_system_service() {
    log_step "安装系统服务..."
    
    # 复制服务文件
    cp "$DEPLOY_DIR/risk-analysis.service" "/etc/systemd/system/"
    
    # 重新加载systemd配置
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable $SERVICE_NAME
    
    log_info "系统服务安装完成"
}

# 启动服务
start_service() {
    log_step "启动服务..."
    
    # 停止现有服务（如果正在运行）
    if systemctl is-active --quiet $SERVICE_NAME; then
        systemctl stop $SERVICE_NAME
        sleep 3
    fi
    
    # 启动服务
    systemctl start $SERVICE_NAME
    
    # 检查服务状态
    sleep 5
    if systemctl is-active --quiet $SERVICE_NAME; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        log_info "查看服务状态: systemctl status $SERVICE_NAME"
        log_info "查看日志: journalctl -u $SERVICE_NAME -f"
        exit 1
    fi
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # 使用ufw配置防火墙
        ufw allow 3000/tcp comment "Risk Analysis Frontend"
        ufw allow 5005/tcp comment "Risk Analysis Backend"
        ufw allow 6379/tcp comment "Redis"
        log_info "UFW防火墙规则已添加"
    elif command -v firewall-cmd &> /dev/null; then
        # 使用firewalld配置防火墙
        firewall-cmd --permanent --add-port=3000/tcp
        firewall-cmd --permanent --add-port=5005/tcp
        firewall-cmd --permanent --add-port=6379/tcp
        firewall-cmd --reload
        log_info "Firewalld防火墙规则已添加"
    else
        log_warn "未检测到防火墙管理工具，请手动配置防火墙规则"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_step "部署完成"
    
    echo "======================================"
    echo "🎉 风险链路分析工具部署成功"
    echo "======================================"
    echo "部署目录: $DEPLOY_DIR"
    echo "服务名称: $SERVICE_NAME"
    echo "服务用户: $SERVICE_USER"
    echo "======================================"
    echo "服务管理命令:"
    echo "  启动服务: systemctl start $SERVICE_NAME"
    echo "  停止服务: systemctl stop $SERVICE_NAME"
    echo "  重启服务: systemctl restart $SERVICE_NAME"
    echo "  查看状态: systemctl status $SERVICE_NAME"
    echo "  查看日志: journalctl -u $SERVICE_NAME -f"
    echo "======================================"
    echo "应用访问地址:"
    echo "  前端: http://$(hostname -I | awk '{print $1}'):5005"
    echo "  后端API: http://$(hostname -I | awk '{print $1}'):5005/api"
    echo "======================================"
    echo "配置文件:"
    echo "  环境配置: $DEPLOY_DIR/production.local.env"
    echo "  服务配置: /etc/systemd/system/risk-analysis.service"
    echo "======================================"
    echo "日志文件:"
    echo "  应用日志: $DEPLOY_DIR/logs/"
    echo "  服务日志: journalctl -u $SERVICE_NAME"
    echo "======================================"
}

# =============================================================================
# 主程序
# =============================================================================

main() {
    log_info "开始部署风险链路分析工具..."
    
    # 检查权限
    check_root
    
    # 执行部署流程
    check_dependencies
    create_service_user
    backup_existing_deployment
    create_deploy_directory
    deploy_application
    configure_environment
    install_python_dependencies
    build_frontend
    configure_redis
    install_system_service
    configure_firewall
    start_service
    show_deployment_info
    
    log_info "部署完成！"
}

# 检查是否直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

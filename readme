# 风险链路分析工具

## 项目简介
风险链路分析工具是一个专业的金融风险分析系统，提供合约风险分析、代理关系分析、用户行为分析等功能。

## 系统架构
- **前端**: Vue.js + Bootstrap + Chart.js
- **后端**: Python Flask + DuckDB
- **端口**: 前端3000，后端5005

## 快速启动
```bash
# 启动系统
./start.sh

# 访问地址
前端: http://localhost:3000
后端: http://localhost:5005
```

## 默认账户
- 用户名：admin
- 密码：admin123
- **注意**: 生产环境请立即修改默认密码

## 主要功能
1. **合约风险分析** - 对敲交易检测、高频交易分析
2. **代理关系分析** - BD金字塔结构、关系网络分析
3. **用户行为分析** - 交易画像、专业度评分
4. **关联风险分析** - 跨模块风险关联分析

## 技术文档
详细文档请参考 `API接口和功能模块文档.md`

---
*版本: v1.0.0*
*更新时间: 2025-07-14*
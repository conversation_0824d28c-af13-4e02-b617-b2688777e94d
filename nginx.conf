# =============================================================================
# Nginx配置文件 - 风险链路分析工具
# 用于生产环境的反向代理和静态文件服务
# 
# 安装位置: /etc/nginx/sites-available/risk-analysis
# 启用: sudo ln -s /etc/nginx/sites-available/risk-analysis /etc/nginx/sites-enabled/
# =============================================================================

# 上游后端服务器配置
upstream risk_analysis_backend {
    server 127.0.0.1:5005;
    keepalive 32;
}

# HTTP服务器配置
server {
    listen 80;
    listen [::]:80;
    
    # 服务器名称 - 请根据实际域名修改
    server_name your-domain.com www.your-domain.com;
    
    # 如果使用HTTPS，可以添加重定向
    # return 301 https://$server_name$request_uri;
    
    # 日志配置
    access_log /var/log/nginx/risk-analysis-access.log;
    error_log /var/log/nginx/risk-analysis-error.log;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 客户端最大请求体大小
    client_max_body_size 100M;
    
    # 静态文件服务
    location /static/ {
        alias /opt/risk-link-analysis-tool/backend/agent_analyzer/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 启用gzip压缩
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/javascript
            application/xml+rss
            application/json;
    }
    
    # 上传文件服务
    location /uploads/ {
        alias /opt/risk-link-analysis-tool/uploads/;
        expires 1d;
        add_header Cache-Control "public";
    }
    
    # API请求代理到后端
    location /api/ {
        proxy_pass http://risk_analysis_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置 - 增加超时时间以避免连接中断
        proxy_connect_timeout 300s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;

        # 保持连接活跃
        proxy_set_header Connection "";
        proxy_http_version 1.1;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
    
    # 认证相关请求
    location ~ ^/(login|2fa-verify|2fa-setup|logout) {
        proxy_pass http://risk_analysis_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 认证请求不缓存
        proxy_no_cache 1;
        proxy_cache_bypass 1;
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://risk_analysis_backend;
        access_log off;
    }
    
    # 主页和其他页面
    location / {
        proxy_pass http://risk_analysis_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存HTML页面
        proxy_cache_valid 200 1h;
        proxy_cache_key $scheme$proxy_host$request_uri;
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 机器人文件
    location = /robots.txt {
        add_header Content-Type text/plain;
        return 200 "User-agent: *\nDisallow: /\n";
    }
    
    # Favicon
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# HTTPS服务器配置（可选）
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     
#     server_name your-domain.com www.your-domain.com;
#     
#     # SSL证书配置
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # SSL安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     
#     # 其他配置与HTTP服务器相同
#     # ... (复制上面的location块)
# }

# =============================================================================
# 使用说明
# =============================================================================

# 1. 安装Nginx:
#    sudo apt update
#    sudo apt install nginx
#
# 2. 复制此配置文件:
#    sudo cp nginx.conf /etc/nginx/sites-available/risk-analysis
#
# 3. 修改配置文件中的域名和路径:
#    sudo nano /etc/nginx/sites-available/risk-analysis
#
# 4. 启用站点:
#    sudo ln -s /etc/nginx/sites-available/risk-analysis /etc/nginx/sites-enabled/
#
# 5. 测试配置:
#    sudo nginx -t
#
# 6. 重新加载Nginx:
#    sudo systemctl reload nginx
#
# 7. 如果使用防火墙，允许HTTP和HTTPS流量:
#    sudo ufw allow 'Nginx Full'
#
# 注意事项:
# - 请根据实际域名修改server_name
# - 如果使用HTTPS，请配置SSL证书
# - 确保应用程序路径正确
# - 定期检查和更新安全配置

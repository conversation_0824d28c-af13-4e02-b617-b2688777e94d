[Unit]
Description=Risk Link Analysis Tool - Production Service
Documentation=https://github.com/your-repo/risk-link-analysis-tool
After=network.target redis.service
Wants=redis.service
Requires=network.target

[Service]
Type=forking
User=www-data
Group=www-data
WorkingDirectory=/opt/risk-link-analysis-tool
Environment=PATH=/opt/risk-link-analysis-tool/venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=FLASK_ENV=production
Environment=NODE_ENV=production
EnvironmentFile=-/opt/risk-link-analysis-tool/production.local.env

# 启动命令
ExecStart=/opt/risk-link-analysis-tool/start_production.sh
ExecStop=/bin/kill -TERM $MAINPID
ExecReload=/bin/kill -HUP $MAINPID

# 进程管理
PIDFile=/opt/risk-link-analysis-tool/.backend.pid
KillMode=mixed
KillSignal=SIGTERM
TimeoutStartSec=60
TimeoutStopSec=30
RestartSec=10
Restart=always

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/risk-link-analysis-tool/data
ReadWritePaths=/opt/risk-link-analysis-tool/logs
ReadWritePaths=/opt/risk-link-analysis-tool/temp
ReadWritePaths=/opt/risk-link-analysis-tool/uploads

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 标准输出和错误输出
StandardOutput=append:/opt/risk-link-analysis-tool/logs/service.log
StandardError=append:/opt/risk-link-analysis-tool/logs/service-error.log

[Install]
WantedBy=multi-user.target

#!/bin/bash

# 确保脚本从项目根目录运行
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 停止之前可能运行的进程
echo "停止现有进程..."
# 尝试普通用户权限停止
pkill -f "python3 app.py" || true
pkill -f "webpack serve" || true
pkill -f "node.*webpack" || true
lsof -ti:5005 | xargs kill -9 2>/dev/null || true
lsof -ti:3000 | xargs kill -9 2>/dev/null || true

# 如果普通用户权限不够，提示使用sudo
if lsof -i :5005 >/dev/null 2>&1 || lsof -i :3000 >/dev/null 2>&1; then
    echo "检测到端口仍被占用，尝试使用sudo清理..."
    sudo pkill -f "python3 app.py" || true
    sudo pkill -f "webpack serve" || true
    sudo pkill -f "node.*webpack" || true
    sudo lsof -ti:5005 | xargs sudo kill -9 2>/dev/null || true
    sudo lsof -ti:3000 | xargs sudo kill -9 2>/dev/null || true
    sleep 2
fi

# 确保数据目录权限正确
echo "检查数据目录权限..."
if [ ! -w "data/" ]; then
    echo "修复数据目录权限..."
    sudo chown -R $USER:$USER data/ 2>/dev/null || true
    chmod -R 755 data/ 2>/dev/null || true
fi

# 启动后端服务
echo "启动后端服务..."
cd backend
python3 app.py &
BACKEND_PID=$!

# 等待后端启动
echo "等待后端服务启动..."
sleep 3

# 启动前端webpack开发服务器
echo "启动前端webpack开发服务器..."
cd ../
npm run dev &
FRONTEND_PID=$!

echo "======================================"
echo "服务启动完成！"
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:5005"
echo "======================================"
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
wait

# 清理函数
cleanup() {
    echo "正在停止所有服务..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    pkill -f "python3 app.py" || true
    pkill -f "webpack serve" || true
    echo "所有服务已停止"
}

# 设置陷阱，确保在脚本退出时清理进程
trap cleanup EXIT INT TERM 
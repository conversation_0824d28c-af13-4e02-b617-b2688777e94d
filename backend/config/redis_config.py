"""
Redis配置 - 缓存和临时数据存储配置
"""
import os

# Redis连接配置
REDIS_CONFIG = {
    # 基本连接配置
    'HOST': os.getenv('REDIS_HOST', 'localhost'),
    'PORT': int(os.getenv('REDIS_PORT', 6379)),
    'DB': int(os.getenv('REDIS_DB', 0)),
    'PASSWORD': os.getenv('REDIS_PASSWORD', None),
    
    # 连接池配置
    'CONNECTION_POOL': {
        'max_connections': 20,
        'retry_on_timeout': True,
        'socket_connect_timeout': 5,
        'socket_timeout': 5,
        'health_check_interval': 30
    },
    
    # 临时token配置
    'TEMP_TOKEN': {
        'prefix': '2fa_temp_token:',
        'default_expiry': 300,  # 5分钟
        'max_expiry': 600,      # 最大10分钟
        'min_expiry': 60        # 最小1分钟
    },
    
    # 缓存配置
    'CACHE': {
        'default_ttl': 3600,    # 1小时
        'user_cache_ttl': 1800, # 30分钟
        'session_cache_ttl': 7200  # 2小时
    }
}

# 环境特定配置
def get_redis_config():
    """获取Redis配置"""
    config = REDIS_CONFIG.copy()
    
    # 开发环境配置
    if os.getenv('FLASK_ENV') == 'development':
        config['DB'] = 1  # 使用数据库1
    
    # 生产环境配置
    elif os.getenv('FLASK_ENV') == 'production':
        config['DB'] = 0  # 使用数据库0
        # 生产环境可能需要密码
        if not config['PASSWORD']:
            config['PASSWORD'] = os.getenv('REDIS_PROD_PASSWORD')
    
    return config

# 导出配置
redis_config = get_redis_config()

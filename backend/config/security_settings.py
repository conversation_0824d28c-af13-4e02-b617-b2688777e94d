"""
安全配置设置
定义系统安全相关的配置参数
"""

# 静态文件安全配置
ALLOWED_STATIC_EXTENSIONS = {
    '.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', 
    '.woff', '.woff2', '.ttf', '.eot', '.map'
}

# 禁止访问的文件名模式
FORBIDDEN_FILE_PATTERNS = [
    'config', 'secret', 'key', 'password', '.env', 'database',
    'backup', 'dump', 'log', 'tmp', 'temp', 'cache'
]

# 安全响应头配置
SECURITY_HEADERS = {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'",
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
}

# 权限级别定义
PERMISSION_LEVELS = {
    'PUBLIC': 0,      # 公开访问
    'LOGIN': 1,       # 需要登录
    'ADMIN': 2        # 需要管理员权限
}

# 接口权限映射
API_PERMISSIONS = {
    # 健康检查
    '/health': PERMISSION_LEVELS['PUBLIC'],
    '/api/health': PERMISSION_LEVELS['PUBLIC'],
    '/api/health/detailed': PERMISSION_LEVELS['LOGIN'],
    
    # 鉴权接口
    '/api/auth/login': PERMISSION_LEVELS['PUBLIC'],
    '/api/auth/check': PERMISSION_LEVELS['PUBLIC'],
    '/api/auth/logout': PERMISSION_LEVELS['LOGIN'],
    
    # 用户管理
    '/api/auth/users': PERMISSION_LEVELS['ADMIN'],
    
    # 权限管理
    '/api/permissions': PERMISSION_LEVELS['ADMIN'],
    '/api/permissions/check': PERMISSION_LEVELS['LOGIN'],
    
    # 数据导出
    '/api/contract/export': PERMISSION_LEVELS['ADMIN'],
    '/api/contract/wash-trading/export': PERMISSION_LEVELS['ADMIN'],
    '/api/agent/export-data': PERMISSION_LEVELS['ADMIN'],
    
    # 数据库管理
    '/api/database/status': PERMISSION_LEVELS['LOGIN'],
    '/api/database/clear': PERMISSION_LEVELS['ADMIN'],
    '/api/database/import': PERMISSION_LEVELS['ADMIN'],
    
    # 业务接口
    '/api/tasks': PERMISSION_LEVELS['LOGIN'],
    '/api/contract': PERMISSION_LEVELS['LOGIN'],
    '/api/agent': PERMISSION_LEVELS['LOGIN'],
    '/api/link': PERMISSION_LEVELS['LOGIN'],
    '/api/user-analysis': PERMISSION_LEVELS['LOGIN']
}

# 页面访问权限
PAGE_PERMISSIONS = {
    '/': PERMISSION_LEVELS['PUBLIC'],
    '/login': PERMISSION_LEVELS['PUBLIC'],
    '/login.html': PERMISSION_LEVELS['PUBLIC'],
    '/test-auth': PERMISSION_LEVELS['PUBLIC'],
    '/test-auth.html': PERMISSION_LEVELS['PUBLIC'],
    
    # 业务页面需要登录
    '/agent_relationship.html': PERMISSION_LEVELS['LOGIN'],
    '/agent-relationship': PERMISSION_LEVELS['LOGIN'],
    '/contract_analysis.html': PERMISSION_LEVELS['LOGIN'],
    '/contract-analysis': PERMISSION_LEVELS['LOGIN'],
    '/contract_integration.html': PERMISSION_LEVELS['LOGIN'],
    '/contract-integration': PERMISSION_LEVELS['LOGIN'],
    '/user_analysis.html': PERMISSION_LEVELS['LOGIN'],
    '/user-analysis': PERMISSION_LEVELS['LOGIN']
}

# 安全日志配置
SECURITY_LOG_EVENTS = {
    'UNAUTHORIZED_ACCESS': 'unauthorized_access',
    'UNSAFE_FILE_ACCESS': 'unsafe_file_access',
    'PERMISSION_DENIED': 'permission_denied',
    'SUSPICIOUS_ACTIVITY': 'suspicious_activity'
}

# 速率限制配置
RATE_LIMIT_CONFIG = {
    'LOGIN_ATTEMPTS': {
        'max_attempts': 5,
        'window_minutes': 15
    },
    'API_REQUESTS': {
        'max_requests': 100,
        'window_minutes': 1
    }
}

# 增量处理器配置文件
# 版本: 1.0
# 创建时间: 2025-08-01

incremental_processing:
  # 基础配置
  basic_settings:
    # 是否启用增量处理
    enabled: true
    # 任务超时时间（秒）
    task_timeout: 3600
    # 最大重试次数
    max_retries: 3
    # 日志级别 (DEBUG, INFO, WARNING, ERROR)
    log_level: "INFO"
  
  # 数据库配置
  database:
    # 等待表名称
    waiting_table_name: "incomplete_positions_waiting"
    # 批量插入大小
    batch_size: 1000
    # 连接超时时间（秒）
    connection_timeout: 30
    # 查询超时时间（秒）
    query_timeout: 300
  
  # 匹配算法配置
  matching:
    # 匹配算法类型 (exact_position_id, multi_layer)
    algorithm_type: "exact_position_id"
    # 数量容差配置
    tolerance:
      # 绝对容差（USDT）
      absolute: 0.0001
      # 相对容差（百分比）
      relative: 0.001
    # 时间窗口配置（分钟）
    time_window:
      # 默认时间窗口
      default: 1440
      # 最大时间窗口
      max: 10080
  
  # 完整性判断配置
  completion_check:
    # 判断方法 (deal_vol_based, amount_based)
    method: "deal_vol_based"
    # 最小完整度阈值
    min_completion_ratio: 0.95
    # 异常检测阈值
    anomaly_threshold: 1.1
  
  # 等待表管理配置
  waiting_table:
    # 最大等待时间（小时）
    max_waiting_hours: 168
    # 清理间隔（小时）
    cleanup_interval: 24
    # 最大检查次数
    max_check_count: 50
    # 统计更新间隔（分钟）
    stats_update_interval: 60
  
  # 性能监控配置
  performance:
    # 是否启用性能监控
    monitoring_enabled: true
    # 性能指标收集间隔（秒）
    metrics_interval: 30
    # 告警阈值
    alerts:
      # 处理速率告警（订单/秒）
      low_processing_rate: 10
      # 错误率告警（百分比）
      high_error_rate: 5.0
      # 内存使用告警（MB）
      high_memory_usage: 1024
      # CPU使用告警（百分比）
      high_cpu_usage: 80.0
  
  # 重复数据处理配置
  duplicate_handling:
    # 默认处理策略 (skip, replace, smart_update, merge)
    default_strategy: "smart_update"
    # 智能更新配置
    smart_update:
      # 数据完整性权重
      completeness_weight: 0.4
      # 数据新鲜度权重
      freshness_weight: 0.3
      # 数量精度权重
      precision_weight: 0.2
      # 检查次数权重
      check_count_weight: 0.1
      # 更新阈值
      update_threshold: 0.6
  
  # 错误处理配置
  error_handling:
    # 是否启用自动重试
    auto_retry: true
    # 重试间隔（秒）
    retry_interval: 60
    # 错误类型处理策略
    strategies:
      database_error: "retry"
      validation_error: "skip"
      processing_error: "retry"
      timeout_error: "retry"
  
  # 数据验证配置
  validation:
    # 是否启用严格验证
    strict_validation: true
    # 必需字段列表
    required_fields:
      - "position_id"
      - "member_id"
      - "contract_name"
      - "side"
      - "deal_vol_usdt"
    # 数据类型验证
    field_types:
      position_id: "string"
      member_id: "string"
      contract_name: "string"
      side: "integer"
      deal_vol_usdt: "float"
      deal_vol: "float"
      deal_price: "float"
      leverage: "float"
  
  # 优化配置
  optimization:
    # 是否启用内存优化
    memory_optimization: true
    # 是否启用并发处理
    concurrent_processing: false
    # 并发线程数
    max_workers: 4
    # 缓存配置
    cache:
      # 是否启用缓存
      enabled: true
      # 缓存大小（条目数）
      max_size: 10000
      # 缓存过期时间（秒）
      ttl: 3600

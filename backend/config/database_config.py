#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置文件 - 统一管理所有数据库相关配置
确保整个项目使用一致的数据库路径和配置
"""

import os
from pathlib import Path
from typing import Dict, Any

class DatabaseConfig:
    """数据库配置管理类"""
    
    def __init__(self):
        # 获取项目根目录
        self.project_root = self._get_project_root()
        
        # 数据库文件配置
        self.DB_CONFIG = {
            # 主数据库文件 - 统一使用 .duckdb 扩展名
            'MAIN_DB_NAME': 'risk_analysis.duckdb',
            'MAIN_DB_PATH': self._get_db_path('risk_analysis.duckdb'),
            
            # 数据目录
            'DATA_DIR': os.path.join(self.project_root, 'data'),
            'BACKUP_DIR': os.path.join(self.project_root, 'data', 'backups'),
            'LOG_FILE': os.path.join(self.project_root, 'data', 'duckdb_operations.log'),
            
            # 数据库连接配置
            'CONNECTION_TIMEOUT': 30,
            'MAX_RETRIES': 3,
            'RETRY_DELAY': 1,
            
            # 性能配置
            'ENABLE_WAL_MODE': True,
            'CACHE_SIZE': '256MB',
            'TEMP_DIRECTORY': os.path.join(self.project_root, 'temp'),
        }
        
        # 确保必要目录存在
        self._ensure_directories()
    
    def _get_project_root(self) -> str:
        """获取项目根目录"""
        # 从当前文件位置向上查找项目根目录
        current_file = Path(__file__).resolve()
        
        # 查找包含 manage_duckdb.sh 的目录作为项目根目录
        for parent in current_file.parents:
            if (parent / 'manage_duckdb.sh').exists():
                return str(parent)
        
        # 如果找不到，使用当前文件的上上级目录
        return str(current_file.parent.parent.parent)
    
    def _get_db_path(self, db_name: str) -> str:
        """获取数据库文件的完整路径"""
        return os.path.join(self.project_root, 'data', db_name)
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.DB_CONFIG['DATA_DIR'],
            self.DB_CONFIG['BACKUP_DIR'],
            self.DB_CONFIG['TEMP_DIRECTORY']
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_main_db_path(self) -> str:
        """获取主数据库路径"""
        return self.DB_CONFIG['MAIN_DB_PATH']
    
    def get_backup_dir(self) -> str:
        """获取备份目录路径"""
        return self.DB_CONFIG['BACKUP_DIR']
    
    def get_log_file(self) -> str:
        """获取日志文件路径"""
        return self.DB_CONFIG['LOG_FILE']
    
    def validate_db_path(self, db_path: str) -> tuple[bool, str]:
        """
        验证数据库路径是否符合规范
        
        Args:
            db_path: 数据库文件路径
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        if not db_path:
            return False, "数据库路径不能为空"
        
        # 转换为绝对路径
        if not os.path.isabs(db_path):
            db_path = os.path.abspath(db_path)
        
        # 检查文件扩展名
        if not db_path.endswith('.duckdb'):
            return False, f"数据库文件必须使用 .duckdb 扩展名，当前: {os.path.splitext(db_path)[1]}"
        
        # 检查是否在项目的 data 目录下
        expected_data_dir = self.DB_CONFIG['DATA_DIR']
        if not db_path.startswith(expected_data_dir):
            return False, f"数据库文件必须位于 {expected_data_dir} 目录下"
        
        # 检查文件名是否符合规范
        filename = os.path.basename(db_path)
        allowed_names = ['risk_analysis.duckdb', 'test_risk_analysis.duckdb']
        if filename not in allowed_names:
            return False, f"数据库文件名不符合规范，允许的名称: {allowed_names}"
        
        return True, ""
    
    def get_connection_config(self) -> Dict[str, Any]:
        """获取数据库连接配置"""
        return {
            'timeout': self.DB_CONFIG['CONNECTION_TIMEOUT'],
            'max_retries': self.DB_CONFIG['MAX_RETRIES'],
            'retry_delay': self.DB_CONFIG['RETRY_DELAY'],
        }
    
    def is_main_database(self, db_path: str) -> bool:
        """检查是否是主数据库"""
        return os.path.abspath(db_path) == os.path.abspath(self.get_main_db_path())
    
    def get_test_db_path(self) -> str:
        """获取测试数据库路径"""
        return self._get_db_path('test_risk_analysis.duckdb')
    
    def cleanup_invalid_db_files(self) -> list[str]:
        """
        清理无效的数据库文件
        
        Returns:
            list: 被清理的文件列表
        """
        cleaned_files = []
        data_dir = self.DB_CONFIG['DATA_DIR']
        
        if not os.path.exists(data_dir):
            return cleaned_files
        
        # 查找所有 .db 文件（错误的扩展名）
        for filename in os.listdir(data_dir):
            if filename.endswith('.db') and not filename.endswith('.duckdb'):
                file_path = os.path.join(data_dir, filename)
                
                # 检查文件是否为空或很小（可能是空数据库）
                if os.path.getsize(file_path) < 50000:  # 小于50KB认为是空数据库
                    try:
                        os.remove(file_path)
                        cleaned_files.append(filename)
                    except OSError as e:
                        print(f"警告: 无法删除文件 {filename}: {e}")
        
        return cleaned_files


# 创建全局配置实例
db_config = DatabaseConfig()

# 导出常用配置
MAIN_DB_PATH = db_config.get_main_db_path()
BACKUP_DIR = db_config.get_backup_dir()
LOG_FILE = db_config.get_log_file()
DATA_DIR = db_config.DB_CONFIG['DATA_DIR']

# 导出验证函数
validate_db_path = db_config.validate_db_path
is_main_database = db_config.is_main_database

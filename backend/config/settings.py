"""
应用程序配置 - 包含应用程序的各种配置参数
"""

import os

# 应用程序基本配置
APP_NAME = "代理关系分析工具"
APP_VERSION = "1.0.0"
APP_PORT = 5005
DEBUG_MODE = False

# 并行处理器配置
PARALLEL_CONFIG = {
    # 默认关闭并行处理，对小数据集更高效
    "enable_parallel": False,
    # 减少工作线程数量
    "max_workers": 4,
    # 只有数据量超过5000才考虑并行处理
    "min_data_size": 5000,
    # 只有分组数超过100才考虑并行处理
    "min_groups_size": 100
}

# 性能测试配置
PERFORMANCE_TEST_CONFIG = {
    # 单线程处理器配置
    "single_thread": {
        "enable_parallel": False,
        "max_workers": 1,
        "min_data_size": 999999999,  # 设置一个很大的值，确保不会启用并行处理
        "min_groups_size": 999999999
    },
    # 多线程处理器配置
    "multi_thread": {
        "enable_parallel": True,
        "max_workers": 8,  # 使用8个线程
        "min_data_size": 0,  # 设置为0，确保启用并行处理
        "min_groups_size": 0
    }
} 
"""
鉴权配置文件 - 必须包含所有必要的配置项
"""
import os

# 会话配置
SESSION_CONFIG = {
    'SECRET_KEY': os.environ.get('SECRET_KEY', 'risk-analysis-system-production-key-2025'),
    'SESSION_TIMEOUT_HOURS': int(os.environ.get('SESSION_TIMEOUT_HOURS', '24')),
    'SESSION_COOKIE_NAME': 'risk_analysis_session',
    'SESSION_COOKIE_HTTPONLY': True,
    'SESSION_COOKIE_SECURE': os.environ.get('HTTPS_ENABLED', 'False').lower() == 'true',
    'SESSION_COOKIE_SAMESITE': 'Lax',  # 改回Lax，None在某些情况下有问题
    'SESSION_COOKIE_PATH': '/',  # 确保cookie在所有路径下都有效
    'SESSION_COOKIE_DOMAIN': None  # 让cookie在不同域名下都有效
}

# 密码策略配置
PASSWORD_CONFIG = {
    'MIN_LENGTH': 6,
    'REQUIRE_UPPERCASE': False,
    'REQUIRE_LOWERCASE': False,
    'REQUIRE_NUMBERS': False,
    'REQUIRE_SPECIAL_CHARS': False,
    'MAX_LOGIN_ATTEMPTS': 5,
    'ACCOUNT_LOCK_MINUTES': 30
}

# 安全配置
SECURITY_CONFIG = {
    'ENABLE_CSRF_PROTECTION': True,
    'ENABLE_RATE_LIMITING': True,
    'MAX_REQUESTS_PER_MINUTE': 60,
    'ENABLE_IP_WHITELIST': False,
    'ALLOWED_IPS': [],
    'ENABLE_AUDIT_LOG': True,
    'FORCE_2FA_ENABLED': True,  # 强制所有用户启用2FA
    'MANDATORY_2FA_MESSAGE': '系统要求所有用户必须启用双因子认证以确保账户安全'
}

# 默认用户配置
DEFAULT_ADMIN = {
    'username': 'admin',
    'password': os.environ.get('DEFAULT_ADMIN_PASSWORD', 'admin123'),  # 生产环境请通过环境变量设置
    'email': '<EMAIL>'
} 
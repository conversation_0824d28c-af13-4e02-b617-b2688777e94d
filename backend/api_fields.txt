"analysis_confidence"
"analysis_timestamp"
"complete_data.abnormal_analysis.arbitrage_count"
"complete_data.abnormal_analysis.funding_arbitrage_ratio"
"complete_data.abnormal_analysis.funding_arbitrage_volume"
"complete_data.abnormal_analysis.high_freq_ratio"
"complete_data.abnormal_analysis.high_freq_volume"
"complete_data.abnormal_analysis.high_frequency_count"
"complete_data.abnormal_analysis.real_trading_scale"
"complete_data.abnormal_analysis.risk_events_count"
"complete_data.abnormal_analysis.total_abnormal_ratio"
"complete_data.abnormal_analysis.total_abnormal_volume"
"complete_data.abnormal_analysis.wash_trading_count"
"complete_data.abnormal_analysis.wash_trading_ratio"
"complete_data.abnormal_analysis.wash_trading_volume"
"complete_data.associations.both_shared_count"
"complete_data.associations.same_device_count"
"complete_data.associations.same_ip_count"
"complete_data.coin_analysis.advantage_coins_count"
"complete_data.coin_analysis.analysis_coins_count"
"complete_data.coin_analysis.average_coins.0.contract"
"complete_data.coin_analysis.average_coins.0.expertise_level"
"complete_data.coin_analysis.average_coins.0.net_pnl"
"complete_data.coin_analysis.average_coins.0.total_trades"
"complete_data.coin_analysis.average_coins.0.win_rate"
"complete_data.coin_analysis.avg_coin_win_rate"
"complete_data.coin_analysis.coin_expertise_summary.average_coins.0.contract"
"complete_data.coin_analysis.coin_expertise_summary.average_coins.0.expertise_level"
"complete_data.coin_analysis.coin_expertise_summary.average_coins.0.net_pnl"
"complete_data.coin_analysis.coin_expertise_summary.average_coins.0.total_trades"
"complete_data.coin_analysis.coin_expertise_summary.average_coins.0.win_rate"
"complete_data.coin_analysis.coin_performance_ranking.0.contract"
"complete_data.coin_analysis.coin_performance_ranking.0.expertise_level"
"complete_data.coin_analysis.coin_performance_ranking.0.net_pnl"
"complete_data.coin_analysis.coin_performance_ranking.0.performance_score"
"complete_data.coin_analysis.coin_performance_ranking.0.profit_factor"
"complete_data.coin_analysis.coin_performance_ranking.0.rank"
"complete_data.coin_analysis.coin_performance_ranking.0.total_trades"
"complete_data.coin_analysis.coin_performance_ranking.0.win_rate"
"complete_data.coin_analysis.coin_ranking_table.0.contract"
"complete_data.coin_analysis.coin_ranking_table.0.expertise_level"
"complete_data.coin_analysis.coin_ranking_table.0.net_pnl"
"complete_data.coin_analysis.coin_ranking_table.0.performance_score"
"complete_data.coin_analysis.coin_ranking_table.0.profit_factor"
"complete_data.coin_analysis.coin_ranking_table.0.rank"
"complete_data.coin_analysis.coin_ranking_table.0.total_trades"
"complete_data.coin_analysis.coin_ranking_table.0.win_rate"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.avg_loss_per_trade"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.avg_profit_per_trade"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.avg_trade_size"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.contract"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.expertise_level"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.max_single_loss"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.max_single_profit"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.net_pnl"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.performance_rank"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.profit_factor"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.profitable_trades"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.total_loss"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.total_profit"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.total_trades"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.total_volume"
"complete_data.coin_analysis.coin_win_rates.BTC_USDT.win_rate"
"complete_data.coin_analysis.expert_coins_count"
"complete_data.derived_analysis.market_understanding.dimension_total"
"complete_data.derived_analysis.market_understanding.execution_efficiency_score"
"complete_data.derived_analysis.market_understanding.execution_efficiency"
"complete_data.derived_analysis.market_understanding.risk_discipline_score"
"complete_data.derived_analysis.market_understanding.risk_discipline"
"complete_data.derived_analysis.market_understanding.timing_ability_score"
"complete_data.derived_analysis.market_understanding.timing_ability"
"complete_data.derived_analysis.market_understanding.total_score"
"complete_data.derived_analysis.profitability.dimension_total"
"complete_data.derived_analysis.profitability.profit_consistency_score"
"complete_data.derived_analysis.profitability.profit_consistency"
"complete_data.derived_analysis.profitability.profit_factor_score"
"complete_data.derived_analysis.profitability.profit_factor"
"complete_data.derived_analysis.profitability.profit_loss_ratio_score"
"complete_data.derived_analysis.profitability.profit_loss_ratio"
"complete_data.derived_analysis.profitability.roi"
"complete_data.derived_analysis.profitability.sharpe_ratio"
"complete_data.derived_analysis.profitability.total_score"
"complete_data.derived_analysis.profitability.win_rate_score"
"complete_data.derived_analysis.profitability.win_rate"
"complete_data.derived_analysis.risk_control.avg_leverage_score"
"complete_data.derived_analysis.risk_control.avg_leverage"
"complete_data.derived_analysis.risk_control.dimension_total"
"complete_data.derived_analysis.risk_control.leverage_stability_score"
"complete_data.derived_analysis.risk_control.leverage_stability"
"complete_data.derived_analysis.risk_control.max_drawdown"
"complete_data.derived_analysis.risk_control.max_leverage_score"
"complete_data.derived_analysis.risk_control.max_leverage"
"complete_data.derived_analysis.risk_control.max_single_loss_score"
"complete_data.derived_analysis.risk_control.max_single_loss"
"complete_data.derived_analysis.risk_control.total_score"
"complete_data.derived_analysis.risk_control.var_95"
"complete_data.derived_analysis.risk_control.volatility"
"complete_data.derived_analysis.trading_behavior.dimension_total"
"complete_data.derived_analysis.trading_behavior.duration_ratio_score"
"complete_data.derived_analysis.trading_behavior.duration_ratio"
"complete_data.derived_analysis.trading_behavior.market_order_ratio_score"
"complete_data.derived_analysis.trading_behavior.market_order_ratio"
"complete_data.derived_analysis.trading_behavior.position_consistency_score"
"complete_data.derived_analysis.trading_behavior.position_consistency"
"complete_data.derived_analysis.trading_behavior.total_score"
"complete_data.derived_analysis.trading_behavior.trading_frequency_score"
"complete_data.derived_analysis.trading_behavior.trading_frequency"
"complete_data.fund_scale_metrics.estimated_capital"
"complete_data.fund_scale_metrics.fund_scale_category"
"complete_data.fund_scale_metrics.fund_scale_range"
"complete_data.fund_scale_metrics.large_trades_ratio"
"complete_data.fund_scale_metrics.large_trades"
"complete_data.fund_scale_metrics.max_position_size"
"complete_data.fund_scale_metrics.medium_trades_ratio"
"complete_data.fund_scale_metrics.medium_trades"
"complete_data.fund_scale_metrics.position_consistency_description"
"complete_data.fund_scale_metrics.position_consistency"
"complete_data.fund_scale_metrics.real_trading_volume"
"complete_data.fund_scale_metrics.small_trades_ratio"
"complete_data.fund_scale_metrics.small_trades"
# 对冲统计字段已移除
# "complete_data.hedge_statistics.avg_hedge_duration"
# "complete_data.hedge_statistics.concurrent_positions_count"
# "complete_data.hedge_statistics.hedge_contracts.0"
# "complete_data.hedge_statistics.hedge_positions_count"
# "complete_data.hedge_statistics.involved_contracts"
# "complete_data.hedge_statistics.max_concurrent_positions"
"complete_data.holding_time_metrics.avg_loss_duration_hours"
"complete_data.holding_time_metrics.avg_loss_duration_minutes"
"complete_data.holding_time_metrics.avg_loss_duration"
"complete_data.holding_time_metrics.avg_profit_duration_hours"
"complete_data.holding_time_metrics.avg_profit_duration_minutes"
"complete_data.holding_time_metrics.avg_profit_duration"
"complete_data.holding_time_metrics.duration_ratio_description"
"complete_data.holding_time_metrics.duration_ratio"
"complete_data.holding_time_metrics.frequency_description"
"complete_data.holding_time_metrics.max_holding_contract"
"complete_data.holding_time_metrics.max_holding_time"
"complete_data.holding_time_metrics.min_holding_description"
"complete_data.holding_time_metrics.min_holding_time"
"complete_data.holding_time_metrics.trading_frequency"
"complete_data.order_type_metrics.close_limit_orders"
"complete_data.order_type_metrics.close_limit_ratio"
"complete_data.order_type_metrics.close_market_orders"
"complete_data.order_type_metrics.close_market_ratio"
"complete_data.order_type_metrics.limit_order_description"
"complete_data.order_type_metrics.limit_order_ratio"
"complete_data.order_type_metrics.market_order_description"
"complete_data.order_type_metrics.market_order_ratio"
"complete_data.order_type_metrics.open_limit_orders"
"complete_data.order_type_metrics.open_limit_ratio"
"complete_data.order_type_metrics.open_market_orders"
"complete_data.order_type_metrics.open_market_ratio"
"complete_data.pnl_metrics.avg_loss_per_trade"
"complete_data.pnl_metrics.avg_loss"
"complete_data.pnl_metrics.avg_profit_per_trade"
"complete_data.pnl_metrics.avg_profit"
"complete_data.pnl_metrics.fee_ratio"
"complete_data.pnl_metrics.loss_count"
"complete_data.pnl_metrics.loss_trades_ratio"
"complete_data.pnl_metrics.loss_trades"
"complete_data.pnl_metrics.max_loss"
"complete_data.pnl_metrics.max_profit"
"complete_data.pnl_metrics.net_pnl"
"complete_data.pnl_metrics.profit_loss_ratio"
"complete_data.pnl_metrics.profit_trades_ratio"
"complete_data.pnl_metrics.profit_trades"
"complete_data.pnl_metrics.profitable_count"
"complete_data.pnl_metrics.return_rate"
"complete_data.pnl_metrics.total_fees"
"complete_data.pnl_metrics.total_loss"
"complete_data.pnl_metrics.total_pnl"
"complete_data.pnl_metrics.total_profit"
"complete_data.pnl_metrics.win_rate"
"complete_data.professional_dashboard.analysis_date"
"complete_data.professional_dashboard.confidence_level"
"complete_data.professional_dashboard.fund_scale"
"complete_data.professional_dashboard.market_understanding_description"
"complete_data.professional_dashboard.market_understanding_score"
"complete_data.professional_dashboard.profitability_description"
"complete_data.professional_dashboard.profitability_score"
"complete_data.professional_dashboard.risk_control_description"
"complete_data.professional_dashboard.risk_control_score"
"complete_data.professional_dashboard.total_score"
"complete_data.professional_dashboard.trader_type"
"complete_data.professional_dashboard.trading_behavior_description"
"complete_data.professional_dashboard.trading_behavior_score"
"complete_data.risk_control_metrics.avg_leverage_description"
"complete_data.risk_control_metrics.avg_leverage"
"complete_data.risk_control_metrics.leverage_stability_description"
"complete_data.risk_control_metrics.leverage_stability"
"complete_data.risk_control_metrics.low_leverage_ratio"
"complete_data.risk_control_metrics.low_leverage_trades"
"complete_data.risk_control_metrics.max_leverage_description"
"complete_data.risk_control_metrics.max_leverage"
"complete_data.risk_control_metrics.max_loss_description"
"complete_data.risk_control_metrics.max_single_loss"
"complete_data.risk_control_metrics.medium_leverage_ratio"
"complete_data.risk_control_metrics.medium_leverage_trades"
"complete_data.risk_control_metrics.risk_reward_ratio"
"complete_data.risk_control_metrics.stop_loss_ratio"
"complete_data.risk_control_metrics.take_profit_ratio"
"complete_data.risk_summary.max_score"
"complete_data.risk_summary.risk_categories_chart.0"
"complete_data.risk_summary.risk_categories_chart.1"
"complete_data.risk_summary.risk_types"
"complete_data.risk_summary.total_risks"
"complete_data.risk_summary.total_volume"
"complete_data.search_area.agent_tasks.0.created_at"
"complete_data.search_area.agent_tasks.0.id"
"complete_data.search_area.agent_tasks.0.name"
"complete_data.search_area.agent_tasks.0.status"
"complete_data.trading_preferences.coin_preference.altcoin_percentage"
"complete_data.trading_preferences.coin_preference.defi_percentage"
"complete_data.trading_preferences.coin_preference.mainstream_percentage"
"complete_data.trading_preferences.coin_preference.others_percentage"
"complete_data.trading_preferences.risk_preference.diversification_score"
"complete_data.trading_preferences.risk_preference.risk_appetite_level"
"complete_data.trading_preferences.risk_preference.volatility_preference"
"complete_data.trading_scale_metrics.avg_trade_size"
"complete_data.trading_scale_metrics.max_single_trade"
"complete_data.trading_scale_metrics.max_trade_contract"
"complete_data.trading_scale_metrics.min_single_trade"
"complete_data.trading_scale_metrics.min_trade_contract"
"complete_data.trading_scale_metrics.total_trades"
"complete_data.trading_scale_metrics.total_trading_days"
"complete_data.trading_scale_metrics.total_volume"
"complete_data.trading_scale_metrics.trading_frequency"
"complete_data.trading_scale_metrics.volume_description"
"complete_data.transaction_details.page_info"
"complete_data.transaction_details.risk_type_filters.0.label"
"complete_data.transaction_details.risk_type_filters.0.value"
"complete_data.transaction_details.risk_type_filters.1.label"
"complete_data.transaction_details.risk_type_filters.1.value"
"complete_data.transaction_details.risk_type_filters.2.label"
"complete_data.transaction_details.risk_type_filters.2.value"
"complete_data.transaction_details.transactions_list.0.detection_method"
"complete_data.transaction_details.transactions_list.0.risk_score"
"complete_data.transaction_details.transactions_list.0.risk_type"
"complete_data.transaction_details.transactions_list.0.transaction_id"
"complete_data.transaction_details.transactions_list.0.transaction_time"
"complete_data.transaction_details.transactions_list.0.transaction_volume"
"complete_data.transaction_details.transactions_list.1.detection_method"
"complete_data.transaction_details.transactions_list.1.risk_score"
"complete_data.transaction_details.transactions_list.1.risk_type"
"complete_data.transaction_details.transactions_list.1.transaction_id"
"complete_data.transaction_details.transactions_list.1.transaction_time"
"complete_data.transaction_details.transactions_list.1.transaction_volume"
"complete_data.transaction_details.transactions_list.2.detection_method"
"complete_data.transaction_details.transactions_list.2.risk_score"
"complete_data.transaction_details.transactions_list.2.risk_type"
"complete_data.transaction_details.transactions_list.2.transaction_id"
"complete_data.transaction_details.transactions_list.2.transaction_time"
"complete_data.transaction_details.transactions_list.2.transaction_volume"
"complete_data.transaction_details.transactions_list.3.detection_method"
"complete_data.transaction_details.transactions_list.3.risk_score"
"complete_data.transaction_details.transactions_list.3.risk_type"
"complete_data.transaction_details.transactions_list.3.transaction_id"
"complete_data.transaction_details.transactions_list.3.transaction_time"
"complete_data.transaction_details.transactions_list.3.transaction_volume"
"complete_data.transaction_details.transactions_list.4.detection_method"
"complete_data.transaction_details.transactions_list.4.risk_score"
"complete_data.transaction_details.transactions_list.4.risk_type"
"complete_data.transaction_details.transactions_list.4.transaction_id"
"complete_data.transaction_details.transactions_list.4.transaction_time"
"complete_data.transaction_details.transactions_list.4.transaction_volume"
"complete_data.transaction_details.transactions_list.5.detection_method"
"complete_data.transaction_details.transactions_list.5.risk_score"
"complete_data.transaction_details.transactions_list.5.risk_type"
"complete_data.transaction_details.transactions_list.5.transaction_id"
"complete_data.transaction_details.transactions_list.5.transaction_time"
"complete_data.transaction_details.transactions_list.5.transaction_volume"
"complete_data.transaction_details.transactions_list.6.detection_method"
"complete_data.transaction_details.transactions_list.6.risk_score"
"complete_data.transaction_details.transactions_list.6.risk_type"
"complete_data.transaction_details.transactions_list.6.transaction_id"
"complete_data.transaction_details.transactions_list.6.transaction_time"
"complete_data.transaction_details.transactions_list.6.transaction_volume"
"complete_data.transaction_details.transactions_list.7.detection_method"
"complete_data.transaction_details.transactions_list.7.risk_score"
"complete_data.transaction_details.transactions_list.7.risk_type"
"complete_data.transaction_details.transactions_list.7.transaction_id"
"complete_data.transaction_details.transactions_list.7.transaction_time"
"complete_data.transaction_details.transactions_list.7.transaction_volume"
"complete_data.user_profile.bd_name"
"complete_data.user_profile.confidence_level"
"complete_data.user_profile.digital_id"
"complete_data.user_profile.fund_scale_category"
"complete_data.user_profile.fund_scale_range"
"complete_data.user_profile.last_activity"
"complete_data.user_profile.member_id"
"complete_data.user_profile.professional_score"
"complete_data.user_profile.total_risks"
"complete_data.user_profile.trader_type"
"complete_data.user_profile.user_type"
"data_quality_score"
"status"
"task_id"
"user_id"

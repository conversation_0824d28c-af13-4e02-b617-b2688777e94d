"""
密码加密工具 - 必须使用bcrypt
"""
import bcrypt
import secrets
import string

class PasswordUtils:
    """密码处理工具类"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """加密密码"""
        # 生成salt并加密
        salt = bcrypt.gensalt(rounds=12)
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """验证密码"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except:
            return False
    
    @staticmethod
    def generate_random_password(length: int = 12) -> str:
        """生成随机密码"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for i in range(length))
        return password
    
    @staticmethod
    def validate_password_strength(password: str) -> tuple[bool, str]:
        """验证密码强度"""
        if len(password) < 6:
            return False, "密码长度至少6位"
        
        # 可以根据需要添加更多验证规则
        return True, "密码强度符合要求" 
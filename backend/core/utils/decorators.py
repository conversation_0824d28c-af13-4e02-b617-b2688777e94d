"""
权限装饰器 - 必须严格按照此实现
"""
from functools import wraps
from flask import request, jsonify, session, g
import logging

logger = logging.getLogger(__name__)

def login_required(f):
    """登录验证装饰器 - 所有需要登录的API必须使用"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # 检查session中的用户信息
            if 'user_id' not in session or 'username' not in session:
                logger.warning(f"未授权访问: {request.endpoint}, IP: {request.remote_addr}")
                return jsonify({
                    'error': '请先登录',
                    'code': 'AUTH_REQUIRED',
                    'redirect': '/login'
                }), 401
            
            # 验证会话是否有效
            from modules.auth.services.session_manager import session_manager
            if not session_manager.validate_session(session.get('session_id')):
                session.clear()
                logger.warning(f"会话已过期: {session.get('username')}, IP: {request.remote_addr}")
                return jsonify({
                    'error': '会话已过期，请重新登录',
                    'code': 'SESSION_EXPIRED',
                    'redirect': '/login'
                }), 401
            
            # 将用户信息添加到请求上下文
            g.current_user = {
                'id': session['user_id'],
                'username': session['username'],
                'role': session['role']
            }
            
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"鉴权检查异常: {str(e)}")
            return jsonify({'error': '鉴权检查失败'}), 500
    
    return decorated_function

def role_required(required_role):
    """角色权限装饰器 - 需要特定角色权限的API必须使用"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 必须先通过登录验证
            if not hasattr(g, 'current_user'):
                return jsonify({
                    'error': '请先登录',
                    'code': 'AUTH_REQUIRED'
                }), 401
            
            user_role = g.current_user.get('role')
            
            # admin角色拥有所有权限
            if user_role == 'admin':
                return f(*args, **kwargs)
            
            # 检查是否有required_role权限
            if user_role != required_role:
                logger.warning(f"权限不足: 用户 {g.current_user['username']} 尝试访问需要 {required_role} 权限的 {request.endpoint}")
                return jsonify({
                    'error': f'权限不足，需要{required_role}权限',
                    'code': 'INSUFFICIENT_PERMISSION',
                    'required_role': required_role,
                    'current_role': user_role
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """管理员权限装饰器 - 只有admin才能访问的API必须使用"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # 检查session中的用户信息（复制login_required的逻辑）
            if 'user_id' not in session or 'username' not in session:
                logger.warning(f"未授权访问: {request.endpoint}, IP: {request.remote_addr}")
                return jsonify({
                    'error': '请先登录',
                    'code': 'AUTH_REQUIRED',
                    'redirect': '/login'
                }), 401
            
            # 验证会话是否有效
            from modules.auth.services.session_manager import session_manager
            if not session_manager.validate_session(session.get('session_id')):
                session.clear()
                logger.warning(f"会话已过期: {session.get('username')}, IP: {request.remote_addr}")
                return jsonify({
                    'error': '会话已过期，请重新登录',
                    'code': 'SESSION_EXPIRED',
                    'redirect': '/login'
                }), 401
            
            # 将用户信息添加到请求上下文
            g.current_user = {
                'id': session['user_id'],
                'username': session['username'],
                'role': session['role']
            }
            
            # 检查admin权限
            user_role = g.current_user.get('role')
            if user_role != 'admin':
                logger.warning(f"权限不足: 用户 {g.current_user['username']} 尝试访问需要 admin 权限的 {request.endpoint}")
                return jsonify({
                    'error': '权限不足，需要管理员权限',
                    'code': 'INSUFFICIENT_PERMISSION',
                    'required_role': 'admin',
                    'current_role': user_role
                }), 403
            
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"管理员权限检查异常: {str(e)}")
            return jsonify({'error': '权限检查失败'}), 500
    
    return decorated_function 
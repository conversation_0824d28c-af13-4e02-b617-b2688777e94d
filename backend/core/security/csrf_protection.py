"""
自定义CSRF保护实现
不依赖Flask-WTF，提供基本的CSRF保护功能
"""
import secrets
import hashlib
import time
from functools import wraps
from flask import session, request, jsonify, current_app
import logging

logger = logging.getLogger(__name__)

class CSRFProtection:
    """CSRF保护类"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化应用"""
        app.config.setdefault('CSRF_TOKEN_TIMEOUT', 3600)  # 1小时
        app.config.setdefault('CSRF_SECRET_KEY', app.secret_key)
        
        # 注册CSRF token生成接口
        @app.route('/api/csrf-token', methods=['GET'])
        def get_csrf_token():
            """获取CSRF token"""
            token = self.generate_csrf_token()
            return jsonify({'csrf_token': token})
    
    def generate_csrf_token(self):
        """生成CSRF token"""
        if 'csrf_token' not in session or self._is_token_expired():
            # 生成新的token
            timestamp = str(int(time.time()))
            random_value = secrets.token_urlsafe(32)
            
            # 创建token数据
            token_data = f"{timestamp}:{random_value}"
            
            # 使用密钥签名
            secret_key = current_app.config.get('CSRF_SECRET_KEY', current_app.secret_key)
            signature = hashlib.sha256(f"{token_data}:{secret_key}".encode()).hexdigest()
            
            # 完整token
            token = f"{token_data}:{signature}"
            
            # 存储到session
            session['csrf_token'] = token
            session['csrf_token_time'] = int(time.time())
            
            logger.debug(f"生成新的CSRF token: {token[:20]}...")
        
        return session['csrf_token']
    
    def validate_csrf_token(self, token):
        """验证CSRF token"""
        if not token:
            return False
        
        try:
            # 解析token
            parts = token.split(':')
            if len(parts) != 3:
                return False
            
            timestamp, random_value, signature = parts
            
            # 检查时间戳
            token_time = int(timestamp)
            current_time = int(time.time())
            timeout = current_app.config.get('CSRF_TOKEN_TIMEOUT', 3600)
            
            if current_time - token_time > timeout:
                logger.warning("CSRF token已过期")
                return False
            
            # 验证签名
            secret_key = current_app.config.get('CSRF_SECRET_KEY', current_app.secret_key)
            expected_signature = hashlib.sha256(f"{timestamp}:{random_value}:{secret_key}".encode()).hexdigest()
            
            if signature != expected_signature:
                logger.warning("CSRF token签名验证失败")
                return False
            
            return True
            
        except (ValueError, IndexError) as e:
            logger.warning(f"CSRF token格式错误: {e}")
            return False
    
    def _is_token_expired(self):
        """检查token是否过期"""
        if 'csrf_token_time' not in session:
            return True
        
        token_time = session['csrf_token_time']
        current_time = int(time.time())
        timeout = current_app.config.get('CSRF_TOKEN_TIMEOUT', 3600)
        
        return current_time - token_time > timeout
    
    def protect(self, f):
        """CSRF保护装饰器"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 只保护POST、PUT、DELETE、PATCH请求
            if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
                # 获取token
                token = request.headers.get('X-CSRFToken') or request.form.get('csrf_token')
                
                if not token:
                    logger.warning(f"CSRF保护: 缺少token - {request.endpoint}")
                    return jsonify({
                        'success': False,
                        'error': 'CSRF token缺失，请刷新页面后重试'
                    }), 400
                
                if not self.validate_csrf_token(token):
                    logger.warning(f"CSRF保护: token验证失败 - {request.endpoint}")
                    return jsonify({
                        'success': False,
                        'error': 'CSRF token无效，请刷新页面后重试'
                    }), 400
                
                logger.debug(f"CSRF保护: token验证成功 - {request.endpoint}")
            
            return f(*args, **kwargs)
        
        return decorated_function

# 创建全局实例
csrf = CSRFProtection()

def csrf_protect(f):
    """CSRF保护装饰器的便捷函数"""
    return csrf.protect(f)

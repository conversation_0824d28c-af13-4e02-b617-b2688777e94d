#!/usr/bin/env python3
"""
清理冗余的用户行为分析表和视图
删除 user_behavior_summary_view 和 user_behavior_analysis
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.duckdb_manager import DuckDBManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RedundantTableCleaner:
    """冗余表清理器"""
    
    def __init__(self):
        self.db_manager = DuckDBManager()
    
    def backup_user_behavior_analysis(self):
        """备份 user_behavior_analysis 表数据（如果存在）"""
        try:
            # 检查表是否存在
            result = self.db_manager.fetch_all(
                "SELECT table_name FROM information_schema.tables WHERE table_name = 'user_behavior_analysis'"
            )
            
            if not result:
                logger.info("user_behavior_analysis 表不存在，跳过备份")
                return True
            
            # 检查表中是否有数据
            count_result = self.db_manager.fetch_all("SELECT COUNT(*) as count FROM user_behavior_analysis")
            record_count = count_result[0]['count'] if count_result else 0
            
            if record_count == 0:
                logger.info("user_behavior_analysis 表为空，跳过备份")
                return True
            
            logger.info(f"开始备份 user_behavior_analysis 表数据 ({record_count} 条记录)...")
            
            # 创建备份表
            backup_sql = """
            CREATE TABLE IF NOT EXISTS user_behavior_analysis_backup AS 
            SELECT *, CURRENT_TIMESTAMP as backup_time 
            FROM user_behavior_analysis
            """
            
            self.db_manager.execute_sql(backup_sql)
            
            # 验证备份
            backup_count = self.db_manager.fetch_all("SELECT COUNT(*) as count FROM user_behavior_analysis_backup")
            backup_records = backup_count[0]['count'] if backup_count else 0
            
            if backup_records == record_count:
                logger.info(f"✅ 备份成功：{backup_records} 条记录已保存到 user_behavior_analysis_backup 表")
                return True
            else:
                logger.error(f"❌ 备份失败：原表 {record_count} 条，备份表 {backup_records} 条")
                return False
                
        except Exception as e:
            logger.error(f"备份 user_behavior_analysis 表失败: {e}")
            return False
    
    def drop_views(self):
        """删除视图"""
        views_to_drop = [
            'user_behavior_summary_view',
            'professional_traders_stats_view'
        ]
        
        for view_name in views_to_drop:
            try:
                # 检查视图是否存在
                result = self.db_manager.fetch_all(
                    f"SELECT table_name FROM information_schema.tables WHERE table_name = '{view_name}'"
                )
                
                if result:
                    self.db_manager.execute_sql(f"DROP VIEW IF EXISTS {view_name}")
                    logger.info(f"✅ 已删除视图: {view_name}")
                else:
                    logger.info(f"视图 {view_name} 不存在，跳过删除")
                    
            except Exception as e:
                logger.error(f"删除视图 {view_name} 失败: {e}")
                return False
        
        return True
    
    def drop_table(self):
        """删除 user_behavior_analysis 表"""
        try:
            # 检查表是否存在
            result = self.db_manager.fetch_all(
                "SELECT table_name FROM information_schema.tables WHERE table_name = 'user_behavior_analysis'"
            )
            
            if result:
                self.db_manager.execute_sql("DROP TABLE IF EXISTS user_behavior_analysis")
                logger.info("✅ 已删除表: user_behavior_analysis")
            else:
                logger.info("表 user_behavior_analysis 不存在，跳过删除")
            
            return True
            
        except Exception as e:
            logger.error(f"删除表 user_behavior_analysis 失败: {e}")
            return False
    
    def verify_cleanup(self):
        """验证清理结果"""
        try:
            logger.info("验证清理结果...")
            
            # 检查视图是否已删除
            views_check = self.db_manager.fetch_all("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_name IN ('user_behavior_summary_view', 'professional_traders_stats_view')
            """)
            
            if views_check:
                logger.warning(f"⚠️ 以下视图仍然存在: {[v['table_name'] for v in views_check]}")
                return False
            
            # 检查表是否已删除
            table_check = self.db_manager.fetch_all("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_name = 'user_behavior_analysis'
            """)
            
            if table_check:
                logger.warning("⚠️ user_behavior_analysis 表仍然存在")
                return False
            
            # 检查核心表是否正常
            core_table_check = self.db_manager.fetch_all("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_name = 'user_trading_profiles'
            """)
            
            if not core_table_check:
                logger.error("❌ 核心表 user_trading_profiles 不存在！")
                return False
            
            logger.info("✅ 清理验证通过：")
            logger.info("  - 冗余视图已删除")
            logger.info("  - 临时表已删除")
            logger.info("  - 核心表 user_trading_profiles 正常")
            
            return True
            
        except Exception as e:
            logger.error(f"验证清理结果失败: {e}")
            return False
    
    def cleanup(self):
        """执行完整的清理流程"""
        logger.info("========== 开始清理冗余的用户行为分析表和视图 ==========")
        
        # 步骤1: 备份数据
        if not self.backup_user_behavior_analysis():
            logger.error("备份失败，停止清理流程")
            return False
        
        # 步骤2: 删除视图
        if not self.drop_views():
            logger.error("删除视图失败，停止清理流程")
            return False
        
        # 步骤3: 删除表
        if not self.drop_table():
            logger.error("删除表失败，停止清理流程")
            return False
        
        # 步骤4: 验证清理结果
        if not self.verify_cleanup():
            logger.error("清理验证失败")
            return False
        
        logger.info("========== 清理完成 ==========")
        logger.info("现在所有用户行为分析数据都通过 user_trading_profiles 表进行管理")
        logger.info("如需查询用户行为摘要，请直接查询 user_trading_profiles 表")
        
        return True

def main():
    """主函数"""
    cleaner = RedundantTableCleaner()
    
    try:
        success = cleaner.cleanup()
        if success:
            print("✅ 清理成功完成")
            sys.exit(0)
        else:
            print("❌ 清理失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"清理过程中发生错误: {e}")
        print("❌ 清理过程中发生错误")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
手续费数据修复脚本 - 严格模式
不生成模拟手续费，而是报告真实数据缺失情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.duckdb_manager import db_manager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_commission_data_issues():
    """分析手续费数据问题，不进行修复"""
    try:
        logger.info("========== 手续费数据问题分析（严格模式）==========")
        
        # 1. 检查当前手续费数据状态
        stats_sql = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN total_commission > 0 THEN 1 END) as non_zero_commission,
            AVG(total_open_amount + total_close_amount) as avg_volume,
            SUM(total_commission) as total_current_commission
        FROM position_analysis
        """
        
        stats = db_manager.execute_sql(stats_sql)
        if not stats:
            logger.error("❌ 无法获取position_analysis表数据，请检查表是否存在")
            return
        
        total_records, non_zero_commission, avg_volume, total_current_commission = stats[0]
        
        logger.info(f"📊 手续费数据现状分析：")
        logger.info(f"  - 总记录数: {total_records}")
        logger.info(f"  - 有手续费记录: {non_zero_commission}")
        logger.info(f"  - 零手续费记录: {total_records - non_zero_commission}")
        logger.info(f"  - 零手续费比例: {((total_records - non_zero_commission) / total_records * 100):.1f}%")
        logger.info(f"  - 平均交易量: {avg_volume:.2f} USDT")
        logger.info(f"  - 当前总手续费: {total_current_commission:.2f} USDT")
        
        # 严格模式：不修复，而是建议数据源改进
        if (total_records - non_zero_commission) > 0:
            logger.error("发现手续费数据缺失问题！")

            
            # 提供详细的缺失数据分析
            missing_analysis_sql = """
            SELECT 
                contract_name,
                COUNT(*) as missing_count,
                AVG(total_open_amount + total_close_amount) as avg_volume_missing
            FROM position_analysis 
            WHERE total_commission = 0 OR total_commission IS NULL
            GROUP BY contract_name
            ORDER BY missing_count DESC
            LIMIT 10
            """
            
            missing_analysis = db_manager.execute_sql(missing_analysis_sql)
            if missing_analysis:
                logger.error("按合约分析缺失手续费的记录：")
                for row in missing_analysis:
                    contract_name, missing_count, avg_volume = row
                    logger.error(f"   - {contract_name}: {missing_count} 条记录缺失手续费，平均交易量 {avg_volume:.2f}")
        
        # 3. 验证现有手续费数据的合理性
        if non_zero_commission > 0:
            logger.info("✅ 验证现有非零手续费数据的合理性...")
            
            validation_sql = """
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN total_commission > 0 THEN 1 END) as non_zero_commission,
                AVG(total_commission) as avg_commission,
                SUM(total_commission) as total_commission,
                MIN(total_commission) as min_commission,
                MAX(total_commission) as max_commission
            FROM position_analysis
            WHERE total_commission > 0
            """
            
            verification_stats = db_manager.execute_sql(validation_sql)
            if verification_stats:
                total_records, non_zero_commission, avg_commission, total_commission, min_commission, max_commission = verification_stats[0]
                
                logger.info(f"✅ 现有手续费数据验证：")
                logger.info(f"  - 非零手续费记录: {non_zero_commission}")
                logger.info(f"  - 平均手续费: {avg_commission:.4f} USDT")
                logger.info(f"  - 总手续费: {total_commission:.2f} USDT")
                logger.info(f"  - 手续费范围: {min_commission:.4f} - {max_commission:.4f} USDT")
                
                # 分析手续费比例
                fee_ratio_sql = """
                SELECT 
                    AVG((total_commission / (total_open_amount + total_close_amount) * 100)) as avg_fee_ratio
                FROM position_analysis 
                WHERE total_commission > 0
                """
                
                fee_ratio_result = db_manager.execute_sql(fee_ratio_sql)
                if fee_ratio_result and fee_ratio_result[0][0]:
                    avg_fee_ratio = fee_ratio_result[0][0]
                    logger.info(f"  - 平均手续费比例: {avg_fee_ratio:.4f}%")
                    
                    if avg_fee_ratio < 0.01:
                        logger.warning("⚠️  手续费比例偏低，可能存在数据质量问题")
                    elif avg_fee_ratio > 1.0:
                        logger.warning("⚠️  手续费比例偏高，可能存在数据质量问题")
                    else:
                        logger.info("✅ 手续费比例在合理范围内")
        

        
    except Exception as e:
        logger.error(f"❌ 手续费数据分析失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    analyze_commission_data_issues() 
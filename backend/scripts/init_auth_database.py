
"""
鉴权数据库初始化脚本 - 部署时必须执行
"""
import logging
import os
import sys

# 添加backend目录到Python路径
backend_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, backend_path)

from database.duckdb_manager import db_manager
from modules.auth.services.auth_service import auth_service
from config.auth_settings import DEFAULT_ADMIN

logger = logging.getLogger(__name__)

def init_auth_database():
    """初始化鉴权相关数据库表和数据"""
    try:
        print("开始初始化鉴权数据库...")
        
        # 1. 读取并执行SQL脚本
        auth_sql_path = os.path.join(backend_path, 'database', 'schema', 'auth_tables.sql')
        if os.path.exists(auth_sql_path):
            print("执行鉴权表结构创建...")
            with open(auth_sql_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 清理并分割SQL语句
            lines = sql_content.split('\n')
            clean_lines = []
            current_statement = []
            
            for line in lines:
                line = line.strip()
                # 跳过注释和空行
                if not line or line.startswith('--'):
                    continue
                
                current_statement.append(line)
                
                # 如果行以分号结束，表示语句完成
                if line.endswith(';'):
                    statement = ' '.join(current_statement).strip()
                    if statement and len(statement) > 10:
                        clean_lines.append(statement)
                    current_statement = []
            
            # 按顺序执行每个SQL语句
            for stmt in clean_lines:
                try:
                    print(f"执行SQL: {stmt[:60]}...")
                    
                    # 使用直接连接方式执行SQL
                    with db_manager.get_connection() as conn:
                        conn.execute(stmt)
                    
                    # 如果是CREATE TABLE语句，验证表是否成功创建
                    if stmt.upper().startswith('CREATE TABLE'):
                        table_name = None
                        if 'auth_users' in stmt:
                            table_name = 'auth_users'
                        elif 'auth_user_sessions' in stmt:
                            table_name = 'auth_user_sessions'
                        elif 'auth_user_activity_logs' in stmt:
                            table_name = 'auth_user_activity_logs'
                        elif 'auth_system_config' in stmt:
                            table_name = 'auth_system_config'
                        
                        if table_name:
                            with db_manager.get_connection() as conn:
                                result = conn.execute(f"SELECT COUNT(*) FROM pragma_table_info('{table_name}')").fetchone()
                                if result[0] == 0:
                                    raise Exception(f"表 {table_name} 创建失败")
                                print(f"✓ 表 {table_name} 创建成功，包含 {result[0]} 个字段")
                    
                except Exception as e:
                    print(f"SQL执行失败: {stmt[:100]}..., 错误: {str(e)}")
                    raise
            print("✓ 鉴权表结构创建完成")
        else:
            print("❌ 未找到鉴权表结构文件")
            return False
        
        # 2. 检查默认管理员是否已存在
        query = "SELECT COUNT(*) as count FROM auth_users WHERE username = ?"
        result = db_manager.fetch_one(query, [DEFAULT_ADMIN['username']])
        
        if result and result['count'] == 0:
            print("创建默认管理员账户...")
            success, message = auth_service.create_user(
                DEFAULT_ADMIN['username'],
                DEFAULT_ADMIN['password'],
                'admin',
                DEFAULT_ADMIN['email']
            )
            
            if success:
                print(f"✓ 默认管理员账户创建成功: {DEFAULT_ADMIN['username']}")
                print(f"  默认密码: {DEFAULT_ADMIN['password']}")
                print("  ⚠️  请在首次登录后立即修改密码！")
            else:
                print(f"❌ 创建默认管理员失败: {message}")
                return False
        else:
            print("✓ 默认管理员账户已存在")
        
        # 3. 验证表结构
        print("验证数据库表结构...")
        required_tables = ['auth_users', 'auth_user_sessions', 'auth_user_activity_logs', 'auth_system_config']
        
        for table in required_tables:
            check_query = f"SELECT COUNT(*) FROM pragma_table_info('{table}')"
            try:
                db_manager.fetch_one(check_query)
                print(f"✓ 表 {table} 创建成功")
            except Exception as e:
                print(f"❌ 表 {table} 验证失败: {str(e)}")
                return False
        
        # 4. 检查系统配置
        config_query = "SELECT COUNT(*) as count FROM auth_system_config"
        config_result = db_manager.fetch_one(config_query)
        if config_result and config_result['count'] > 0:
            print("✓ 系统配置已初始化")
        else:
            print("❌ 系统配置初始化失败")
            return False
        
        print("\n🎉 鉴权数据库初始化完成！")
        print("\n初始化完成后可以使用以下账户登录：")
        print(f"用户名: {DEFAULT_ADMIN['username']}")
        print(f"密码: {DEFAULT_ADMIN['password']}")
        print("\n⚠️  重要提醒：")
        print("1. 请立即登录并修改默认密码")
        print("2. 建议创建其他管理员账户")
        print("3. 定期检查用户权限")
        
        return True
        
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        logger.error(f"数据库初始化失败: {str(e)}")
        return False

def check_auth_status():
    """检查鉴权系统状态"""
    try:
        print("检查鉴权系统状态...")
        
        # 检查表是否存在
        tables_status = {}
        required_tables = ['auth_users', 'auth_user_sessions', 'auth_user_activity_logs', 'auth_system_config']
        
        for table in required_tables:
            try:
                query = f"SELECT COUNT(*) as count FROM {table}"
                result = db_manager.fetch_one(query)
                tables_status[table] = {
                    'exists': True,
                    'count': result['count'] if result else 0
                }
            except:
                tables_status[table] = {'exists': False, 'count': 0}
        
        print("\n数据库表状态:")
        for table, status in tables_status.items():
            if status['exists']:
                print(f"✓ {table}: 存在 (记录数: {status['count']})")
            else:
                print(f"❌ {table}: 不存在")
        
        # 检查管理员账户
        try:
            admin_query = "SELECT COUNT(*) as count FROM auth_users WHERE role = 'admin'"
            admin_result = db_manager.fetch_one(admin_query)
            admin_count = admin_result['count'] if admin_result else 0
            print(f"\n管理员账户数量: {admin_count}")
            
            if admin_count == 0:
                print("⚠️  警告: 没有管理员账户，系统无法正常使用")
                return False
        except:
            print("❌ 无法检查管理员账户")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 状态检查失败: {str(e)}")
        return False

def create_test_users():
    """创建测试用户"""
    test_users = [
        {'username': 'viewer1', 'password': 'viewer123', 'role': 'viewer', 'email': '<EMAIL>'},
        {'username': 'viewer2', 'password': 'viewer456', 'role': 'viewer', 'email': '<EMAIL>'},
        {'username': 'admin2', 'password': 'admin456', 'role': 'admin', 'email': '<EMAIL>'}
    ]
    
    print("创建测试用户...")
    for user_data in test_users:
        success, message = auth_service.create_user(
            user_data['username'],
            user_data['password'],
            user_data['role'],
            user_data['email']
        )
        
        if success:
            print(f"✓ 创建测试用户: {user_data['username']} ({user_data['role']})")
        else:
            print(f"❌ 创建用户失败: {user_data['username']} - {message}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='鉴权数据库管理工具')
    parser.add_argument('--init', action='store_true', help='初始化鉴权数据库')
    parser.add_argument('--check', action='store_true', help='检查鉴权系统状态')
    parser.add_argument('--create-test-users', action='store_true', help='创建测试用户')
    
    args = parser.parse_args()
    
    if args.init:
        success = init_auth_database()
        sys.exit(0 if success else 1)
    elif args.check:
        success = check_auth_status()
        sys.exit(0 if success else 1)
    elif args.create_test_users:
        create_test_users()
    else:
        print("请指定操作参数:")
        print("  --init: 初始化鉴权数据库")
        print("  --check: 检查鉴权系统状态")
        print("  --create-test-users: 创建测试用户")
        parser.print_help() 

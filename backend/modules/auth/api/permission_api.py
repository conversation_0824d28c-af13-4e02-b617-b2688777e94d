"""
权限管理API接口
提供权限矩阵管理的REST API
"""

import logging
from flask import Blueprint, request, jsonify, g
from modules.auth.services.permission_service import permission_service
from core.utils.decorators import admin_required, login_required
from core.security.csrf_protection import csrf_protect

logger = logging.getLogger(__name__)

# 创建权限管理蓝图
permission_bp = Blueprint('permission', __name__)

@permission_bp.route('/permissions', methods=['GET'])
@admin_required
def get_permissions():
    """获取所有权限资源 - 仅管理员可访问"""
    try:
        permissions = permission_service.get_all_permissions()
        
        return jsonify({
            'success': True,
            'data': {
                'permissions': permissions,
                'total': len(permissions)
            }
        })
        
    except Exception as e:
        logger.error(f"获取权限列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取权限列表失败，请重试'
        }), 500

@permission_bp.route('/actions', methods=['GET'])
@admin_required
def get_actions():
    """获取所有操作类型 - 仅管理员可访问"""
    try:
        actions = permission_service.get_all_actions()
        
        return jsonify({
            'success': True,
            'data': {
                'actions': actions,
                'total': len(actions)
            }
        })
        
    except Exception as e:
        logger.error(f"获取操作列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取操作列表失败，请重试'
        }), 500

@permission_bp.route('/roles', methods=['GET'])
@admin_required
def get_roles():
    """获取所有角色 - 仅管理员可访问"""
    try:
        roles = permission_service.get_all_roles()
        
        return jsonify({
            'success': True,
            'data': {
                'roles': roles,
                'total': len(roles)
            }
        })
        
    except Exception as e:
        logger.error(f"获取角色列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取角色列表失败，请重试'
        }), 500

@permission_bp.route('/groups', methods=['GET'])
@admin_required
def get_permission_groups():
    """获取权限组 - 仅管理员可访问"""
    try:
        groups = permission_service.get_permission_groups()
        
        return jsonify({
            'success': True,
            'data': {
                'groups': groups,
                'total': len(groups)
            }
        })
        
    except Exception as e:
        logger.error(f"获取权限组失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取权限组失败，请重试'
        }), 500

@permission_bp.route('/groups/<int:group_id>/permissions', methods=['GET'])
@admin_required
def get_group_permissions(group_id):
    """获取权限组中的权限 - 仅管理员可访问"""
    try:
        permissions = permission_service.get_group_permissions(group_id)
        
        return jsonify({
            'success': True,
            'data': {
                'permissions': permissions,
                'total': len(permissions)
            }
        })
        
    except Exception as e:
        logger.error(f"获取权限组权限失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取权限组权限失败，请重试'
        }), 500

@permission_bp.route('/matrix', methods=['GET'])
@admin_required
def get_permission_matrix():
    """获取完整的权限矩阵 - 仅管理员可访问"""
    try:
        matrix = permission_service.get_permission_matrix()
        
        return jsonify({
            'success': True,
            'data': matrix
        })
        
    except Exception as e:
        logger.error(f"获取权限矩阵失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取权限矩阵失败，请重试'
        }), 500

@permission_bp.route('/users/<int:user_id>/permissions', methods=['GET'])
@admin_required
def get_user_permissions(user_id):
    """获取用户权限 - 仅管理员可访问"""
    try:
        permissions = permission_service.get_user_permissions(user_id)
        
        return jsonify({
            'success': True,
            'data': {
                'user_id': user_id,
                'permissions': permissions,
                'total': len(permissions)
            }
        })
        
    except Exception as e:
        logger.error(f"获取用户权限失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取用户权限失败，请重试'
        }), 500

@permission_bp.route('/roles/<int:role_id>/permissions', methods=['GET'])
@admin_required
def get_role_permissions(role_id):
    """获取角色权限 - 仅管理员可访问"""
    try:
        permissions = permission_service.get_role_permissions(role_id)
        
        return jsonify({
            'success': True,
            'data': {
                'role_id': role_id,
                'permissions': permissions,
                'total': len(permissions)
            }
        })
        
    except Exception as e:
        logger.error(f"获取角色权限失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取角色权限失败，请重试'
        }), 500

@admin_required
@csrf_protect
@permission_bp.route('/roles/<int:role_id>/permissions', methods=['POST'])
def assign_role_permission(role_id):
    """为角色分配权限 - 仅管理员可访问"""
    try:
        data = request.get_json()
        permission_id = data.get('permission_id')
        action_id = data.get('action_id')
        is_granted = data.get('is_granted', True)
        
        if not permission_id or not action_id:
            return jsonify({
                'success': False,
                'error': '权限ID和操作ID不能为空'
            }), 400
        
        success = permission_service.assign_role_permission(
            role_id=role_id,
            permission_id=permission_id,
            action_id=action_id,
            granted_by=g.current_user['id'],
            is_granted=is_granted
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': '角色权限分配成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '角色权限分配失败'
            }), 500
            
    except Exception as e:
        logger.error(f"分配角色权限失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '分配角色权限失败，请重试'
        }), 500

@admin_required
@csrf_protect
@permission_bp.route('/users/<int:user_id>/permissions', methods=['POST'])
def assign_user_permission(user_id):
    """为用户分配特殊权限 - 仅管理员可访问"""
    try:
        data = request.get_json()
        permission_id = data.get('permission_id')
        action_id = data.get('action_id')
        is_granted = data.get('is_granted', True)
        expires_at = data.get('expires_at')
        reason = data.get('reason', '')
        
        if not permission_id or not action_id:
            return jsonify({
                'success': False,
                'error': '权限ID和操作ID不能为空'
            }), 400
        
        # 处理过期时间
        expires_datetime = None
        if expires_at:
            from datetime import datetime
            try:
                expires_datetime = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': '过期时间格式不正确'
                }), 400
        
        success = permission_service.assign_user_permission(
            user_id=user_id,
            permission_id=permission_id,
            action_id=action_id,
            granted_by=g.current_user['id'],
            is_granted=is_granted,
            expires_at=expires_datetime,
            reason=reason
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': '用户特殊权限分配成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '用户特殊权限分配失败'
            }), 500
            
    except Exception as e:
        logger.error(f"分配用户特殊权限失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '分配用户特殊权限失败，请重试'
        }), 500

@admin_required
@csrf_protect
@permission_bp.route('/users/<int:user_id>/roles', methods=['POST'])
def assign_user_role(user_id):
    """为用户分配角色 - 仅管理员可访问"""
    try:
        data = request.get_json()
        role_id = data.get('role_id')
        expires_at = data.get('expires_at')
        
        if not role_id:
            return jsonify({
                'success': False,
                'error': '角色ID不能为空'
            }), 400
        
        # 处理过期时间
        expires_datetime = None
        if expires_at:
            from datetime import datetime
            try:
                expires_datetime = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': '过期时间格式不正确'
                }), 400
        
        success = permission_service.assign_user_role(
            user_id=user_id,
            role_id=role_id,
            assigned_by=g.current_user['id'],
            expires_at=expires_datetime
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': '用户角色分配成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '用户角色分配失败'
            }), 500
            
    except Exception as e:
        logger.error(f"分配用户角色失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '分配用户角色失败，请重试'
        }), 500

@admin_required
@csrf_protect
@permission_bp.route('/users/<int:user_id>/roles/<int:role_id>', methods=['DELETE'])
def remove_user_role(user_id, role_id):
    """移除用户角色 - 仅管理员可访问"""
    try:
        success = permission_service.remove_user_role(user_id, role_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': '用户角色移除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '用户角色移除失败'
            }), 500
            
    except Exception as e:
        logger.error(f"移除用户角色失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '移除用户角色失败，请重试'
        }), 500

@login_required
@csrf_protect
@permission_bp.route('/check', methods=['POST'])
def check_permission():
    """检查权限 - 需要登录"""
    try:
        data = request.get_json()
        resource_name = data.get('resource_name')
        action_name = data.get('action_name')
        
        if not resource_name or not action_name:
            return jsonify({
                'success': False,
                'error': '资源名称和操作名称不能为空'
            }), 400
        
        has_permission = permission_service.check_user_permission(
            user_id=g.current_user['id'],
            resource_name=resource_name,
            action_name=action_name
        )
        
        return jsonify({
            'success': True,
            'data': {
                'has_permission': has_permission,
                'resource_name': resource_name,
                'action_name': action_name
            }
        })
        
    except Exception as e:
        logger.error(f"权限检查失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '权限检查失败，请重试'
        }), 500

@admin_required
@csrf_protect
@permission_bp.route('/initialize', methods=['POST'])
def initialize_permissions():
    """初始化权限系统 - 仅管理员可访问"""
    try:
        success = permission_service.initialize_permissions()
        
        if success:
            return jsonify({
                'success': True,
                'message': '权限系统初始化成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '权限系统初始化失败'
            }), 500
            
    except Exception as e:
        logger.error(f"初始化权限系统失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '初始化权限系统失败，请重试'
        }), 500

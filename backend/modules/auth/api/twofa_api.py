"""
2FA相关API接口
"""

from flask import Blueprint, request, jsonify, session, g
from core.utils.decorators import login_required, admin_required
from core.security.csrf_protection import csrf_protect
from modules.auth.services.auth_service import auth_service
from modules.auth.services.totp_service import totp_service
from modules.auth.services.session_manager import session_manager
from modules.auth.services.temp_token_service import temp_token_service
import logging

logger = logging.getLogger(__name__)

twofa_bp = Blueprint('twofa', __name__)


@twofa_bp.route('/2fa/verify', methods=['POST'])
def verify_2fa():
    """验证2FA码并完成登录"""
    try:
        data = request.get_json()
        temp_token = data.get('temp_token')
        totp_code = data.get('totp_code', '').strip()

        if not temp_token or not totp_code:
            return jsonify({
                'success': False,
                'error': '临时令牌和验证码不能为空'
            }), 400

        # 验证临时token并获取用户信息
        token_data = temp_token_service.verify_temp_token(temp_token)
        if not token_data:
            return jsonify({
                'success': False,
                'error': '临时令牌无效或已过期，请重新登录'
            }), 401

        user_id = token_data.get('user_id')
        if not user_id:
            logger.error(f"临时token中缺少user_id: {temp_token[:8]}...")
            return jsonify({
                'success': False,
                'error': '令牌数据异常，请重新登录'
            }), 401

        # 验证2FA并完成登录
        user = auth_service.verify_2fa_and_complete_login(
            user_id,
            totp_code,
            request.remote_addr
        )
        
        if not user:
            return jsonify({
                'success': False,
                'error': '验证码错误或已过期'
            }), 401

        # 2FA验证成功，使临时token失效
        temp_token_service.invalidate_temp_token(temp_token)

        # 创建会话
        session_id = session_manager.create_session(
            user['id'],
            request.remote_addr,
            request.headers.get('User-Agent', '')
        )

        # 设置session
        session['user_id'] = user['id']
        session['username'] = user['username']
        session['role'] = user['role']
        session['session_id'] = session_id
        session.permanent = True

        logger.info(f"用户2FA验证成功并完成登录: {user['username']}")

        return jsonify({
            'success': True,
            'message': '验证成功',
            'user': user
        })
        
    except Exception as e:
        logger.error(f"2FA验证异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '验证失败，请重试'
        }), 500


@twofa_bp.route('/2fa/setup-verify', methods=['POST'])
def setup_verify_2fa():
    """首次设置2FA时的验证"""
    try:
        data = request.get_json()
        temp_token = data.get('temp_token')
        totp_code = data.get('totp_code', '').strip()

        if not temp_token or not totp_code:
            return jsonify({
                'success': False,
                'error': '临时令牌和验证码不能为空'
            }), 400

        # 验证临时token并获取用户信息
        token_data = temp_token_service.verify_temp_token(temp_token)
        if not token_data:
            return jsonify({
                'success': False,
                'error': '临时令牌无效或已过期，请重新登录'
            }), 401

        user_id = token_data.get('user_id')
        if not user_id:
            logger.error(f"临时token中缺少user_id: {temp_token[:8]}...")
            return jsonify({
                'success': False,
                'error': '令牌数据异常，请重新登录'
            }), 401

        # 验证并启用2FA
        success = totp_service.verify_and_enable_2fa(user_id, totp_code)
        
        if success:
            # 2FA设置成功，使临时token失效
            temp_token_service.invalidate_temp_token(temp_token)

            # 获取用户信息并创建会话
            user = auth_service._get_user_by_id(user_id)
            if user:
                # 创建会话
                session_id = session_manager.create_session(
                    user['id'],
                    request.remote_addr,
                    request.headers.get('User-Agent', '')
                )

                # 设置session
                session['user_id'] = user['id']
                session['username'] = user['username']
                session['role'] = user['role']
                session['session_id'] = session_id
                session.permanent = True

                logger.info(f"用户2FA设置成功并完成登录: {user['username']}")

                return jsonify({
                    'success': True,
                    'message': '2FA设置成功',
                    'user': {
                        'id': user['id'],
                        'username': user['username'],
                        'role': user['role']
                    }
                })
            else:
                logger.error(f"2FA设置成功但获取用户信息失败: user_id={user_id}")
                return jsonify({
                    'success': False,
                    'error': '设置成功但登录失败，请重新登录'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': '验证码错误，请重试'
            }), 400
        
    except Exception as e:
        logger.error(f"2FA设置验证异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '设置失败，请重试'
        }), 500


@twofa_bp.route('/2fa/status/<int:user_id>', methods=['GET'])
@login_required
def get_2fa_status(user_id):
    """获取用户2FA状态"""
    try:
        # 只允许用户查看自己的状态或管理员查看任何用户
        if g.current_user['id'] != user_id and g.current_user['role'] != 'admin':
            return jsonify({
                'success': False,
                'error': '权限不足'
            }), 403
        
        status = totp_service.get_user_2fa_status(user_id)
        
        return jsonify({
            'success': True,
            'status': status
        })
        
    except Exception as e:
        logger.error(f"获取2FA状态异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取状态失败'
        }), 500


@twofa_bp.route('/2fa/disable/<int:user_id>', methods=['POST'])
@admin_required
@csrf_protect
def disable_2fa(user_id):
    """禁用用户2FA（仅管理员）"""
    try:
        success = totp_service.disable_2fa_for_user(user_id)
        
        if success:
            # 记录操作日志
            auth_service.log_user_activity(
                g.current_user['id'],
                'disable_2fa',
                f"禁用用户2FA: {user_id}",
                request.remote_addr,
                True
            )
            
            return jsonify({
                'success': True,
                'message': '2FA已禁用'
            })
        else:
            return jsonify({
                'success': False,
                'error': '禁用失败'
            }), 500
        
    except Exception as e:
        logger.error(f"禁用2FA异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '操作失败，请重试'
        }), 500


@twofa_bp.route('/2fa/reset/<int:user_id>', methods=['POST'])
@admin_required
@csrf_protect
def reset_2fa(user_id):
    """重置用户2FA（仅管理员）"""
    try:
        # 获取用户信息
        user = auth_service._get_user_by_id(user_id)
        if not user:
            return jsonify({
                'success': False,
                'error': '用户不存在'
            }), 404
        
        # 重新设置2FA
        twofa_setup = totp_service.setup_2fa_for_user(user_id, user['username'])
        
        # 记录操作日志
        auth_service.log_user_activity(
            g.current_user['id'],
            'reset_2fa',
            f"重置用户2FA: {user_id}",
            request.remote_addr,
            True
        )
        
        return jsonify({
            'success': True,
            'message': '2FA已重置',
            '2fa_setup': twofa_setup
        })
        
    except Exception as e:
        logger.error(f"重置2FA异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '重置失败，请重试'
        }), 500

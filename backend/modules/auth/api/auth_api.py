"""
鉴权API - 登录/登出等核心功能
"""
from flask import Blueprint, request, jsonify, session
import logging
import json
from modules.auth.services.auth_service import auth_service
from modules.auth.services.session_manager import session_manager
from modules.auth.services.temp_token_service import temp_token_service
from database.duckdb_manager import db_manager
from core.utils.decorators import login_required
from core.security.csrf_protection import csrf_protect

auth_bp = Blueprint('auth', __name__)
logger = logging.getLogger(__name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '')
        
        if not username or not password:
            return jsonify({
                'success': False,
                'error': '用户名和密码不能为空'
            }), 400
        
        # 验证用户凭据
        user = auth_service.authenticate_user(username, password, request.remote_addr)
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户名或密码错误'
            }), 401

        # 强制2FA验证 - 所有用户都必须启用并验证2FA
        twofa_status = user.get('2fa_status', {})

        # 检查系统是否强制启用2FA
        from modules.auth.services.config_service import auth_config_service
        force_2fa = auth_config_service.is_2fa_forced()
        mandatory_message = auth_config_service.get_2fa_mandatory_message()

        # 如果用户还没有启用2FA，强制设置
        if not twofa_status.get('is_enabled'):
            if force_2fa:
                try:
                    from modules.auth.services.totp_service import totp_service

                    # 为用户强制设置2FA
                    twofa_setup = totp_service.setup_2fa_for_user(user['id'], user['username'])

                    # 生成临时token用于2FA设置
                    temp_token_data = {
                        'user_id': user['id'],
                        'username': user['username'],
                        'email': user.get('email'),
                        'role': user.get('role'),
                        'ip_address': request.remote_addr,
                        'user_agent': request.headers.get('User-Agent', '')
                    }

                    temp_token = temp_token_service.generate_temp_token(temp_token_data)

                    if not temp_token:
                        logger.error(f"生成临时token失败: user_id={user['id']}")
                        return jsonify({
                            'success': False,
                            'error': '系统繁忙，请稍后重试'
                        }), 500

                    logger.info(f"为用户 {user['username']} 强制设置2FA")

                    return jsonify({
                        'success': True,
                        'require_2fa_setup': True,
                        'message': mandatory_message,
                        'temp_token': temp_token,
                        'username': user['username'],
                        '2fa_setup': twofa_setup
                    })
                except Exception as e:
                    logger.error(f"强制设置2FA失败: {str(e)}")
                    return jsonify({
                        'success': False,
                        'error': '系统安全设置失败，请联系管理员'
                    }), 500
            else:
                # 如果不强制2FA，允许直接登录（但这种情况在当前需求下不应该发生）
                logger.warning(f"用户 {user['username']} 未启用2FA但系统未强制要求")
                # 继续到后面的直接登录逻辑

        # 如果2FA已启用但未验证，需要完成首次验证
        elif twofa_status.get('is_enabled') and not twofa_status.get('is_verified'):
            try:
                from modules.auth.services.totp_service import totp_service

                # 获取2FA设置信息
                setup_query = "SELECT totp_secret, backup_codes FROM auth_users WHERE id = ?"
                setup_result = db_manager.fetch_one(setup_query, [user['id']])

                if setup_result and setup_result['totp_secret']:
                    # 重新生成二维码和URI
                    provisioning_uri = totp_service.get_provisioning_uri(user['username'], setup_result['totp_secret'])
                    qr_code = totp_service.generate_qr_code(provisioning_uri)
                    backup_codes = json.loads(setup_result['backup_codes']) if setup_result['backup_codes'] else []

                    setup_data = {
                        'secret_key': setup_result['totp_secret'],
                        'backup_codes': backup_codes,
                        'qr_code': qr_code,
                        'provisioning_uri': provisioning_uri
                    }

                    # 生成临时token用于2FA设置
                    temp_token_data = {
                        'user_id': user['id'],
                        'username': user['username'],
                        'email': user.get('email'),
                        'role': user.get('role'),
                        'ip_address': request.remote_addr,
                        'user_agent': request.headers.get('User-Agent', '')
                    }

                    temp_token = temp_token_service.generate_temp_token(temp_token_data)

                    if not temp_token:
                        logger.error(f"生成临时token失败: user_id={user['id']}")
                        return jsonify({
                            'success': False,
                            'error': '系统繁忙，请稍后重试'
                        }), 500

                    return jsonify({
                        'success': True,
                        'require_2fa_setup': True,
                        'message': '请完成双因子认证设置',
                        'temp_token': temp_token,
                        'username': user['username'],
                        '2fa_setup': setup_data
                    })
                else:
                    # 如果没有设置数据，重新设置
                    twofa_setup = totp_service.setup_2fa_for_user(user['id'], user['username'])

                    # 生成临时token用于2FA设置
                    temp_token_data = {
                        'user_id': user['id'],
                        'username': user['username'],
                        'email': user.get('email'),
                        'role': user.get('role'),
                        'ip_address': request.remote_addr,
                        'user_agent': request.headers.get('User-Agent', '')
                    }

                    temp_token = temp_token_service.generate_temp_token(temp_token_data)

                    if not temp_token:
                        logger.error(f"生成临时token失败: user_id={user['id']}")
                        return jsonify({
                            'success': False,
                            'error': '系统繁忙，请稍后重试'
                        }), 500

                    return jsonify({
                        'success': True,
                        'require_2fa_setup': True,
                        'message': '请完成双因子认证设置',
                        'temp_token': temp_token,
                        'username': user['username'],
                        '2fa_setup': twofa_setup
                    })
            except Exception as e:
                logger.error(f"获取2FA设置数据失败: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': '获取安全设置失败，请重试'
                }), 500

        # 2FA已启用且已验证，需要进行2FA验证
        elif twofa_status.get('is_enabled') and twofa_status.get('is_verified'):
            # 生成临时token用于2FA验证
            temp_token_data = {
                'user_id': user['id'],
                'username': user['username'],
                'email': user.get('email'),
                'role': user.get('role'),
                'ip_address': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', '')
            }

            temp_token = temp_token_service.generate_temp_token(temp_token_data)

            if not temp_token:
                logger.error(f"生成临时token失败: user_id={user['id']}")
                return jsonify({
                    'success': False,
                    'error': '系统繁忙，请稍后重试'
                }), 500

            logger.info(f"为用户 {user['username']} 生成2FA临时token")

            # 需要2FA验证，返回临时token
            return jsonify({
                'success': True,
                'require_2fa': True,
                'message': '请输入双因子认证码',
                'temp_token': temp_token,
                'username': user['username']
            })

        # 如果不强制2FA且用户未启用2FA，允许直接登录
        else:
            if not force_2fa:
                # 创建会话
                session_id = session_manager.create_session(
                    user['id'],
                    request.remote_addr,
                    request.headers.get('User-Agent', '')
                )

                # 设置session
                session['user_id'] = user['id']
                session['username'] = user['username']
                session['role'] = user['role']
                session['session_id'] = session_id
                session.permanent = True

                # 记录登录成功日志
                auth_service.log_user_activity(
                    user['id'],
                    'login_success',
                    f"登录成功（未启用2FA）",
                    request.remote_addr,
                    True
                )

                logger.info(f"用户登录成功（未启用2FA）: {username}, IP: {request.remote_addr}")

                return jsonify({
                    'success': True,
                    'message': '登录成功',
                    'user': {
                        'id': user['id'],
                        'username': user['username'],
                        'role': user['role']
                    }
                })
            else:
                # 强制2FA但状态异常
                logger.error(f"强制2FA模式下用户 {user['username']} 的2FA状态异常: {twofa_status}")
                return jsonify({
                    'success': False,
                    'error': '账户安全状态异常，请联系管理员'
                }), 500
        
    except Exception as e:
        logger.error(f"登录处理异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '登录处理失败，请重试'
        }), 500

@csrf_protect
@auth_bp.route('/logout', methods=['POST'])
@login_required
def logout():
    """用户登出"""
    try:
        user_id = session.get('user_id')
        username = session.get('username')
        session_id = session.get('session_id')
        
        # 销毁会话
        if session_id:
            session_manager.destroy_session(session_id)
        
        # 记录登出日志
        if user_id:
            auth_service.log_user_activity(
                user_id, 
                'logout', 
                "用户登出", 
                request.remote_addr, 
                True
            )
        
        # 清除session
        session.clear()
        
        logger.info(f"用户登出: {username}, IP: {request.remote_addr}")
        
        return jsonify({
            'success': True,
            'message': '已安全登出'
        })
        
    except Exception as e:
        logger.error(f"登出处理异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '登出处理失败'
        }), 500

@auth_bp.route('/check', methods=['GET'])
def check_auth():
    """检查登录状态"""
    try:
        if 'user_id' not in session:
            return jsonify({
                'authenticated': False,
                'user': None
            })
        
        # 验证会话
        if not session_manager.validate_session(session.get('session_id')):
            session.clear()
            return jsonify({
                'authenticated': False,
                'user': None
            })
        
        return jsonify({
            'authenticated': True,
            'user': {
                'id': session['user_id'],
                'username': session['username'],
                'role': session['role']
            }
        })
        
    except Exception as e:
        logger.error(f"检查认证状态异常: {str(e)}")
        return jsonify({
            'authenticated': False,
            'user': None
        })

@csrf_protect
@auth_bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """修改密码"""
    try:
        data = request.get_json()
        old_password = data.get('old_password', '')
        new_password = data.get('new_password', '')
        
        if not old_password or not new_password:
            return jsonify({
                'success': False,
                'error': '原密码和新密码不能为空'
            }), 400
        
        user_id = session['user_id']
        success, message = auth_service.change_password(user_id, old_password, new_password)
        
        # 记录密码修改日志
        auth_service.log_user_activity(
            user_id, 
            'change_password', 
            "修改密码", 
            request.remote_addr, 
            success,
            None if success else message
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 400
            
    except Exception as e:
        logger.error(f"修改密码异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '修改密码失败，请重试'
        }), 500 
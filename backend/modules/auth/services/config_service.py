"""
认证配置服务 - 管理系统安全配置
"""
import logging
from database.duckdb_manager import db_manager
from config.auth_settings import SECURITY_CONFIG

logger = logging.getLogger(__name__)

class AuthConfigService:
    """认证配置服务"""
    
    def __init__(self):
        self.db = db_manager
    
    def is_2fa_forced(self) -> bool:
        """检查是否强制启用2FA"""
        try:
            # 首先检查数据库配置
            query = "SELECT value FROM auth_system_config WHERE key = 'force_2fa_enabled'"
            result = self.db.fetch_one(query)
            
            if result:
                return result['value'].lower() == 'true'
            
            # 如果数据库没有配置，使用代码配置
            return SECURITY_CONFIG.get('FORCE_2FA_ENABLED', True)
            
        except Exception as e:
            logger.error(f"检查2FA强制配置失败: {str(e)}")
            # 默认强制启用2FA以确保安全
            return True
    
    def get_2fa_mandatory_message(self) -> str:
        """获取强制2FA的提示信息"""
        try:
            # 首先检查数据库配置
            query = "SELECT value FROM auth_system_config WHERE key = '2fa_mandatory_message'"
            result = self.db.fetch_one(query)
            
            if result:
                return result['value']
            
            # 如果数据库没有配置，使用代码配置
            return SECURITY_CONFIG.get('MANDATORY_2FA_MESSAGE', '系统要求所有用户必须启用双因子认证以确保账户安全')
            
        except Exception as e:
            logger.error(f"获取2FA强制消息失败: {str(e)}")
            return '系统要求所有用户必须启用双因子认证以确保账户安全'
    
    def get_2fa_issuer_name(self) -> str:
        """获取2FA发行者名称"""
        try:
            query = "SELECT value FROM auth_system_config WHERE key = '2fa_issuer_name'"
            result = self.db.fetch_one(query)
            
            if result:
                return result['value']
            
            return 'Risk Analysis System'
            
        except Exception as e:
            logger.error(f"获取2FA发行者名称失败: {str(e)}")
            return 'Risk Analysis System'
    
    def get_2fa_window_size(self) -> int:
        """获取TOTP验证窗口大小"""
        try:
            query = "SELECT value FROM auth_system_config WHERE key = '2fa_window_size'"
            result = self.db.fetch_one(query)
            
            if result:
                return int(result['value'])
            
            return 1
            
        except Exception as e:
            logger.error(f"获取2FA窗口大小失败: {str(e)}")
            return 1
    
    def get_backup_codes_count(self) -> int:
        """获取备用恢复码数量"""
        try:
            query = "SELECT value FROM auth_system_config WHERE key = 'backup_codes_count'"
            result = self.db.fetch_one(query)
            
            if result:
                return int(result['value'])
            
            return 10
            
        except Exception as e:
            logger.error(f"获取备用码数量失败: {str(e)}")
            return 10
    
    def update_2fa_config(self, force_enabled: bool = None, mandatory_message: str = None):
        """更新2FA配置"""
        try:
            if force_enabled is not None:
                query = """
                INSERT OR REPLACE INTO auth_system_config (key, value, description, updated_at)
                VALUES ('force_2fa_enabled', ?, '是否强制所有用户启用2FA（安全要求）', CURRENT_TIMESTAMP)
                """
                self.db.execute_query(query, ['true' if force_enabled else 'false'])
                logger.info(f"更新强制2FA配置: {force_enabled}")
            
            if mandatory_message:
                query = """
                INSERT OR REPLACE INTO auth_system_config (key, value, description, updated_at)
                VALUES ('2fa_mandatory_message', ?, '强制2FA时显示的提示信息', CURRENT_TIMESTAMP)
                """
                self.db.execute_query(query, [mandatory_message])
                logger.info(f"更新2FA强制消息: {mandatory_message}")
            
            return True
            
        except Exception as e:
            logger.error(f"更新2FA配置失败: {str(e)}")
            return False

# 创建全局实例
auth_config_service = AuthConfigService()

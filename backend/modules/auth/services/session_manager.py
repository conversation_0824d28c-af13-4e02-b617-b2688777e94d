"""
会话管理器 - 必须严格按照此实现
"""
import secrets
import logging
from datetime import datetime, timedelta
from database.duckdb_manager import db_manager

logger = logging.getLogger(__name__)

class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        self.db = db_manager
    
    def create_session(self, user_id, ip_address, user_agent):
        """创建新会话"""
        try:
            # 生成安全的会话ID
            session_id = secrets.token_urlsafe(32)
            
            # 获取会话超时配置
            timeout_hours = self._get_session_timeout()
            expires_at = datetime.now() + timedelta(hours=timeout_hours)
            
            # 插入会话记录
            query = """
            INSERT INTO auth_user_sessions (session_id, user_id, expires_at, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?)
            """
            self.db.execute_query(query, [session_id, user_id, expires_at, ip_address, user_agent])
            
            logger.info(f"创建会话: user_id={user_id}, session_id={session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"创建会话失败: {str(e)}")
            raise
    
    def validate_session(self, session_id):
        """验证会话是否有效"""
        if not session_id:
            return False
        
        try:
            query = """
            SELECT s.user_id, s.expires_at, u.username, u.role, u.is_active
            FROM auth_user_sessions s
            JOIN auth_users u ON s.user_id = u.id
            WHERE s.session_id = ? AND s.is_active = true
            """
            result = self.db.fetch_one(query, [session_id])
            
            if not result:
                return False
            
            # 检查用户是否被禁用
            if not result['is_active']:
                self._deactivate_session(session_id)
                return False
            
            # 检查会话是否过期
            if datetime.now() > result['expires_at']:
                self._deactivate_session(session_id)
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证会话失败: {str(e)}")
            return False
    
    def destroy_session(self, session_id):
        """销毁会话"""
        try:
            query = "UPDATE auth_user_sessions SET is_active = false WHERE session_id = ?"
            self.db.execute_query(query, [session_id])
            logger.info(f"销毁会话: {session_id}")
        except Exception as e:
            logger.error(f"销毁会话失败: {str(e)}")
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        try:
            query = "DELETE FROM auth_user_sessions WHERE expires_at < ?"
            self.db.execute_query(query, [datetime.now()])
            logger.info("清理过期会话完成")
        except Exception as e:
            logger.error(f"清理过期会话失败: {str(e)}")
    
    def _get_session_timeout(self):
        """获取会话超时配置"""
        try:
            query = "SELECT value FROM auth_system_config WHERE key = 'session_timeout_hours'"
            result = self.db.fetch_one(query)
            return int(result['value']) if result else 24
        except:
            return 24  # 默认24小时
    
    def _deactivate_session(self, session_id):
        """停用会话"""
        query = "UPDATE auth_user_sessions SET is_active = false WHERE session_id = ?"
        self.db.execute_query(query, [session_id])

# 全局会话管理器实例
session_manager = SessionManager() 
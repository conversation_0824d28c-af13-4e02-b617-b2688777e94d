"""
2FA TOTP服务
提供TOTP密钥生成、验证码验证、二维码生成等功能
"""

try:
    import pyotp
    import qrcode
    TOTP_AVAILABLE = True
except ImportError:
    TOTP_AVAILABLE = False

import secrets
import json
import base64
from io import BytesIO
from typing import Tuple, List, Optional, Dict, Any
from datetime import datetime, timedelta
from database.duckdb_manager import db_manager
import logging

logger = logging.getLogger(__name__)


class TOTPService:
    """TOTP双因子认证服务"""
    
    def __init__(self):
        self.db = db_manager
        self.issuer_name = self._get_config('2fa_issuer_name', 'Risk Analysis System')
        self.window_size = int(self._get_config('2fa_window_size', '1'))
        self.backup_codes_count = int(self._get_config('backup_codes_count', '10'))
    
    def _get_config(self, key: str, default: str) -> str:
        """获取系统配置"""
        try:
            query = "SELECT value FROM auth_system_config WHERE key = ?"
            result = self.db.fetch_one(query, [key])
            return result['value'] if result else default
        except Exception as e:
            logger.warning(f"获取配置失败 {key}: {str(e)}")
            return default
    
    def generate_secret_key(self) -> str:
        """生成TOTP密钥"""
        if not TOTP_AVAILABLE:
            # 简单的Base32密钥生成
            import string
            import random
            chars = string.ascii_uppercase + '234567'  # Base32字符集
            return ''.join(random.choice(chars) for _ in range(32))
        return pyotp.random_base32()
    
    def generate_backup_codes(self) -> List[str]:
        """生成备用恢复码"""
        codes = []
        for _ in range(self.backup_codes_count):
            # 生成8位数字恢复码
            code = ''.join([str(secrets.randbelow(10)) for _ in range(8)])
            codes.append(code)
        return codes
    
    def get_provisioning_uri(self, username: str, secret_key: str) -> str:
        """获取TOTP配置URI（用于生成二维码）"""
        if not TOTP_AVAILABLE:
            # 手动构建URI
            import urllib.parse
            return f"otpauth://totp/{urllib.parse.quote(self.issuer_name)}:{urllib.parse.quote(username)}?secret={secret_key}&issuer={urllib.parse.quote(self.issuer_name)}"

        totp = pyotp.TOTP(secret_key)
        return totp.provisioning_uri(
            name=username,
            issuer_name=self.issuer_name
        )
    
    def generate_qr_code(self, provisioning_uri: str) -> str:
        """生成二维码图片（Base64编码）"""
        if not TOTP_AVAILABLE:
            # 返回一个占位符图片或URI
            logger.warning("QR码生成库不可用，返回URI文本")
            return f"data:text/plain;base64,{base64.b64encode(provisioning_uri.encode()).decode()}"

        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(provisioning_uri)
            qr.make(fit=True)

            # 创建二维码图片
            img = qr.make_image(fill_color="black", back_color="white")

            # 转换为Base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            return f"data:image/png;base64,{img_str}"
        except Exception as e:
            logger.error(f"生成二维码失败: {str(e)}")
            raise
    
    def verify_totp_code(self, secret_key: str, code: str) -> bool:
        """验证TOTP验证码"""
        if not TOTP_AVAILABLE:
            logger.warning("TOTP库不可用，使用测试验证码")
            return code == "123456"

        try:
            totp = pyotp.TOTP(secret_key)
            return totp.verify(code, valid_window=self.window_size)
        except Exception as e:
            logger.error(f"验证TOTP码失败: {str(e)}")
            return False
    
    def verify_backup_code(self, user_id: int, code: str) -> bool:
        """验证备用恢复码"""
        try:
            # 获取用户的备用码
            query = "SELECT backup_codes FROM auth_users WHERE id = ?"
            result = self.db.fetch_one(query, [user_id])
            
            if not result or not result['backup_codes']:
                return False
            
            backup_codes = json.loads(result['backup_codes'])
            
            # 检查码是否存在且未使用
            if code in backup_codes:
                # 使用后移除该码
                backup_codes.remove(code)
                
                # 更新数据库
                update_query = "UPDATE auth_users SET backup_codes = ? WHERE id = ?"
                self.db.execute_query(update_query, [json.dumps(backup_codes), user_id])
                
                logger.info(f"用户 {user_id} 使用备用恢复码")
                return True
            
            return False
        except Exception as e:
            logger.error(f"验证备用码失败: {str(e)}")
            return False
    
    def setup_2fa_for_user(self, user_id: int, username: str) -> Dict[str, Any]:
        """为用户设置2FA"""
        try:
            # 生成密钥和备用码
            secret_key = self.generate_secret_key()
            backup_codes = self.generate_backup_codes()
            
            # 生成二维码
            provisioning_uri = self.get_provisioning_uri(username, secret_key)
            qr_code = self.generate_qr_code(provisioning_uri)
            
            # 更新数据库
            query = """
            UPDATE auth_users 
            SET totp_secret = ?, backup_codes = ?, is_2fa_enabled = TRUE, is_2fa_verified = FALSE
            WHERE id = ?
            """
            self.db.execute_query(query, [
                secret_key, 
                json.dumps(backup_codes), 
                user_id
            ])
            
            logger.info(f"为用户 {username} 设置2FA")
            
            return {
                'secret_key': secret_key,
                'backup_codes': backup_codes,
                'qr_code': qr_code,
                'provisioning_uri': provisioning_uri
            }
        except Exception as e:
            logger.error(f"设置2FA失败: {str(e)}")
            raise
    
    def verify_and_enable_2fa(self, user_id: int, code: str) -> bool:
        """验证并启用2FA（首次绑定）"""
        try:
            # 使用事务保护，避免并发冲突
            with self.db.get_connection() as conn:
                try:
                    conn.execute("BEGIN TRANSACTION")

                    # 获取用户的密钥（加锁查询）
                    result = conn.execute(
                        "SELECT totp_secret FROM auth_users WHERE id = ? AND is_2fa_enabled = TRUE",
                        [user_id]
                    ).fetchone()

                    if not result or not result[0]:  # result是tuple，不是dict
                        conn.execute("ROLLBACK")
                        return False

                    secret_key = result[0]

                    # 验证码（首次绑定只支持TOTP验证码）
                    if self.verify_totp_code(secret_key, code):
                        # 标记为已验证
                        conn.execute("""
                            UPDATE auth_users
                            SET is_2fa_verified = TRUE, last_2fa_verify = ?
                            WHERE id = ?
                        """, [datetime.now(), user_id])

                        # 提交事务
                        conn.execute("COMMIT")

                        logger.info(f"用户 {user_id} 完成2FA首次验证")
                        return True
                    else:
                        conn.execute("ROLLBACK")
                        return False

                except Exception as e:
                    # 安全的回滚事务
                    try:
                        conn.execute("ROLLBACK")
                    except Exception as rollback_error:
                        logger.warning(f"回滚事务失败: {rollback_error}")
                    raise e

        except Exception as e:
            logger.error(f"验证并启用2FA失败: {str(e)}")
            return False
    
    def verify_2fa_login(self, user_id: int, code: str) -> bool:
        """登录时验证2FA"""
        try:
            # 使用事务保护，避免并发冲突
            with self.db.get_connection() as conn:
                conn.execute("BEGIN TRANSACTION")
                try:
                    # 获取用户2FA信息（加锁查询）
                    result = conn.execute("""
                        SELECT totp_secret, is_2fa_enabled, is_2fa_verified, backup_codes
                        FROM auth_users
                        WHERE id = ?
                    """, [user_id]).fetchone()

                    if not result or not result[1] or not result[2]:  # is_2fa_enabled, is_2fa_verified
                        conn.execute("ROLLBACK")
                        return False

                    totp_secret = result[0]
                    backup_codes_json = result[3]

                    # 先尝试TOTP验证
                    if self.verify_totp_code(totp_secret, code):
                        # 更新最后验证时间
                        conn.execute("UPDATE auth_users SET last_2fa_verify = ? WHERE id = ?",
                                   [datetime.now(), user_id])
                        conn.execute("COMMIT")
                        return True

                    # 如果TOTP失败，尝试备用码
                    if backup_codes_json:
                        try:
                            backup_codes = __import__('json').loads(backup_codes_json)
                            if code in backup_codes:
                                # 使用后移除该码
                                backup_codes.remove(code)

                                # 更新数据库
                                conn.execute("""
                                    UPDATE auth_users
                                    SET backup_codes = ?, last_2fa_verify = ?
                                    WHERE id = ?
                                """, [__import__('json').dumps(backup_codes), datetime.now(), user_id])

                                conn.execute("COMMIT")
                                logger.info(f"用户 {user_id} 使用备用恢复码")
                                return True
                        except Exception as backup_error:
                            logger.error(f"备用码验证失败: {backup_error}")

                    conn.execute("ROLLBACK")
                    return False

                except Exception as e:
                    conn.execute("ROLLBACK")
                    raise e

        except Exception as e:
            logger.error(f"登录2FA验证失败: {str(e)}")
            return False
    
    def get_user_2fa_status(self, user_id: int) -> Dict[str, Any]:
        """获取用户2FA状态"""
        try:
            query = """
            SELECT is_2fa_enabled, is_2fa_verified, last_2fa_verify, backup_codes
            FROM auth_users
            WHERE id = ?
            """
            result = self.db.fetch_one(query, [user_id])

            if result:
                # 计算备用码数量
                backup_codes_count = 0
                if result['backup_codes']:
                    try:
                        backup_codes = json.loads(result['backup_codes'])
                        backup_codes_count = len(backup_codes) if isinstance(backup_codes, list) else 0
                    except:
                        backup_codes_count = 0

                return {
                    'is_enabled': result['is_2fa_enabled'],
                    'is_verified': result['is_2fa_verified'],
                    'last_verify': result['last_2fa_verify'],
                    'backup_codes_count': backup_codes_count
                }

            return {
                'is_enabled': False,
                'is_verified': False,
                'last_verify': None,
                'backup_codes_count': 0
            }
        except Exception as e:
            logger.error(f"获取2FA状态失败: {str(e)}")
            return {
                'is_enabled': False,
                'is_verified': False,
                'last_verify': None,
                'backup_codes_count': 0
            }
    
    def disable_2fa_for_user(self, user_id: int) -> bool:
        """禁用用户2FA"""
        try:
            query = """
            UPDATE auth_users 
            SET totp_secret = NULL, is_2fa_enabled = FALSE, 
                is_2fa_verified = FALSE, backup_codes = NULL
            WHERE id = ?
            """
            self.db.execute_query(query, [user_id])
            
            logger.info(f"禁用用户 {user_id} 的2FA")
            return True
        except Exception as e:
            logger.error(f"禁用2FA失败: {str(e)}")
            return False


# 创建全局实例
totp_service = TOTPService()

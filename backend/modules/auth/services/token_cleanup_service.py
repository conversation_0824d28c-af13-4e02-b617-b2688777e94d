"""
Token清理服务 - 定期清理过期的临时token
"""
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any
from modules.auth.services.temp_token_service import temp_token_service
from core.cache.redis_manager import redis_manager

logger = logging.getLogger(__name__)

class TokenCleanupService:
    """Token清理服务"""
    
    def __init__(self):
        self.cleanup_interval = 300  # 5分钟清理一次
        self.is_running = False
        self.cleanup_thread = None
        self.stats = {
            'last_cleanup': None,
            'total_cleanups': 0,
            'total_tokens_cleaned': 0
        }
    
    def start_cleanup_service(self):
        """启动清理服务"""
        if self.is_running:
            logger.warning("Token清理服务已在运行")
            return
        
        self.is_running = True
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        logger.info("Token清理服务已启动")
    
    def stop_cleanup_service(self):
        """停止清理服务"""
        if not self.is_running:
            logger.warning("Token清理服务未在运行")
            return
        
        self.is_running = False
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)
        logger.info("Token清理服务已停止")
    
    def _cleanup_loop(self):
        """清理循环"""
        logger.info(f"Token清理循环开始，间隔: {self.cleanup_interval}秒")
        
        while self.is_running:
            try:
                self._perform_cleanup()
                time.sleep(self.cleanup_interval)
            except Exception as e:
                logger.error(f"Token清理循环异常: {e}")
                time.sleep(60)  # 出错时等待1分钟再重试
    
    def _perform_cleanup(self):
        """执行清理操作"""
        try:
            start_time = datetime.now()
            
            # 检查Redis连接
            if not redis_manager.is_connected():
                logger.warning("Redis未连接，跳过token清理")
                return
            
            # 获取所有临时token键
            pattern = f"{temp_token_service.token_prefix}*"
            keys = redis_manager.redis_client.keys(pattern)
            
            if not keys:
                logger.debug("没有找到临时token，无需清理")
                return
            
            # 统计过期的token
            expired_count = 0
            active_count = 0
            
            for key in keys:
                ttl = redis_manager.get_ttl(key)
                if ttl == -2:  # 键已过期
                    expired_count += 1
                elif ttl > 0:  # 键仍然有效
                    active_count += 1
            
            # 更新统计信息
            self.stats['last_cleanup'] = start_time
            self.stats['total_cleanups'] += 1
            self.stats['total_tokens_cleaned'] += expired_count
            
            cleanup_time = (datetime.now() - start_time).total_seconds()
            
            if expired_count > 0:
                logger.info(f"Token清理完成: 清理了 {expired_count} 个过期token, "
                          f"剩余 {active_count} 个活跃token, 耗时 {cleanup_time:.2f}秒")
            else:
                logger.debug(f"Token清理完成: 无过期token, "
                           f"当前 {active_count} 个活跃token, 耗时 {cleanup_time:.2f}秒")
                
        except Exception as e:
            logger.error(f"执行token清理失败: {e}")
    
    def manual_cleanup(self) -> Dict[str, Any]:
        """手动执行清理"""
        try:
            start_time = datetime.now()
            
            if not redis_manager.is_connected():
                return {
                    'success': False,
                    'error': 'Redis未连接'
                }
            
            # 获取清理前的统计
            pattern = f"{temp_token_service.token_prefix}*"
            keys_before = redis_manager.redis_client.keys(pattern)
            
            # 执行清理
            self._perform_cleanup()
            
            # 获取清理后的统计
            keys_after = redis_manager.redis_client.keys(pattern)
            
            cleanup_time = (datetime.now() - start_time).total_seconds()
            cleaned_count = len(keys_before) - len(keys_after)
            
            return {
                'success': True,
                'tokens_before': len(keys_before),
                'tokens_after': len(keys_after),
                'tokens_cleaned': cleaned_count,
                'cleanup_time_seconds': cleanup_time,
                'timestamp': start_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"手动清理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_cleanup_stats(self) -> Dict[str, Any]:
        """获取清理统计信息"""
        try:
            # 获取当前token统计
            if redis_manager.is_connected():
                pattern = f"{temp_token_service.token_prefix}*"
                current_keys = redis_manager.redis_client.keys(pattern)
                current_token_count = len(current_keys)
                
                # 统计即将过期的token（剩余时间少于1分钟）
                expiring_soon = 0
                for key in current_keys:
                    ttl = redis_manager.get_ttl(key)
                    if 0 < ttl < 60:  # 剩余时间少于1分钟
                        expiring_soon += 1
            else:
                current_token_count = 0
                expiring_soon = 0
            
            return {
                'service_running': self.is_running,
                'cleanup_interval_seconds': self.cleanup_interval,
                'current_token_count': current_token_count,
                'tokens_expiring_soon': expiring_soon,
                'last_cleanup': self.stats['last_cleanup'].isoformat() if self.stats['last_cleanup'] else None,
                'total_cleanups': self.stats['total_cleanups'],
                'total_tokens_cleaned': self.stats['total_tokens_cleaned'],
                'redis_connected': redis_manager.is_connected()
            }
            
        except Exception as e:
            logger.error(f"获取清理统计失败: {e}")
            return {
                'service_running': self.is_running,
                'error': str(e)
            }
    
    def set_cleanup_interval(self, interval_seconds: int):
        """设置清理间隔"""
        if interval_seconds < 60:
            logger.warning("清理间隔不能少于60秒")
            interval_seconds = 60
        elif interval_seconds > 3600:
            logger.warning("清理间隔不能超过1小时")
            interval_seconds = 3600
        
        self.cleanup_interval = interval_seconds
        logger.info(f"Token清理间隔已设置为: {interval_seconds}秒")

# 全局token清理服务实例
token_cleanup_service = TokenCleanupService()

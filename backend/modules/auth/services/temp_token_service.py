"""
临时Token服务 - 用于2FA验证过程中的临时身份验证
"""
import secrets
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from core.cache.redis_manager import redis_manager
from modules.auth.utils.error_handler import auth_error_handler
from modules.auth.config.logging_config import log_token_event, log_redis_event

logger = logging.getLogger(__name__)

class TempTokenService:
    """临时Token服务"""
    
    def __init__(self):
        # 从Redis配置获取token设置
        from config.redis_config import redis_config
        token_config = redis_config['TEMP_TOKEN']

        self.token_prefix = token_config['prefix']
        self.default_expiry = token_config['default_expiry']
        self.max_expiry = token_config['max_expiry']
        self.min_expiry = token_config['min_expiry']
    
    def generate_temp_token(self, user_data: Dict[str, Any], expiry_seconds: Optional[int] = None) -> Optional[str]:
        """
        生成临时token并存储用户数据到Redis
        
        Args:
            user_data: 用户数据字典，包含user_id, username, email等
            expiry_seconds: 过期时间（秒），默认5分钟
            
        Returns:
            临时token字符串，失败返回None
        """
        try:
            # 生成安全的随机token
            token = secrets.token_urlsafe(32)

            # 使用过期时间，并验证合理性
            expiry = expiry_seconds or self.default_expiry

            # 验证过期时间范围
            if expiry < self.min_expiry:
                logger.warning(f"过期时间太短，调整为最小值: {self.min_expiry}秒")
                expiry = self.min_expiry
            elif expiry > self.max_expiry:
                logger.warning(f"过期时间太长，调整为最大值: {self.max_expiry}秒")
                expiry = self.max_expiry
            
            # 准备存储的数据
            token_data = {
                'user_id': user_data.get('user_id'),
                'username': user_data.get('username'),
                'email': user_data.get('email'),
                'role': user_data.get('role'),
                'ip_address': user_data.get('ip_address'),
                'user_agent': user_data.get('user_agent'),
                'created_at': datetime.now().isoformat(),
                'expires_at': (datetime.now() + timedelta(seconds=expiry)).isoformat()
            }
            
            # 存储到Redis
            redis_key = f"{self.token_prefix}{token}"
            success = redis_manager.set_with_expiry(redis_key, token_data, expiry)
            
            if success:
                log_token_event("token_generated", token, user_data.get('user_id'), expiry, True)
                return token
            else:
                log_redis_event("token_storage_failed", "set_with_expiry", False, "Redis存储失败")
                return None

        except Exception as e:
            error_info = auth_error_handler.handle_token_error(e, "generate_temp_token")
            log_token_event("token_generation_failed", None, user_data.get('user_id'), expiry, False)
            return None
    
    def verify_temp_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证临时token并获取用户数据
        
        Args:
            token: 临时token字符串
            
        Returns:
            用户数据字典，失败返回None
        """
        if not token:
            log_token_event("token_validation_failed", None, None, None, False)
            return None
        
        try:
            redis_key = f"{self.token_prefix}{token}"
            
            # 从Redis获取数据
            token_data = redis_manager.get(redis_key)
            
            if not token_data:
                logger.warning(f"临时token无效或已过期: {token[:8]}...")
                return None
            
            # 检查数据完整性
            required_fields = ['user_id', 'username', 'created_at', 'expires_at']
            for field in required_fields:
                if field not in token_data:
                    logger.error(f"临时token数据缺少必要字段: {field}")
                    return None
            
            # 检查是否过期（双重检查，Redis TTL + 数据中的时间戳）
            try:
                expires_at = datetime.fromisoformat(token_data['expires_at'])
                if datetime.now() > expires_at:
                    logger.warning(f"临时token已过期: {token[:8]}...")
                    self.invalidate_temp_token(token)  # 清理过期token
                    return None
            except (ValueError, KeyError) as e:
                logger.error(f"临时token时间戳格式错误: {e}")
                return None
            
            logger.info(f"临时token验证成功: user_id={token_data.get('user_id')}, token={token[:8]}...")
            return token_data
            
        except Exception as e:
            logger.error(f"验证临时token失败: {e}")
            return None
    
    def invalidate_temp_token(self, token: str) -> bool:
        """
        使临时token失效（删除）
        
        Args:
            token: 临时token字符串
            
        Returns:
            成功返回True，失败返回False
        """
        if not token:
            return False
        
        try:
            redis_key = f"{self.token_prefix}{token}"
            success = redis_manager.delete(redis_key)
            
            if success:
                logger.info(f"临时token已失效: {token[:8]}...")
            else:
                logger.warning(f"临时token失效失败: {token[:8]}...")
            
            return success
            
        except Exception as e:
            logger.error(f"使临时token失效失败: {e}")
            return False
    
    def get_token_info(self, token: str) -> Optional[Dict[str, Any]]:
        """
        获取token信息（不验证有效性，仅获取信息）
        
        Args:
            token: 临时token字符串
            
        Returns:
            token信息字典，包含剩余时间等
        """
        if not token:
            return None
        
        try:
            redis_key = f"{self.token_prefix}{token}"
            
            # 检查是否存在
            if not redis_manager.exists(redis_key):
                return None
            
            # 获取剩余时间
            ttl = redis_manager.get_ttl(redis_key)
            
            # 获取数据
            token_data = redis_manager.get(redis_key)
            
            if token_data:
                return {
                    'exists': True,
                    'ttl_seconds': ttl,
                    'user_id': token_data.get('user_id'),
                    'username': token_data.get('username'),
                    'created_at': token_data.get('created_at'),
                    'expires_at': token_data.get('expires_at')
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取token信息失败: {e}")
            return None
    
    def cleanup_expired_tokens(self) -> int:
        """
        清理过期的临时token
        
        Returns:
            清理的token数量
        """
        try:
            # Redis会自动清理过期键，这里主要用于统计和日志
            redis_manager.cleanup_expired_keys(f"{self.token_prefix}*")
            logger.info("临时token清理检查完成")
            return 0
            
        except Exception as e:
            logger.error(f"清理过期临时token失败: {e}")
            return 0
    
    def get_service_stats(self) -> Dict[str, Any]:
        """
        获取服务统计信息
        
        Returns:
            统计信息字典
        """
        try:
            redis_info = redis_manager.get_info()
            
            return {
                'redis_connected': redis_manager.is_connected(),
                'redis_info': redis_info,
                'token_prefix': self.token_prefix,
                'default_expiry_seconds': self.default_expiry,
                'service_status': 'active' if redis_manager.is_connected() else 'inactive'
            }
            
        except Exception as e:
            logger.error(f"获取服务统计信息失败: {e}")
            return {
                'redis_connected': False,
                'service_status': 'error',
                'error': str(e)
            }

# 全局临时token服务实例
temp_token_service = TempTokenService()

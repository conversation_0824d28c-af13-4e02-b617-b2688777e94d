"""
权限管理服务
提供细粒度的权限控制功能
"""

import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from database.duckdb_manager import db_manager

logger = logging.getLogger(__name__)

class PermissionService:
    """权限管理服务类"""
    
    def __init__(self):
        self.db = db_manager
    
    def check_user_permission(self, user_id: int, resource_name: str, action_name: str) -> bool:
        """
        检查用户是否有特定资源的操作权限
        
        Args:
            user_id: 用户ID
            resource_name: 资源名称
            action_name: 操作名称
            
        Returns:
            bool: 是否有权限
        """
        try:
            query = """
            SELECT COUNT(*) as has_permission
            FROM v_user_permissions
            WHERE user_id = ? 
            AND resource_name = ? 
            AND action_name = ?
            AND is_granted = true
            """
            
            result = self.db.fetch_one(query, [user_id, resource_name, action_name])
            return result['has_permission'] > 0 if result else False
            
        except Exception as e:
            logger.error(f"检查用户权限失败: {str(e)}")
            return False
    
    def get_user_permissions(self, user_id: int) -> List[Dict]:
        """
        获取用户的所有权限
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[Dict]: 权限列表
        """
        try:
            query = """
            SELECT 
                resource_name,
                resource_type,
                permission_name,
                action_name,
                action_name_display,
                is_granted,
                permission_source
            FROM v_user_permissions
            WHERE user_id = ?
            ORDER BY resource_name, action_name
            """
            
            return self.db.fetch_all(query, [user_id])
            
        except Exception as e:
            logger.error(f"获取用户权限失败: {str(e)}")
            return []
    
    def get_role_permissions(self, role_id: int) -> List[Dict]:
        """
        获取角色的所有权限
        
        Args:
            role_id: 角色ID
            
        Returns:
            List[Dict]: 权限列表
        """
        try:
            query = """
            SELECT 
                p.resource_name,
                p.resource_type,
                p.display_name as permission_name,
                a.action_name,
                a.display_name as action_name_display,
                rp.is_granted,
                rp.is_inherited
            FROM auth_role_permissions rp
            JOIN auth_permissions p ON rp.permission_id = p.id
            JOIN auth_actions a ON rp.action_id = a.id
            WHERE rp.role_id = ?
            AND p.is_active = true
            AND a.is_active = true
            ORDER BY p.resource_name, a.action_name
            """
            
            return self.db.fetch_all(query, [role_id])
            
        except Exception as e:
            logger.error(f"获取角色权限失败: {str(e)}")
            return []
    
    def assign_role_permission(self, role_id: int, permission_id: int, action_id: int, 
                             granted_by: int, is_granted: bool = True) -> bool:
        """
        为角色分配权限
        
        Args:
            role_id: 角色ID
            permission_id: 权限ID
            action_id: 操作ID
            granted_by: 授权人ID
            is_granted: 是否授予权限
            
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            INSERT OR REPLACE INTO auth_role_permissions 
            (role_id, permission_id, action_id, is_granted, granted_by, granted_at)
            VALUES (?, ?, ?, ?, ?, ?)
            """
            
            self.db.execute_query(query, [
                role_id, permission_id, action_id, is_granted, 
                granted_by, datetime.now()
            ])
            
            logger.info(f"角色权限分配成功: role_id={role_id}, permission_id={permission_id}, action_id={action_id}")
            return True
            
        except Exception as e:
            logger.error(f"分配角色权限失败: {str(e)}")
            return False
    
    def assign_user_permission(self, user_id: int, permission_id: int, action_id: int,
                             granted_by: int, is_granted: bool = True, 
                             expires_at: Optional[datetime] = None, reason: str = "") -> bool:
        """
        为用户分配特殊权限
        
        Args:
            user_id: 用户ID
            permission_id: 权限ID
            action_id: 操作ID
            granted_by: 授权人ID
            is_granted: 是否授予权限
            expires_at: 过期时间
            reason: 授权原因
            
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            INSERT OR REPLACE INTO auth_user_permissions 
            (user_id, permission_id, action_id, is_granted, granted_by, granted_at, expires_at, reason)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            self.db.execute_query(query, [
                user_id, permission_id, action_id, is_granted,
                granted_by, datetime.now(), expires_at, reason
            ])
            
            logger.info(f"用户特殊权限分配成功: user_id={user_id}, permission_id={permission_id}")
            return True
            
        except Exception as e:
            logger.error(f"分配用户特殊权限失败: {str(e)}")
            return False
    
    def assign_user_role(self, user_id: int, role_id: int, assigned_by: int,
                        expires_at: Optional[datetime] = None) -> bool:
        """
        为用户分配角色
        
        Args:
            user_id: 用户ID
            role_id: 角色ID
            assigned_by: 分配人ID
            expires_at: 过期时间
            
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            INSERT OR REPLACE INTO auth_user_roles 
            (user_id, role_id, assigned_by, assigned_at, expires_at, is_active)
            VALUES (?, ?, ?, ?, ?, true)
            """
            
            self.db.execute_query(query, [
                user_id, role_id, assigned_by, datetime.now(), expires_at
            ])
            
            logger.info(f"用户角色分配成功: user_id={user_id}, role_id={role_id}")
            return True
            
        except Exception as e:
            logger.error(f"分配用户角色失败: {str(e)}")
            return False
    
    def remove_user_role(self, user_id: int, role_id: int) -> bool:
        """
        移除用户角色
        
        Args:
            user_id: 用户ID
            role_id: 角色ID
            
        Returns:
            bool: 是否成功
        """
        try:
            query = "UPDATE auth_user_roles SET is_active = false WHERE user_id = ? AND role_id = ?"
            self.db.execute_query(query, [user_id, role_id])
            
            logger.info(f"用户角色移除成功: user_id={user_id}, role_id={role_id}")
            return True
            
        except Exception as e:
            logger.error(f"移除用户角色失败: {str(e)}")
            return False
    
    def get_all_permissions(self) -> List[Dict]:
        """
        获取所有权限资源
        
        Returns:
            List[Dict]: 权限列表
        """
        try:
            query = """
            SELECT 
                id,
                resource_name,
                resource_type,
                display_name,
                description,
                parent_id,
                is_active
            FROM auth_permissions
            WHERE is_active = true
            ORDER BY resource_type, resource_name
            """
            
            return self.db.fetch_all(query)
            
        except Exception as e:
            logger.error(f"获取权限列表失败: {str(e)}")
            return []
    
    def get_all_actions(self) -> List[Dict]:
        """
        获取所有操作类型
        
        Returns:
            List[Dict]: 操作列表
        """
        try:
            query = """
            SELECT 
                id,
                action_name,
                display_name,
                description,
                is_active
            FROM auth_actions
            WHERE is_active = true
            ORDER BY action_name
            """
            
            return self.db.fetch_all(query)
            
        except Exception as e:
            logger.error(f"获取操作列表失败: {str(e)}")
            return []
    
    def get_all_roles(self) -> List[Dict]:
        """
        获取所有角色
        
        Returns:
            List[Dict]: 角色列表
        """
        try:
            query = """
            SELECT 
                id,
                role_name,
                display_name,
                description,
                is_system_role,
                is_active
            FROM auth_roles
            WHERE is_active = true
            ORDER BY is_system_role DESC, role_name
            """
            
            return self.db.fetch_all(query)
            
        except Exception as e:
            logger.error(f"获取角色列表失败: {str(e)}")
            return []
    
    def get_permission_groups(self) -> List[Dict]:
        """
        获取权限组
        
        Returns:
            List[Dict]: 权限组列表
        """
        try:
            query = """
            SELECT 
                pg.id,
                pg.group_name,
                pg.display_name,
                pg.description,
                pg.sort_order,
                COUNT(pgi.permission_id) as permission_count
            FROM auth_permission_groups pg
            LEFT JOIN auth_permission_group_items pgi ON pg.id = pgi.group_id
            WHERE pg.is_active = true
            GROUP BY pg.id, pg.group_name, pg.display_name, pg.description, pg.sort_order
            ORDER BY pg.sort_order, pg.group_name
            """
            
            return self.db.fetch_all(query)
            
        except Exception as e:
            logger.error(f"获取权限组失败: {str(e)}")
            return []
    
    def get_group_permissions(self, group_id: int) -> List[Dict]:
        """
        获取权限组中的权限
        
        Args:
            group_id: 权限组ID
            
        Returns:
            List[Dict]: 权限列表
        """
        try:
            query = """
            SELECT 
                p.id,
                p.resource_name,
                p.resource_type,
                p.display_name,
                p.description,
                pgi.sort_order
            FROM auth_permission_group_items pgi
            JOIN auth_permissions p ON pgi.permission_id = p.id
            WHERE pgi.group_id = ?
            AND p.is_active = true
            ORDER BY pgi.sort_order, p.display_name
            """
            
            return self.db.fetch_all(query, [group_id])
            
        except Exception as e:
            logger.error(f"获取权限组权限失败: {str(e)}")
            return []
    
    def get_permission_matrix(self) -> Dict:
        """
        获取完整的权限矩阵
        
        Returns:
            Dict: 权限矩阵数据
        """
        try:
            # 获取所有角色
            roles = self.get_all_roles()
            
            # 获取所有权限组
            groups = self.get_permission_groups()
            
            # 获取所有操作
            actions = self.get_all_actions()
            
            # 构建权限矩阵
            matrix = {
                'roles': roles,
                'groups': [],
                'actions': actions
            }
            
            for group in groups:
                group_data = {
                    'id': group['id'],
                    'name': group['group_name'],
                    'display_name': group['display_name'],
                    'description': group['description'],
                    'permissions': self.get_group_permissions(group['id']),
                    'role_permissions': {}
                }
                
                # 获取每个角色对该组权限的分配情况
                for role in roles:
                    role_permissions = self.get_role_permissions(role['id'])
                    group_data['role_permissions'][role['id']] = {}
                    
                    for permission in group_data['permissions']:
                        group_data['role_permissions'][role['id']][permission['id']] = {}
                        
                        for action in actions:
                            # 检查该角色是否有该权限的该操作
                            has_permission = any(
                                rp['resource_name'] == permission['resource_name'] and 
                                rp['action_name'] == action['action_name'] and 
                                rp['is_granted']
                                for rp in role_permissions
                            )
                            
                            group_data['role_permissions'][role['id']][permission['id']][action['id']] = has_permission
                
                matrix['groups'].append(group_data)
            
            return matrix
            
        except Exception as e:
            logger.error(f"获取权限矩阵失败: {str(e)}")
            return {'roles': [], 'groups': [], 'actions': []}
    
    def initialize_permissions(self):
        """
        初始化权限系统
        执行权限表的创建和基础数据插入
        """
        try:
            # 读取并执行权限表结构SQL
            import os
            sql_file = os.path.join(os.path.dirname(__file__), '../../database/permissions_schema.sql')
            
            if os.path.exists(sql_file):
                with open(sql_file, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
                
                # 分割SQL语句并执行
                statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
                
                for statement in statements:
                    if statement and not statement.startswith('--'):
                        try:
                            self.db.execute_query(statement)
                        except Exception as e:
                            # 忽略已存在的表等错误
                            if 'already exists' not in str(e).lower():
                                logger.warning(f"执行SQL语句时出现警告: {str(e)}")
                
                logger.info("权限系统初始化完成")
                return True
            else:
                logger.error("权限表结构文件不存在")
                return False
                
        except Exception as e:
            logger.error(f"初始化权限系统失败: {str(e)}")
            return False

# 创建全局权限服务实例
permission_service = PermissionService()

"""
认证模块错误处理工具
"""
import logging
import traceback
from typing import Dict, Any, Optional
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class AuthErrorType(Enum):
    """认证错误类型"""
    REDIS_CONNECTION_ERROR = "redis_connection_error"
    TOKEN_GENERATION_ERROR = "token_generation_error"
    TOKEN_VALIDATION_ERROR = "token_validation_error"
    TOKEN_EXPIRED_ERROR = "token_expired_error"
    TOKEN_INVALID_ERROR = "token_invalid_error"
    USER_NOT_FOUND_ERROR = "user_not_found_error"
    TOTP_VERIFICATION_ERROR = "totp_verification_error"
    SESSION_ERROR = "session_error"
    PERMISSION_ERROR = "permission_error"
    SYSTEM_ERROR = "system_error"

class AuthErrorHandler:
    """认证错误处理器"""
    
    @staticmethod
    def handle_redis_error(error: Exception, operation: str = "unknown") -> Dict[str, Any]:
        """处理Redis相关错误"""
        error_info = {
            'error_type': AuthErrorType.REDIS_CONNECTION_ERROR.value,
            'operation': operation,
            'error_message': str(error),
            'timestamp': datetime.now().isoformat(),
            'user_message': '系统繁忙，请稍后重试'
        }
        
        logger.error(f"Redis错误 - 操作: {operation}, 错误: {error}")
        logger.debug(f"Redis错误详情: {traceback.format_exc()}")
        
        return error_info
    
    @staticmethod
    def handle_token_error(error: Exception, token_operation: str, token_hint: str = None) -> Dict[str, Any]:
        """处理Token相关错误"""
        error_info = {
            'error_type': AuthErrorType.TOKEN_VALIDATION_ERROR.value,
            'operation': token_operation,
            'error_message': str(error),
            'token_hint': token_hint[:8] + "..." if token_hint else None,
            'timestamp': datetime.now().isoformat(),
            'user_message': '认证令牌异常，请重新登录'
        }
        
        logger.error(f"Token错误 - 操作: {token_operation}, 错误: {error}")
        if token_hint:
            logger.debug(f"Token提示: {token_hint[:8]}...")
        
        return error_info
    
    @staticmethod
    def handle_token_expired(token_hint: str = None) -> Dict[str, Any]:
        """处理Token过期错误"""
        error_info = {
            'error_type': AuthErrorType.TOKEN_EXPIRED_ERROR.value,
            'token_hint': token_hint[:8] + "..." if token_hint else None,
            'timestamp': datetime.now().isoformat(),
            'user_message': '认证令牌已过期，请重新登录'
        }
        
        logger.warning(f"Token已过期: {token_hint[:8] + '...' if token_hint else 'unknown'}")
        
        return error_info
    
    @staticmethod
    def handle_token_invalid(token_hint: str = None, reason: str = "unknown") -> Dict[str, Any]:
        """处理Token无效错误"""
        error_info = {
            'error_type': AuthErrorType.TOKEN_INVALID_ERROR.value,
            'token_hint': token_hint[:8] + "..." if token_hint else None,
            'reason': reason,
            'timestamp': datetime.now().isoformat(),
            'user_message': '认证令牌无效，请重新登录'
        }
        
        logger.warning(f"Token无效: {token_hint[:8] + '...' if token_hint else 'unknown'}, 原因: {reason}")
        
        return error_info
    
    @staticmethod
    def handle_totp_error(error: Exception, user_id: int = None) -> Dict[str, Any]:
        """处理TOTP验证错误"""
        error_info = {
            'error_type': AuthErrorType.TOTP_VERIFICATION_ERROR.value,
            'user_id': user_id,
            'error_message': str(error),
            'timestamp': datetime.now().isoformat(),
            'user_message': '验证码错误或已过期'
        }
        
        logger.error(f"TOTP验证错误 - 用户ID: {user_id}, 错误: {error}")
        
        return error_info
    
    @staticmethod
    def handle_user_not_found(user_identifier: str) -> Dict[str, Any]:
        """处理用户不存在错误"""
        error_info = {
            'error_type': AuthErrorType.USER_NOT_FOUND_ERROR.value,
            'user_identifier': user_identifier,
            'timestamp': datetime.now().isoformat(),
            'user_message': '用户不存在'
        }
        
        logger.warning(f"用户不存在: {user_identifier}")
        
        return error_info
    
    @staticmethod
    def handle_session_error(error: Exception, session_id: str = None) -> Dict[str, Any]:
        """处理会话错误"""
        error_info = {
            'error_type': AuthErrorType.SESSION_ERROR.value,
            'session_id': session_id[:8] + "..." if session_id else None,
            'error_message': str(error),
            'timestamp': datetime.now().isoformat(),
            'user_message': '会话异常，请重新登录'
        }
        
        logger.error(f"会话错误 - 会话ID: {session_id[:8] + '...' if session_id else 'unknown'}, 错误: {error}")
        
        return error_info
    
    @staticmethod
    def handle_permission_error(user_id: int, required_permission: str, current_role: str = None) -> Dict[str, Any]:
        """处理权限错误"""
        error_info = {
            'error_type': AuthErrorType.PERMISSION_ERROR.value,
            'user_id': user_id,
            'required_permission': required_permission,
            'current_role': current_role,
            'timestamp': datetime.now().isoformat(),
            'user_message': '权限不足'
        }
        
        logger.warning(f"权限不足 - 用户ID: {user_id}, 当前角色: {current_role}, 需要权限: {required_permission}")
        
        return error_info
    
    @staticmethod
    def handle_system_error(error: Exception, operation: str = "unknown") -> Dict[str, Any]:
        """处理系统错误"""
        error_info = {
            'error_type': AuthErrorType.SYSTEM_ERROR.value,
            'operation': operation,
            'error_message': str(error),
            'timestamp': datetime.now().isoformat(),
            'user_message': '系统错误，请联系管理员'
        }
        
        logger.error(f"系统错误 - 操作: {operation}, 错误: {error}")
        logger.debug(f"系统错误详情: {traceback.format_exc()}")
        
        return error_info
    
    @staticmethod
    def log_security_event(event_type: str, user_id: int = None, ip_address: str = None, 
                          details: Dict[str, Any] = None):
        """记录安全事件"""
        security_log = {
            'event_type': event_type,
            'user_id': user_id,
            'ip_address': ip_address,
            'details': details or {},
            'timestamp': datetime.now().isoformat()
        }
        
        logger.warning(f"安全事件: {event_type}, 用户ID: {user_id}, IP: {ip_address}, 详情: {details}")
        
        # 这里可以添加发送到安全监控系统的逻辑
        return security_log
    
    @staticmethod
    def create_api_error_response(error_info: Dict[str, Any], http_status: int = 500) -> tuple:
        """创建API错误响应"""
        response_data = {
            'success': False,
            'error': error_info.get('user_message', '系统错误'),
            'error_code': error_info.get('error_type'),
            'timestamp': error_info.get('timestamp')
        }
        
        # 在开发环境下可以包含更多调试信息
        import os
        if os.getenv('FLASK_ENV') == 'development':
            response_data['debug_info'] = {
                'operation': error_info.get('operation'),
                'error_message': error_info.get('error_message')
            }
        
        return response_data, http_status

# 全局错误处理器实例
auth_error_handler = AuthErrorHandler()

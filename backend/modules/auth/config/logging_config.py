"""
认证模块日志配置
"""
import logging
import logging.handlers
import os
from datetime import datetime

def setup_auth_logging():
    """设置认证模块的日志配置"""
    
    # 创建日志目录
    log_dir = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 认证模块日志文件
    auth_log_file = os.path.join(log_dir, 'auth.log')
    security_log_file = os.path.join(log_dir, 'security.log')
    
    # 创建认证模块logger
    auth_logger = logging.getLogger('modules.auth')
    auth_logger.setLevel(logging.INFO)
    
    # 创建安全事件logger
    security_logger = logging.getLogger('security')
    security_logger.setLevel(logging.WARNING)
    
    # 清除现有的handlers
    auth_logger.handlers.clear()
    security_logger.handlers.clear()
    
    # 创建格式化器
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s'
    )
    
    security_formatter = logging.Formatter(
        '%(asctime)s - SECURITY - %(levelname)s - %(message)s'
    )
    
    # 认证模块文件handler（带轮转）
    auth_file_handler = logging.handlers.RotatingFileHandler(
        auth_log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    auth_file_handler.setLevel(logging.INFO)
    auth_file_handler.setFormatter(detailed_formatter)
    
    # 安全事件文件handler（带轮转）
    security_file_handler = logging.handlers.RotatingFileHandler(
        security_log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=10,  # 保留更多安全日志
        encoding='utf-8'
    )
    security_file_handler.setLevel(logging.WARNING)
    security_file_handler.setFormatter(security_formatter)
    
    # 控制台handler（仅错误级别）
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.ERROR)
    console_handler.setFormatter(detailed_formatter)
    
    # 添加handlers
    auth_logger.addHandler(auth_file_handler)
    auth_logger.addHandler(console_handler)
    
    security_logger.addHandler(security_file_handler)
    security_logger.addHandler(console_handler)
    
    # 防止日志传播到根logger
    auth_logger.propagate = False
    security_logger.propagate = False
    
    logging.info("认证模块日志配置完成")
    
    return auth_logger, security_logger

def log_auth_event(event_type: str, user_id: int = None, username: str = None, 
                   ip_address: str = None, success: bool = True, details: str = None):
    """记录认证事件"""
    logger = logging.getLogger('modules.auth')
    
    log_message = f"认证事件: {event_type}"
    if user_id:
        log_message += f", 用户ID: {user_id}"
    if username:
        log_message += f", 用户名: {username}"
    if ip_address:
        log_message += f", IP: {ip_address}"
    if details:
        log_message += f", 详情: {details}"
    
    log_message += f", 结果: {'成功' if success else '失败'}"
    
    if success:
        logger.info(log_message)
    else:
        logger.warning(log_message)

def log_security_event(event_type: str, severity: str = "medium", user_id: int = None, 
                      ip_address: str = None, details: str = None):
    """记录安全事件"""
    security_logger = logging.getLogger('security')
    
    log_message = f"[{severity.upper()}] {event_type}"
    if user_id:
        log_message += f" - 用户ID: {user_id}"
    if ip_address:
        log_message += f" - IP: {ip_address}"
    if details:
        log_message += f" - 详情: {details}"
    
    if severity.lower() in ['high', 'critical']:
        security_logger.error(log_message)
    else:
        security_logger.warning(log_message)

def log_token_event(event_type: str, token_hint: str = None, user_id: int = None, 
                   expiry_seconds: int = None, success: bool = True):
    """记录Token事件"""
    logger = logging.getLogger('modules.auth')
    
    log_message = f"Token事件: {event_type}"
    if token_hint:
        log_message += f", Token: {token_hint[:8]}..."
    if user_id:
        log_message += f", 用户ID: {user_id}"
    if expiry_seconds:
        log_message += f", 过期时间: {expiry_seconds}秒"
    
    log_message += f", 结果: {'成功' if success else '失败'}"
    
    if success:
        logger.info(log_message)
    else:
        logger.warning(log_message)

def log_redis_event(event_type: str, operation: str = None, success: bool = True, 
                   error_message: str = None):
    """记录Redis事件"""
    logger = logging.getLogger('modules.auth')
    
    log_message = f"Redis事件: {event_type}"
    if operation:
        log_message += f", 操作: {operation}"
    if error_message and not success:
        log_message += f", 错误: {error_message}"
    
    log_message += f", 结果: {'成功' if success else '失败'}"
    
    if success:
        logger.info(log_message)
    else:
        logger.error(log_message)

# 初始化日志配置
try:
    auth_logger, security_logger = setup_auth_logging()
except Exception as e:
    print(f"设置认证模块日志失败: {e}")
    # 使用默认logger
    auth_logger = logging.getLogger('modules.auth')
    security_logger = logging.getLogger('security')

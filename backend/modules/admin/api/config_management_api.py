"""
配置管理API
提供前端配置管理界面的后端接口
"""

from flask import Blueprint, request, jsonify
import logging
from core.utils.decorators import login_required, admin_required
from ..services.config_management_service import config_manager

logger = logging.getLogger(__name__)

# 创建配置管理蓝图
config_bp = Blueprint('config_management', __name__)

@config_bp.route('/configs', methods=['GET'])
@login_required
@admin_required
def list_configs():
    """获取所有配置文件列表"""
    try:
        configs = config_manager.list_all_configs()
        return jsonify({
            'status': 'success',
            'data': configs
        })
    except Exception as e:
        logger.error(f"获取配置列表失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@config_bp.route('/configs/<config_type>', methods=['GET'])
@login_required
@admin_required
def get_config(config_type):
    """获取指定配置文件内容"""
    try:
        config_data = config_manager.load_config(config_type)
        return jsonify({
            'status': 'success',
            'data': config_data
        })
    except FileNotFoundError:
        return jsonify({
            'status': 'error',
            'error': f'配置文件不存在: {config_type}'
        }), 404
    except ValueError as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 400
    except Exception as e:
        logger.error(f"获取配置失败 {config_type}: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@config_bp.route('/configs/<config_type>', methods=['POST'])
@login_required
@admin_required
def update_config(config_type):
    """更新指定配置文件"""
    try:
        # 获取请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify({
                'status': 'error',
                'error': '请求数据为空'
            }), 400
        
        config_data = request_data.get('config_data')
        if not config_data:
            return jsonify({
                'status': 'error',
                'error': '缺少config_data字段'
            }), 400
        
        create_backup = request_data.get('create_backup', True)
        
        # 保存配置
        result = config_manager.save_config(
            config_type=config_type,
            config_data=config_data,
            create_backup=create_backup
        )
        
        return jsonify({
            'status': 'success',
            'data': result,
            'message': f'配置 {config_type} 更新成功'
        })
        
    except ValueError as e:
        return jsonify({
            'status': 'error',
            'error': f'配置验证失败: {str(e)}'
        }), 400
    except Exception as e:
        logger.error(f"更新配置失败 {config_type}: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@config_bp.route('/configs/<config_type>/validate', methods=['POST'])
@login_required
@admin_required
def validate_config(config_type):
    """验证配置文件格式"""
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({
                'status': 'error',
                'error': '请求数据为空'
            }), 400
        
        config_data = request_data.get('config_data')
        if not config_data:
            return jsonify({
                'status': 'error',
                'error': '缺少config_data字段'
            }), 400
        
        # 只验证，不保存
        config_manager._validate_config(config_type, config_data)
        
        return jsonify({
            'status': 'success',
            'message': '配置格式验证通过'
        })
        
    except ValueError as e:
        return jsonify({
            'status': 'error',
            'error': f'配置验证失败: {str(e)}'
        }), 400
    except Exception as e:
        logger.error(f"验证配置失败 {config_type}: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@config_bp.route('/configs/<config_type>/backups', methods=['GET'])
@login_required
@admin_required
def get_config_backups(config_type):
    """获取配置文件备份列表"""
    try:
        backups = config_manager.get_backup_list(config_type)
        return jsonify({
            'status': 'success',
            'data': backups
        })
    except Exception as e:
        logger.error(f"获取备份列表失败 {config_type}: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@config_bp.route('/configs/backups', methods=['GET'])
@login_required
@admin_required
def get_all_backups():
    """获取所有配置文件备份列表"""
    try:
        backups = config_manager.get_backup_list()
        return jsonify({
            'status': 'success',
            'data': backups
        })
    except Exception as e:
        logger.error(f"获取所有备份列表失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@config_bp.route('/configs/<config_type>/reset', methods=['POST'])
@login_required
@admin_required
def reset_config(config_type):
    """重置配置文件到默认值"""
    try:
        # 这里可以实现重置到默认配置的逻辑
        # 暂时返回未实现
        return jsonify({
            'status': 'error',
            'error': '重置功能暂未实现'
        }), 501
        
    except Exception as e:
        logger.error(f"重置配置失败 {config_type}: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

# 健康检查接口
@config_bp.route('/health', methods=['GET'])
def health_check():
    """配置管理服务健康检查"""
    return jsonify({
        'status': 'success',
        'service': 'config_management',
        'version': '1.0.0'
    })

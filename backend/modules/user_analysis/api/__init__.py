"""
用户分析API模块统一入口
统一管理用户分析功能，提供完整的API接口
"""

from flask import Blueprint, request, redirect, url_for, jsonify
from .unified_user_api import unified_user_bp

# 创建统一的用户分析蓝图
unified_user_analysis_bp = Blueprint('user_analysis_unified', __name__, url_prefix='/api/user-analysis')

def register_unified_apis(app):
    """
    注册统一的用户分析API
    提供完整的用户分析功能和兼容性支持
    """
    
    # 1. 注册新的统一API（主要接口）
    app.register_blueprint(unified_user_bp)
    

    
    # 注册统一蓝图
    app.register_blueprint(unified_user_analysis_bp)
    
    # 3. 为了向后兼容，创建旧路径的重定向（添加权限验证）
    from core.utils.decorators import login_required
    from flask import redirect, request

    @app.route('/api/user/search/<member_id>', methods=['GET'])
    @login_required
    def old_user_search_redirect(member_id):
        """旧用户搜索接口重定向"""
        query_string = request.query_string.decode('utf-8')
        redirect_url = f'/api/user-analysis/search/{member_id}'
        if query_string:
            redirect_url += f'?{query_string}'
        return redirect(redirect_url)

    @app.route('/api/user/search-by-digital/<digital_id>', methods=['GET'])
    @login_required
    def old_digital_search_redirect(digital_id):
        """旧数字ID搜索接口重定向"""
        query_string = request.query_string.decode('utf-8')
        redirect_url = f'/api/user-analysis/search-by-digital/{digital_id}'
        if query_string:
            redirect_url += f'?{query_string}'
        return redirect(redirect_url)

    @app.route('/api/user/batch-search', methods=['POST'])
    @login_required
    def old_batch_search_redirect():
        """旧批量搜索接口重定向"""
        return redirect('/api/user-analysis/batch-analysis', code=307)

    @app.route('/api/user/available-tasks', methods=['GET'])
    @login_required
    def old_available_tasks_redirect():
        """旧任务列表接口重定向"""
        return redirect('/api/user-analysis/available-tasks')
    
    # 用户行为分析API的兼容性重定向
    @app.route('/api/user-behavior/<path:endpoint>', methods=['GET', 'POST'])
    def old_behavior_api_redirect(endpoint):
        """旧用户行为分析接口重定向"""
        # 根据endpoint映射到新的接口
        if endpoint.startswith('complete-analysis/'):
            user_id = endpoint.replace('complete-analysis/', '')
            query_string = request.query_string.decode('utf-8')
            redirect_url = f'/api/user-analysis/complete-analysis/{user_id}'
            if query_string:
                redirect_url += f'?{query_string}'
            return redirect(redirect_url)
        elif endpoint == 'batch-analysis':
            return redirect('/api/user-analysis/batch-analysis', code=307)
        elif endpoint.startswith('analysis-report/'):
            user_id = endpoint.replace('analysis-report/', '')
            query_string = request.query_string.decode('utf-8')
            redirect_url = f'/api/user-analysis/analysis-report/{user_id}'
            if query_string:
                redirect_url += f'?{query_string}'
            return redirect(redirect_url)
        elif endpoint.startswith('coin-analysis/'):
            user_id = endpoint.replace('coin-analysis/', '')
            query_string = request.query_string.decode('utf-8')
            redirect_url = f'/api/user-analysis/coin-analysis/{user_id}'
            if query_string:
                redirect_url += f'?{query_string}'
            return redirect(redirect_url)
        elif endpoint == 'analyzer-status':
            return redirect('/api/user-analysis/analyzer-status')
        else:
            return jsonify({
                'status': 'error',
                'message': f'接口 {endpoint} 已废弃，请使用新的统一API'
            }), 404


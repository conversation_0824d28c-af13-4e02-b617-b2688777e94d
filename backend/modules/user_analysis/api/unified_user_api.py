"""
统一的用户分析API接口
合并原有的user_api.py和user_behavior_api.py功能
提供完整的用户分析、行为分析、批量分析等功能
"""

import logging
from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import traceback

# 导入统一的数据访问层和工具函数
from modules.user_analysis.services.unified_data_repository import UnifiedUserDataRepository
from modules.user_analysis.services.unified_utils import (
    unified_error_handler, UnifiedTimeUtils, UnifiedDataFormatter,
    UnifiedResponseBuilder, UnifiedValidators, UnifiedDescriptionGenerator,
    unified_cache_manager, convert_datetime_to_string, UnifiedUtils
)

# 导入用户行为分析器
from modules.user_analysis.services.user_behavior_analyzer import UserBehaviorAnalyzer
from modules.user_analysis.models.user_behavior_models import PositionData, UserBehaviorProfile

# 导入鉴权装饰器
from core.utils.decorators import login_required, admin_required
from core.security.csrf_protection import csrf_protect

# 创建蓝图
unified_user_bp = Blueprint('unified_user_analysis', __name__, url_prefix='/api/user-analysis')

logger = logging.getLogger(__name__)

# 全局实例
data_repository = UnifiedUserDataRepository()
behavior_analyzer = UserBehaviorAnalyzer()
unified_utils = UnifiedUtils()  # 🚀 新增前端数据映射器实例

@unified_user_bp.route('/search/<member_id>', methods=['GET'])
@login_required
@unified_error_handler
def search_user_analysis(member_id):
    """
    基于member_id搜索用户全貌分析
    整合来自合约风险、代理关系、链路风险的所有相关数据
    """
    logger.info(f"开始用户分析查询 - member_id: {member_id}")
    
    # 验证用户ID
    if not UnifiedValidators.validate_user_id(member_id):
        return jsonify(UnifiedResponseBuilder.build_error_response(
            "无效的用户ID格式", {"member_id": member_id}
        )), 400
    
    # 获取查询参数
    contract_task_id = request.args.get('contract_task_id', '')
    agent_task_id = request.args.get('agent_task_id', '')
    include_behavior = request.args.get('include_behavior', 'false').lower() == 'true'
    
    # 如果没有指定任务ID，尝试获取最新的任务
    if not contract_task_id:
        contract_task_id = data_repository.get_latest_task_id('contract_analysis')
    if not agent_task_id:
        agent_task_id = data_repository.get_latest_task_id('agent_analysis')
    
    # 检查缓存
    cache_key = f"user_analysis_{member_id}_{contract_task_id}_{agent_task_id}_{include_behavior}"
    cached_result = unified_cache_manager.get_cached_data(cache_key)
    if cached_result:
        logger.info(f"返回缓存的用户分析结果 - member_id: {member_id}")
        return jsonify(cached_result)
    
    # 执行用户分析
    analysis_result = _execute_unified_user_analysis(
        member_id, contract_task_id, agent_task_id, include_behavior
    )
    
    # 标记搜索方法
    analysis_result['search_method'] = 'member_id'
    analysis_result['analysis_timestamp'] = datetime.now().isoformat()
    
    # 缓存结果
    unified_cache_manager.set_cached_data(cache_key, analysis_result, ttl=300)
    
    return jsonify(analysis_result)

@unified_user_bp.route('/search-by-digital/<digital_id>', methods=['GET'])
@login_required
@unified_error_handler
def search_user_analysis_by_digital(digital_id):
    """
    基于digital_id搜索用户全貌分析
    先通过digital_id找到member_id，然后执行完整的用户分析
    """
    logger.info(f"开始基于digital_id的用户分析查询 - digital_id: {digital_id}")
    
    # 获取查询参数
    contract_task_id = request.args.get('contract_task_id', '')
    agent_task_id = request.args.get('agent_task_id', '')
    include_behavior = request.args.get('include_behavior', 'false').lower() == 'true'
    
    # 如果没有指定任务ID，尝试获取最新的任务
    if not contract_task_id:
        contract_task_id = data_repository.get_latest_task_id('contract_analysis')
    if not agent_task_id:
        agent_task_id = data_repository.get_latest_task_id('agent_analysis')
    
    # 通过digital_id查找对应的member_id
    member_id = data_repository.find_member_id_by_digital_id(digital_id, agent_task_id)

    if not member_id:
        logger.info(f"用户不存在: digital_id {digital_id}")
        return jsonify({
            'status': 'not_found',
            'message': '查无此人',
            'details': {
                'searched_digital_id': digital_id,
                'search_method': 'digital_id_to_member_id',
                'reason': '在系统中未找到该digital_id对应的用户数据',
                'suggestions': [
                    '请检查digital_id是否正确',
                    '确认该用户是否已在系统中进行过交易',
                    '如果是新用户，可能需要等待数据同步'
                ]
            },
            'timestamp': datetime.now().isoformat()
        }), 200  # 改为200状态码，让前端正常处理
    
    logger.info(f"找到对应的member_id: {member_id} (digital_id: {digital_id})")
    
    # 使用找到的member_id执行用户分析
    analysis_result = _execute_unified_user_analysis(
        member_id, contract_task_id, agent_task_id, include_behavior
    )
    
    # 在结果中标记这是通过digital_id搜索的
    analysis_result['search_method'] = 'digital_id'
    analysis_result['searched_digital_id'] = digital_id
    analysis_result['analysis_timestamp'] = datetime.now().isoformat()
    
    return jsonify(analysis_result)

@unified_error_handler
@csrf_protect
@unified_user_bp.route('/batch-analysis', methods=['POST'])
@login_required
def batch_analyze_users():
    """
    批量分析多个用户
    支持风险分析和行为分析
    """
    data = request.get_json()
    if not data:
        return jsonify(UnifiedResponseBuilder.build_error_response(
            "请求体不能为空"
        )), 400
    
    user_ids = data.get('user_ids', [])
    if not user_ids:
        return jsonify(UnifiedResponseBuilder.build_error_response(
            "user_ids 不能为空"
        )), 400
    
    # 验证用户ID
    invalid_ids = [uid for uid in user_ids if not UnifiedValidators.validate_user_id(uid)]
    if invalid_ids:
        return jsonify(UnifiedResponseBuilder.build_error_response(
            f"无效的用户ID: {invalid_ids}"
        )), 400
    
    contract_task_id = data.get('contract_task_id', '')
    agent_task_id = data.get('agent_task_id', '')
    include_behavior = data.get('include_behavior', False)
    include_details = data.get('include_details', False)
    
    logger.info(f"开始批量分析 {len(user_ids)} 个用户")
    
    # 批量查询结果
    results = []
    for user_id in user_ids:
        try:
            # 执行单个用户分析
            user_result = _execute_unified_user_analysis(
                user_id, contract_task_id, agent_task_id, include_behavior
            )
            
            # 如果不需要详细信息，简化结果
            if not include_details:
                user_result = _simplify_user_analysis_result(user_result)
            
            results.append(user_result)
            
        except Exception as e:
            logger.warning(f"批量查询中用户 {user_id} 失败: {str(e)}")
            results.append({
                'member_id': user_id,
                'status': 'error',
                'error': str(e)
            })
    
    return jsonify(UnifiedResponseBuilder.build_batch_response(results, len(user_ids)))

@unified_user_bp.route('/complete-analysis/<user_id>', methods=['GET'])
@login_required
@unified_error_handler
def get_complete_user_analysis(user_id: str):
    """
    获取用户完整行为分析 - 懒加载策略：优先从user_trading_profiles表读取，无数据时实时计算并保存

    流程：
    1. 优先从user_trading_profiles表读取已保存的分析结果
    2. 如果没有数据，则从position_analysis表获取持仓数据进行实时计算
    3. 计算完成后保存结果到user_trading_profiles表
    4. 从数据库重新查询刚保存的数据，确保前端显示的数据与数据库完全一致
    5. 返回数据库查询结果给前端

    优势：
    - 避免批量预计算的性能问题
    - 按需计算，资源利用更高效
    - 数据时效性更好（基于最新的position_analysis数据）
    - 计算结果会被保存，后续查询更快
    - 确保前端显示数据与数据库数据的一致性
    """
    logger.info(f"获取用户完整分析 - user_id: {user_id}")
    
    # 验证用户ID格式
    if not UnifiedValidators.validate_user_id(user_id):
        return jsonify(UnifiedResponseBuilder.build_error_response(
            "无效的用户ID格式", {"user_id": user_id}
        )), 400
    
    task_id = request.args.get('task_id', '')
    days = int(request.args.get('days', 30))
    # 🚀 新增：强制实时分析参数
    force_realtime = request.args.get('force_realtime', 'false').lower() == 'true'

    try:
        # 🚀 步骤1：根据参数决定是否使用缓存数据
        if not force_realtime:
            logger.info(f"步骤1: 尝试从user_trading_profiles表获取用户 {user_id} 的数据")

            profile_data = data_repository.get_user_trading_profile(user_id)
        else:
            logger.info(f"步骤1: 跳过缓存数据，强制进行实时分析 (force_realtime=true)")
            profile_data = None
        
        if profile_data:
            logger.info(f"✅ 成功从user_trading_profiles表获取用户 {user_id} 的数据")
            
            # 使用数据库数据直接映射为前端格式
            frontend_data = unified_utils.map_user_trading_profile_to_frontend(profile_data, task_id)
            
            # 获取额外的用户数据
            additional_data = _get_additional_user_data(user_id, task_id)
            
            # 构建完整响应
            complete_response = {
                'user_id': user_id,
                'task_id': task_id,
                'status': 'success',
                'analysis_timestamp': frontend_data['metadata']['analysis_timestamp'],
                'data_quality_score': frontend_data['metadata']['data_quality_score'],
                'analysis_confidence': frontend_data['metadata']['analysis_confidence'],
                'mapping_version': frontend_data['metadata']['mapping_version'],
                'data_source': 'user_trading_profiles表',
                
                # 🚀 前端格式化的完整数据
                'complete_data': frontend_data,
                
                # 保留原始additional_data供调试
                'additional_data': additional_data
            }
            
            logger.info(f"✅ 用户 {user_id} 从数据库数据构建响应完成")
            return jsonify(convert_datetime_to_string(complete_response))
        
        # 🚀 步骤2：进行实时分析
        if force_realtime:
            logger.info(f"步骤2: 强制实时分析模式，直接进行实时计算")
        else:
            logger.info(f"步骤2: user_trading_profiles表中没有用户 {user_id} 的数据，进行实时分析")
        
        # 🚀 第2.1步：检查用户是否有交易数据（更宽松的检查）
        # 优先检查position_analysis表中是否有交易数据，而不是依赖users表
        positions = data_repository.get_user_positions(user_id, task_id, days)

        if not positions or len(positions) == 0:
            logger.info(f"用户 {user_id} 无交易数据")
            return jsonify({
                'status': 'not_found',
                'message': '查无此人',
                'details': {
                    'searched_user_id': user_id,
                    'search_method': 'user_positions',
                    'reason': '该用户在系统中没有交易记录',
                    'suggestions': [
                        '确认该用户是否已进行过合约交易',
                        '检查是否已完成合约分析',
                        '如果是新交易，可能需要等待数据处理完成'
                    ]
                },
                'timestamp': datetime.now().isoformat()
            }), 200  # 改为200状态码，让前端正常处理

        # 🚀 第2.2步：验证持仓数据质量
        valid_positions = [pos for pos in positions if pos.get('pnl') is not None and pos.get('pnl') != 0]
        if len(valid_positions) < 1:  # 至少需要1笔有效交易
            logger.info(f"用户 {user_id} 有效交易数据不足: {len(valid_positions)} 笔")
            return jsonify({
                'status': 'not_found',
                'message': '查无此人',
                'details': {
                    'searched_user_id': user_id,
                    'valid_trades_count': len(valid_positions),
                    'required_trades_count': 1,
                    'total_positions_count': len(positions),
                    'reason': '有效交易数据不足，无法进行准确的行为分析'
                },
                'timestamp': datetime.now().isoformat()
            }), 200

        # 🚀 第2.3步：转换数据格式并执行分析
        logger.info(f"用户 {user_id}: 开始转换 {len(positions)} 条持仓数据并进行分析")

        # 转换为PositionData对象
        position_objects = [_convert_to_position_data(pos) for pos in positions]

        # 执行行为分析
        behavior_result = behavior_analyzer.analyze_user_behavior(user_id, position_objects)
        
        # 🚀 第2.4步：检查分析结果质量
        if not behavior_result or behavior_result.data_quality_score <= 0.1:
            logger.warning(f"用户 {user_id} 分析结果质量过低或分析失败")
            return jsonify({
                'status': 'not_found',
                'message': '查无此人',
                'details': {
                    'searched_user_id': user_id,
                    'reason': '数据质量过低，无法生成可靠的分析结果'
                },
                'timestamp': datetime.now().isoformat()
            }), 200

        # 检查是否应该返回错误（空结果处理）
        if hasattr(behavior_result, '_should_return_error') and behavior_result._should_return_error:
            error_message = getattr(behavior_result, '_error_message', '分析失败')
            logger.warning(f"用户 {user_id} 分析失败: {error_message}")
            return jsonify({
                'status': 'not_found',
                'message': '查无此人',
                'details': {
                    'searched_user_id': user_id,
                    'reason': error_message
                },
                'timestamp': datetime.now().isoformat()
            }), 200

        # 检查基础交易数据的合理性
        basic_metrics = behavior_result.basic_metrics
        if basic_metrics and (basic_metrics.total_trades < 1 or basic_metrics.total_volume < 10):
            logger.warning(f"用户 {user_id} 基础数据不足：交易{basic_metrics.total_trades}笔，交易量{basic_metrics.total_volume}")
            return jsonify({
                'status': 'not_found',
                'message': '查无此人',
                'details': {
                    'searched_user_id': user_id,
                    'reason': '交易数据不足，无法进行分析'
                },
                'timestamp': datetime.now().isoformat()
            }), 200

        # 🚀 第2.5步：保存分析结果到user_trading_profiles表
        try:
            if force_realtime:
                logger.info(f"步骤2.5: 保存用户 {user_id} 的实时分析结果到user_trading_profiles表")
            else:
                logger.info(f"步骤2.5: 保存用户 {user_id} 的分析结果到user_trading_profiles表")

            # 导入合约分析器的保存方法
            from modules.contract_risk_analysis.services.contract_analyzer import CTContractAnalyzer

            # 创建临时分析器实例来调用保存方法
            temp_analyzer = CTContractAnalyzer()
            temp_analyzer._save_user_behavior_result(user_id, behavior_result)

            logger.info(f"✅ 用户 {user_id} 分析结果已保存到user_trading_profiles表")

        except Exception as save_error:
            logger.error(f"保存用户 {user_id} 分析结果失败: {str(save_error)}")
            # 保存失败则返回实时计算结果
            logger.warning(f"保存失败，返回实时计算结果给用户 {user_id}")
            additional_data = _get_additional_user_data(user_id, task_id)
            complete_response = _build_complete_analysis_response(
                behavior_result, additional_data, user_id, task_id
            )
            return jsonify(complete_response)

        # 🚀 第2.6步：从数据库重新查询保存的数据并返回
        logger.info(f"步骤2.6: 从user_trading_profiles表重新查询用户 {user_id} 的数据")

        # 重新从数据库查询刚保存的数据
        saved_profile_data = data_repository.get_user_trading_profile(user_id)

        if saved_profile_data:
            logger.info(f"✅ 成功从数据库获取用户 {user_id} 刚保存的数据")

            # 使用数据库数据映射为前端格式
            frontend_data = unified_utils.map_user_trading_profile_to_frontend(saved_profile_data, task_id)

            # 获取额外的用户数据
            additional_data = _get_additional_user_data(user_id, task_id)

            # 构建完整响应（使用数据库数据）
            complete_response = {
                'user_id': user_id,
                'task_id': task_id,
                'status': 'success',
                'analysis_timestamp': frontend_data['metadata']['analysis_timestamp'],
                'data_quality_score': frontend_data['metadata']['data_quality_score'],
                'analysis_confidence': frontend_data['metadata']['analysis_confidence'],
                'mapping_version': frontend_data['metadata']['mapping_version'],
                'data_source': 'user_trading_profiles表（刚计算并保存）',

                # 🚀 前端格式化的完整数据
                'complete_data': frontend_data,

                # 保留原始additional_data供调试
                'additional_data': additional_data
            }

            logger.info(f"✅ 用户 {user_id} 实时计算→保存→重新查询完成，返回数据库数据")
            return jsonify(convert_datetime_to_string(complete_response))
        else:
            # 如果重新查询失败，返回实时计算结果作为备选
            logger.warning(f"重新查询失败，返回实时计算结果给用户 {user_id}")
            additional_data = _get_additional_user_data(user_id, task_id)
            complete_response = _build_complete_analysis_response(
                behavior_result, additional_data, user_id, task_id
            )
            return jsonify(complete_response)
        
    except Exception as e:
        logger.error(f"获取用户完整分析失败: {str(e)}")
        return jsonify(UnifiedResponseBuilder.build_error_response(
            f"分析失败: {str(e)}", {"user_id": user_id, "error_details": str(e)}
        )), 500

@unified_user_bp.route('/analysis-report/<user_id>', methods=['GET'])
@login_required
@unified_error_handler
def generate_user_report(user_id: str):
    """
    生成用户分析报告
    """
    logger.info(f"生成用户分析报告 - user_id: {user_id}")
    
    task_id = request.args.get('task_id', '')
    days = int(request.args.get('days', 30))
    report_format = request.args.get('format', 'json')
    
    # 获取用户持仓数据
    positions = data_repository.get_user_positions(user_id, task_id, days)
    
    if not positions:
        return jsonify(UnifiedResponseBuilder.build_not_found_response(
            "用户数据", user_id
        )), 404
    
    # 转换为PositionData对象
    position_objects = [_convert_to_position_data(pos) for pos in positions]
    
    # 执行分析
    result = behavior_analyzer.analyze_user_behavior(user_id, position_objects)
    
    # 生成报告
    report = behavior_analyzer.generate_analysis_report(result)
    
    if report_format == 'summary':
        # 返回简化版报告
        summary_report = _build_summary_report(report, user_id)
        return jsonify(summary_report)
    else:
        # 返回完整报告
        return jsonify(report)

@unified_user_bp.route('/coin-analysis/<user_id>', methods=['GET'])
@login_required
@unified_error_handler
def get_user_coin_analysis(user_id: str):
    """
    获取用户币种分析
    """
    logger.info(f"获取用户币种分析 - user_id: {user_id}")
    
    task_id = request.args.get('task_id', '')
    days = int(request.args.get('days', 30))
    
    # 获取用户持仓数据
    positions = data_repository.get_user_positions(user_id, task_id, days)
    
    if not positions:
        return jsonify(UnifiedResponseBuilder.build_not_found_response(
            "用户数据", user_id
        )), 404
    
    # 转换为PositionData对象
    position_objects = [_convert_to_position_data(pos) for pos in positions]
    
    # 执行币种分析
    coin_analysis = behavior_analyzer.analyze_coin_performance(user_id, position_objects)
    
    return jsonify(UnifiedResponseBuilder.build_success_response(
        coin_analysis, "币种分析完成"
    ))

@unified_user_bp.route('/available-tasks', methods=['GET'])
@login_required
@unified_error_handler
def get_available_tasks():
    """获取可用的分析任务列表"""
    logger.info("获取可用任务列表...")
    
    # 使用统一的数据访问层获取任务列表
    task_lists = data_repository.get_unified_task_lists()
    
    logger.info(f"返回结果: contract_tasks={len(task_lists['contract_tasks'])}, "
                f"agent_tasks={len(task_lists['agent_tasks'])}")
    
    return jsonify(task_lists)

@unified_user_bp.route('/analyzer-status', methods=['GET'])
@login_required
def get_analyzer_status():
    """获取分析器状态"""
    try:
        status = {
            'status': 'healthy',
            'analyzer_initialized': behavior_analyzer is not None,
            'data_repository_initialized': data_repository is not None,
            'cache_size': len(unified_cache_manager._cache),
            'timestamp': datetime.now().isoformat()
        }
        
        return jsonify(UnifiedResponseBuilder.build_success_response(
            status, "分析器状态正常"
        ))
        
    except Exception as e:
        logger.error(f"获取分析器状态失败: {str(e)}")
        return jsonify(UnifiedResponseBuilder.build_error_response(
            f"获取分析器状态失败: {str(e)}"
        )), 500

# 内部辅助函数

def _execute_unified_user_analysis(member_id: str, contract_task_id: str, 
                                   agent_task_id: str, include_behavior: bool = False) -> Dict:
    """执行统一的用户分析"""
    try:
        # 获取用户基础信息
        user_basic_info = data_repository.get_unified_user_basic_info(member_id)
        
        # 获取用户关联信息
        user_associations = data_repository.get_comprehensive_user_associations(
            member_id, user_basic_info.get('digital_id', ''), agent_task_id
        )
        
        # 获取交易详情
        transaction_details = data_repository.get_unified_transaction_details(
            member_id, contract_task_id
        )
        
        # 构建基础分析结果
        analysis_result = {
            'member_id': member_id,
            'user_profile': user_basic_info,
            'associations': user_associations,
            'transaction_summary': transaction_details['summary'],
            'transaction_details': transaction_details,  # 🔧 添加完整的交易详情
            'risk_analysis': _analyze_user_risks(transaction_details['transactions']),
            'status': 'success'
        }
        
        # 如果需要包含行为分析
        if include_behavior:
            try:
                positions = data_repository.get_user_positions(member_id, contract_task_id)
                if positions:
                    position_objects = [_convert_to_position_data(pos) for pos in positions]
                    behavior_result = behavior_analyzer.analyze_user_behavior(member_id, position_objects)
                    analysis_result['behavior_analysis'] = _format_behavior_analysis(behavior_result)
                else:
                    analysis_result['behavior_analysis'] = {'status': 'no_data', 'message': '无行为数据'}
            except Exception as e:
                logger.warning(f"行为分析失败: {str(e)}")
                analysis_result['behavior_analysis'] = {'status': 'error', 'message': str(e)}
        
        return analysis_result
        
    except Exception as e:
        logger.error(f"执行统一用户分析失败: {str(e)}")
        raise

def _analyze_user_risks(transactions: List[Dict]) -> Dict:
    """分析用户风险"""
    try:
        if not transactions:
            return {
                'risk_level': '无风险',
                'total_risks': 0,
                'max_risk_score': 0,
                'risk_categories': []
            }
        
        # 🔧 修复：对敲交易即使风险评分为0也应该被认为是风险交易
        risk_transactions = [t for t in transactions if t.get('risk_score', 0) > 0.5 or t.get('detection_type') == 'wash_trading']
        max_risk_score = max([t.get('risk_score', 0) for t in transactions])
        
        risk_level = UnifiedDescriptionGenerator.get_risk_level_description(
            len(risk_transactions), max_risk_score
        )
        
        return {
            'risk_level': risk_level,
            'total_risks': len(risk_transactions),
            'max_risk_score': max_risk_score,
            'risk_categories': list(set([t.get('trade_type', '未知') for t in risk_transactions]))
        }
        
    except Exception as e:
        logger.warning(f"分析用户风险失败: {str(e)}")
        return {
            'risk_level': '分析失败',
            'total_risks': 0,
            'max_risk_score': 0,
            'risk_categories': []
        }

def _convert_to_position_data(position_dict: Dict) -> PositionData:
    """将字典转换为PositionData对象 - 修复版本"""
    try:
        # 处理时间字段
        open_time = position_dict.get('open_time')
        close_time = position_dict.get('close_time')

        # 转换字符串时间为datetime对象
        if isinstance(open_time, str):
            try:
                open_time = datetime.fromisoformat(open_time.replace('Z', '+00:00'))
            except:
                open_time = datetime.now()
        elif open_time is None:
            open_time = datetime.now()

        if isinstance(close_time, str):
            try:
                close_time = datetime.fromisoformat(close_time.replace('Z', '+00:00'))
            except:
                close_time = open_time
        elif close_time is None:
            # 🚀 修复: 对于未完成的持仓，使用开仓时间 + 15分钟作为估算平仓时间
            close_time = open_time + timedelta(minutes=15)

        # 计算持仓时长
        duration_minutes = (close_time - open_time).total_seconds() / 60.0

        # 转换position_type为primary_side
        position_type = position_dict.get('position_type', 'LONG')
        primary_side = 1 if position_type.upper() == 'LONG' else 3

        # 🚀 修复: 从position_dict中获取订单类型统计数据
        order_type_stats = {
            'market_orders_open': int(position_dict.get('market_orders_open', 0)),
            'limit_orders_open': int(position_dict.get('limit_orders_open', 0)),
            'market_orders_close': int(position_dict.get('market_orders_close', 0)),
            'limit_orders_close': int(position_dict.get('limit_orders_close', 0))
        }
        
        # 🚀 修复: 计算真实手续费而不是硬编码为0
        real_commission = _calculate_real_commission(position_dict)

        return PositionData(
            position_id=f"{position_dict.get('member_id', '')}_{position_dict.get('symbol', '')}_{int(open_time.timestamp())}",
            member_id=position_dict.get('member_id', ''),
            contract_name=position_dict.get('symbol', 'UNKNOWN'),
            primary_side=primary_side,
            open_time=open_time,
            close_time=close_time,
            duration_minutes=duration_minutes,
            total_open_amount=float(position_dict.get('position_size', 0)),
            total_close_amount=float(position_dict.get('position_size', 0)),
            avg_open_price=float(position_dict.get('entry_price', 0)),
            avg_close_price=float(position_dict.get('current_price', 0)),
            total_pnl=float(position_dict.get('pnl', 0)),
            total_commission=real_commission,  # 🚀 修复: 使用计算的真实手续费
            net_pnl=float(position_dict.get('pnl', 0)) - real_commission,  # 🚀 修复: 扣除手续费
            leverage=float(position_dict.get('leverage', 1)),
            task_id='',
            # 修复订单类型字段
            market_orders_open=order_type_stats.get('market_orders_open', 0),
            limit_orders_open=order_type_stats.get('limit_orders_open', 0),
            market_orders_close=order_type_stats.get('market_orders_close', 0),
            limit_orders_close=order_type_stats.get('limit_orders_close', 0),
            # 🚀 新增：仓位模式字段（从position_dict中获取）
            cross_margin_positions=int(position_dict.get('cross_margin_positions', 0)),
            isolated_margin_positions=int(position_dict.get('isolated_margin_positions', 0))
        )
    except Exception as e:
        logger.error(f"转换PositionData失败: {str(e)}, position_dict: {position_dict}")
        # 返回一个最小的PositionData对象
        return PositionData(
            position_id='error',
            member_id=position_dict.get('member_id', ''),
            contract_name='UNKNOWN',
            primary_side=1,
            open_time=datetime.now(),
            close_time=datetime.now(),
            duration_minutes=0,
            total_open_amount=0,
            total_close_amount=0,
            avg_open_price=0,
            avg_close_price=0,
            total_pnl=0,
            total_commission=0,
            net_pnl=0,
            leverage=1
        )

# 🚀 已废弃：订单类型统计现在在数据源层面正确处理
# 通过liquidity字段（taker=市价单，maker=限价单）和open_type字段（2=全仓，1=逐仓）
# 在build_complete_positions阶段统计，不再需要启发式推断

def _calculate_real_commission(position_dict: Dict) -> float:
    """从原始数据中计算真实手续费"""
    try:
        # 🚀 需要根据实际数据源结构实现
        # 如果原始数据中有手续费信息，从中提取
        # 如果没有，可以基于价格差异等启发式方法估算

        # 检查是否有additional_data字段包含手续费信息
        additional_data_str = position_dict.get('additional_data', '{}')
        try:
            import json
            additional_data = json.loads(additional_data_str) if additional_data_str else {}
        except:
            additional_data = {}

        # 示例实现（需要根据实际数据调整）
        commission = additional_data.get('commission', 0)

        return float(commission)

    except Exception as e:
        logger.warning(f"计算真实手续费失败: {str(e)}")
        # 返回默认值
        return 0.0

def _get_additional_user_data(user_id: str, task_id: str = '') -> Dict:
    """获取额外的用户数据"""
    try:
        return {
            'basic_info': data_repository.get_unified_user_basic_info(user_id),
            'associations': data_repository.get_comprehensive_user_associations(user_id, '', ''),
            'task_lists': data_repository.get_unified_task_lists()
        }
    except Exception as e:
        logger.warning(f"获取额外用户数据失败: {str(e)}")
        return {}

def _build_complete_analysis_response(result: UserBehaviorProfile, additional_data: Dict, 
                                      user_id: str, task_id: str = '') -> Dict:
    """
    🚀 构建完整的分析响应 - 使用新的前端数据映射器
    这是方案2的核心实现 - 完善数据映射和格式化
    """
    try:
        logger.info(f"开始构建用户 {user_id} 的完整分析响应")
        
        # 🚀 使用新的前端数据映射器
        frontend_data = unified_utils.map_to_frontend_format(result, task_id)
        
        # 补充额外数据到映射结果中
        if additional_data:
            # 补充任务列表
            if 'task_lists' in additional_data:
                frontend_data['search_area']['contract_tasks'] = _format_task_list_for_frontend(
                    additional_data['task_lists'].get('contract_tasks', [])
                )
                frontend_data['search_area']['agent_tasks'] = _format_task_list_for_frontend(
                    additional_data['task_lists'].get('agent_tasks', [])
                )
            
            # 补充用户基础信息
            if 'basic_info' in additional_data:
                basic_info = additional_data['basic_info']
                frontend_data['user_profile'].update({
                    'digital_id': basic_info.get('digital_id', frontend_data['user_profile']['digital_id']),
                    'bd_name': basic_info.get('bd_name', ''),
                    'last_activity': basic_info.get('last_activity', frontend_data['user_profile']['last_activity'])
                })
            
            # 补充关联分析
            if 'associations' in additional_data:
                frontend_data['associations'] = additional_data['associations']
        
        # 构建最终响应
        response = {
            'user_id': user_id,
            'task_id': task_id,
            'status': 'success',
            'analysis_timestamp': frontend_data['metadata']['analysis_timestamp'],
            'data_quality_score': frontend_data['metadata']['data_quality_score'],
            'analysis_confidence': frontend_data['metadata']['analysis_confidence'],
            'mapping_version': frontend_data['metadata']['mapping_version'],
            
            # 🚀 前端格式化的完整数据
            'complete_data': frontend_data,
            
            # 保留原始additional_data供调试
            'additional_data': additional_data
        }
        
        logger.info(f"用户 {user_id} 的完整分析响应构建完成，包含 {len(frontend_data)} 个数据分类")
        return convert_datetime_to_string(response)
        
    except Exception as e:
        logger.error(f"构建完整分析响应失败: {str(e)}")
        traceback.print_exc()
        return {
            'user_id': user_id,
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat(),
            'error_type': 'mapping_error'
        }

def _format_behavior_analysis(behavior_result: UserBehaviorProfile) -> Dict:
    """格式化行为分析结果"""
    try:
        return {
            'professional_scores': {
                'total_score': behavior_result.professional_scores.total_score,
                'trader_type': behavior_result.professional_scores.trader_type,
                'confidence_level': behavior_result.professional_scores.confidence_level
            },
            'fund_scale_category': behavior_result.fund_scale_category,
            'data_quality_score': behavior_result.data_quality_score,
            'basic_metrics_summary': {
                'total_volume': behavior_result.basic_metrics.total_volume if behavior_result.basic_metrics else 0,
                'total_trades': behavior_result.basic_metrics.total_trades if behavior_result.basic_metrics else 0
            }
        }
    except Exception as e:
        logger.warning(f"格式化行为分析结果失败: {str(e)}")
        return {'status': 'format_error', 'message': str(e)}

def _simplify_user_analysis_result(result: Dict) -> Dict:
    """简化用户分析结果（用于批量查询）"""
    try:
        return {
            'member_id': result.get('member_id'),
            'status': result.get('status'),
            'risk_level': result.get('risk_analysis', {}).get('risk_level', '未知'),
            'total_transactions': result.get('transaction_summary', {}).get('total_transactions', 0),
            'total_volume': result.get('transaction_summary', {}).get('total_volume', 0),
            'associated_users': result.get('associations', {}).get('association_summary', {}).get('total_associated_users', 0)
        }
    except Exception as e:
        logger.warning(f"简化用户分析结果失败: {str(e)}")
        return {'member_id': result.get('member_id'), 'status': 'error', 'message': str(e)}

def _format_task_list_for_frontend(task_list: List[Dict]) -> List[Dict]:
    """格式化任务列表为前端期望的格式"""
    try:
        formatted_tasks = []
        for task in task_list:
            formatted_task = {
                'id': task.get('task_id', task.get('id', '')),  # 前端期望的字段名
                'name': task.get('task_name', task.get('name', 'Unknown')),  # 前端期望的字段名
                'status': task.get('status', 'unknown'),
                'created_at': task.get('created_at', ''),
                'description': task.get('description', ''),
                'file_path': task.get('file_path', '')
            }
            formatted_tasks.append(formatted_task)
        return formatted_tasks
    except Exception as e:
        logger.warning(f"格式化任务列表失败: {str(e)}")
        return task_list  # 返回原始列表

def _build_summary_report(report: Dict, user_id: str) -> Dict:
    """构建摘要报告"""
    try:
        return {
            'user_id': user_id,
            'analysis_date': report.get('analysis_date'),
            'status': report.get('status'),
            'summary': {
                'trader_type': report.get('professionalism_score', {}).get('trader_type', '未知'),
                'total_score': report.get('professionalism_score', {}).get('total_score', 0),
                'fund_scale': report.get('professionalism_score', {}).get('fund_scale', '未知'),
                'total_volume': report.get('trading_summary', {}).get('total_volume', 0),
                'win_rate': report.get('trading_summary', {}).get('win_rate', 0),
                'total_trades': report.get('trading_summary', {}).get('total_trades', 0)
            },
            'key_insights': {
                'top_strength': '数据分析中',
                'main_weakness': '数据分析中',
                'recommendation': '建议继续观察交易行为'
            }
        }
    except Exception as e:
        logger.warning(f"构建摘要报告失败: {str(e)}")
        return {
            'user_id': user_id,
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        } 
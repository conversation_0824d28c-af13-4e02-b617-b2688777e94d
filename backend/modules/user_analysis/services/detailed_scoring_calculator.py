"""
详细评分计算器
负责计算11个详细评分字段，为前端提供更细粒度的评分展示
"""

import logging
from typing import List, Dict
import numpy as np

from modules.user_analysis.models.user_behavior_models import (
    BasicMetrics, DerivedMetrics, PositionData
)

logger = logging.getLogger(__name__)


class DetailedScoringCalculator:
    """详细评分计算器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化详细评分计算器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.scoring_weights = self.config.get('detailed_scoring_weights', {
            'win_rate': {'excellent': 0.7, 'good': 0.5, 'poor': 0.3},
            'profit_loss_ratio': {'excellent': 2.0, 'good': 1.5, 'poor': 1.0},
            'profit_factor': {'excellent': 2.0, 'good': 1.5, 'poor': 1.0},
            'leverage': {'safe': 5, 'moderate': 10, 'risky': 20},
            'trading_frequency': {'optimal': 0.5, 'acceptable': 1.0, 'excessive': 2.0}
        })
        
        # logger.info("详细评分计算器初始化完成")
    
    def calculate_detailed_scores(self, basic_metrics: BasicMetrics, 
                                derived_metrics: DerivedMetrics,
                                positions: List[PositionData]) -> Dict:
        """
        计算详细评分
        
        Args:
            basic_metrics: 基础指标
            derived_metrics: 衍生指标
            positions: 持仓数据列表
            
        Returns:
            Dict: 详细评分字典
        """
        # logger.info("开始计算详细评分")
        
        try:
            # 1. 盈利能力评分 (4个)
            profitability_scores = self._calculate_profitability_scores(
                basic_metrics, derived_metrics)
            
            # 2. 风险控制评分 (3个)
            risk_control_scores = self._calculate_risk_control_scores(
                basic_metrics, derived_metrics, positions)
            
            # 3. 交易行为评分 (4个)
            trading_behavior_scores = self._calculate_trading_behavior_scores(
                basic_metrics, derived_metrics, positions)
            
            # 4. 市场理解评分 (3个)
            market_understanding_scores = self._calculate_market_understanding_scores(
                derived_metrics, positions)
            
            # 🚀 5. 额外评分字段 (6个)
            additional_scores = self._calculate_additional_scores(
                basic_metrics, derived_metrics, positions)
            
            # 合并所有评分
            detailed_scores = {
                **profitability_scores,
                **risk_control_scores,
                **trading_behavior_scores,
                **market_understanding_scores,
                **additional_scores
            }
            
            # logger.info("详细评分计算完成")
            return detailed_scores
            
        except Exception as e:
            logger.error(f"计算详细评分时发生错误: {str(e)}")
            return self._get_empty_scores()
    
    def _calculate_profitability_scores(self, basic_metrics: BasicMetrics, 
                                      derived_metrics: DerivedMetrics) -> Dict:
        """计算盈利能力评分 (4个字段)"""
        
        # 1. 胜率评分 (0-100)
        win_rate = derived_metrics.win_rate
        if win_rate >= 0.7:
            win_rate_score = 100
        elif win_rate >= 0.5:
            win_rate_score = 50 + (win_rate - 0.5) / 0.2 * 50
        else:
            win_rate_score = win_rate / 0.5 * 50
        
        # 2. 盈亏比评分 (0-100)
        profit_loss_ratio = derived_metrics.profit_loss_ratio
        if profit_loss_ratio >= 2.0:
            profit_loss_ratio_score = 100
        elif profit_loss_ratio >= 1.5:
            profit_loss_ratio_score = 70 + (profit_loss_ratio - 1.5) / 0.5 * 30
        elif profit_loss_ratio >= 1.0:
            profit_loss_ratio_score = 40 + (profit_loss_ratio - 1.0) / 0.5 * 30
        else:
            profit_loss_ratio_score = profit_loss_ratio * 40
        
        # 3. 盈利因子评分 (0-100)
        profit_factor = derived_metrics.profit_factor
        if profit_factor >= 2.0:
            profit_factor_score = 100
        elif profit_factor >= 1.5:
            profit_factor_score = 70 + (profit_factor - 1.5) / 0.5 * 30
        elif profit_factor >= 1.0:
            profit_factor_score = 40 + (profit_factor - 1.0) / 0.5 * 30
        else:
            profit_factor_score = profit_factor * 40
        
        # 4. 盈利一致性评分 (0-100)
        profit_consistency = derived_metrics.profit_consistency
        profit_consistency_score = profit_consistency * 100
        
        return {
            'win_rate_score': round(win_rate_score, 2),
            'profit_loss_ratio_score': round(profit_loss_ratio_score, 2),
            'profit_factor_score': round(profit_factor_score, 2),
            'profit_consistency_score': round(profit_consistency_score, 2)
        }
    
    def _calculate_risk_control_scores(self, basic_metrics: BasicMetrics,
                                     derived_metrics: DerivedMetrics,
                                     positions: List[PositionData]) -> Dict:
        """计算风险控制评分 (3个字段)"""
        
        # 1. 平均杠杆评分 (0-100) - 杠杆越低评分越高
        avg_leverage = derived_metrics.avg_leverage
        if avg_leverage <= 3:
            avg_leverage_score = 100
        elif avg_leverage <= 5:
            avg_leverage_score = 80 - (avg_leverage - 3) / 2 * 20
        elif avg_leverage <= 10:
            avg_leverage_score = 60 - (avg_leverage - 5) / 5 * 30
        else:
            avg_leverage_score = max(0, 30 - (avg_leverage - 10) / 10 * 30)
        
        # 2. 最大杠杆评分 (0-100)
        max_leverage = derived_metrics.max_leverage
        if max_leverage <= 5:
            max_leverage_score = 100
        elif max_leverage <= 10:
            max_leverage_score = 80 - (max_leverage - 5) / 5 * 20
        elif max_leverage <= 20:
            max_leverage_score = 60 - (max_leverage - 10) / 10 * 30
        else:
            max_leverage_score = max(0, 30 - (max_leverage - 20) / 30 * 30)
        
        # 3. 杠杆稳定性评分 (0-100)
        leverage_stability = derived_metrics.leverage_stability
        leverage_stability_score = leverage_stability * 100
        
        return {
            'avg_leverage_score': round(avg_leverage_score, 2),
            'max_leverage_score': round(max_leverage_score, 2),
            'leverage_stability_score': round(leverage_stability_score, 2)
        }
    
    def _calculate_trading_behavior_scores(self, basic_metrics: BasicMetrics,
                                         derived_metrics: DerivedMetrics,
                                         positions: List[PositionData]) -> Dict:
        """计算交易行为评分 (4个字段)"""
        
        # 1. 交易频率评分 (0-100) - 适中的频率最好
        trading_frequency = derived_metrics.trading_frequency
        if 0.3 <= trading_frequency <= 0.7:
            trading_frequency_score = 100
        elif 0.1 <= trading_frequency < 0.3:
            trading_frequency_score = 50 + (trading_frequency - 0.1) / 0.2 * 50
        elif 0.7 < trading_frequency <= 1.0:
            trading_frequency_score = 100 - (trading_frequency - 0.7) / 0.3 * 50
        else:
            trading_frequency_score = max(0, 50 - abs(trading_frequency - 0.5) * 100)
        
        # 2. 市价单比例评分 (0-100) - 20-40%为最佳
        market_order_ratio = derived_metrics.market_order_ratio
        if 0.2 <= market_order_ratio <= 0.4:
            market_order_ratio_score = 100
        elif market_order_ratio < 0.2:
            market_order_ratio_score = market_order_ratio / 0.2 * 100
        else:  # market_order_ratio > 0.4
            market_order_ratio_score = max(0, 100 - (market_order_ratio - 0.4) / 0.3 * 100)
        
        # 3. 盈亏时长比评分 (0-100) - 2:1或更高为最佳
        duration_ratio = derived_metrics.profit_loss_duration_ratio
        if duration_ratio >= 2.0:
            duration_ratio_score = 100
        elif duration_ratio >= 1.0:
            duration_ratio_score = 50 + (duration_ratio - 1.0) * 50
        else:
            duration_ratio_score = duration_ratio * 50
        
        # 4. 仓位一致性评分 (0-100)
        position_consistency = derived_metrics.position_size_consistency
        position_consistency_score = position_consistency * 100
        
        return {
            'trading_frequency_score': round(trading_frequency_score, 2),
            'market_order_ratio_score': round(market_order_ratio_score, 2),
            'duration_ratio_score': round(duration_ratio_score, 2),
            'position_consistency_score': round(position_consistency_score, 2)
        }
    
    def _calculate_market_understanding_scores(self, derived_metrics: DerivedMetrics,
                                             positions: List[PositionData]) -> Dict:
        """计算市场理解评分 (3个字段)"""
        
        # 1. 时机把握能力评分 (0-100)
        timing_ability = derived_metrics.position_timing_ability
        timing_ability_score = timing_ability * 100
        
        # 2. 风险纪律性评分 (0-100)
        risk_discipline = derived_metrics.risk_management_discipline
        risk_discipline_score = risk_discipline * 100
        
        # 3. 执行效率评分 (0-100)
        execution_efficiency = derived_metrics.trading_execution_efficiency
        execution_efficiency_score = execution_efficiency * 100
        
        return {
            'timing_ability_score': round(timing_ability_score, 2),
            'risk_discipline_score': round(risk_discipline_score, 2),
            'execution_efficiency_score': round(execution_efficiency_score, 2)
        }
    
    def _calculate_additional_scores(self, basic_metrics: BasicMetrics,
                                   derived_metrics: DerivedMetrics,
                                   positions: List[PositionData]) -> Dict:
        """计算额外的评分字段 (6个)"""
        
        # 1. 活跃度评分 (0-100) - 基于交易频率和持续性
        total_days = basic_metrics.total_trading_days
        total_trades = basic_metrics.total_trades
        activity_rate = total_trades / total_days if total_days > 0 else 0
        
        if activity_rate >= 1.0:
            activity_score = 100
        elif activity_rate >= 0.5:
            activity_score = 70 + (activity_rate - 0.5) / 0.5 * 30
        elif activity_rate >= 0.2:
            activity_score = 40 + (activity_rate - 0.2) / 0.3 * 30
        else:
            activity_score = activity_rate / 0.2 * 40
        
        # 2. 一致性评分 (0-100) - 基于交易规模和时间的一致性
        if hasattr(basic_metrics, 'volume_distribution') and basic_metrics.volume_distribution:
            volume_cv = basic_metrics.volume_distribution.get('std', 0) / basic_metrics.volume_distribution.get('mean', 1) if basic_metrics.volume_distribution.get('mean', 0) > 0 else 1
            consistency_score = max(0, 100 - volume_cv * 100)
        else:
            consistency_score = 50  # 默认分数
        
        # 3. 市场时机评分 (0-100) - 基于盈利交易的时机把握
        profitable_duration = basic_metrics.avg_profit_duration
        loss_duration = basic_metrics.avg_loss_duration
        
        if loss_duration > 0:
            timing_ratio = profitable_duration / loss_duration
            if timing_ratio >= 2.0:
                market_timing_score = 100
            elif timing_ratio >= 1.0:
                market_timing_score = 50 + (timing_ratio - 1.0) * 50
            else:
                market_timing_score = timing_ratio * 50
        else:
            market_timing_score = 80  # 如果没有亏损，给高分
        
        # 4. 仓位管理评分 (0-100) - 基于仓位规模的合理性
        avg_trade_size = basic_metrics.avg_trade_size
        total_volume = basic_metrics.total_volume
        
        # 假设合理的单笔交易不应超过总资金的20%
        if total_volume > 0:
            position_ratio = avg_trade_size / total_volume * basic_metrics.total_trades
            if position_ratio <= 0.2:
                position_management_score = 100
            elif position_ratio <= 0.5:
                position_management_score = 80 - (position_ratio - 0.2) / 0.3 * 30
            else:
                position_management_score = max(0, 50 - (position_ratio - 0.5) * 100)
        else:
            position_management_score = 50
        
        # 5. 综合表现评分 (0-100) - 基于多个维度的综合评价
        profitability = min(100, derived_metrics.win_rate * 100 + derived_metrics.profit_factor * 20)
        risk_control = min(100, (1 / max(derived_metrics.avg_leverage, 1)) * 100)
        overall_performance_score = (profitability * 0.6 + risk_control * 0.4)
        
        # 6. 最大单笔亏损评分 (0-100) - 基于风险控制能力
        max_single_loss = derived_metrics.max_single_loss
        total_volume = basic_metrics.total_volume
        
        if total_volume > 0:
            loss_ratio = abs(max_single_loss) / total_volume
            if loss_ratio <= 0.05:  # 亏损不超过总资金的5%
                max_single_loss_score = 100
            elif loss_ratio <= 0.1:
                max_single_loss_score = 80 - (loss_ratio - 0.05) / 0.05 * 30
            elif loss_ratio <= 0.2:
                max_single_loss_score = 50 - (loss_ratio - 0.1) / 0.1 * 30
            else:
                max_single_loss_score = max(0, 20 - (loss_ratio - 0.2) * 100)
        else:
            max_single_loss_score = 50
        
        return {
            'activity_score': round(activity_score, 2),
            'consistency_score': round(consistency_score, 2),
            'market_timing_score': round(market_timing_score, 2),
            'position_management_score': round(position_management_score, 2),
            'overall_performance_score': round(overall_performance_score, 2),
            'max_single_loss_score': round(max_single_loss_score, 2)
        }

    def _get_empty_scores(self) -> Dict:
        """获取空的评分结果"""
        return {
            # 盈利能力评分
            'win_rate_score': 0.0,
            'profit_loss_ratio_score': 0.0,
            'profit_factor_score': 0.0,
            'profit_consistency_score': 0.0,
            
            # 风险控制评分
            'avg_leverage_score': 0.0,
            'max_leverage_score': 0.0,
            'leverage_stability_score': 0.0,
            
            # 交易行为评分
            'trading_frequency_score': 0.0,
            'market_order_ratio_score': 0.0,
            'duration_ratio_score': 0.0,
            'position_consistency_score': 0.0,
            
            # 市场理解评分
            'timing_ability_score': 0.0,
            'risk_discipline_score': 0.0,
            'execution_efficiency_score': 0.0,
            
            # 🚀 额外评分字段
            'activity_score': 0.0,
            'consistency_score': 0.0,
            'market_timing_score': 0.0,
            'position_management_score': 0.0,
            'overall_performance_score': 0.0,
            'max_single_loss_score': 0.0
        }
    
    def get_score_explanations(self) -> Dict:
        """获取评分说明"""
        return {
            'profitability_scores': {
                'win_rate_score': '胜率评分：基于交易胜率，70%以上为优秀',
                'profit_loss_ratio_score': '盈亏比评分：基于平均盈利/平均亏损，2:1以上为优秀',
                'profit_factor_score': '盈利因子评分：基于总盈利/总亏损，2.0以上为优秀',
                'profit_consistency_score': '盈利一致性评分：基于盈利的稳定性'
            },
            'risk_control_scores': {
                'avg_leverage_score': '平均杠杆评分：杠杆越低评分越高，3倍以下为优秀',
                'max_leverage_score': '最大杠杆评分：最大杠杆越低评分越高，5倍以下为优秀',
                'leverage_stability_score': '杠杆稳定性评分：杠杆使用的一致性'
            },
            'trading_behavior_scores': {
                'trading_frequency_score': '交易频率评分：适中的交易频率最佳',
                'market_order_ratio_score': '市价单比例评分：20-40%为最佳比例',
                'duration_ratio_score': '盈亏时长比评分：盈利持仓时间/亏损持仓时间',
                'position_consistency_score': '仓位一致性评分：仓位大小的一致性'
            },
            'market_understanding_scores': {
                'timing_ability_score': '时机把握评分：开仓和平仓时机的准确性',
                'risk_discipline_score': '风险纪律评分：风险管理的严格程度',
                'execution_efficiency_score': '执行效率评分：交易执行的效率'
            }
        }

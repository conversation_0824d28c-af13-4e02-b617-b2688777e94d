"""
交易偏好分析器
负责分析用户的交易偏好，包括币种偏好、时间偏好、风险偏好等
"""

import logging
from typing import List, Dict
from datetime import datetime
import numpy as np
from collections import defaultdict

from modules.user_analysis.models.user_behavior_models import PositionData

logger = logging.getLogger(__name__)


class TradingPreferenceAnalyzer:
    """交易偏好分析器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化交易偏好分析器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        
        # 币种分类定义
        self.major_coins = ['BTC', 'ETH']
        self.defi_coins = ['UNI', 'SUSHI', 'AAVE', 'COMP', 'MKR', 'YFI', 'CRV', 'BAL', 'LINK']
        self.meme_coins = ['DOGE', 'SHIB', 'PEPE', 'FLOKI']
        self.layer2_coins = ['MATIC', 'ARB', 'OP', 'LRC']
        
        # logger.info("交易偏好分析器初始化完成")
    
    def analyze_trading_preferences(self, positions: List[PositionData]) -> Dict:
        """
        分析交易偏好
        
        Args:
            positions: 持仓数据列表
            
        Returns:
            Dict: 交易偏好分析结果
        """
        if not positions:
            return self._get_empty_preferences()
        
        # logger.info(f"开始分析交易偏好，持仓数量: {len(positions)}")
        
        try:
            # 1. 币种偏好分析
            coin_preference = self._analyze_coin_preference(positions)
            
            # 2. 时间偏好分析
            time_preference = self._analyze_time_preference(positions)
            
            # 3. 风险偏好分析
            risk_preference = self._analyze_risk_preference(positions)
            
            # 4. 交易规模偏好分析
            size_preference = self._analyze_size_preference(positions)
            
            # 5. 杠杆偏好分析
            leverage_preference = self._analyze_leverage_preference(positions)
            
            result = {
                'coin_preference': coin_preference,
                'time_preference': time_preference,
                'risk_preference': risk_preference,
                'size_preference': size_preference,
                'leverage_preference': leverage_preference
            }
            
            # logger.info("交易偏好分析完成")
            return result
            
        except Exception as e:
            logger.error(f"分析交易偏好时发生错误: {str(e)}")
            return self._get_empty_preferences()
    
    def _analyze_coin_preference(self, positions: List[PositionData]) -> Dict:
        """分析币种偏好"""
        total_volume = sum(pos.total_open_amount for pos in positions)
        
        # 按类别统计交易量
        major_volume = 0
        defi_volume = 0
        meme_volume = 0
        layer2_volume = 0
        others_volume = 0
        
        # 统计各币种交易次数和交易量
        coin_stats = defaultdict(lambda: {'count': 0, 'volume': 0})
        
        for pos in positions:
            contract_upper = pos.contract_name.upper()
            volume = pos.total_open_amount
            
            # 提取币种名称（去掉-USDT等后缀）
            coin_name = contract_upper.split('-')[0] if '-' in contract_upper else contract_upper
            coin_stats[coin_name]['count'] += 1
            coin_stats[coin_name]['volume'] += volume
            
            # 分类统计
            if any(coin in contract_upper for coin in self.major_coins):
                major_volume += volume
            elif any(coin in contract_upper for coin in self.defi_coins):
                defi_volume += volume
            elif any(coin in contract_upper for coin in self.meme_coins):
                meme_volume += volume
            elif any(coin in contract_upper for coin in self.layer2_coins):
                layer2_volume += volume
            else:
                others_volume += volume
        
        # 计算比例
        major_percentage = major_volume / total_volume if total_volume > 0 else 0
        defi_percentage = defi_volume / total_volume if total_volume > 0 else 0
        meme_percentage = meme_volume / total_volume if total_volume > 0 else 0
        layer2_percentage = layer2_volume / total_volume if total_volume > 0 else 0
        others_percentage = others_volume / total_volume if total_volume > 0 else 0
        
        # 找出最喜欢的币种（按交易量排序）
        favorite_coins = sorted(coin_stats.items(), 
                               key=lambda x: x[1]['volume'], 
                               reverse=True)[:5]
        
        # 计算多样化评分
        unique_coins = len(coin_stats)
        diversification_score = min(1.0, unique_coins / 10.0)  # 10种币种为满分
        
        return {
            'major_coins_ratio': major_percentage,
            'defi_percentage': defi_percentage,
            'meme_percentage': meme_percentage,
            'layer2_percentage': layer2_percentage,
            'others_percentage': others_percentage,
            'favorite_contracts': [coin for coin, _ in favorite_coins],
            'diversification_score': diversification_score,
            'unique_coins_count': unique_coins,
            'coin_distribution': dict(coin_stats)
        }
    
    def _analyze_time_preference(self, positions: List[PositionData]) -> Dict:
        """分析时间偏好"""
        hour_counts = defaultdict(int)
        weekday_counts = defaultdict(int)

        for pos in positions:
            if pos.open_time:
                hour = pos.open_time.hour
                weekday = pos.open_time.weekday()  # 0=Monday, 6=Sunday

                hour_counts[hour] += 1
                weekday_counts[weekday] += 1

        # 🚀 修改：生成完整的24小时交易分布统计（0-23小时，每小时对应交易次数）
        # 格式：{"0": 15, "1": 3, "2": 0, ..., "23": 8}
        hourly_trading_distribution = {}
        for hour in range(24):
            hourly_trading_distribution[str(hour)] = hour_counts.get(hour, 0)

        # 找出最活跃的时间段（保留原有逻辑用于其他分析）
        if hour_counts:
            peak_hour = max(hour_counts.items(), key=lambda x: x[1])[0]
            peak_hours = [h for h, count in hour_counts.items()
                         if count >= max(hour_counts.values()) * 0.8]
        else:
            peak_hour = 12
            peak_hours = [12]

        # 找出最活跃的工作日
        weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday',
                        'Friday', 'Saturday', 'Sunday']
        if weekday_counts:
            peak_weekday = max(weekday_counts.items(), key=lambda x: x[1])[0]
            peak_weekday_name = weekday_names[peak_weekday]
        else:
            peak_weekday_name = 'Monday'

        # 🚀 修改：计算时间分布（保持兼容性，使用02d格式）
        time_distribution = {}
        total_trades = sum(hour_counts.values())
        for hour in range(24):
            time_distribution[f"{hour:02d}"] = hour_counts.get(hour, 0)

        return {
            'peak_trading_hours': hourly_trading_distribution,  # 🚀 修改：使用新的24小时分布格式
            'peak_hour': peak_hour,
            'peak_weekday': peak_weekday_name,
            'time_distribution': time_distribution,  # 保留原有格式用于兼容性
            'hour_counts': dict(hour_counts),
            'weekday_counts': dict(weekday_counts),
            'legacy_peak_hours': peak_hours  # 🚀 新增：保留原有的峰值小时列表用于兼容性
        }
    
    def _analyze_risk_preference(self, positions: List[PositionData]) -> Dict:
        """分析风险偏好"""
        if not positions:
            return {'risk_appetite_level': 'medium', 'volatility_preference': 0.5}
        
        # 基于杠杆分析风险偏好
        leverages = [pos.leverage for pos in positions if pos.leverage > 0]
        avg_leverage = np.mean(leverages) if leverages else 1.0
        max_leverage = max(leverages) if leverages else 1.0
        
        # 基于仓位大小分析风险偏好
        position_sizes = [pos.total_open_amount for pos in positions]
        position_cv = np.std(position_sizes) / np.mean(position_sizes) if position_sizes else 0
        
        # 风险等级判断
        if avg_leverage <= 3 and max_leverage <= 5:
            risk_level = 'conservative'
            risk_score = 0.3
        elif avg_leverage <= 5 and max_leverage <= 10:
            risk_level = 'medium'
            risk_score = 0.5
        elif avg_leverage <= 10 and max_leverage <= 20:
            risk_level = 'aggressive'
            risk_score = 0.7
        else:
            risk_level = 'very_aggressive'
            risk_score = 0.9
        
        # 波动性偏好（基于币种选择）
        volatility_preference = min(1.0, (avg_leverage - 1) / 19)  # 1-20倍杠杆映射到0-1
        
        return {
            'risk_appetite_level': risk_level,
            'risk_score': risk_score,
            'volatility_preference': volatility_preference,
            'avg_leverage': avg_leverage,
            'max_leverage': max_leverage,
            'position_size_cv': position_cv
        }
    
    def _analyze_size_preference(self, positions: List[PositionData]) -> Dict:
        """分析交易规模偏好"""
        sizes = [pos.total_open_amount for pos in positions]
        
        if not sizes:
            return {'preferred_size_range': 'small', 'size_consistency': 0.5}
        
        avg_size = np.mean(sizes)
        median_size = np.median(sizes)
        size_std = np.std(sizes)
        size_cv = size_std / avg_size if avg_size > 0 else 0
        
        # 规模偏好分类
        if avg_size < 1000:
            size_range = 'small'
        elif avg_size < 10000:
            size_range = 'medium'
        elif avg_size < 100000:
            size_range = 'large'
        else:
            size_range = 'xlarge'
        
        # 规模一致性（CV越小越一致）
        size_consistency = max(0, 1 - size_cv)
        
        return {
            'preferred_size_range': size_range,
            'avg_size': avg_size,
            'median_size': median_size,
            'size_consistency': size_consistency,
            'size_cv': size_cv
        }
    
    def _analyze_leverage_preference(self, positions: List[PositionData]) -> Dict:
        """分析杠杆偏好"""
        leverages = [pos.leverage for pos in positions if pos.leverage > 0]
        
        if not leverages:
            return {'preferred_leverage_range': 'low', 'leverage_consistency': 0.5}
        
        # 杠杆分布统计
        low_leverage = len([lev for lev in leverages if 1 <= lev <= 5])
        medium_leverage = len([lev for lev in leverages if 5 < lev <= 10])
        high_leverage = len([lev for lev in leverages if lev > 10])
        
        total_trades = len(leverages)
        low_ratio = low_leverage / total_trades
        medium_ratio = medium_leverage / total_trades
        high_ratio = high_leverage / total_trades
        
        # 偏好判断
        if low_ratio >= 0.6:
            preferred_range = 'low'
        elif medium_ratio >= 0.4:
            preferred_range = 'medium'
        else:
            preferred_range = 'high'
        
        # 杠杆一致性
        leverage_std = np.std(leverages)
        leverage_mean = np.mean(leverages)
        leverage_cv = leverage_std / leverage_mean if leverage_mean > 0 else 0
        leverage_consistency = max(0, 1 - leverage_cv / 2)  # 归一化到0-1
        
        return {
            'preferred_leverage_range': preferred_range,
            'low_leverage_trades': low_leverage,
            'medium_leverage_trades': medium_leverage,
            'high_leverage_trades': high_leverage,
            'leverage_consistency': leverage_consistency,
            'avg_leverage': leverage_mean,
            'leverage_cv': leverage_cv
        }
    
    def _get_empty_preferences(self) -> Dict:
        """获取空的偏好分析结果"""
        return {
            'coin_preference': {
                'major_coins_ratio': 0,
                'defi_percentage': 0,
                'others_percentage': 0,
                'diversification_score': 0,
                'favorite_contracts': []
            },
            'time_preference': {
                'peak_trading_hours': [],
                'time_distribution': {}
            },
            'risk_preference': {
                'risk_appetite_level': 'medium',
                'volatility_preference': 0.5
            },
            'size_preference': {
                'preferred_size_range': 'small',
                'size_consistency': 0.5
            },
            'leverage_preference': {
                'preferred_leverage_range': 'low',
                'low_leverage_trades': 0,
                'medium_leverage_trades': 0,
                'high_leverage_trades': 0
            }
        }

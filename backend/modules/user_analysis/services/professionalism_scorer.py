"""
专业度评分计算器
负责基于各项指标计算用户的专业度评分和分类
"""

import logging
from typing import Dict, <PERSON><PERSON>, List
import numpy as np

from modules.user_analysis.models.user_behavior_models import (
    BasicMetrics, DerivedMetrics, CoinWinRateAnalysis,  # HedgeStatistics已移除
    ProfessionalScores, TraderType, FundScale
)

logger = logging.getLogger(__name__)


class ProfessionalismScorer:
    """专业度评分计算器"""
    
    def __init__(self, config: Dict):
        """
        初始化专业度评分计算器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.scoring_weights = config.get('scoring_weights', {})
        self.trader_classification = config.get('trader_classification', {})
        self.fund_scale_thresholds = config.get('fund_scale_thresholds', {})
        
    def calculate_professionalism_score(self,
                                      basic_metrics: BasicMetrics,
                                      derived_metrics: DerivedMetrics,
                                      coin_analysis: CoinWinRateAnalysis,
                                      hedge_statistics = None) -> ProfessionalScores:  # hedge_statistics参数已移除类型注解
        """
        计算专业度评分 - 严格模式，数据不足时明确返回"数据不足"
        """
        # 严格的数据质量验证
        is_valid, error_message = self._validate_input_data(basic_metrics, derived_metrics)
        if not is_valid:
            return ProfessionalScores(
                profitability_score=0.0,
                risk_control_score=0.0,
                trading_behavior_score=0.0,
                market_understanding_score=0.0,
                total_score=0.0,
                trader_type="数据不足",
                confidence_level=0.0
            )
        
        try:
            # 计算各维度评分
            profitability_score = self._calculate_profitability_score(derived_metrics)
            risk_control_score = self._calculate_risk_control_score(derived_metrics)
            trading_behavior_score = self._calculate_trading_behavior_score(derived_metrics)
            market_understanding_score = self._calculate_market_understanding_score(derived_metrics)
            
            # 计算总分
            total_score = self._calculate_total_score(
                profitability_score, risk_control_score,
                trading_behavior_score, market_understanding_score
            )
            
            # 验证计算结果的合理性
            if total_score <= 0 or all(score <= 0 for score in [profitability_score, risk_control_score, trading_behavior_score, market_understanding_score]):
                return ProfessionalScores(
                    total_score=0.0,
                    profitability_score=0.0,
                    risk_control_score=0.0,
                    trading_behavior_score=0.0,
                    market_understanding_score=0.0,
                    trader_type="数据不足",
                    confidence_level=0.0
                )
            
            # 分类交易者类型
            trader_type = self._classify_trader_type(total_score, basic_metrics, coin_analysis)
            
            # 4. 分类资金规模
            fund_scale = self._classify_fund_scale(basic_metrics.total_volume)
            
            # 5. 生成评分详情
            score_details = self._generate_score_details(
                profitability_score, risk_control_score,
                trading_behavior_score, market_understanding_score,
                basic_metrics, derived_metrics, coin_analysis
            )
            
            # 6. 生成改进建议
            improvement_suggestions = self._generate_improvement_suggestions(
                total_score, trader_type, score_details, derived_metrics
            )
            
            result = ProfessionalScores(
                total_score=round(total_score, 2),
                profitability_score=round(profitability_score, 2),
                risk_control_score=round(risk_control_score, 2),
                trading_behavior_score=round(trading_behavior_score, 2),
                market_understanding_score=round(market_understanding_score, 2),
                trader_type=trader_type,
                confidence_level=0.85  # 设置默认置信度
            )
            
            return result
            
        except Exception as e:
            logger.debug(f"计算专业度评分时发生错误: {str(e)}")
            # 异常情况下也返回明确的"数据不足"结果
            return ProfessionalScores(
                total_score=0.0,
                profitability_score=0.0,
                risk_control_score=0.0,
                trading_behavior_score=0.0,
                market_understanding_score=0.0,
                trader_type="计算错误",
                confidence_level=0.0
            )
    
    def _calculate_profitability_score(self, derived_metrics: DerivedMetrics) -> float:
        """计算盈利能力评分 (40% 权重)"""
        # 各子指标权重
        win_rate_weight = 0.35
        profit_loss_ratio_weight = 0.25
        profit_factor_weight = 0.25
        profit_consistency_weight = 0.15
        
        # 胜率评分 (0-100)
        win_rate_score = min(100, derived_metrics.win_rate * 100 / 0.65)  # 65%胜率为满分
        
        # 盈亏比评分 (0-100)
        profit_loss_ratio_score = min(100, derived_metrics.profit_loss_ratio * 100 / 3.0)  # 3:1为满分
        
        # 盈利因子评分 (0-100)
        if derived_metrics.profit_factor >= 999 or derived_metrics.profit_factor == float('inf'):
            profit_factor_score = 100  # 🚀 修复：处理极大值和无穷大
        else:
            profit_factor_score = min(100, (derived_metrics.profit_factor - 1) * 100 / 2.0)  # 盈利因子3为满分
        
        # 盈利一致性评分 (0-100)
        profit_consistency_score = derived_metrics.profit_consistency * 100
        
        # 加权计算
        profitability_score = (
            win_rate_score * win_rate_weight +
            profit_loss_ratio_score * profit_loss_ratio_weight +
            profit_factor_score * profit_factor_weight +
            profit_consistency_score * profit_consistency_weight
        )
        
        return min(100, profitability_score)
    
    def _calculate_risk_control_score(self, derived_metrics: DerivedMetrics) -> float:
        """计算风险控制评分 (25% 权重)"""
        # 各子指标权重
        leverage_weight = 0.4
        leverage_stability_weight = 0.3
        max_loss_weight = 0.3
        
        # 平均杠杆评分 (0-100) - 杠杆越低评分越高
        if derived_metrics.avg_leverage <= 1:
            avg_leverage_score = 100
        elif derived_metrics.avg_leverage <= 5:
            avg_leverage_score = 100 - (derived_metrics.avg_leverage - 1) * 15  # 5倍杠杆得40分
        elif derived_metrics.avg_leverage <= 10:
            avg_leverage_score = 40 - (derived_metrics.avg_leverage - 5) * 6   # 10倍杠杆得10分
        else:
            avg_leverage_score = max(0, 10 - (derived_metrics.avg_leverage - 10) * 2)
        
        # 杠杆稳定性评分 (0-100)
        leverage_stability_score = derived_metrics.leverage_stability * 100
        
        # 最大单笔亏损评分 (0-100) - 相对于平均交易规模
        # 这里简化处理，实际应该基于具体的风险指标
        max_loss_score = 70  # 给一个中等评分，实际需要更复杂的计算
        
        # 加权计算
        risk_control_score = (
            avg_leverage_score * leverage_weight +
            leverage_stability_score * leverage_stability_weight +
            max_loss_score * max_loss_weight
        )
        
        return min(100, risk_control_score)
    
    def _calculate_trading_behavior_score(self, derived_metrics: DerivedMetrics) -> float:
        """计算交易行为评分 (20% 权重)"""
        # 各子指标权重
        frequency_weight = 0.3
        order_type_weight = 0.25
        duration_ratio_weight = 0.25
        position_consistency_weight = 0.2
        
        # 交易频率评分 (0-100) - 合理频率0.5-2笔/天
        frequency = derived_metrics.trading_frequency
        if 0.5 <= frequency <= 2.0:
            frequency_score = 100
        elif frequency < 0.5:
            frequency_score = frequency / 0.5 * 100
        else:  # frequency > 2.0
            frequency_score = max(0, 100 - (frequency - 2.0) / 3.0 * 100)
        
        # 市价单比例评分 (0-100) - 合理比例20-40%
        market_ratio = derived_metrics.market_order_ratio
        if 0.2 <= market_ratio <= 0.4:
            order_type_score = 100
        elif market_ratio < 0.2:
            order_type_score = market_ratio / 0.2 * 100
        else:  # market_ratio > 0.4
            order_type_score = max(0, 100 - (market_ratio - 0.4) / 0.3 * 100)
        
        # 盈亏时长比评分 (0-100) - 理想比例2:1或更高
        duration_ratio = derived_metrics.profit_loss_duration_ratio
        duration_ratio_score = min(100, duration_ratio / 2.0 * 100)
        
        # 仓位规模一致性评分 (0-100)
        position_consistency_score = derived_metrics.position_size_consistency * 100
        
        # 加权计算
        trading_behavior_score = (
            frequency_score * frequency_weight +
            order_type_score * order_type_weight +
            duration_ratio_score * duration_ratio_weight +
            position_consistency_score * position_consistency_weight
        )
        
        return min(100, trading_behavior_score)
    
    def _calculate_market_understanding_score(self, derived_metrics: DerivedMetrics) -> float:
        """计算市场理解评分 (15% 权重)"""
        # 使用更新后的权重：35%/40%/25%
        timing_weight = 0.35      # 从40%调整到35%
        discipline_weight = 0.40  # 从35%调整到40%
        efficiency_weight = 0.25  # 保持25%
        
        # 持仓时机把握能力评分 (0-100)
        timing_score = derived_metrics.position_timing_ability * 100
        
        # 风险管理纪律性评分 (0-100)
        discipline_score = derived_metrics.risk_management_discipline * 100
        
        # 交易执行效率评分 (0-100)
        efficiency_score = derived_metrics.trading_execution_efficiency * 100
        
        # 加权计算
        market_understanding_score = (
            timing_score * timing_weight +
            discipline_score * discipline_weight +
            efficiency_score * efficiency_weight
        )
        
        return min(100, market_understanding_score)
    
    def _calculate_total_score(self, profitability: float, risk_control: float,
                             trading_behavior: float, market_understanding: float) -> float:
        """计算总评分"""
        weights = self.scoring_weights
        
        total_score = (
            profitability * weights.get('profitability', 0.40) +
            risk_control * weights.get('risk_control', 0.25) +
            trading_behavior * weights.get('trading_behavior', 0.20) +
            market_understanding * weights.get('market_understanding', 0.15)
        )
        
        return min(100, total_score)
    
    def _classify_trader_type(self, total_score: float, basic_metrics: BasicMetrics,
                            coin_analysis: CoinWinRateAnalysis) -> str:
        """
        分类交易者类型 - 严格模式
        """
        # 获取配置
        trader_ranges = self.config.get('trader_type_ranges', {})
        new_user_threshold = self.config.get('new_user_criteria', {})
        
        total_trades = basic_metrics.total_trades or 0
        total_volume = basic_metrics.total_volume or 0.0
        
        # 🚀 严格验证：数据不足的情况
        if total_trades < 5 or total_volume < 1000:
            return "数据不足"
        
        # 检查胜率是否合理
        win_rate = (basic_metrics.profitable_count or 0) / max(total_trades, 1)
        if win_rate < 0 or win_rate > 1:
            return "数据不足"
        
        # 根据评分区间分类
        if total_score >= trader_ranges.get('专业交易员', [75, 100])[0]:
            return TraderType.PROFESSIONAL.value
        elif total_score >= trader_ranges.get('半专业交易员', [50, 75])[0]:
            return TraderType.SEMI_PROFESSIONAL.value
        elif total_score >= trader_ranges.get('普通散户', [0, 50])[0]:
            # 检查是否为新用户
            if (total_trades < new_user_threshold.get('max_trades', 20) or
                total_volume < new_user_threshold.get('max_volume', 20000)):
                return TraderType.NEW_USER.value
            else:
                return TraderType.RETAIL.value

        # 🚀 其他情况返回数据不足（评分异常情况）
        return "数据不足"
    
    def _classify_fund_scale(self, total_volume: float) -> str:
        """分类资金规模"""
        thresholds = self.fund_scale_thresholds
        
        if total_volume >= thresholds.get('large', 500000):
            return FundScale.LARGE.value
        elif total_volume >= thresholds.get('medium', 100000):
            return FundScale.MEDIUM.value
        elif total_volume >= thresholds.get('retail', 20000):
            return FundScale.RETAIL.value
        else:
            return FundScale.SMALL.value
    
    def _generate_score_details(self, profitability: float, risk_control: float,
                              trading_behavior: float, market_understanding: float,
                              basic_metrics: BasicMetrics, derived_metrics: DerivedMetrics,
                              coin_analysis: CoinWinRateAnalysis) -> Dict:
        """生成评分详情"""
        return {
            'profitability_details': {
                'score': round(profitability, 2),
                'weight': self.scoring_weights.get('profitability', 0.40),
                'key_metrics': {
                    'win_rate': derived_metrics.win_rate,
                    'profit_loss_ratio': derived_metrics.profit_loss_ratio,
                    'profit_factor': derived_metrics.profit_factor,
                    'profit_consistency': derived_metrics.profit_consistency
                },
                'strengths': self._identify_profitability_strengths(derived_metrics),
                'weaknesses': self._identify_profitability_weaknesses(derived_metrics)
            },
            'risk_control_details': {
                'score': round(risk_control, 2),
                'weight': self.scoring_weights.get('risk_control', 0.25),
                'key_metrics': {
                    'avg_leverage': derived_metrics.avg_leverage,
                    'max_leverage': derived_metrics.max_leverage,
                    'leverage_stability': derived_metrics.leverage_stability
                },
                'strengths': self._identify_risk_control_strengths(derived_metrics),
                'weaknesses': self._identify_risk_control_weaknesses(derived_metrics)
            },
            'trading_behavior_details': {
                'score': round(trading_behavior, 2),
                'weight': self.scoring_weights.get('trading_behavior', 0.20),
                'key_metrics': {
                    'trading_frequency': derived_metrics.trading_frequency,
                    'market_order_ratio': derived_metrics.market_order_ratio,
                    'profit_loss_duration_ratio': derived_metrics.profit_loss_duration_ratio,
                    'position_size_consistency': derived_metrics.position_size_consistency
                },
                'strengths': self._identify_behavior_strengths(derived_metrics),
                'weaknesses': self._identify_behavior_weaknesses(derived_metrics)
            },
            'market_understanding_details': {
                'score': round(market_understanding, 2),
                'weight': self.scoring_weights.get('market_understanding', 0.15),
                'key_metrics': {
                    'position_timing_ability': derived_metrics.position_timing_ability,
                    'risk_management_discipline': derived_metrics.risk_management_discipline,
                    'trading_execution_efficiency': derived_metrics.trading_execution_efficiency
                },
                'strengths': self._identify_market_understanding_strengths(derived_metrics),
                'weaknesses': self._identify_market_understanding_weaknesses(derived_metrics)
            },
            'coin_analysis_summary': {
                'total_analyzed_coins': coin_analysis.total_analyzed_coins,
                'avg_coin_win_rate': coin_analysis.avg_coin_win_rate,
                'expert_coins_count': len(coin_analysis.coin_expertise_summary.get('expert_coins', [])),
                'advantage_coins_count': len(coin_analysis.advantage_coins)
            }
        }
    
    def _identify_profitability_strengths(self, derived_metrics: DerivedMetrics) -> List[str]:
        """识别盈利能力优势"""
        strengths = []
        
        if derived_metrics.win_rate >= 0.6:
            strengths.append(f"胜率优秀 ({derived_metrics.win_rate:.1%})")
        if derived_metrics.profit_loss_ratio >= 2.0:
            strengths.append(f"盈亏比良好 ({derived_metrics.profit_loss_ratio:.2f})")
        if derived_metrics.profit_factor >= 2.0:
            strengths.append(f"盈利因子理想 ({derived_metrics.profit_factor:.2f})")
        if derived_metrics.profit_consistency >= 0.7:
            strengths.append("盈利一致性强")
        
        return strengths
    
    def _identify_profitability_weaknesses(self, derived_metrics: DerivedMetrics) -> List[str]:
        """识别盈利能力弱项"""
        weaknesses = []
        
        if derived_metrics.win_rate < 0.4:
            weaknesses.append(f"胜率偏低 ({derived_metrics.win_rate:.1%})")
        if derived_metrics.profit_loss_ratio < 1.0:
            weaknesses.append(f"盈亏比不足 ({derived_metrics.profit_loss_ratio:.2f})")
        if derived_metrics.profit_factor < 1.2:
            weaknesses.append(f"盈利因子偏低 ({derived_metrics.profit_factor:.2f})")
        if derived_metrics.profit_consistency < 0.4:
            weaknesses.append("盈利一致性差")
        
        return weaknesses
    
    def _identify_risk_control_strengths(self, derived_metrics: DerivedMetrics) -> List[str]:
        """识别风险控制优势"""
        strengths = []
        
        if derived_metrics.avg_leverage <= 3:
            strengths.append(f"杠杆使用保守 ({derived_metrics.avg_leverage:.1f}x)")
        if derived_metrics.leverage_stability >= 0.8:
            strengths.append("杠杆使用稳定")
        if derived_metrics.max_leverage <= 5:
            strengths.append(f"最大杠杆合理 ({derived_metrics.max_leverage:.1f}x)")
        
        return strengths
    
    def _identify_risk_control_weaknesses(self, derived_metrics: DerivedMetrics) -> List[str]:
        """识别风险控制弱项"""
        weaknesses = []
        
        if derived_metrics.avg_leverage > 10:
            weaknesses.append(f"平均杠杆过高 ({derived_metrics.avg_leverage:.1f}x)")
        if derived_metrics.leverage_stability < 0.5:
            weaknesses.append("杠杆使用不稳定")
        if derived_metrics.max_leverage > 20:
            weaknesses.append(f"最大杠杆风险过高 ({derived_metrics.max_leverage:.1f}x)")
        
        return weaknesses
    
    def _identify_behavior_strengths(self, derived_metrics: DerivedMetrics) -> List[str]:
        """识别交易行为优势"""
        strengths = []
        
        if 0.5 <= derived_metrics.trading_frequency <= 2.0:
            strengths.append(f"交易频率合理 ({derived_metrics.trading_frequency:.1f}笔/天)")
        if 0.2 <= derived_metrics.market_order_ratio <= 0.4:
            strengths.append(f"订单类型使用均衡 ({derived_metrics.market_order_ratio:.1%}市价单)")
        if derived_metrics.profit_loss_duration_ratio >= 1.5:
            strengths.append("持仓时间管理良好")
        if derived_metrics.position_size_consistency >= 0.7:
            strengths.append("仓位管理一致")
        
        return strengths
    
    def _identify_behavior_weaknesses(self, derived_metrics: DerivedMetrics) -> List[str]:
        """识别交易行为弱项"""
        weaknesses = []
        
        if derived_metrics.trading_frequency > 5:
            weaknesses.append(f"交易过于频繁 ({derived_metrics.trading_frequency:.1f}笔/天)")
        elif derived_metrics.trading_frequency < 0.2:
            weaknesses.append(f"交易频率过低 ({derived_metrics.trading_frequency:.1f}笔/天)")
        
        if derived_metrics.market_order_ratio > 0.7:
            weaknesses.append("过度依赖市价单")
        elif derived_metrics.market_order_ratio < 0.1:
            weaknesses.append("缺乏灵活性，过度依赖限价单")
        
        if derived_metrics.profit_loss_duration_ratio < 0.8:
            weaknesses.append("止损不够及时")
        if derived_metrics.position_size_consistency < 0.4:
            weaknesses.append("仓位管理不一致")
        
        return weaknesses
    
    def _identify_market_understanding_strengths(self, derived_metrics: DerivedMetrics) -> List[str]:
        """识别市场理解优势"""
        strengths = []
        
        if derived_metrics.position_timing_ability >= 0.7:
            strengths.append("持仓时机把握能力强")
        if derived_metrics.risk_management_discipline >= 0.7:
            strengths.append("风险管理纪律性好")
        if derived_metrics.trading_execution_efficiency >= 0.7:
            strengths.append("交易执行效率高")
        
        return strengths
    
    def _identify_market_understanding_weaknesses(self, derived_metrics: DerivedMetrics) -> List[str]:
        """识别市场理解弱项"""
        weaknesses = []
        
        if derived_metrics.position_timing_ability < 0.4:
            weaknesses.append("持仓时机把握能力待提升")
        if derived_metrics.risk_management_discipline < 0.4:
            weaknesses.append("风险管理纪律性不足")
        if derived_metrics.trading_execution_efficiency < 0.4:
            weaknesses.append("交易执行效率偏低")
        
        return weaknesses
    
    def _generate_improvement_suggestions(self, total_score: float, trader_type: str,
                                        score_details: Dict, derived_metrics: DerivedMetrics) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 基于总分的建议
        if total_score < 40:
            suggestions.append("建议系统学习交易知识，从基础技术分析开始")
            suggestions.append("控制交易频率，专注于提高单笔交易质量")
        elif total_score < 60:
            suggestions.append("在现有基础上优化风险控制，降低杠杆使用")
            suggestions.append("提高止损执行的纪律性")
        elif total_score < 80:
            suggestions.append("继续优化交易策略，提高盈利一致性")
            suggestions.append("可以考虑增加交易品种的多元化")
        
        # 基于具体弱项的建议
        profitability_details = score_details['profitability_details']
        if profitability_details['score'] < 50:
            suggestions.append("重点提升盈利能力：优化入场时机，改善盈亏比")
        
        risk_control_details = score_details['risk_control_details']
        if risk_control_details['score'] < 50:
            suggestions.append("加强风险控制：降低杠杆使用，设置合理止损")
        
        behavior_details = score_details['trading_behavior_details']
        if behavior_details['score'] < 50:
            suggestions.append("优化交易行为：控制交易频率，提高执行一致性")
        
        market_details = score_details['market_understanding_details']
        if market_details['score'] < 50:
            suggestions.append("提升市场理解：加强技术分析学习，提高时机判断")
        
        # 基于交易者类型的建议
        if trader_type == TraderType.NEW_USER.value:
            suggestions.append("新手阶段建议：从小资金开始，积累交易经验")
            suggestions.append("建议学习基础的风险管理知识")
        elif trader_type == TraderType.RETAIL.value:
            suggestions.append("提升建议：专注1-2个熟悉的币种深度研究")
            suggestions.append("建立完整的交易计划和风险控制体系")
        
        return list(set(suggestions))  # 去重
    
    def _validate_input_data(self, basic_metrics: BasicMetrics, derived_metrics: DerivedMetrics) -> tuple[bool, str]:
        """
        严格验证输入数据质量
        
        Returns:
            (is_valid, error_message)
        """
        # 🚀 新增：数据质量验证
        total_trades = basic_metrics.total_trades or 0
        total_volume = basic_metrics.total_volume or 0.0
        
        # 检查基础数据是否充足
        if total_trades < 3:
            return False, f"交易笔数不足：{total_trades} 笔，至少需要3笔"
        
        if total_volume < 10:
            return False, f"交易量不足：{total_volume:.2f} USDT，至少需要10 USDT"
        
        # 检查交易天数
        trading_days = basic_metrics.total_trading_days or 0
        if trading_days < 1:
            return False, "交易天数不足：至少需要1天的交易记录"
        
        # 检查盈亏数据是否有效
        total_pnl = (basic_metrics.total_profit or 0) + (basic_metrics.total_loss or 0)
        if abs(total_pnl) < 1:  # 总盈亏太小，可能是无效数据
            return False, "盈亏数据无效：总盈亏金额过小"
        
        # 检查胜率是否在合理范围内
        win_rate = derived_metrics.win_rate or 0
        if win_rate < 0 or win_rate > 1:
            return False, f"胜率数据异常：{win_rate:.2%}"
        
        # 检查杠杆数据
        avg_leverage = derived_metrics.avg_leverage or 0
        if avg_leverage <= 0 or avg_leverage > 500:  # 🚀 修复：放宽杠杆上限以适应真实交易数据
            return False, f"杠杆数据异常：{avg_leverage:.2f}x"
        
        # 检查核心指标是否全部为零（这通常意味着数据有问题）
        core_indicators = [
            basic_metrics.profitable_count or 0,
            basic_metrics.loss_count or 0,
            basic_metrics.total_volume or 0,
            basic_metrics.total_trades or 0
        ]
        
        if all(indicator == 0 for indicator in core_indicators):
            return False, "核心指标全部为零，数据质量不足"
        
        return True, ""
    
    def get_score_interpretation(self, professionalism_score: ProfessionalScores) -> Dict:
        """获取评分解读"""
        total_score = professionalism_score.total_score
        trader_type = professionalism_score.trader_type
        
        # 评分等级
        if total_score >= 80:
            score_level = "优秀"
            level_description = "交易水平优秀，具备专业交易员素质"
        elif total_score >= 65:
            score_level = "良好"
            level_description = "交易水平良好，有一定的专业基础"
        elif total_score >= 50:
            score_level = "中等"
            level_description = "交易水平中等，有提升空间"
        elif total_score >= 35:
            score_level = "偏低"
            level_description = "交易水平偏低，需要加强学习"
        else:
            score_level = "较差"
            level_description = "交易水平较差，建议系统学习"
        
        return {
            'total_score': total_score,
            'score_level': score_level,
            'level_description': level_description,
            'trader_type': trader_type,
            # 'fund_scale': 资金规模信息在UserBehaviorProfile中
            'percentile_rank': self._calculate_percentile_rank(total_score),
            'next_level_target': self._get_next_level_target(total_score),
            'key_strengths': self._extract_key_strengths(professionalism_score.score_details),
            'priority_improvements': self._extract_priority_improvements(professionalism_score.score_details)
        }
    
    def _calculate_percentile_rank(self, score: float) -> str:
        """计算百分位排名（模拟）"""
        if score >= 80:
            return "前10%"
        elif score >= 65:
            return "前25%"
        elif score >= 50:
            return "前50%"
        elif score >= 35:
            return "前75%"
        else:
            return "后25%"
    
    def _get_next_level_target(self, current_score: float) -> Dict:
        """获取下一级别目标"""
        if current_score < 35:
            return {"target_score": 35, "target_level": "偏低", "improvement_needed": 35 - current_score}
        elif current_score < 50:
            return {"target_score": 50, "target_level": "中等", "improvement_needed": 50 - current_score}
        elif current_score < 65:
            return {"target_score": 65, "target_level": "良好", "improvement_needed": 65 - current_score}
        elif current_score < 80:
            return {"target_score": 80, "target_level": "优秀", "improvement_needed": 80 - current_score}
        else:
            return {"target_score": 100, "target_level": "完美", "improvement_needed": 100 - current_score}
    
    def _extract_key_strengths(self, score_details: Dict) -> List[str]:
        """提取关键优势"""
        strengths = []
        
        for category, details in score_details.items():
            if category.endswith('_details') and details.get('score', 0) >= 70:
                category_strengths = details.get('strengths', [])
                strengths.extend(category_strengths[:2])  # 每个类别最多取2个优势
        
        return strengths[:5]  # 最多返回5个关键优势
    
    def _extract_priority_improvements(self, score_details: Dict) -> List[str]:
        """提取优先改进项"""
        improvements = []
        
        # 找出评分最低的类别
        category_scores = []
        for category, details in score_details.items():
            if category.endswith('_details'):
                category_scores.append((category, details.get('score', 0)))
        
        # 按评分排序，优先改进评分最低的
        category_scores.sort(key=lambda x: x[1])
        
        for category, score in category_scores[:2]:  # 取评分最低的两个类别
            details = score_details[category]
            weaknesses = details.get('weaknesses', [])
            improvements.extend(weaknesses[:2])  # 每个类别最多取2个弱项
        
        return improvements[:4]  # 最多返回4个优先改进项 
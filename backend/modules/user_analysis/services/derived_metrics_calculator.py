"""
衍生指标计算器
负责基于基础指标计算衍生分析指标，包括盈利能力、风险控制、交易行为、市场理解等
"""

import logging
from typing import List, Dict
import numpy as np
from scipy import stats

from modules.user_analysis.models.user_behavior_models import (
    BasicMetrics, DerivedMetrics, PositionData
)

logger = logging.getLogger(__name__)


class DerivedMetricsCalculator:
    """衍生指标计算器"""
    
    def __init__(self, config: Dict):
        """
        初始化衍生指标计算器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.market_weights = config.get('market_understanding_weights', {})
        
    def calculate_derived_metrics(self, basic_metrics: BasicMetrics, 
                                positions: List[PositionData]) -> DerivedMetrics:
        """
        计算衍生指标
        
        Args:
            basic_metrics: 基础指标
            positions: 持仓数据列表
            
        Returns:
            DerivedMetrics: 衍生指标对象
        """
        logger.debug("开始计算衍生指标")
        
        try:
            # 计算盈利能力指标
            profitability_metrics = self._calculate_profitability_metrics(basic_metrics)
            
            # 计算风险控制指标
            risk_control_metrics = self._calculate_risk_control_metrics(basic_metrics, positions)
            
            # 计算交易行为指标
            trading_behavior_metrics = self._calculate_trading_behavior_metrics(
                basic_metrics, positions
            )
            
            # 计算市场理解指标
            market_understanding_metrics = self._calculate_market_understanding_metrics(
                basic_metrics, positions
            )
            
            # 构建衍生指标对象
            derived_metrics = DerivedMetrics(
                # 盈利能力指标 (40% 权重)
                win_rate=profitability_metrics['win_rate'],
                profit_loss_ratio=profitability_metrics['profit_loss_ratio'],
                profit_factor=profitability_metrics['profit_factor'],
                profit_consistency=profitability_metrics['profit_consistency'],
                
                # 风险控制指标 (25% 权重)
                avg_leverage=risk_control_metrics['avg_leverage'],
                max_leverage=risk_control_metrics['max_leverage'],
                leverage_stability=risk_control_metrics['leverage_stability'],
                max_single_loss=risk_control_metrics['max_single_loss'],
                max_single_loss_ratio=risk_control_metrics['max_single_loss_ratio'],
                
                # 交易行为指标 (20% 权重)
                trading_frequency=trading_behavior_metrics['trading_frequency'],
                market_order_ratio=trading_behavior_metrics['market_order_ratio'],
                profit_loss_duration_ratio=trading_behavior_metrics['profit_loss_duration_ratio'],
                position_size_consistency=trading_behavior_metrics['position_size_consistency'],
                
                # 市场理解指标 (15% 权重)
                position_timing_ability=market_understanding_metrics['position_timing_ability'],
                risk_management_discipline=market_understanding_metrics['risk_management_discipline'],
                trading_execution_efficiency=market_understanding_metrics['trading_execution_efficiency']
            )
            
            # 🚀 新增: 计算高级分析指标 (4个字段)
            advanced_metrics = self._calculate_advanced_analysis_metrics(
                basic_metrics, positions, derived_metrics)

            # 添加高级分析指标到衍生指标对象
            derived_metrics.position_consistency = advanced_metrics['position_consistency']
            derived_metrics.timing_ability = advanced_metrics['timing_ability']
            derived_metrics.risk_discipline = advanced_metrics['risk_discipline']
            derived_metrics.execution_efficiency = advanced_metrics['execution_efficiency']

            logger.info(f"衍生指标计算完成: 胜率={derived_metrics.win_rate:.3f}, "
                       f"盈亏比={derived_metrics.profit_loss_ratio:.2f}, "
                       f"平均杠杆={derived_metrics.avg_leverage:.1f}x")

            return derived_metrics
            
        except Exception as e:
            logger.error(f"计算衍生指标时发生错误: {str(e)}")
            raise
    
    def _calculate_profitability_metrics(self, basic_metrics: BasicMetrics) -> Dict:
        """计算盈利能力指标"""
        # 胜率计算
        win_rate = 0.0
        if basic_metrics.total_trades > 0:
            win_rate = basic_metrics.profitable_count / basic_metrics.total_trades
        
        # 盈亏比计算
        profit_loss_ratio = 0.0
        if basic_metrics.loss_count > 0 and basic_metrics.profitable_count > 0:
            avg_profit = basic_metrics.total_profit / basic_metrics.profitable_count
            avg_loss = basic_metrics.total_loss / basic_metrics.loss_count
            if avg_loss > 0:
                profit_loss_ratio = avg_profit / avg_loss
        
        # 盈利因子计算
        profit_factor = 0.0
        if basic_metrics.total_loss > 0:
            profit_factor = basic_metrics.total_profit / basic_metrics.total_loss
        elif basic_metrics.total_profit > 0:
            profit_factor = 999.99  # 🚀 修复：用大数值替代 Infinity，避免 JSON 解析错误
        
        # 盈利一致性计算（基于胜率和盈亏比的综合指标）
        profit_consistency = self._calculate_profit_consistency(
            win_rate, profit_loss_ratio, profit_factor
        )
        
        return {
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'profit_factor': profit_factor,
            'profit_consistency': profit_consistency
        }
    
    def _calculate_risk_control_metrics(self, basic_metrics: BasicMetrics, positions: List[PositionData] = None) -> Dict:
        """计算风险控制指标"""
        # 平均杠杆
        avg_leverage = 0.0
        if basic_metrics.leverage_list:
            avg_leverage = np.mean(basic_metrics.leverage_list)
        
        # 最大杠杆
        max_leverage = 0.0
        if basic_metrics.leverage_list:
            max_leverage = np.max(basic_metrics.leverage_list)
        
        # 杠杆稳定性（基于标准差计算）
        leverage_stability = 0.0
        if len(basic_metrics.leverage_list) > 1:
            leverage_std = np.std(basic_metrics.leverage_list)
            if avg_leverage > 0:
                # 变异系数的倒数，值越大表示越稳定
                leverage_stability = 1.0 / (1.0 + leverage_std / avg_leverage)
        elif len(basic_metrics.leverage_list) == 1:
            leverage_stability = 1.0  # 只有一个值，完全稳定
        
        # 单笔最大亏损（从实际亏损数据获取）
        max_single_loss = 0.0
        max_single_loss_ratio = 0.0
        if positions:
            # 获取所有亏损交易的亏损金额（取绝对值）
            loss_amounts = [abs(pos.total_pnl) for pos in positions if pos.total_pnl < 0]
            max_single_loss = max(loss_amounts) if loss_amounts else 0.0

            # 计算最大单笔亏损比例（相对于总交易量）
            total_volume = basic_metrics.total_volume if basic_metrics else 0.0
            if total_volume > 0 and max_single_loss > 0:
                max_single_loss_ratio = max_single_loss / total_volume

        return {
            'avg_leverage': avg_leverage,
            'max_leverage': max_leverage,
            'leverage_stability': leverage_stability,
            'max_single_loss': max_single_loss,
            'max_single_loss_ratio': max_single_loss_ratio
        }
    
    def _calculate_trading_behavior_metrics(self, basic_metrics: BasicMetrics, 
                                          positions: List[PositionData]) -> Dict:
        """计算交易行为指标"""
        # 交易频率 (笔/天)
        trading_frequency = 0.0
        if basic_metrics.total_trading_days > 0:
            trading_frequency = basic_metrics.total_trades / basic_metrics.total_trading_days
        
        # 市价单比例
        total_orders = (basic_metrics.market_orders_open + basic_metrics.limit_orders_open +
                       basic_metrics.market_orders_close + basic_metrics.limit_orders_close)
        market_order_ratio = 0.0
        if total_orders > 0:
            market_orders = basic_metrics.market_orders_open + basic_metrics.market_orders_close
            market_order_ratio = market_orders / total_orders
        
        # 盈亏时长比
        profit_loss_duration_ratio = 0.0
        if basic_metrics.avg_loss_duration > 0:
            profit_loss_duration_ratio = basic_metrics.avg_profit_duration / basic_metrics.avg_loss_duration
        elif basic_metrics.avg_profit_duration > 0:
            profit_loss_duration_ratio = 2.0  # 如果没有亏损时长，给一个合理的默认值
        
        # 仓位规模一致性
        position_size_consistency = self._calculate_position_size_consistency(
            basic_metrics.position_sizes
        )
        
        return {
            'trading_frequency': trading_frequency,
            'market_order_ratio': market_order_ratio,
            'profit_loss_duration_ratio': profit_loss_duration_ratio,
            'position_size_consistency': position_size_consistency
        }
    
    def _calculate_market_understanding_metrics(self, basic_metrics: BasicMetrics, 
                                              positions: List[PositionData]) -> Dict:
        """计算市场理解指标（更新权重：35%/40%/25%）"""
        
        # 1. 持仓时机把握能力 (35% - 从40%调整)
        timing_ability = self._calculate_timing_ability(basic_metrics, positions)
        
        # 2. 风险管理纪律性 (40% - 从35%调整)
        risk_discipline = self._calculate_risk_discipline(basic_metrics, positions)
        
        # 3. 交易执行效率 (25% - 保持)
        execution_efficiency = self._calculate_execution_efficiency(basic_metrics, positions)
        
        return {
            'position_timing_ability': timing_ability,
            'risk_management_discipline': risk_discipline,
            'trading_execution_efficiency': execution_efficiency
        }
    
    def _calculate_profit_consistency(self, win_rate: float, profit_loss_ratio: float, 
                                    profit_factor: float) -> float:
        """计算盈利一致性"""
        if win_rate == 0 or profit_loss_ratio == 0:
            return 0.0
        
        # 基于胜率和盈亏比的综合评估
        # 理想情况：高胜率 + 高盈亏比 = 高一致性
        consistency_score = 0.0
        
        # 胜率贡献（0.6权重）
        win_rate_score = min(1.0, win_rate / 0.6)  # 60%胜率为满分
        consistency_score += 0.6 * win_rate_score
        
        # 盈亏比贡献（0.4权重）
        ratio_score = min(1.0, profit_loss_ratio / 2.0)  # 2:1盈亏比为满分
        consistency_score += 0.4 * ratio_score
        
        return min(1.0, consistency_score)
    
    def _calculate_position_size_consistency(self, position_sizes: List[float]) -> float:
        """计算仓位规模一致性"""
        if not position_sizes or len(position_sizes) < 2:
            return 1.0
        
        # 使用变异系数计算一致性
        mean_size = np.mean(position_sizes)
        if mean_size == 0:
            return 1.0
        
        std_size = np.std(position_sizes)
        coefficient_of_variation = std_size / mean_size
        
        # 变异系数越小，一致性越高
        # 使用指数衰减函数，变异系数为0时一致性为1，变异系数为1时一致性约为0.37
        consistency = np.exp(-coefficient_of_variation)
        
        return consistency
    
    def _calculate_timing_ability(self, basic_metrics: BasicMetrics, 
                                positions: List[PositionData]) -> float:
        """计算持仓时机把握能力"""
        if not positions:
            return 0.0
        
        # 基于盈利订单与亏损订单的持仓时间对比
        timing_score = 0.0
        
        # 1. 盈利订单应该持有更长时间（让利润奔跑）
        if basic_metrics.avg_profit_duration > 0 and basic_metrics.avg_loss_duration > 0:
            duration_ratio = basic_metrics.avg_profit_duration / basic_metrics.avg_loss_duration
            # 理想比例是2:1或更高
            timing_score += 0.5 * min(1.0, duration_ratio / 2.0)
        
        # 2. 高胜率也反映时机把握能力
        win_rate = basic_metrics.profitable_count / basic_metrics.total_trades if basic_metrics.total_trades > 0 else 0
        timing_score += 0.5 * min(1.0, win_rate / 0.65)  # 65%胜率为满分
        
        return min(1.0, timing_score)
    
    def _calculate_risk_discipline(self, basic_metrics: BasicMetrics, 
                                 positions: List[PositionData]) -> float:
        """计算风险管理纪律性"""
        discipline_score = 0.0
        
        # 1. 杠杆使用纪律性（40%权重）
        if basic_metrics.leverage_list:
            avg_leverage = np.mean(basic_metrics.leverage_list)
            max_leverage = np.max(basic_metrics.leverage_list)
            
            # 合理杠杆范围：1-10倍
            leverage_score = 0.0
            if avg_leverage <= 10:
                leverage_score = 1.0 - (avg_leverage - 1) / 20  # 1倍杠杆得满分，10倍得0.55分
            else:
                leverage_score = max(0.0, 0.5 - (avg_leverage - 10) / 40)  # 超过10倍快速下降
            
            discipline_score += 0.4 * max(0.0, leverage_score)
        
        # 2. 止损纪律性（35%权重）- 基于亏损订单的平均持仓时间
        if basic_metrics.avg_loss_duration > 0 and basic_metrics.avg_profit_duration > 0:
            # 亏损订单应该更快止损
            loss_discipline = min(1.0, basic_metrics.avg_profit_duration / basic_metrics.avg_loss_duration / 2.0)
            discipline_score += 0.35 * loss_discipline
        
        # 3. 仓位管理纪律性（25%权重）
        if basic_metrics.position_sizes:
            position_consistency = self._calculate_position_size_consistency(basic_metrics.position_sizes)
            discipline_score += 0.25 * position_consistency
        
        return min(1.0, discipline_score)
    
    def _calculate_execution_efficiency(self, basic_metrics: BasicMetrics, 
                                      positions: List[PositionData]) -> float:
        """计算交易执行效率"""
        efficiency_score = 0.0
        
        # 1. 订单类型效率（50%权重）
        total_orders = (basic_metrics.market_orders_open + basic_metrics.limit_orders_open +
                       basic_metrics.market_orders_close + basic_metrics.limit_orders_close)
        
        if total_orders > 0:
            # 适当的市价单比例（20-40%为最优）
            market_orders = basic_metrics.market_orders_open + basic_metrics.market_orders_close
            market_ratio = market_orders / total_orders
            
            if 0.2 <= market_ratio <= 0.4:
                order_efficiency = 1.0
            elif market_ratio < 0.2:
                order_efficiency = market_ratio / 0.2
            else:  # market_ratio > 0.4
                order_efficiency = max(0.0, 1.0 - (market_ratio - 0.4) / 0.3)
            
            efficiency_score += 0.5 * order_efficiency
        
        # 2. 交易频率效率（30%权重）
        if basic_metrics.total_trading_days > 0:
            trading_frequency = basic_metrics.total_trades / basic_metrics.total_trading_days
            # 合理的交易频率：0.5-2笔/天
            if 0.5 <= trading_frequency <= 2.0:
                frequency_efficiency = 1.0
            elif trading_frequency < 0.5:
                frequency_efficiency = trading_frequency / 0.5
            else:  # trading_frequency > 2.0
                frequency_efficiency = max(0.0, 1.0 - (trading_frequency - 2.0) / 5.0)
            
            efficiency_score += 0.3 * frequency_efficiency
        
        # 3. 手续费效率（20%权重）
        if basic_metrics.total_volume > 0:
            commission_ratio = basic_metrics.total_commission / basic_metrics.total_volume
            # 手续费比例越低越好，0.1%以下为满分
            commission_efficiency = max(0.0, 1.0 - commission_ratio / 0.001)
            efficiency_score += 0.2 * commission_efficiency
        
        return min(1.0, efficiency_score)
    
    def get_derived_metrics_summary(self, derived_metrics: DerivedMetrics) -> Dict:
        """
        获取衍生指标摘要
        
        Args:
            derived_metrics: 衍生指标对象
            
        Returns:
            Dict: 衍生指标摘要
        """
        summary = {
            # 盈利能力指标
            'profitability': {
                'win_rate': derived_metrics.win_rate,
                'profit_loss_ratio': derived_metrics.profit_loss_ratio,
                'profit_factor': derived_metrics.profit_factor,
                'profit_consistency': derived_metrics.profit_consistency
            },
            
            # 风险控制指标
            'risk_control': {
                'avg_leverage': derived_metrics.avg_leverage,
                'max_leverage': derived_metrics.max_leverage,
                'leverage_stability': derived_metrics.leverage_stability,
                'max_single_loss': derived_metrics.max_single_loss,
                'max_single_loss_ratio': derived_metrics.max_single_loss_ratio
            },
            
            # 交易行为指标
            'trading_behavior': {
                'trading_frequency': derived_metrics.trading_frequency,
                'market_order_ratio': derived_metrics.market_order_ratio,
                'profit_loss_duration_ratio': derived_metrics.profit_loss_duration_ratio,
                'position_size_consistency': derived_metrics.position_size_consistency
            },
            
            # 市场理解指标
            'market_understanding': {
                'position_timing_ability': derived_metrics.position_timing_ability,
                'risk_management_discipline': derived_metrics.risk_management_discipline,
                'trading_execution_efficiency': derived_metrics.trading_execution_efficiency
            }
        }
        
        return summary

    def _calculate_advanced_analysis_metrics(self, basic_metrics: BasicMetrics,
                                           positions: List[PositionData],
                                           derived_metrics: DerivedMetrics) -> Dict:
        """计算高级分析指标 (4个新字段)"""

        # 1. 仓位一致性 (position_consistency)
        position_consistency = self._calculate_position_consistency_advanced(positions)

        # 2. 时机把握能力 (timing_ability)
        timing_ability = self._calculate_timing_ability_advanced(positions, basic_metrics)

        # 3. 风险纪律性 (risk_discipline)
        risk_discipline = self._calculate_risk_discipline_advanced(positions, derived_metrics)

        # 4. 执行效率 (execution_efficiency)
        execution_efficiency = self._calculate_execution_efficiency_advanced(positions, basic_metrics)

        return {
            'position_consistency': position_consistency,
            'timing_ability': timing_ability,
            'risk_discipline': risk_discipline,
            'execution_efficiency': execution_efficiency
        }

    def _calculate_position_consistency_advanced(self, positions: List[PositionData]) -> float:
        """计算仓位一致性 - 高级版本"""
        if not positions:
            return 0.0

        try:
            # 基于仓位大小的一致性
            position_sizes = [pos.total_open_amount for pos in positions]
            size_cv = np.std(position_sizes) / np.mean(position_sizes) if position_sizes else 0
            size_consistency = max(0, 1 - size_cv)

            # 基于杠杆使用的一致性
            leverages = [pos.leverage for pos in positions if pos.leverage > 0]
            if leverages:
                leverage_cv = np.std(leverages) / np.mean(leverages)
                leverage_consistency = max(0, 1 - leverage_cv)
            else:
                leverage_consistency = 0.5

            # 基于持仓时间的一致性 - 🚀 修复：只使用已完成持仓
            completed_positions = [pos for pos in positions if pos.close_time is not None]
            durations = [pos.duration_minutes for pos in completed_positions if pos.duration_minutes > 0]
            if durations:
                duration_cv = np.std(durations) / np.mean(durations)
                duration_consistency = max(0, 1 - duration_cv / 2)  # 时间变化容忍度更高
            else:
                duration_consistency = 0.5

            # 综合一致性评分 (权重: 仓位40%, 杠杆35%, 时间25%)
            overall_consistency = (
                size_consistency * 0.4 +
                leverage_consistency * 0.35 +
                duration_consistency * 0.25
            )

            return min(1.0, max(0.0, overall_consistency))

        except Exception as e:
            logger.warning(f"计算仓位一致性时发生错误: {str(e)}")
            return 0.5

    def _calculate_timing_ability_advanced(self, positions: List[PositionData],
                                         basic_metrics: BasicMetrics) -> float:
        """计算时机把握能力 - 高级版本"""
        if not positions:
            return 0.0

        try:
            # 1. 基于盈利交易的时机把握 - 🚀 修复：只使用已完成持仓
            completed_positions = [pos for pos in positions if pos.close_time is not None]
            profitable_positions = [pos for pos in completed_positions if pos.total_pnl > 0]
            loss_positions = [pos for pos in completed_positions if pos.total_pnl < 0]

            # 盈利交易的平均持仓时间相对合理性
            if profitable_positions:
                profit_durations = [pos.duration_minutes for pos in profitable_positions]
                avg_profit_duration = np.mean(profit_durations)
                # 理想的盈利持仓时间: 2-24小时 (120-1440分钟)
                if 120 <= avg_profit_duration <= 1440:
                    profit_timing_score = 1.0
                elif avg_profit_duration < 120:
                    profit_timing_score = avg_profit_duration / 120
                else:
                    profit_timing_score = max(0.3, 1440 / avg_profit_duration)
            else:
                profit_timing_score = 0.0

            # 2. 基于亏损控制的时机把握
            if loss_positions:
                loss_durations = [pos.duration_minutes for pos in loss_positions]
                avg_loss_duration = np.mean(loss_durations)
                # 理想的亏损持仓时间: 越短越好，但不能太短 (30-360分钟)
                if 30 <= avg_loss_duration <= 360:
                    loss_timing_score = 1.0 - (avg_loss_duration - 30) / 330 * 0.5
                elif avg_loss_duration < 30:
                    loss_timing_score = 0.5  # 太快可能是冲动
                else:
                    loss_timing_score = max(0.1, 360 / avg_loss_duration)
            else:
                loss_timing_score = 1.0  # 没有亏损是好事

            # 3. 盈亏时长比的合理性
            duration_ratio = getattr(basic_metrics, 'profit_loss_duration_ratio', 1.0)
            if duration_ratio >= 1.5:
                duration_ratio_score = min(1.0, duration_ratio / 3.0)
            else:
                duration_ratio_score = duration_ratio / 1.5 * 0.7

            # 综合时机把握评分 (权重: 盈利时机40%, 亏损控制40%, 时长比20%)
            timing_ability = (
                profit_timing_score * 0.4 +
                loss_timing_score * 0.4 +
                duration_ratio_score * 0.2
            )

            return min(1.0, max(0.0, timing_ability))

        except Exception as e:
            logger.warning(f"计算时机把握能力时发生错误: {str(e)}")
            return 0.5

    def _calculate_risk_discipline_advanced(self, positions: List[PositionData],
                                          derived_metrics: DerivedMetrics) -> float:
        """计算风险纪律性 - 高级版本"""
        if not positions:
            return 0.0

        try:
            # 1. 杠杆使用纪律性
            leverages = [pos.leverage for pos in positions if pos.leverage > 0]
            if leverages:
                max_leverage = max(leverages)
                avg_leverage = np.mean(leverages)

                # 杠杆控制评分 (低杠杆高分)
                if avg_leverage <= 3:
                    leverage_discipline = 1.0
                elif avg_leverage <= 5:
                    leverage_discipline = 0.8
                elif avg_leverage <= 10:
                    leverage_discipline = 0.6
                else:
                    leverage_discipline = max(0.2, 1 - (avg_leverage - 10) / 20)

                # 杠杆一致性评分
                leverage_cv = np.std(leverages) / np.mean(leverages)
                leverage_consistency = max(0, 1 - leverage_cv)
            else:
                leverage_discipline = 0.5
                leverage_consistency = 0.5

            # 2. 止损纪律性 (基于亏损交易的持仓时间)
            loss_positions = [pos for pos in positions if pos.total_pnl < 0]
            if loss_positions:
                loss_durations = [pos.duration_minutes for pos in loss_positions]
                avg_loss_duration = np.mean(loss_durations)

                # 理想的止损时间: 30分钟-6小时
                if 30 <= avg_loss_duration <= 360:
                    stop_loss_discipline = 1.0
                elif avg_loss_duration < 30:
                    stop_loss_discipline = 0.7  # 太快止损可能是恐慌
                else:
                    stop_loss_discipline = max(0.2, 360 / avg_loss_duration)
            else:
                stop_loss_discipline = 1.0  # 没有亏损交易

            # 3. 仓位控制纪律性
            position_sizes = [pos.total_open_amount for pos in positions]
            if position_sizes:
                size_cv = np.std(position_sizes) / np.mean(position_sizes)
                position_discipline = max(0, 1 - size_cv / 2)  # 仓位变化容忍度
            else:
                position_discipline = 0.5

            # 综合风险纪律评分 (权重: 杠杆40%, 止损35%, 仓位25%)
            risk_discipline = (
                (leverage_discipline * 0.7 + leverage_consistency * 0.3) * 0.4 +
                stop_loss_discipline * 0.35 +
                position_discipline * 0.25
            )

            return min(1.0, max(0.0, risk_discipline))

        except Exception as e:
            logger.warning(f"计算风险纪律性时发生错误: {str(e)}")
            return 0.5

    def _calculate_execution_efficiency_advanced(self, positions: List[PositionData],
                                               basic_metrics: BasicMetrics) -> float:
        """计算执行效率 - 高级版本"""
        if not positions:
            return 0.0

        try:
            # 1. 订单执行效率 (基于市价单/限价单比例)
            total_market_orders = sum(pos.market_orders_open + pos.market_orders_close for pos in positions)
            total_limit_orders = sum(pos.limit_orders_open + pos.limit_orders_close for pos in positions)
            total_orders = total_market_orders + total_limit_orders

            if total_orders > 0:
                market_ratio = total_market_orders / total_orders
                # 理想的市价单比例: 20-40%
                if 0.2 <= market_ratio <= 0.4:
                    order_efficiency = 1.0
                elif market_ratio < 0.2:
                    order_efficiency = market_ratio / 0.2 * 0.8 + 0.2
                else:
                    order_efficiency = max(0.3, 1 - (market_ratio - 0.4) / 0.3 * 0.7)
            else:
                order_efficiency = 0.5

            # 2. 交易频率效率
            total_trading_days = getattr(basic_metrics, 'total_trading_days', 1)
            if total_trading_days > 0:
                trading_frequency = len(positions) / total_trading_days
                # 理想的交易频率: 0.5-2笔/天
                if 0.5 <= trading_frequency <= 2.0:
                    frequency_efficiency = 1.0
                elif trading_frequency < 0.5:
                    frequency_efficiency = trading_frequency / 0.5 * 0.8 + 0.2
                else:
                    frequency_efficiency = max(0.3, 2.0 / trading_frequency)
            else:
                frequency_efficiency = 0.5

            # 3. 盈利效率 (盈利交易的平均收益率)
            profitable_positions = [pos for pos in positions if pos.total_pnl > 0]
            if profitable_positions:
                profit_rates = []
                for pos in profitable_positions:
                    if pos.total_open_amount > 0:
                        profit_rate = pos.total_pnl / pos.total_open_amount
                        profit_rates.append(profit_rate)

                if profit_rates:
                    avg_profit_rate = np.mean(profit_rates)
                    # 理想的平均盈利率: 5-20%
                    if 0.05 <= avg_profit_rate <= 0.2:
                        profit_efficiency = 1.0
                    elif avg_profit_rate < 0.05:
                        profit_efficiency = avg_profit_rate / 0.05 * 0.8 + 0.2
                    else:
                        profit_efficiency = min(1.0, 0.2 / avg_profit_rate + 0.5)
                else:
                    profit_efficiency = 0.5
            else:
                profit_efficiency = 0.0

            # 4. 时间效率 (基于持仓时间的合理性) - 🚀 修复：只使用已完成持仓
            completed_positions = [pos for pos in positions if pos.close_time is not None]
            durations = [pos.duration_minutes for pos in completed_positions if pos.duration_minutes > 0]
            if durations:
                avg_duration = np.mean(durations)
                # 理想的平均持仓时间: 1-12小时 (60-720分钟)
                if 60 <= avg_duration <= 720:
                    time_efficiency = 1.0
                elif avg_duration < 60:
                    time_efficiency = avg_duration / 60 * 0.7 + 0.3
                else:
                    time_efficiency = max(0.3, 720 / avg_duration)
            else:
                time_efficiency = 0.5

            # 综合执行效率评分 (权重: 订单25%, 频率25%, 盈利30%, 时间20%)
            execution_efficiency = (
                order_efficiency * 0.25 +
                frequency_efficiency * 0.25 +
                profit_efficiency * 0.30 +
                time_efficiency * 0.20
            )

            return min(1.0, max(0.0, execution_efficiency))

        except Exception as e:
            logger.warning(f"计算执行效率时发生错误: {str(e)}")
            return 0.5
"""
基础指标计算器
负责计算用户交易的基础统计指标，包括交易规模、盈亏统计、持仓时间等
"""

import logging
from typing import List, Dict, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
import numpy as np

from modules.user_analysis.models.user_behavior_models import (
    BasicMetrics, PositionData
)

logger = logging.getLogger(__name__)


class BasicMetricsCalculator:
    """基础指标计算器"""
    
    def __init__(self, config: Dict):
        """
        初始化基础指标计算器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.thresholds = config.get('thresholds', {})
        
    def calculate_basic_metrics(self, positions: List[PositionData]) -> BasicMetrics:
        """
        计算基础指标
        
        Args:
            positions: 持仓数据列表
            
        Returns:
            BasicMetrics: 基础指标对象
        """
        if not positions:
            logger.warning("持仓数据为空，返回默认基础指标")
            return BasicMetrics()
        
        logger.debug(f"开始计算基础指标，持仓数量: {len(positions)}")
        
        try:
            # 计算交易规模指标
            volume_metrics = self._calculate_volume_metrics(positions)
            
            # 计算盈亏统计指标
            pnl_metrics = self._calculate_pnl_metrics(positions)
            
            # 计算持仓时间指标
            time_metrics = self._calculate_time_metrics(positions)
            
            # 计算订单类型分布
            order_type_metrics = self._calculate_order_type_metrics(positions)

            # 🚀 新增：计算仓位模式分布
            margin_mode_metrics = self._calculate_margin_mode_metrics(positions)

            # 计算风险控制指标
            risk_metrics = self._calculate_risk_metrics(positions)
            
            # 计算资金规模分布
            size_distribution = self._calculate_size_distribution(positions)
            
            # 🚀 计算最大最小交易信息
            max_min_metrics = self._calculate_max_min_trade_metrics(positions)
            
            # 🚀 修复: 计算杠杆分布
            leverage_distribution = self._calculate_leverage_distribution_extended(positions)
            
            # 🚀 修复: 计算手续费率
            fee_ratio = 0.0
            if volume_metrics['total_volume'] > 0:
                fee_ratio = risk_metrics['total_commission'] / volume_metrics['total_volume']

            # 构建基础指标对象
            basic_metrics = BasicMetrics(
                # 交易规模指标
                total_volume=volume_metrics['total_volume'],
                total_trades=volume_metrics['total_trades'],
                avg_trade_size=volume_metrics['avg_trade_size'],
                total_positions=len(positions),
                completed_positions=len([pos for pos in positions if pos.close_time is not None]),
                
                # 🚀 新增：最大最小交易指标
                max_trade_amount=max_min_metrics['max_trade_amount'],
                max_trade_contract=max_min_metrics['max_trade_contract'],
                min_trade_amount=max_min_metrics['min_trade_amount'],
                min_trade_contract=max_min_metrics['min_trade_contract'],
                
                # 盈亏统计指标
                profitable_count=pnl_metrics['profitable_count'],
                loss_count=pnl_metrics['loss_count'],
                total_profit=pnl_metrics['total_profit'],
                total_loss=pnl_metrics['total_loss'],
                
                # 持仓时间指标
                avg_profit_duration=time_metrics['avg_profit_duration'],
                avg_loss_duration=time_metrics['avg_loss_duration'],
                total_trading_days=time_metrics['total_trading_days'],
                min_holding_time=time_metrics['min_holding_time'],  # 🚀 新增
                
                # 订单类型分布
                market_orders_open=order_type_metrics['market_orders_open'],
                limit_orders_open=order_type_metrics['limit_orders_open'],
                market_orders_close=order_type_metrics['market_orders_close'],
                limit_orders_close=order_type_metrics['limit_orders_close'],
                open_limit_orders=order_type_metrics['limit_orders_open'],  # 🚀 新增：等于limit_orders_open
                close_limit_orders=order_type_metrics['limit_orders_close'],  # 🚀 新增：等于limit_orders_close

                # 🚀 新增：仓位模式分布
                cross_margin_positions=margin_mode_metrics['cross_margin_positions'],
                isolated_margin_positions=margin_mode_metrics['isolated_margin_positions'],
                cross_margin_ratio=margin_mode_metrics['cross_margin_ratio'],
                isolated_margin_ratio=margin_mode_metrics['isolated_margin_ratio'],

                # 风险控制指标
                leverage_list=risk_metrics['leverage_list'],
                total_commission=risk_metrics['total_commission'],
                fee_ratio=fee_ratio,  # 🚀 新增
                medium_leverage_trades=leverage_distribution['medium_leverage_trades'],  # 🚀 新增
                high_leverage_trades=leverage_distribution['high_leverage_trades'],  # 🚀 新增
                low_leverage_ratio=leverage_distribution.get('low_leverage_ratio', 0.0),  # 🚀 新增
                high_leverage_ratio=leverage_distribution.get('high_leverage_ratio', 0.0),  # 🚀 新增
                
                # 资金规模分布
                volume_distribution=size_distribution['volume_distribution'],
                position_sizes=size_distribution['position_sizes'],
                
                # 真实交易规模（初始值等于总交易量，后续会被异常数据分析调整）
                real_trading_volume=volume_metrics['total_volume']
            )
            
            logger.info(f"基础指标计算完成: 总交易量={basic_metrics.total_volume:.2f}, "
                       f"总交易笔数={basic_metrics.total_trades}, "
                       f"胜率={basic_metrics.profitable_count / basic_metrics.total_trades * 100:.1f}%")
            
            return basic_metrics
            
        except Exception as e:
            logger.error(f"计算基础指标时发生错误: {str(e)}")
            raise
    
    def _calculate_volume_metrics(self, positions: List[PositionData]) -> Dict:
        """计算交易规模指标"""
        # 🚀 修复：买卖都要计算交易量，因为都要收手续费
        # total_volume = 开仓金额 + 平仓金额
        total_volume = sum(pos.total_open_amount + pos.total_close_amount for pos in positions)
        total_trades = len(positions)
        avg_trade_size = total_volume / total_trades if total_trades > 0 else 0.0

        return {
            'total_volume': total_volume,
            'total_trades': total_trades,
            'avg_trade_size': avg_trade_size
        }
    
    def _calculate_pnl_metrics(self, positions: List[PositionData]) -> Dict:
        """计算盈亏统计指标"""
        profitable_positions = [pos for pos in positions if pos.is_profitable]
        loss_positions = [pos for pos in positions if not pos.is_profitable]
        
        profitable_count = len(profitable_positions)
        loss_count = len(loss_positions)
        
        total_profit = sum(pos.total_pnl for pos in profitable_positions)
        total_loss = sum(abs(pos.total_pnl) for pos in loss_positions)  # 转为正数
        
        return {
            'profitable_count': profitable_count,
            'loss_count': loss_count,
            'total_profit': total_profit,
            'total_loss': total_loss
        }
    
    def _calculate_time_metrics(self, positions: List[PositionData]) -> Dict:
        """计算持仓时间指标 - 修复版本：只计算已完成持仓的时间"""
        # 🚀 修复：只使用已完成的持仓（close_time 不为 None）来计算持续时间
        completed_positions = [pos for pos in positions if pos.close_time is not None]

        profitable_positions = [pos for pos in completed_positions if pos.is_profitable]
        loss_positions = [pos for pos in completed_positions if not pos.is_profitable]

        # 计算平均持仓时间 - 只基于已完成的持仓
        avg_profit_duration = 0.0
        if profitable_positions:
            avg_profit_duration = np.mean([pos.duration_minutes for pos in profitable_positions])

        avg_loss_duration = 0.0
        if loss_positions:
            avg_loss_duration = np.mean([pos.duration_minutes for pos in loss_positions])

        # 🚀 修复: 计算总交易天数 - 处理边界情况
        total_trading_days = 0
        if positions:
            try:
                # 过滤掉close_time为None的记录
                valid_positions = [pos for pos in positions if pos.close_time is not None]

                if valid_positions:
                    start_time = min(pos.open_time for pos in valid_positions)
                    end_time = max(pos.close_time for pos in valid_positions)
                    total_trading_days = (end_time.date() - start_time.date()).days + 1
                else:
                    # 如果没有有效的close_time，使用open_time计算
                    start_time = min(pos.open_time for pos in positions)
                    end_time = max(pos.open_time for pos in positions)
                    total_trading_days = (end_time.date() - start_time.date()).days + 1

            except Exception as e:
                logger.warning(f"计算总交易天数时发生错误: {str(e)}")
                total_trading_days = 1  # 默认值

        # 🚀 修复: 计算最小持仓时间 - 只使用已完成的持仓
        min_holding_time = 0.0
        if completed_positions:
            valid_durations = [pos.duration_minutes for pos in completed_positions if pos.duration_minutes > 0]
            if valid_durations:
                min_holding_time = min(valid_durations)

        return {
            'avg_profit_duration': avg_profit_duration,
            'avg_loss_duration': avg_loss_duration,
            'total_trading_days': total_trading_days,
            'min_holding_time': min_holding_time  # 🚀 新增
        }
    
    def _calculate_order_type_metrics(self, positions: List[PositionData]) -> Dict:
        """计算订单类型分布"""
        market_orders_open = sum(pos.market_orders_open for pos in positions)
        limit_orders_open = sum(pos.limit_orders_open for pos in positions)
        market_orders_close = sum(pos.market_orders_close for pos in positions)
        limit_orders_close = sum(pos.limit_orders_close for pos in positions)

        return {
            'market_orders_open': market_orders_open,
            'limit_orders_open': limit_orders_open,
            'market_orders_close': market_orders_close,
            'limit_orders_close': limit_orders_close
        }

    def _calculate_margin_mode_metrics(self, positions: List[PositionData]) -> Dict:
        """计算仓位模式分布"""
        cross_margin_positions = sum(pos.cross_margin_positions for pos in positions)
        isolated_margin_positions = sum(pos.isolated_margin_positions for pos in positions)

        total_positions = cross_margin_positions + isolated_margin_positions
        cross_margin_ratio = cross_margin_positions / total_positions if total_positions > 0 else 0
        isolated_margin_ratio = isolated_margin_positions / total_positions if total_positions > 0 else 0

        return {
            'cross_margin_positions': cross_margin_positions,
            'isolated_margin_positions': isolated_margin_positions,
            'cross_margin_ratio': cross_margin_ratio,
            'isolated_margin_ratio': isolated_margin_ratio
        }
    
    def _calculate_risk_metrics(self, positions: List[PositionData]) -> Dict:
        """计算风险控制指标"""
        leverage_list = [pos.leverage for pos in positions if pos.leverage > 0]
        total_commission = sum(pos.total_commission for pos in positions)
        
        return {
            'leverage_list': leverage_list,
            'total_commission': total_commission
        }
    
    def _calculate_size_distribution(self, positions: List[PositionData]) -> Dict:
        """计算资金规模分布"""
        position_sizes = [pos.total_open_amount for pos in positions]
        
        # 计算交易量分布
        volume_distribution = self._calculate_volume_distribution(position_sizes)
        
        return {
            'volume_distribution': volume_distribution,
            'position_sizes': position_sizes
        }
    
    def _calculate_volume_distribution(self, position_sizes: List[float]) -> Dict:
        """计算交易量分布统计"""
        if not position_sizes:
            return {}
        
        position_sizes_array = np.array(position_sizes)
        
        # 定义区间
        percentiles = [10, 25, 50, 75, 90, 95, 99]
        distribution = {}
        
        for p in percentiles:
            distribution[f'p{p}'] = float(np.percentile(position_sizes_array, p))
        
        # 添加基础统计
        distribution.update({
            'min': float(np.min(position_sizes_array)),
            'max': float(np.max(position_sizes_array)),
            'mean': float(np.mean(position_sizes_array)),
            'std': float(np.std(position_sizes_array)),
            'count': len(position_sizes)
        })
        
        return distribution
    
    def validate_data_quality(self, positions: List[PositionData]) -> Tuple[bool, str]:
        """
        验证数据质量
        
        Args:
            positions: 持仓数据列表
            
        Returns:
            Tuple[bool, str]: (是否通过验证, 验证信息)
        """
        if not positions:
            return False, "持仓数据为空"
        
        # 检查最小交易量阈值
        total_volume = sum(pos.total_open_amount for pos in positions)
        min_volume = self.thresholds.get('min_total_volume', 10000)
        
        if total_volume < min_volume:
            return False, f"总交易量{total_volume:.2f}低于最小阈值{min_volume}"
        
        # 检查最小交易笔数阈值
        total_trades = len(positions)
        min_trades = self.thresholds.get('min_trades_count', 10)
        
        if total_trades < min_trades:
            return False, f"总交易笔数{total_trades}低于最小阈值{min_trades}"
        
        # 检查数据完整性 - 🚀 修复: close_time 为 None 不应视为不完整数据
        incomplete_data_count = 0
        for pos in positions:
            if (pos.total_open_amount <= 0 or
                pos.open_time is None):
                incomplete_data_count += 1
        
        incomplete_ratio = incomplete_data_count / total_trades
        if incomplete_ratio > 0.1:  # 超过10%的数据不完整
            return False, f"数据不完整比例{incomplete_ratio:.1%}过高"
        
        return True, "数据质量验证通过"
    
    def get_metrics_summary(self, basic_metrics: BasicMetrics) -> Dict:
        """
        获取指标摘要
        
        Args:
            basic_metrics: 基础指标对象
            
        Returns:
            Dict: 指标摘要
        """
        summary = {
            'total_volume': basic_metrics.total_volume,
            'total_trades': basic_metrics.total_trades,
            'avg_trade_size': basic_metrics.avg_trade_size,
            'win_rate': (basic_metrics.profitable_count / basic_metrics.total_trades 
                        if basic_metrics.total_trades > 0 else 0),
            'total_pnl': basic_metrics.total_profit - basic_metrics.total_loss,
            'total_trading_days': basic_metrics.total_trading_days,
            'avg_leverage': (np.mean(basic_metrics.leverage_list) 
                           if basic_metrics.leverage_list else 0),
            'total_commission': basic_metrics.total_commission
        }
        
        return summary



    def _calculate_leverage_distribution_extended(self, positions: List[PositionData]) -> Dict:
        """计算杠杆分布扩展 - 调整为1-49倍和50-400倍两档"""
        low_leverage = len([pos for pos in positions if 1 <= pos.leverage <= 49])
        high_leverage = len([pos for pos in positions if 50 <= pos.leverage <= 400])
        total_leverage_trades = low_leverage + high_leverage

        # 计算比例
        low_leverage_ratio = (low_leverage / total_leverage_trades) if total_leverage_trades > 0 else 0.0
        high_leverage_ratio = (high_leverage / total_leverage_trades) if total_leverage_trades > 0 else 0.0

        return {
            'low_leverage_trades': low_leverage,
            'medium_leverage_trades': 0,  # 保持字段兼容性，但不使用
            'high_leverage_trades': high_leverage,
            'low_leverage_ratio': low_leverage_ratio,
            'high_leverage_ratio': high_leverage_ratio
        }

    def _calculate_coin_preference_distribution_extended(self, positions: List[PositionData]) -> Dict:
        """计算币种偏好分布扩展"""
        # 定义币种分类
        major_coins = ['BTC', 'ETH']
        defi_coins = ['UNI', 'SUSHI', 'AAVE', 'COMP', 'MKR', 'YFI', 'CRV', 'BAL']

        total_volume = sum(pos.total_open_amount for pos in positions)
        major_volume = 0
        defi_volume = 0

        for pos in positions:
            contract_upper = pos.contract_name.upper()
            if any(coin in contract_upper for coin in major_coins):
                major_volume += pos.total_open_amount
            elif any(coin in contract_upper for coin in defi_coins):
                defi_volume += pos.total_open_amount

        altcoin_volume = total_volume - major_volume - defi_volume

        return {
            'major_coins_ratio': major_volume / total_volume if total_volume > 0 else 0,
            'defi_percentage': defi_volume / total_volume if total_volume > 0 else 0,
            'altcoins_ratio': altcoin_volume / total_volume if total_volume > 0 else 0,
            'others_percentage': 0  # 其他特殊币种
        }

    # 🚀 已清理：_calculate_order_type_distribution_extended 方法
    # 清理原因：与 _calculate_order_type_metrics 功能重复
    # 统一使用主路径的 _calculate_order_type_metrics 方法
    
    def calculate_extended_metrics(self, positions: List[PositionData]) -> Dict:
        """
        计算扩展指标，包括最大最小交易、币种偏好、时间分布等
        
        Args:
            positions: 持仓数据列表
            
        Returns:
            Dict: 扩展指标字典
        """
        if not positions:
            return self._get_empty_extended_metrics()
        
        logger.debug(f"开始计算扩展指标，持仓数量: {len(positions)}")
        
        try:
            # 计算最大最小交易信息
            max_min_metrics = self._calculate_max_min_trade_metrics(positions)
            
            # 计算币种偏好分布
            coin_preference = self._calculate_coin_preference_distribution(positions)
            
            # 计算时间分布
            time_distribution = self._calculate_time_distribution(positions)
            
            # 计算杠杆分布
            leverage_distribution = self._calculate_leverage_distribution(positions)
            
            # 🚀 修复：使用主路径的订单类型计算方法
            order_type_distribution = self._calculate_order_type_metrics(positions)
            
            # 计算仓位规模分布
            position_size_distribution = self._calculate_position_size_distribution(positions)

            # 🚀 添加缺失的扩展指标计算
            additional_metrics = self._calculate_additional_extended_metrics(positions)

            extended_metrics = {
                # 原有字段（保持兼容性）
                'max_trade_amount': max_min_metrics['max_trade_amount'],
                'max_trade_contract': max_min_metrics['max_trade_contract'],
                'min_trade_amount': max_min_metrics['min_trade_amount'],
                'min_trade_contract': max_min_metrics['min_trade_contract'],
                'coin_preference_distribution': coin_preference,
                'time_distribution': time_distribution,
                'leverage_distribution': leverage_distribution,
                'order_type_distribution': order_type_distribution,
                'position_size_distribution': position_size_distribution,

                # 新增字段（与数据库字段对应）
                'max_trade_size': max_min_metrics['max_trade_amount'],
                'min_trade_size': max_min_metrics['min_trade_amount'],

                # 交易规模分布字段
                'small_trades': position_size_distribution.get('small_trades', 0),
                'medium_trades': position_size_distribution.get('medium_trades', 0),
                'large_trades': position_size_distribution.get('large_trades', 0),
                'small_trades_ratio': position_size_distribution.get('small_trades_ratio', 0.0),
                'medium_trades_ratio': position_size_distribution.get('medium_trades_ratio', 0.0),
                'large_trades_ratio': position_size_distribution.get('large_trades_ratio', 0.0),

                **additional_metrics
            }
            
            logger.debug("扩展指标计算完成")
            return extended_metrics
            
        except Exception as e:
            logger.error(f"计算扩展指标时发生错误: {str(e)}")
            return self._get_empty_extended_metrics()

    def _calculate_additional_extended_metrics(self, positions: List[PositionData]) -> Dict:
        """计算额外的扩展指标"""
        try:
            # 盈亏统计扩展
            total_profit = sum(pos.total_pnl for pos in positions if pos.total_pnl > 0)
            total_loss = sum(abs(pos.total_pnl) for pos in positions if pos.total_pnl < 0)
            total_pnl = total_profit - total_loss
            total_trades = len(positions)
            profitable_count = len([pos for pos in positions if pos.total_pnl > 0])
            loss_count = len([pos for pos in positions if pos.total_pnl < 0])
            total_volume = sum(pos.total_open_amount for pos in positions)
            total_commission = sum(pos.total_commission for pos in positions)

            profit_trades_ratio = profitable_count / total_trades if total_trades > 0 else 0
            loss_trades_ratio = loss_count / total_trades if total_trades > 0 else 0
            avg_profit_per_trade = total_profit / profitable_count if profitable_count > 0 else 0
            avg_loss_per_trade = total_loss / loss_count if loss_count > 0 else 0
            return_rate = total_pnl / total_volume if total_volume > 0 else 0
            fee_ratio = total_commission / total_volume if total_volume > 0 else 0

            # 时间分析扩展 - 🚀 修复：只使用已完成持仓
            completed_positions = [pos for pos in positions if pos.close_time is not None]
            durations = [pos.duration_minutes for pos in completed_positions]
            max_holding_time = max(durations) if durations else 0
            min_holding_time = min(durations) if durations else 0

            # 杠杆分布扩展 - 调整为1-49倍和50-400倍两档
            low_leverage = len([pos for pos in positions if 1 <= pos.leverage <= 49])
            medium_leverage = 0  # 保持字段兼容性，但不使用
            high_leverage = len([pos for pos in positions if 50 <= pos.leverage <= 400])
            total_leverage_trades = low_leverage + high_leverage

            # 计算比例
            low_leverage_ratio = (low_leverage / total_leverage_trades) if total_leverage_trades > 0 else 0.0
            high_leverage_ratio = (high_leverage / total_leverage_trades) if total_leverage_trades > 0 else 0.0

            # 币种偏好扩展
            major_coins = ['BTC', 'ETH']
            defi_coins = ['UNI', 'SUSHI', 'AAVE', 'COMP', 'MKR', 'YFI', 'CRV', 'BAL']

            major_volume = 0
            defi_volume = 0

            for pos in positions:
                contract_upper = pos.contract_name.upper()
                if any(coin in contract_upper for coin in major_coins):
                    major_volume += pos.total_open_amount
                elif any(coin in contract_upper for coin in defi_coins):
                    defi_volume += pos.total_open_amount

            others_volume = total_volume - major_volume - defi_volume

            # 订单类型扩展
            total_market = sum(pos.market_orders_open + pos.market_orders_close for pos in positions)
            total_limit = sum(pos.limit_orders_open + pos.limit_orders_close for pos in positions)
            total_orders = total_market + total_limit

            return {
                # 盈亏统计扩展
                'total_pnl': total_pnl,
                'profit_trades_ratio': profit_trades_ratio,
                'loss_trades_ratio': loss_trades_ratio,
                'avg_profit_per_trade': avg_profit_per_trade,
                'avg_loss_per_trade': avg_loss_per_trade,
                'return_rate': return_rate,
                'fee_ratio': fee_ratio,

                # 时间分析扩展
                'max_holding_time': max_holding_time,
                'min_holding_time': min_holding_time,

                # 杠杆分布扩展
                'low_leverage_trades': low_leverage,
                'medium_leverage_trades': medium_leverage,
                'high_leverage_trades': high_leverage,
                'low_leverage_ratio': low_leverage_ratio,
                'high_leverage_ratio': high_leverage_ratio,

                # 🚀 币种偏好扩展 - 使用小数格式而非百分比格式（数据库DECIMAL(5,4)字段）
                'defi_percentage': defi_volume / total_volume if total_volume > 0 else 0,
                'others_percentage': others_volume / total_volume if total_volume > 0 else 0,
                'risk_appetite_level': 'medium',  # 默认值，后续可以根据杠杆等计算
                'volatility_preference': 0.5,  # 默认值，后续可以根据币种选择计算

                # 订单类型扩展
                'open_market_orders': sum(pos.market_orders_open for pos in positions),
                'open_limit_orders': sum(pos.limit_orders_open for pos in positions),
                'close_market_orders': sum(pos.market_orders_close for pos in positions),
                'close_limit_orders': sum(pos.limit_orders_close for pos in positions)
            }

        except Exception as e:
            logger.error(f"计算额外扩展指标时发生错误: {str(e)}")
            return {}

    def _calculate_max_min_trade_metrics(self, positions: List[PositionData]) -> Dict:
        """计算最大最小交易指标"""
        if not positions:
            return {
                'max_trade_amount': 0.0,
                'max_trade_contract': '',
                'min_trade_amount': 0.0,
                'min_trade_contract': ''
            }
        
        # 🚀 过滤掉交易金额为0的持仓
        valid_positions = [pos for pos in positions if pos.total_open_amount > 0]
        
        if not valid_positions:
            return {
                'max_trade_amount': 0.0,
                'max_trade_contract': '',
                'min_trade_amount': 0.0,
                'min_trade_contract': ''
            }
        
        # 找到最大和最小交易
        max_position = max(valid_positions, key=lambda x: x.total_open_amount)
        min_position = min(valid_positions, key=lambda x: x.total_open_amount)
        
        return {
            'max_trade_amount': max_position.total_open_amount,
            'max_trade_contract': max_position.contract_name,
            'min_trade_amount': min_position.total_open_amount,
            'min_trade_contract': min_position.contract_name
        }
    
    def _calculate_coin_preference_distribution(self, positions: List[PositionData]) -> Dict:
        """计算币种偏好分布"""
        coin_volumes = defaultdict(float)
        
        # 统计每个币种的交易量
        for pos in positions:
            # 从合约名称中提取币种（假设格式为 BTC-USDT 或 BTC/USDT）
            if hasattr(pos, 'contract_name') and pos.contract_name:
                # 支持多种分隔符格式
                if '-' in pos.contract_name:
                    coin = pos.contract_name.split('-')[0]
                elif '/' in pos.contract_name:
                    coin = pos.contract_name.split('/')[0]
                else:
                    coin = pos.contract_name
                coin_volumes[coin] += pos.total_open_amount
        
        total_volume = sum(coin_volumes.values())
        if total_volume == 0:
            return {
                'mainstream': 0.0,  # BTC, ETH
                'altcoin': 0.0,     # 主要山寨币
                'defi': 0.0,        # DeFi代币
                'others': 0.0       # 其他币种
            }
        
        # 币种分类
        mainstream_coins = {'BTC', 'ETH'}
        defi_coins = {'UNI', 'AAVE', 'COMP', 'MKR', 'SNX', 'CRV', 'YFI', 'SUSHI'}
        major_altcoins = {'ADA', 'DOT', 'LINK', 'LTC', 'XRP', 'BCH', 'BNB', 'SOL', 'AVAX', 'MATIC'}
        
        mainstream_volume = sum(volume for coin, volume in coin_volumes.items() if coin in mainstream_coins)
        defi_volume = sum(volume for coin, volume in coin_volumes.items() if coin in defi_coins)
        altcoin_volume = sum(volume for coin, volume in coin_volumes.items() if coin in major_altcoins)
        others_volume = total_volume - mainstream_volume - defi_volume - altcoin_volume
        
        return {
            'mainstream': mainstream_volume / total_volume,
            'altcoin': altcoin_volume / total_volume,
            'defi': defi_volume / total_volume,
            'others': others_volume / total_volume
        }
    
    def _calculate_time_distribution(self, positions: List[PositionData]) -> Dict:
        """计算24小时交易时间分布"""
        hour_volumes = defaultdict(float)
        
        # 统计每小时的交易量
        for pos in positions:
            if hasattr(pos, 'open_time') and pos.open_time:
                hour = pos.open_time.hour
                hour_volumes[hour] += pos.total_open_amount
        
        # 生成24小时分布
        time_distribution = {}
        for hour in range(24):
            time_distribution[f"{hour:02d}"] = hour_volumes.get(hour, 0.0)
        
        return time_distribution
    
    def _calculate_leverage_distribution(self, positions: List[PositionData]) -> Dict:
        """计算杠杆分布 - 调整为1-49倍和50-400倍两档"""
        leverage_counts = defaultdict(int)
        total_positions = len(positions)

        for pos in positions:
            if hasattr(pos, 'leverage') and pos.leverage > 0:
                # 杠杆分组 - 新的两档分类
                if 1 <= pos.leverage <= 49:
                    leverage_counts['1-49x'] += 1
                elif 50 <= pos.leverage <= 400:
                    leverage_counts['50-400x'] += 1

        # 转换为小数比率（数据库字段为DECIMAL(5,4)）
        leverage_distribution = {}
        for level, count in leverage_counts.items():
            leverage_distribution[level] = (count / total_positions) if total_positions > 0 else 0.0

        return leverage_distribution
    
    # 🚀 已清理：_calculate_detailed_order_type_distribution 方法
    # 清理原因：与 _calculate_order_type_metrics 功能重复，且比例计算已在前端处理
    # 统一使用主路径的 _calculate_order_type_metrics 方法
    
    def _calculate_position_size_distribution(self, positions: List[PositionData]) -> Dict:
        """计算仓位规模分布"""
        position_sizes = [pos.total_open_amount for pos in positions]
        total_positions = len(positions)

        if total_positions == 0:
            return {
                'small': 0.0,    # < 1000 USDT
                'medium': 0.0,   # 1000 - 10000 USDT
                'large': 0.0,    # 10000+ USDT
                # 新增：具体数量和比例
                'small_trades': 0,
                'medium_trades': 0,
                'large_trades': 0,
                'small_trades_ratio': 0.0,
                'medium_trades_ratio': 0.0,
                'large_trades_ratio': 0.0
            }

        small_count = sum(1 for size in position_sizes if size < 1000)
        medium_count = sum(1 for size in position_sizes if 1000 <= size < 10000)
        large_count = sum(1 for size in position_sizes if size >= 10000)

        return {
            'small': small_count / total_positions,
            'medium': medium_count / total_positions,
            'large': large_count / total_positions,
            # 新增：具体数量和比例
            'small_trades': small_count,
            'medium_trades': medium_count,
            'large_trades': large_count,
            'small_trades_ratio': small_count / total_positions,
            'medium_trades_ratio': medium_count / total_positions,
            'large_trades_ratio': large_count / total_positions
        }
    
    def _get_empty_extended_metrics(self) -> Dict:
        """获取空的扩展指标"""
        return {
            'max_trade_amount': 0.0,
            'max_trade_contract': '',
            'min_trade_amount': 0.0,
            'min_trade_contract': '',
            'coin_preference_distribution': {
                'mainstream': 0.0,
                'altcoin': 0.0,
                'defi': 0.0,
                'others': 0.0
            },
            'time_distribution': {f"{hour:02d}": 0.0 for hour in range(24)},
            'leverage_distribution': {
                '1x': 0.0,
                '2-5x': 0.0,
                '6-10x': 0.0,
                '11-20x': 0.0,
                '20x+': 0.0
            },
            'order_type_distribution': {
                'market_open_ratio': 0.0,
                'limit_open_ratio': 0.0,
                'market_close_ratio': 0.0,
                'limit_close_ratio': 0.0,
                'total_market_ratio': 0.0
            },
            'position_size_distribution': {
                'small': 0.0,
                'medium': 0.0,
                'large': 0.0,
                'small_trades': 0,
                'medium_trades': 0,
                'large_trades': 0,
                'small_trades_ratio': 0.0,
                'medium_trades_ratio': 0.0,
                'large_trades_ratio': 0.0
            },

            # 交易规模分布字段（直接字段）
            'small_trades': 0,
            'medium_trades': 0,
            'large_trades': 0,
            'small_trades_ratio': 0.0,
            'medium_trades_ratio': 0.0,
            'large_trades_ratio': 0.0,

            # 添加额外的扩展指标字段（来自_calculate_additional_extended_metrics）
            'total_pnl': 0.0,
            'profit_trades_ratio': 0.0,
            'loss_trades_ratio': 0.0,
            'avg_profit_per_trade': 0.0,
            'avg_loss_per_trade': 0.0,
            'return_rate': 0.0,
            'fee_ratio': 0.0,
            'max_holding_time': 0,
            'min_holding_time': 0,
            'defi_percentage': 0.0,
            'others_percentage': 0.0,
            'risk_appetite_level': 'medium',
            'volatility_preference': 0.5
        }

    
    def calculate_leverage_distribution(self, positions: List) -> Dict:
        """计算杠杆分布 - 调整为1-49倍和50-400倍两档"""
        try:
            leverage_counts = {'low': 0, 'high': 0}

            for position in positions:
                leverage = getattr(position, 'leverage', 1.0)
                if 1 <= leverage <= 49:
                    leverage_counts['low'] += 1
                elif 50 <= leverage <= 400:
                    leverage_counts['high'] += 1

            total_leverage_trades = leverage_counts['low'] + leverage_counts['high']

            # 计算比例
            low_leverage_ratio = (leverage_counts['low'] / total_leverage_trades) if total_leverage_trades > 0 else 0.0
            high_leverage_ratio = (leverage_counts['high'] / total_leverage_trades) if total_leverage_trades > 0 else 0.0

            return {
                'medium_leverage_trades': 0,  # 保持字段兼容性，但不使用
                'high_leverage_trades': leverage_counts['high'],
                'low_leverage_trades': leverage_counts['low'],
                'low_leverage_ratio': low_leverage_ratio,
                'high_leverage_ratio': high_leverage_ratio
            }
        except Exception as e:
            logger.warning(f"计算杠杆分布失败: {str(e)}")
            return {'medium_leverage_trades': 0, 'high_leverage_trades': 0, 'low_leverage_trades': 0, 'low_leverage_ratio': 0.0, 'high_leverage_ratio': 0.0}
    
    def calculate_trade_size_metrics(self, positions: List) -> Dict:
        """计算交易规模指标"""
        try:
            if not positions:
                return {'max_trade_size': 0.0, 'min_trade_size': 0.0}
            
            sizes = []
            for position in positions:
                size = getattr(position, 'total_open_amount', 0.0)
                if size > 0:
                    sizes.append(size)
            
            if not sizes:
                return {'max_trade_size': 0.0, 'min_trade_size': 0.0}
            
            return {
                'max_trade_size': max(sizes),
                'min_trade_size': min(sizes)
            }
        except Exception as e:
            logger.warning(f"计算交易规模指标失败: {str(e)}")
            return {'max_trade_size': 0.0, 'min_trade_size': 0.0}
    
    def calculate_commission_metrics(self, positions: List) -> Dict:
        """计算手续费相关指标"""
        try:
            total_commission = 0.0
            total_volume = 0.0
            
            for position in positions:
                commission = getattr(position, 'total_commission', 0.0)
                volume = getattr(position, 'total_open_amount', 0.0)
                
                if commission and commission > 0:
                    total_commission += float(commission)
                if volume and volume > 0:
                    total_volume += float(volume)
            
            fee_ratio = (total_commission / total_volume) if total_volume > 0 else 0.0
            
            return {
                'total_commission': total_commission,
                'fee_ratio': fee_ratio
            }
        except Exception as e:
            logger.warning(f"计算手续费指标失败: {str(e)}")
            return {'total_commission': 0.0, 'fee_ratio': 0.0}
    
    def calculate_holding_time_metrics(self, positions: List) -> Dict:
        """计算持仓时间指标 - 🚀 修复：只使用已完成持仓"""
        try:
            if not positions:
                return {'min_holding_time': 0.0, 'max_holding_time': 0.0, 'avg_holding_time': 0.0}

            # 🚀 修复：只使用已完成的持仓（close_time 不为 None）
            durations = []
            for position in positions:
                # 检查是否为已完成持仓
                close_time = getattr(position, 'close_time', None)
                if close_time is not None:
                    duration = getattr(position, 'duration_minutes', 0.0)
                    if duration and duration > 0:
                        durations.append(float(duration))

            if not durations:
                return {'min_holding_time': 0.0, 'max_holding_time': 0.0, 'avg_holding_time': 0.0}

            return {
                'min_holding_time': min(durations),
                'max_holding_time': max(durations),
                'avg_holding_time': sum(durations) / len(durations)
            }
        except Exception as e:
            logger.warning(f"计算持仓时间指标失败: {str(e)}")
            return {'min_holding_time': 0.0, 'max_holding_time': 0.0, 'avg_holding_time': 0.0}
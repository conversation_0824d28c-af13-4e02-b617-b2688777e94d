class WashTradingClassifier:
    SELF_TRADING = "自成交对敲"
    INTRA_BD_TRADING = "同BD对敲"
    CROSS_BD_TRADING = "跨BD对敲"
    DIRECT_CLIENT_TRADING = "直客对敲"
    UNCLASSIFIED = "未分类"
    
    @staticmethod
    def classify_single_record(risk, member_map):
        # 🔧 修复：优先使用member_id，如果为空则使用user_a_id
        member_id = risk.get('member_id') or risk.get('user_a_id', '')
        
        # 确保member_id为字符串，处理None值
        if member_id is None:
            member_id = ''
        member_id = str(member_id)
        
        # 获取对手方信息，支持多种格式
        counterparty_ids = risk.get('counterparty_ids', [])
        if not counterparty_ids and risk.get('user_b_id'):
            # 如果没有counterparty_ids但有user_b_id，使用user_b_id作为对手方
            counterparty_ids = [str(risk.get('user_b_id'))]
        
        detection_method = risk.get('detection_method', '')
        
        # 🔧 修复：如果member_id为空或'None'，尝试错误处理
        if not member_id or member_id == 'None' or member_id == '':
            return {
                'category': WashTradingClassifier.UNCLASSIFIED,
                'subcategory': '无效用户ID',
                'description': '用户ID为空或无效的对敲记录',
                'risk_level': 'low',
                'participants': [member_id]
            }
        
        if not counterparty_ids:
            if 'same_account' in detection_method or 'self' in detection_method:
                return {
                    'category': WashTradingClassifier.SELF_TRADING,
                    'subcategory': '自成交',
                    'description': '用户自己与自己进行的对敲交易',
                    'risk_level': 'medium',
                    'participants': [member_id]
                }
            else:
                return {
                    'category': WashTradingClassifier.UNCLASSIFIED,
                    'subcategory': '无对手方但非自成交',
                    'description': '检测方法异常的对敲记录',
                    'risk_level': 'low',
                    'participants': [member_id]
                }
        
        main_info = member_map.get(member_id)
        
        for cp_id in counterparty_ids:
            cp_info = member_map.get(str(cp_id))
            
            main_belongs = main_info['belongs_to'] if main_info else None
            main_category = main_info['category'] if main_info else None
            cp_belongs = cp_info['belongs_to'] if cp_info else None
            cp_category = cp_info['category'] if cp_info else None
            
            participants = [member_id, str(cp_id)]
            
            if not main_info and not cp_info:
                return {
                    'category': WashTradingClassifier.DIRECT_CLIENT_TRADING,
                    'subcategory': '双方无映射-推测为直客',
                    'description': '双方都不在BD体系中的对敲交易',
                    'risk_level': 'medium',
                    'participants': participants
                }
            elif main_info and cp_info:
                if main_belongs == cp_belongs:
                    return {
                        'category': WashTradingClassifier.INTRA_BD_TRADING,
                        'subcategory': f'同BD内部-{main_belongs}',
                        'description': f'同一BD体系({main_belongs})内的对敲交易',
                        'risk_level': 'high',
                        'participants': participants,
                        'bd_name': main_belongs
                    }
                else:
                    if main_category == '直客' or cp_category == '直客':
                        subcategory = 'BD-直客跨越'
                        description = 'BD体系与直客之间的对敲交易'
                    else:
                        subcategory = 'BD-BD跨越'
                        description = '不同BD体系之间的对敲交易'
                    
                    return {
                        'category': WashTradingClassifier.CROSS_BD_TRADING,
                        'subcategory': subcategory,
                        'description': description,
                        'risk_level': 'critical',
                        'participants': participants,
                        'bd_names': [main_belongs, cp_belongs]
                    }
            else:
                mapped_info = main_info if main_info else cp_info
                mapped_category = mapped_info['category']
                mapped_belongs = mapped_info['belongs_to']
                
                if mapped_category == '直客':
                    return {
                        'category': WashTradingClassifier.DIRECT_CLIENT_TRADING,
                        'subcategory': '直客与无映射用户',
                        'description': '直客与系统外用户的对敲交易',
                        'risk_level': 'medium',
                        'participants': participants
                    }
                else:
                    return {
                        'category': WashTradingClassifier.CROSS_BD_TRADING,
                        'subcategory': 'BD与无映射用户',
                        'description': 'BD体系与系统外用户的对敲交易',
                        'risk_level': 'critical',
                        'participants': participants,
                        'bd_names': [mapped_belongs]
                    }
        
        return {
            'category': WashTradingClassifier.UNCLASSIFIED,
            'subcategory': '未知情况',
            'description': '无法确定分类的对敲记录',
            'risk_level': 'low',
            'participants': [member_id]
        }
    
    @staticmethod
    def classify_batch_records(wash_risks, member_map):
        classification = {
            WashTradingClassifier.SELF_TRADING: [],
            WashTradingClassifier.INTRA_BD_TRADING: [],
            WashTradingClassifier.CROSS_BD_TRADING: [],
            WashTradingClassifier.DIRECT_CLIENT_TRADING: [],
            WashTradingClassifier.UNCLASSIFIED: []
        }
        
        for risk in wash_risks:
            result = WashTradingClassifier.classify_single_record(risk, member_map)
            category = result['category']
            result['original_risk'] = risk
            classification[category].append(result)
        
        return classification
    
    @staticmethod
    def get_classification_summary(classification):
        total_records = sum(len(records) for records in classification.values())
        
        summary = {
            'total_records': total_records,
            'categories': {}
        }
        
        for category, records in classification.items():
            count = len(records)
            percentage = (count / total_records * 100) if total_records > 0 else 0
            
            subtypes = {}
            risk_levels = {}
            for record in records:
                subtype = record.get('subcategory', '未知')
                risk_level = record.get('risk_level', 'unknown')
                subtypes[subtype] = subtypes.get(subtype, 0) + 1
                risk_levels[risk_level] = risk_levels.get(risk_level, 0) + 1
            
            summary['categories'][category] = {
                'count': count,
                'percentage': round(percentage, 1),
                'subtypes': subtypes,
                'risk_levels': risk_levels
            }
        
        return summary


def classify_wash_trading_risks(wash_risks, member_map):
    classifier = WashTradingClassifier()
    classification = classifier.classify_batch_records(wash_risks, member_map)
    summary = classifier.get_classification_summary(classification)
    
    return {
        'classification': classification,
        'summary': summary
    } 
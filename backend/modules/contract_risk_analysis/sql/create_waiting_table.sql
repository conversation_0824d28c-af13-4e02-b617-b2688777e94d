-- =============================================
-- 基于等待表的增量处理方案 - 等待表结构
-- 版本: 1.0
-- 创建时间: 2025-08-01
-- =============================================

-- 不完整订单等待表（极简设计）
CREATE TABLE IF NOT EXISTS incomplete_positions_waiting (
    position_id VARCHAR(100) PRIMARY KEY,
    member_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(50) NOT NULL,
    primary_side INTEGER NOT NULL CHECK (primary_side IN (1, 3)),
    first_open_time TIMESTAMP NOT NULL,
    total_open_amount DECIMAL(15,2) NOT NULL CHECK (total_open_amount > 0),
    open_trades_count INTEGER DEFAULT 1,
    avg_open_price DECIMAL(15,8) DEFAULT 0,
    total_open_volume DECIMAL(15,2) DEFAULT 0,
    waiting_since TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    check_count INTEGER DEFAULT 0,
    source_task_id VARCHAR(50),
    data_version VARCHAR(10) DEFAULT '1.0',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_incomplete_member_contract 
ON incomplete_positions_waiting (member_id, contract_name);

CREATE INDEX IF NOT EXISTS idx_incomplete_waiting_since 
ON incomplete_positions_waiting (waiting_since);

CREATE INDEX IF NOT EXISTS idx_incomplete_last_check 
ON incomplete_positions_waiting (last_check_time);

CREATE INDEX IF NOT EXISTS idx_incomplete_source_task 
ON incomplete_positions_waiting (source_task_id);

-- 数据完整性约束已在CREATE TABLE中定义

-- 表结构创建完成
-- 字段说明：
-- position_id: 订单ID，主键
-- member_id: 用户ID
-- contract_name: 合约名称
-- primary_side: 主要交易方向（1=开多, 3=开空）
-- first_open_time: 首次开仓时间
-- total_open_amount: 总开仓金额
-- waiting_since: 开始等待补全的时间
-- check_count: 检查次数，用于清理长期未匹配的记录

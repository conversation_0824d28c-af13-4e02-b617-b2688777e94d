-- 对敲交易详情表
-- 用于存储详细的交易对信息，包括具体的时间差、金额、利润等

CREATE TABLE IF NOT EXISTS wash_trading_pairs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    result_id BIGINT NOT NULL COMMENT '关联主结果表ID',
    pair_index INT NOT NULL COMMENT '配对序号(一个结果可能有多个配对)',
    
    -- 交易对基本信息
    user_a_id VARCHAR(50) NOT NULL COMMENT '用户A的ID',
    user_b_id VARCHAR(50) NOT NULL COMMENT '用户B的ID', 
    contract_name VARCHAR(50) NOT NULL COMMENT '合约名称',
    
    -- 用户A的position信息
    user_a_position_id VARCHAR(100) COMMENT '用户A的position_id',
    user_a_open_time DATETIME COMMENT '用户A开仓时间',
    user_a_open_side TINYINT COMMENT '用户A开仓方向: 1=开多, 3=开空',
    user_a_open_amount DECIMAL(15,2) COMMENT '用户A开仓金额(USDT)',
    user_a_close_time DATETIME COMMENT '用户A平仓时间',
    user_a_close_side TINYINT COMMENT '用户A平仓方向: 2=平空, 4=平多',
    user_a_close_amount DECIMAL(15,2) COMMENT '用户A平仓金额(USDT)',
    user_a_profit DECIMAL(15,2) COMMENT '用户A利润(USDT)',
    
    -- 用户B的position信息
    user_b_position_id VARCHAR(100) COMMENT '用户B的position_id',
    user_b_open_time DATETIME COMMENT '用户B开仓时间',
    user_b_open_side TINYINT COMMENT '用户B开仓方向: 1=开多, 3=开空',
    user_b_open_amount DECIMAL(15,2) COMMENT '用户B开仓金额(USDT)',
    user_b_close_time DATETIME COMMENT '用户B平仓时间',
    user_b_close_side TINYINT COMMENT '用户B平仓方向: 2=平空, 4=平多',
    user_b_close_amount DECIMAL(15,2) COMMENT '用户B平仓金额(USDT)',
    user_b_profit DECIMAL(15,2) COMMENT '用户B利润(USDT)',
    
    -- 配对分析数据
    open_time_diff_seconds INT COMMENT '开仓时间差(秒)',
    close_time_diff_seconds INT COMMENT '平仓时间差(秒)',
    total_amount DECIMAL(15,2) COMMENT '总交易金额(USDT)',
    net_profit DECIMAL(15,2) COMMENT '净利润(USDT)',
    
    -- 元数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_result_id (result_id),
    INDEX idx_user_a_id (user_a_id),
    INDEX idx_user_b_id (user_b_id),
    INDEX idx_contract_name (contract_name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对敲交易详情表';

-- 创建总交易金额视图（用于快速查询汇总信息）
CREATE OR REPLACE VIEW wash_trading_summary AS
SELECT 
    contract_name,
    COUNT(*) as pair_count,
    SUM(total_amount) as total_volume,
    AVG(open_time_diff_seconds) as avg_open_time_diff,
    AVG(close_time_diff_seconds) as avg_close_time_diff,
    SUM(net_profit) as total_net_profit
FROM wash_trading_pairs
GROUP BY contract_name; 
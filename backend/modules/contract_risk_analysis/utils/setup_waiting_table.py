#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于等待表的增量处理方案 - 等待表初始化脚本
版本: 1.0
创建时间: 2025-08-01
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(project_root, 'backend'))

from database.duckdb_manager import DuckDBManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_waiting_table():
    """
    创建等待表结构
    """
    try:
        logger.info("🚀 开始创建等待表结构...")
        
        # 创建数据库管理器
        db_manager = DuckDBManager()
        
        # 读取SQL文件
        sql_file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'sql', 'create_waiting_table.sql')
        
        if not os.path.exists(sql_file_path):
            logger.error(f"❌ SQL文件不存在: {sql_file_path}")
            return False
        
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        logger.info("📄 读取SQL文件成功")
        
        # 执行SQL脚本
        logger.info("🔧 执行SQL脚本...")
        
        # 分割SQL语句（按分号分割，忽略注释）
        sql_statements = []
        current_statement = ""
        
        for line in sql_content.split('\n'):
            line = line.strip()
            # 跳过空行和注释行
            if not line or line.startswith('--'):
                continue
            
            current_statement += line + " "
            
            # 如果行以分号结尾，表示一个完整的SQL语句
            if line.endswith(';'):
                sql_statements.append(current_statement.strip())
                current_statement = ""
        
        # 执行每个SQL语句
        for i, sql in enumerate(sql_statements, 1):
            if sql.strip():
                try:
                    logger.info(f"执行SQL语句 {i}/{len(sql_statements)}")
                    db_manager.execute_sql(sql)
                    logger.info(f"✅ SQL语句 {i} 执行成功")
                except Exception as e:
                    logger.error(f"❌ SQL语句 {i} 执行失败: {e}")
                    logger.error(f"SQL内容: {sql[:100]}...")
                    return False
        
        # 验证表是否创建成功
        logger.info("🔍 验证表结构...")
        
        # 检查表是否存在
        check_table_sql = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_name = 'incomplete_positions_waiting'
        """
        
        result = db_manager.execute_sql(check_table_sql)
        if result and result[0]['count'] > 0:
            logger.info("✅ 等待表创建成功")
            
            # 获取表结构信息
            describe_sql = "DESCRIBE incomplete_positions_waiting"
            columns = db_manager.execute_sql(describe_sql)
            
            logger.info(f"📊 表结构信息 ({len(columns)} 个字段):")
            for col in columns:
                logger.info(f"  - {col['column_name']}: {col['column_type']}")
            
            # 检查索引
            logger.info("🔍 检查索引...")
            # DuckDB的索引检查方式
            try:
                # 简单测试查询来验证索引是否工作
                test_sql = "SELECT COUNT(*) FROM incomplete_positions_waiting WHERE member_id = 'test'"
                db_manager.execute_sql(test_sql)
                logger.info("✅ 索引验证通过")
            except Exception as e:
                logger.warning(f"⚠️ 索引验证失败: {e}")
            
            return True
        else:
            logger.error("❌ 等待表创建失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 创建等待表失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("基于等待表的增量处理方案 - 等待表初始化")
    print("=" * 60)
    
    success = setup_waiting_table()
    
    if success:
        print("\n🎉 等待表初始化完成！")
        print("✅ incomplete_positions_waiting 表已创建")
        print("✅ 相关索引已创建")
        print("✅ 数据完整性约束已添加")
        print("\n💡 提示: 现在可以开始实现 SimpleIncrementalProcessor 类")
    else:
        print("\n❌ 等待表初始化失败！")
        print("请检查错误日志并修复问题")
        sys.exit(1)

if __name__ == "__main__":
    main()

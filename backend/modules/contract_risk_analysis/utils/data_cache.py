"""
数据缓存管理器
用于优化重复计算，提高算法性能
"""
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple, Set
import hashlib
import time
from datetime import datetime, timedelta

class DataCache:
    """数据缓存管理器"""
    
    def __init__(self, max_cache_size: int = 1000, cache_ttl_seconds: int = 3600):
        """
        初始化缓存管理器
        
        参数:
            max_cache_size: 最大缓存条目数
            cache_ttl_seconds: 缓存生存时间（秒）
        """
        self.max_cache_size = max_cache_size
        self.cache_ttl_seconds = cache_ttl_seconds
        
        # 缓存存储
        self._cache = {}
        self._cache_timestamps = {}
        self._access_order = []  # LRU访问顺序
        
    def _generate_key(self, data: pd.DataFrame, operation: str, **kwargs) -> str:
        """
        生成缓存键
        
        参数:
            data: DataFrame数据
            operation: 操作类型
            **kwargs: 其他参数
            
        返回:
            缓存键字符串
        """
        # 使用数据的形状、列名、和部分数据生成唯一键
        shape_info = f"{data.shape[0]}x{data.shape[1]}"
        columns_info = "_".join(sorted(data.columns))
        
        # 使用前几行和后几行的数据生成hash（避免处理全部数据）
        sample_rows = min(5, len(data))
        if sample_rows > 0:
            head_data = data.head(sample_rows).to_string()
            tail_data = data.tail(sample_rows).to_string() if len(data) > sample_rows else ""
            data_hash = hashlib.md5((head_data + tail_data).encode()).hexdigest()[:8]
        else:
            data_hash = "empty"
        
        # 构建完整key
        key_parts = [operation, shape_info, columns_info, data_hash]
        if kwargs:
            kwargs_str = "_".join(f"{k}={v}" for k, v in sorted(kwargs.items()))
            key_parts.append(kwargs_str)
        
        return "_".join(key_parts)
    
    def get(self, data: pd.DataFrame, operation: str, **kwargs) -> Optional[Any]:
        """
        从缓存获取数据
        
        参数:
            data: DataFrame数据
            operation: 操作类型
            **kwargs: 其他参数
            
        返回:
            缓存的结果或None
        """
        key = self._generate_key(data, operation, **kwargs)
        
        # 检查是否存在且未过期
        if key in self._cache:
            cache_time = self._cache_timestamps.get(key, 0)
            if time.time() - cache_time < self.cache_ttl_seconds:
                # 更新访问顺序
                if key in self._access_order:
                    self._access_order.remove(key)
                self._access_order.append(key)
                return self._cache[key]
            else:
                # 过期删除
                self._remove_cache_entry(key)
        
        return None
    
    def set(self, data: pd.DataFrame, operation: str, result: Any, **kwargs) -> None:
        """
        设置缓存数据
        
        参数:
            data: DataFrame数据
            operation: 操作类型
            result: 计算结果
            **kwargs: 其他参数
        """
        key = self._generate_key(data, operation, **kwargs)
        
        # 检查缓存大小限制
        if len(self._cache) >= self.max_cache_size:
            self._evict_oldest()
        
        # 设置缓存
        self._cache[key] = result
        self._cache_timestamps[key] = time.time()
        
        # 更新访问顺序
        if key in self._access_order:
            self._access_order.remove(key)
        self._access_order.append(key)
    
    def _remove_cache_entry(self, key: str) -> None:
        """移除缓存条目"""
        if key in self._cache:
            del self._cache[key]
        if key in self._cache_timestamps:
            del self._cache_timestamps[key]
        if key in self._access_order:
            self._access_order.remove(key)
    
    def _evict_oldest(self) -> None:
        """淘汰最旧的缓存条目"""
        if self._access_order:
            oldest_key = self._access_order[0]
            self._remove_cache_entry(oldest_key)
    
    def clear(self) -> None:
        """清空所有缓存"""
        self._cache.clear()
        self._cache_timestamps.clear()
        self._access_order.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()
        valid_entries = sum(
            1 for timestamp in self._cache_timestamps.values() 
            if current_time - timestamp < self.cache_ttl_seconds
        )
        
        return {
            'total_entries': len(self._cache),
            'valid_entries': valid_entries,
            'max_size': self.max_cache_size,
            'ttl_seconds': self.cache_ttl_seconds,
            'hit_ratio': getattr(self, '_hit_count', 0) / max(getattr(self, '_access_count', 1), 1)
        }


class PositionDataCache:
    """Position数据专用缓存"""
    
    def __init__(self):
        self._position_cache = {}
        self._close_cache = {}
    
    def get_position_data(self, position_ids: Set[str], full_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        获取Position数据（带缓存）
        
        参数:
            position_ids: Position ID集合
            full_df: 完整数据
            
        返回:
            Position数据字典
        """
        result = {}
        uncached_ids = set()
        
        # 检查缓存
        for pos_id in position_ids:
            if pos_id in self._position_cache:
                result[pos_id] = self._position_cache[pos_id]
            else:
                uncached_ids.add(pos_id)
        
        # 批量获取未缓存的数据
        if uncached_ids:
            position_data = full_df[full_df['position_id'].isin(uncached_ids)]
            for pos_id in uncached_ids:
                pos_data = position_data[position_data['position_id'] == pos_id]
                if not pos_data.empty:
                    self._position_cache[pos_id] = pos_data.copy()
                    result[pos_id] = pos_data
        
        return result
    
    def clear(self):
        """清空缓存"""
        self._position_cache.clear()
        self._close_cache.clear() 
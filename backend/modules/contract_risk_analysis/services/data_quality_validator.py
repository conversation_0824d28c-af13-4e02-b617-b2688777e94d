"""
对敲检测数据质量验证器
Data Quality Validator for Wash Trading Detection
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict

logger = logging.getLogger(__name__)

class WashTradingDataValidator:
    """对敲检测数据质量验证器"""
    
    def __init__(self):
        self.validation_rules = {
            'time_consistency': self._validate_time_consistency,
            'amount_logic': self._validate_amount_logic,
            'position_lifecycle': self._validate_position_lifecycle,
            'cross_reference': self._validate_cross_reference
        }
        
        self.quality_metrics = {
            'completeness': 0.0,
            'accuracy': 0.0,
            'consistency': 0.0,
            'timeliness': 0.0
        }
        
        # 配置参数
        self.config = {
            'time_window_seconds': 15,  # 同账户对敲时间窗口
            'cross_account_window_seconds': 10,  # 跨账户对敲时间窗口
            'min_amount_threshold': 10.0,  # 最小交易金额
            'amount_tolerance': 0.05,  # 金额匹配容差5%
            'quality_thresholds': {
                'high': 0.8,
                'medium': 0.6,
                'low': 0.0
            }
        }
    
    def validate_detection_result(self, result: Dict, raw_data: pd.DataFrame) -> Dict:
        """
        验证检测结果的质量
        
        参数:
            result: 检测结果
            raw_data: 原始交易数据
            
        返回:
            {
                'is_valid': bool,
                'quality_score': float,
                'validation_details': dict,
                'recommended_action': str,
                'confidence_level': str
            }
        """
        validation_result = {
            'is_valid': True,
            'quality_score': 0.0,
            'validation_details': {},
            'recommended_action': 'display',
            'confidence_level': 'low'
        }
        
        try:
            # 执行各项验证规则
            for rule_name, rule_func in self.validation_rules.items():
                try:
                    rule_result = rule_func(result, raw_data)
                    validation_result['validation_details'][rule_name] = rule_result
                    
                    if not rule_result.get('passed', False):
                        validation_result['is_valid'] = False
                        
                except Exception as e:
                    logger.error(f"验证规则 {rule_name} 执行失败: {e}")
                    validation_result['validation_details'][rule_name] = {
                        'passed': False,
                        'error': str(e)
                    }
                    validation_result['is_valid'] = False
            
            # 计算质量评分
            validation_result['quality_score'] = self._calculate_quality_score(
                validation_result['validation_details']
            )
            
            # 确定置信度等级
            validation_result['confidence_level'] = self._determine_confidence_level(
                validation_result['quality_score']
            )
            
            # 推荐处理动作
            validation_result['recommended_action'] = self._recommend_action(
                validation_result['quality_score']
            )
            
            logger.info(f"数据质量验证完成: 用户{result.get('member_id')}, 质量评分: {validation_result['quality_score']:.2f}")
            
        except Exception as e:
            logger.error(f"数据质量验证失败: {e}")
            validation_result.update({
                'is_valid': False,
                'quality_score': 0.0,
                'recommended_action': 'hide',
                'error': str(e)
            })
        
        return validation_result
    
    def _validate_time_consistency(self, result: Dict, raw_data: pd.DataFrame) -> Dict:
        """验证时间一致性"""
        try:
            user_id = result.get('member_id')
            contract = result.get('contract_name')
            detection_method = result.get('detection_method', '')
            
            if not user_id or not contract:
                return {'passed': False, 'reason': '缺少用户ID或合约名称'}
            
            # 获取用户在该合约的所有交易
            user_trades = raw_data[
                (raw_data['member_id'] == user_id) &
                (raw_data['contract_name'] == contract)
            ].copy()
            
            if len(user_trades) < 2:
                return {'passed': False, 'reason': '交易记录不足'}
            
            # 确保时间戳格式正确
            user_trades['timestamp'] = pd.to_datetime(user_trades['timestamp'])
            user_trades = user_trades.sort_values('timestamp')
            
            # 检查是否存在合理的多空配对时间窗口
            opens = user_trades[user_trades['side'].isin([1, 3])]
            long_opens = opens[opens['side'] == 1]
            short_opens = opens[opens['side'] == 3]
            
            if len(long_opens) == 0 or len(short_opens) == 0:
                return {'passed': False, 'reason': '缺少多空配对'}
            
            # 检查最小时间间隔
            min_time_gap = float('inf')
            valid_pairs = 0
            
            # 确定时间窗口
            time_window = (self.config['cross_account_window_seconds'] 
                          if 'cross' in detection_method 
                          else self.config['time_window_seconds'])
            
            for _, long_trade in long_opens.iterrows():
                for _, short_trade in short_opens.iterrows():
                    time_gap = abs((short_trade['timestamp'] - 
                                  long_trade['timestamp']).total_seconds())
                    
                    if time_gap <= time_window:
                        valid_pairs += 1
                        min_time_gap = min(min_time_gap, time_gap)
            
            if valid_pairs > 0:
                confidence = max(0.1, 1.0 - (min_time_gap / time_window))
                return {
                    'passed': True,
                    'min_time_gap': min_time_gap,
                    'valid_pairs': valid_pairs,
                    'confidence': confidence,
                    'time_window_used': time_window
                }
            else:
                return {
                    'passed': False,
                    'reason': f'没有在{time_window}秒时间窗口内的有效配对',
                    'min_time_gap': min_time_gap if min_time_gap != float('inf') else None
                }
                
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _validate_amount_logic(self, result: Dict, raw_data: pd.DataFrame) -> Dict:
        """验证金额逻辑"""
        try:
            user_id = result.get('member_id')
            contract = result.get('contract_name')
            
            if not user_id or not contract:
                return {'passed': False, 'reason': '缺少用户ID或合约名称'}
            
            # 获取用户交易数据
            user_trades = raw_data[
                (raw_data['member_id'] == user_id) &
                (raw_data['contract_name'] == contract) &
                (raw_data['side'].isin([1, 3]))  # 只看开仓
            ].copy()
            
            if len(user_trades) < 2:
                return {'passed': False, 'reason': '开仓交易记录不足'}
            
            # 检查金额范围
            amounts = user_trades['deal_vol_usdt'].dropna()
            if len(amounts) == 0:
                return {'passed': False, 'reason': '缺少有效的交易金额数据'}
            
            min_amount = amounts.min()
            max_amount = amounts.max()
            avg_amount = amounts.mean()
            
            # 最小金额检查
            if min_amount < self.config['min_amount_threshold']:
                return {
                    'passed': False,
                    'reason': f'存在低于阈值的交易金额: {min_amount:.2f}',
                    'min_amount': min_amount,
                    'threshold': self.config['min_amount_threshold']
                }
            
            # 检查金额匹配的可能性
            long_amounts = user_trades[user_trades['side'] == 1]['deal_vol_usdt'].dropna()
            short_amounts = user_trades[user_trades['side'] == 3]['deal_vol_usdt'].dropna()
            
            if len(long_amounts) == 0 or len(short_amounts) == 0:
                return {'passed': False, 'reason': '缺少多空金额数据'}
            
            # 寻找匹配的金额对
            matching_pairs = 0
            for long_amt in long_amounts:
                for short_amt in short_amounts:
                    if self._amounts_match(long_amt, short_amt):
                        matching_pairs += 1
            
            if matching_pairs > 0:
                match_ratio = matching_pairs / (len(long_amounts) * len(short_amounts))
                return {
                    'passed': True,
                    'matching_pairs': matching_pairs,
                    'match_ratio': match_ratio,
                    'amount_range': {'min': min_amount, 'max': max_amount, 'avg': avg_amount},
                    'confidence': min(1.0, match_ratio * 2)  # 匹配比例越高置信度越高
                }
            else:
                return {
                    'passed': False,
                    'reason': '没有找到匹配的金额对',
                    'amount_range': {'min': min_amount, 'max': max_amount, 'avg': avg_amount}
                }
                
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _validate_position_lifecycle(self, result: Dict, raw_data: pd.DataFrame) -> Dict:
        """验证仓位生命周期"""
        try:
            user_id = result.get('member_id')
            contract = result.get('contract_name')
            
            if not user_id or not contract:
                return {'passed': False, 'reason': '缺少用户ID或合约名称'}
            
            # 获取用户的所有交易（包括开平仓）
            user_trades = raw_data[
                (raw_data['member_id'] == user_id) &
                (raw_data['contract_name'] == contract)
            ].copy()
            
            if len(user_trades) == 0:
                return {'passed': False, 'reason': '没有交易记录'}
            
            # 按仓位ID分组
            if 'position_id' not in user_trades.columns:
                return {'passed': False, 'reason': '缺少position_id字段'}
            
            position_groups = user_trades.groupby('position_id')
            complete_positions = 0
            incomplete_positions = 0
            total_positions = 0
            
            for position_id, group in position_groups:
                if pd.isna(position_id):
                    continue
                    
                total_positions += 1
                
                # 检查是否有开仓和平仓
                has_open = any(group['side'].isin([1, 3]))  # 开多或开空
                has_close = any(group['side'].isin([2, 4]))  # 平空或平多
                
                if has_open and has_close:
                    complete_positions += 1
                else:
                    incomplete_positions += 1
            
            if total_positions == 0:
                return {'passed': False, 'reason': '没有有效的仓位记录'}
            
            completeness_ratio = complete_positions / total_positions
            
            # 如果有一定比例的完整仓位，认为通过验证
            if completeness_ratio >= 0.3:  # 至少30%的仓位是完整的
                return {
                    'passed': True,
                    'complete_positions': complete_positions,
                    'incomplete_positions': incomplete_positions,
                    'total_positions': total_positions,
                    'completeness_ratio': completeness_ratio,
                    'confidence': completeness_ratio
                }
            else:
                return {
                    'passed': False,
                    'reason': f'完整仓位比例过低: {completeness_ratio:.2f}',
                    'complete_positions': complete_positions,
                    'incomplete_positions': incomplete_positions,
                    'total_positions': total_positions
                }
                
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _validate_cross_reference(self, result: Dict, raw_data: pd.DataFrame) -> Dict:
        """验证交叉引用"""
        try:
            user_id = result.get('member_id')
            contract = result.get('contract_name')
            counterparty_ids = result.get('counterparty_ids', [])
            detection_method = result.get('detection_method', '')
            
            if not user_id or not contract:
                return {'passed': False, 'reason': '缺少用户ID或合约名称'}
            
            # 对于同账户对敲，检查用户是否真的有多空交易
            if 'same_account' in detection_method:
                user_trades = raw_data[
                    (raw_data['member_id'] == user_id) &
                    (raw_data['contract_name'] == contract) &
                    (raw_data['side'].isin([1, 3]))
                ]
                
                has_long = any(user_trades['side'] == 1)
                has_short = any(user_trades['side'] == 3)
                
                if has_long and has_short:
                    return {
                        'passed': True,
                        'validation_type': 'same_account',
                        'has_both_sides': True,
                        'confidence': 0.9
                    }
                else:
                    return {
                        'passed': False,
                        'reason': '用户没有同时进行多空交易',
                        'has_long': has_long,
                        'has_short': has_short
                    }
            
            # 对于跨账户对敲，检查对手方是否存在
            elif 'cross' in detection_method and counterparty_ids:
                valid_counterparties = 0
                
                for counterparty_id in counterparty_ids:
                    counterparty_trades = raw_data[
                        (raw_data['member_id'] == counterparty_id) &
                        (raw_data['contract_name'] == contract)
                    ]
                    
                    if len(counterparty_trades) > 0:
                        valid_counterparties += 1
                
                if valid_counterparties > 0:
                    return {
                        'passed': True,
                        'validation_type': 'cross_account',
                        'valid_counterparties': valid_counterparties,
                        'total_counterparties': len(counterparty_ids),
                        'confidence': valid_counterparties / len(counterparty_ids)
                    }
                else:
                    return {
                        'passed': False,
                        'reason': '没有找到有效的对手方交易记录',
                        'counterparty_ids': counterparty_ids
                    }
            
            # 默认情况
            return {
                'passed': True,
                'validation_type': 'basic',
                'confidence': 0.5
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _amounts_match(self, amount1: float, amount2: float) -> bool:
        """检查两个金额是否匹配"""
        if amount1 <= 0 or amount2 <= 0:
            return False
        
        max_amount = max(amount1, amount2)
        min_amount = min(amount1, amount2)
        
        # 小额交易使用绝对容差
        if max_amount <= 100:
            return abs(amount1 - amount2) <= 3
        
        # 其他使用相对容差
        return (max_amount - min_amount) / max_amount <= self.config['amount_tolerance']
    
    def _calculate_quality_score(self, validation_details: Dict) -> float:
        """计算数据质量评分"""
        if not validation_details:
            return 0.0
        
        # 权重配置
        weights = {
            'time_consistency': 0.3,
            'amount_logic': 0.25,
            'position_lifecycle': 0.25,
            'cross_reference': 0.2
        }
        
        total_score = 0.0
        total_weight = 0.0
        
        for rule_name, rule_result in validation_details.items():
            if rule_name in weights:
                weight = weights[rule_name]
                total_weight += weight
                
                if rule_result.get('passed', False):
                    # 使用置信度作为得分，如果没有则使用1.0
                    confidence = rule_result.get('confidence', 1.0)
                    total_score += weight * confidence
        
        if total_weight == 0:
            return 0.0
        
        return total_score / total_weight
    
    def _determine_confidence_level(self, quality_score: float) -> str:
        """确定置信度等级"""
        thresholds = self.config['quality_thresholds']
        
        if quality_score >= thresholds['high']:
            return 'high'
        elif quality_score >= thresholds['medium']:
            return 'medium'
        else:
            return 'low'
    
    def _recommend_action(self, quality_score: float) -> str:
        """推荐处理动作"""
        thresholds = self.config['quality_thresholds']
        
        if quality_score >= thresholds['high']:
            return 'display'  # 高质量，直接显示
        elif quality_score >= thresholds['medium']:
            return 'display_with_warning'  # 中等质量，带警告显示
        else:
            return 'hide'  # 低质量，隐藏
    
    def get_validation_summary(self, validation_results: List[Dict]) -> Dict:
        """获取验证结果汇总"""
        if not validation_results:
            return {
                'total_results': 0,
                'high_quality': 0,
                'medium_quality': 0,
                'low_quality': 0,
                'average_quality_score': 0.0
            }
        
        high_quality = sum(1 for r in validation_results if r.get('confidence_level') == 'high')
        medium_quality = sum(1 for r in validation_results if r.get('confidence_level') == 'medium')
        low_quality = sum(1 for r in validation_results if r.get('confidence_level') == 'low')
        
        quality_scores = [r.get('quality_score', 0.0) for r in validation_results]
        avg_quality = np.mean(quality_scores) if quality_scores else 0.0
        
        return {
            'total_results': len(validation_results),
            'high_quality': high_quality,
            'medium_quality': medium_quality,
            'low_quality': low_quality,
            'average_quality_score': avg_quality,
            'quality_distribution': {
                'high': high_quality / len(validation_results),
                'medium': medium_quality / len(validation_results),
                'low': low_quality / len(validation_results)
            }
        } 
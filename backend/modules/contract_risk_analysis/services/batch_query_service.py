"""
合约分析批量查询服务
复用代理商关系的Excel生成功能，查询指定member_id的各类算法异常金额总和
"""

import logging
import tempfile
import pandas as pd
from io import BytesIO
from datetime import datetime
from typing import List, Dict, Any

from database.duckdb_manager import DuckDBManager

logger = logging.getLogger(__name__)

class ContractBatchQueryService:
    """合约分析批量查询服务"""
    
    def __init__(self):
        self.logger = logger
        self.db_manager = DuckDBManager()
    
    def create_batch_query_task(self, member_ids: List[str]) -> Dict[str, Any]:
        """创建批量查询任务"""
        try:
            # 验证输入
            if not member_ids:
                raise ValueError("member_ids不能为空")

            # 去重并过滤空值
            input_ids = list(set([mid.strip() for mid in member_ids if mid.strip()]))

            if not input_ids:
                raise ValueError("没有有效的用户ID")

            if len(input_ids) > 5000:
                raise ValueError("用户ID数量不能超过5000个")

            self.logger.info(f"开始批量查询 {len(input_ids)} 个用户的合约异常金额")

            # 🔧 修复：支持digital_id到member_id的转换（优化版本）
            converted_ids = self._convert_ids_to_member_ids(input_ids)

            if not converted_ids:
                raise ValueError("没有找到有效的用户数据")

            self.logger.info(f"ID转换完成：输入{len(input_ids)}个ID，转换成功{len(converted_ids)}个member_id")

            # 查询各类算法的异常金额
            results = self._query_abnormal_amounts(list(converted_ids.values()))

            # 生成Excel文件（传入ID映射关系用于显示）
            file_path = self._generate_excel_file(input_ids, results, converted_ids)

            return {
                'status': 'success',
                'download_url': f'/api/contract/batch-query-download/{file_path.split("/")[-1]}',
                'file_path': file_path,
                'total_users': len(input_ids),
                'converted_users': len(converted_ids),
                'results_count': len(results)
            }

        except Exception as e:
            self.logger.error(f"批量查询失败: {str(e)}")
            raise

    def _convert_ids_to_member_ids(self, input_ids: List[str]) -> Dict[str, str]:
        """
        将输入的ID转换为member_id
        支持digital_id和member_id混合输入
        优化：先判断ID格式，避免不必要的数据库查询

        参数:
            input_ids: 输入的ID列表（可能包含digital_id或member_id）

        返回:
            Dict[input_id, member_id]: ID映射字典
        """
        try:
            converted_ids = {}

            # 分离digital_id和member_id
            digital_ids = []
            member_ids = []

            for input_id in input_ids:
                if self._is_likely_digital_id(input_id):
                    digital_ids.append(input_id)
                else:
                    member_ids.append(input_id)

            # 批量处理digital_id转换
            if digital_ids:
                digital_id_mappings = self._batch_find_member_ids_by_digital_ids(digital_ids)
                converted_ids.update(digital_id_mappings)

            # 批量验证member_id
            if member_ids:
                valid_member_ids = self._batch_validate_member_ids(member_ids)
                for member_id in valid_member_ids:
                    converted_ids[member_id] = member_id

            self.logger.info(f"ID转换完成: digital_id转换{len(digital_ids)}个，member_id验证{len(member_ids)}个，成功{len(converted_ids)}个")
            return converted_ids

        except Exception as e:
            self.logger.error(f"ID转换失败: {str(e)}")
            return {}

    def _is_likely_digital_id(self, input_id: str) -> bool:
        """
        判断输入ID是否可能是digital_id
        digital_id通常是数字格式，通常8位数字
        member_id通常是长字符串（哈希值）
        """
        # 去除空白字符
        input_id = input_id.strip()

        # digital_id通常是纯数字，长度相对较短（如8位）
        if input_id.isdigit() and len(input_id) <= 12:
            return True

        # member_id通常是长字符串，包含字母和数字的混合
        if len(input_id) > 20 and not input_id.isdigit():
            return False

        # 如果长度在中等范围且包含字母，更可能是member_id
        if len(input_id) > 12 and any(c.isalpha() for c in input_id):
            return False

        # 默认情况下，短的数字字符串认为是digital_id
        return input_id.isdigit()

    def _batch_find_member_ids_by_digital_ids(self, digital_ids: List[str]) -> Dict[str, str]:
        """批量通过digital_id查找member_id"""
        try:
            if not digital_ids:
                return {}

            placeholders = ','.join(['?' for _ in digital_ids])
            sql = f"""
            SELECT digital_id, member_id FROM users
            WHERE digital_id IN ({placeholders})
            """
            results = self.db_manager.execute_sql(sql, digital_ids)

            mappings = {}
            for result in results:
                mappings[result['digital_id']] = result['member_id']

            # 记录未找到的digital_id
            for digital_id in digital_ids:
                if digital_id not in mappings:
                    self.logger.warning(f"digital_id未找到对应用户: {digital_id}")

            return mappings
        except Exception as e:
            self.logger.error(f"批量查找member_id失败: {str(e)}")
            return {}

    def _batch_validate_member_ids(self, member_ids: List[str]) -> List[str]:
        """批量验证member_id是否存在"""
        try:
            if not member_ids:
                return []

            placeholders = ','.join(['?' for _ in member_ids])
            sql = f"""
            SELECT member_id FROM users
            WHERE member_id IN ({placeholders})
            """
            results = self.db_manager.execute_sql(sql, member_ids)

            valid_ids = [result['member_id'] for result in results]

            # 记录不存在的member_id
            for member_id in member_ids:
                if member_id not in valid_ids:
                    self.logger.warning(f"member_id不存在: {member_id}")

            return valid_ids
        except Exception as e:
            self.logger.error(f"批量验证member_id失败: {str(e)}")
            return []

    def _find_member_id_by_digital_id(self, digital_id: str) -> str:
        """通过digital_id查找member_id"""
        try:
            sql = """
            SELECT member_id FROM users
            WHERE digital_id = ?
            LIMIT 1
            """
            results = self.db_manager.execute_sql(sql, [digital_id])
            if results:
                return results[0]['member_id']
            return None
        except Exception as e:
            self.logger.debug(f"查找member_id失败: {digital_id}, 错误: {str(e)}")
            return None

    def _validate_member_id_exists(self, member_id: str) -> bool:
        """验证member_id是否存在于用户表中"""
        try:
            sql = """
            SELECT COUNT(*) as count FROM users
            WHERE member_id = ?
            """
            results = self.db_manager.execute_sql(sql, [member_id])
            return results and results[0]['count'] > 0
        except Exception as e:
            self.logger.debug(f"验证member_id失败: {member_id}, 错误: {str(e)}")
            return False

    def _query_abnormal_amounts(self, member_ids: List[str]) -> List[Dict[str, Any]]:
        """查询各类算法的异常金额"""
        try:
            # 构建查询SQL - 从contract_risk_details表查询
            placeholders = ','.join(['?' for _ in member_ids])
            
            sql = f"""
            SELECT 
                member_id,
                detection_type,
                SUM(abnormal_volume) as total_abnormal_volume,
                COUNT(*) as risk_count,
                AVG(risk_score) as avg_risk_score,
                MAX(created_at) as latest_detection
            FROM contract_risk_details 
            WHERE member_id IN ({placeholders})
            GROUP BY member_id, detection_type
            ORDER BY member_id, detection_type
            """
            
            results = self.db_manager.execute_sql(sql, member_ids)
            
            self.logger.info(f"查询到 {len(results)} 条异常金额记录")
            return results
            
        except Exception as e:
            self.logger.error(f"查询异常金额失败: {str(e)}")
            return []
    
    def _generate_excel_file(self, input_ids: List[str], query_results: List[Dict[str, Any]], id_mapping: Dict[str, str] = None) -> str:
        """生成Excel文件"""
        try:
            # 转换数据格式为Excel生成器需要的格式
            excel_data = self._prepare_excel_data(input_ids, query_results, id_mapping)

            # 生成Excel文件
            output = BytesIO()
            
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # 主要汇总表
                if excel_data['summary']:
                    df_summary = pd.DataFrame(excel_data['summary'])
                    df_summary.to_excel(writer, sheet_name='异常金额汇总', index=False)
                
                # 详细数据表
                if excel_data['details']:
                    df_details = pd.DataFrame(excel_data['details'])
                    df_details.to_excel(writer, sheet_name='详细数据', index=False)
                
                # 统计信息表
                if excel_data['statistics']:
                    df_stats = pd.DataFrame(excel_data['statistics'])
                    df_stats.to_excel(writer, sheet_name='统计信息', index=False)
            
            # 保存文件到临时目录
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
            temp_file.write(output.getvalue())
            temp_file.close()
            
            self.logger.info(f"Excel文件生成完成: {temp_file.name}")
            return temp_file.name
            
        except Exception as e:
            self.logger.error(f"生成Excel文件失败: {str(e)}")
            raise
    
    def _prepare_excel_data(self, input_ids: List[str], query_results: List[Dict[str, Any]], id_mapping: Dict[str, str] = None) -> Dict[str, List[Dict]]:
        """准备Excel数据"""
        try:
            # 创建反向映射：member_id -> input_id
            reverse_mapping = {}
            if id_mapping:
                reverse_mapping = {v: k for k, v in id_mapping.items()}

            # 按用户ID分组数据
            user_data = {}
            for result in query_results:
                member_id = result['member_id']
                if member_id not in user_data:
                    user_data[member_id] = {}

                detection_type = result['detection_type']
                volume = float(result['total_abnormal_volume'] or 0)

                user_data[member_id][detection_type] = {
                    'total_volume': volume,
                    'risk_count': result['risk_count'],
                    'avg_risk_score': float(result['avg_risk_score'] or 0),
                    'latest_detection': result['latest_detection']
                }
            
            # 算法类型中文映射（根据数据库实际字段）
            algorithm_mapping = {
                'wash_trading': '对敲交易',
                'high_frequency': '高频交易',
                'funding_arbitrage': '资金费率套利',
                'regular_brush': '常规刷量'
            }
            
            # 生成汇总数据（按输入ID顺序）
            summary_data = []
            for input_id in input_ids:
                # 查找该输入ID对应的member_id和数据
                member_id = id_mapping.get(input_id, input_id) if id_mapping else input_id

                # 显示输入ID，但在备注中显示转换信息
                display_id = input_id
                id_note = ""
                if id_mapping and input_id in id_mapping and id_mapping[input_id] != input_id:
                    id_note = f"(member_id: {member_id[:8]}...)"
                elif id_mapping and input_id not in id_mapping:
                    id_note = "未找到用户"

                row = {
                    '输入ID': display_id,
                    'ID备注': id_note
                }

                total_abnormal = 0
                total_risks = 0

                for algo_type, algo_name in algorithm_mapping.items():
                    if member_id in user_data and algo_type in user_data[member_id]:
                        volume = user_data[member_id][algo_type]['total_volume']
                        count = user_data[member_id][algo_type]['risk_count']
                        row[f'{algo_name}异常金额(USDT)'] = f"{volume:,.2f}" if volume > 0 else "0"
                        row[f'{algo_name}风险次数'] = count
                        total_abnormal += volume
                        total_risks += count
                    else:
                        row[f'{algo_name}异常金额(USDT)'] = "0"
                        row[f'{algo_name}风险次数'] = 0

                row['异常金额总计(USDT)'] = f"{total_abnormal:,.2f}"
                row['风险事件总数'] = total_risks

                summary_data.append(row)
            
            # 生成详细数据
            details_data = []
            for result in query_results:
                member_id = result['member_id']
                # 查找对应的输入ID
                input_id = reverse_mapping.get(member_id, member_id)

                algo_type_cn = algorithm_mapping.get(result['detection_type'], result['detection_type'])
                details_data.append({
                    '输入ID': input_id,
                    '实际member_id': member_id[:8] + '...' if len(member_id) > 8 else member_id,
                    '算法类型': algo_type_cn,
                    '异常金额(USDT)': f"{float(result['total_abnormal_volume'] or 0):,.2f}",
                    '风险次数': result['risk_count'],
                    '平均风险分数': f"{float(result['avg_risk_score'] or 0):.2f}",
                    '最新检测时间': result['latest_detection']
                })
            
            # 生成统计信息
            converted_count = len(id_mapping) if id_mapping else len(input_ids)
            statistics_data = [
                {'统计项目': '输入用户总数', '数值': len(input_ids)},
                {'统计项目': 'ID转换成功数', '数值': converted_count},
                {'统计项目': '有异常记录用户数', '数值': len(user_data)},
                {'统计项目': '异常记录总数', '数值': len(query_results)},
                {'统计项目': '生成时间', '数值': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            ]
            
            return {
                'summary': summary_data,
                'details': details_data,
                'statistics': statistics_data
            }
            
        except Exception as e:
            self.logger.error(f"准备Excel数据失败: {str(e)}")
            raise

# 创建全局服务实例
contract_batch_query_service = ContractBatchQueryService()

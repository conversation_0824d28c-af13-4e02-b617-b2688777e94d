"""
报告生成器 - 生成分析结果摘要
"""

def generate_summary(suspicious_data):
    """
    生成异常交易检测结果摘要
    
    参数:
        suspicious_data (list): 异常交易数据列表
        
    返回:
        dict: 摘要数据
    """
    if not suspicious_data:
        return {
            'total_count': 0,
            'by_type': {},
            'top_users': [],
            'severity_distribution': {}
        }
    
    # 按类型统计
    type_counts = {}
    user_counts = {}
    severity_counts = {}
    
    for item in suspicious_data:
        # 统计异常类型
        anomaly_type = item.get('type', '未知')
        type_counts[anomaly_type] = type_counts.get(anomaly_type, 0) + 1
        
        # 统计用户异常数量
        user_id = item.get('user_id') or item.get('mid', '')
        if user_id:
            user_counts[user_id] = user_counts.get(user_id, 0) + 1
        
        # 统计严重程度
        severity = item.get('severity', '中等')
        severity_counts[severity] = severity_counts.get(severity, 0) + 1
    
    # 排序获取最异常的用户
    top_users = sorted(user_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    top_users = [{'user_id': uid, 'count': count} for uid, count in top_users]
    
    return {
        'total_count': len(suspicious_data),
        'by_type': type_counts,
        'top_users': top_users,
        'severity_distribution': severity_counts
    } 
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple

from ..config.algorithms_config import ConfigManager
# 临时内联定义AlgorithmRegistry以避免导入问题
class AlgorithmRegistry:
    """算法注册中心"""
    _algorithms = {}
    
    @classmethod
    def register_algorithm(cls, name: str, algorithm):
        """注册算法"""
        cls._algorithms[name] = algorithm
        
    @classmethod
    def get_algorithm(cls, name: str):
        """获取算法"""
        return cls._algorithms.get(name)
    
    @classmethod
    def get_score_calculator(cls, name: str):
        """获取评分计算器"""
        return cls._algorithms.get(f"{name}_score_calculator")

class RiskEvaluator:
    """风险评估器，负责计算和评估风险等级"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        # 导入并初始化report_generator
        try:
            from .report_generator import ReportGenerator
            self.report_generator = ReportGenerator(self.config_manager)
        except ImportError:
            # 如果导入失败，创建一个简单的默认实现
            self.report_generator = self._create_simple_report_generator()
        self.user_trades = None
        self.detected_algorithms = []
    
    def _create_simple_report_generator(self):
        """创建简单的报告生成器"""
        class SimpleReportGenerator:
            def generate_risk_reason(self, final_level: str, processed_results: list) -> str:
                if not processed_results:
                    return "未检测到异常交易行为"
                
                # 统计算法类型
                algo_types = [result.get('algorithm_name', 'unknown') for result in processed_results]
                algo_count = len(algo_types)
                
                # 生成简单的风险描述
                if final_level == "high":
                    return f"检测到{algo_count}个高风险算法触发，包括：{', '.join(set(algo_types))}"
                elif final_level == "medium":
                    return f"检测到{algo_count}个中等风险算法触发，包括：{', '.join(set(algo_types))}"
                else:
                    return f"检测到{algo_count}个低风险算法触发，包括：{', '.join(set(algo_types))}"
        
        return SimpleReportGenerator()
        
    def set_user_trades(self, trades_df: pd.DataFrame) -> None:
        """设置用户交易数据"""
        self.user_trades = trades_df
        # 提取合约名称
        self.coin_name = self._extract_coin_name()
        # 获取合约类别
        self.coin_category = self._get_coin_category()
        # 获取交易量阈值
        self.volume_thresholds = self._get_volume_thresholds()
        
    def set_detected_algorithms(self, algorithms: List[Dict]) -> None:
        """设置检测到的算法结果"""
        self.detected_algorithms = algorithms
    
    def get_final_risk_assessment(self) -> Dict:
        """获取最终风险评估结果"""
        if not self.detected_algorithms:
            return {
                "final_score": 0, 
                "final_level": "low",
                "reason": "未检测到异常交易行为",
                "details": []
            }
            
        # 处理每个检测到的算法
        processed_results = []
        final_scores = []
        
        # 计算加成项
        leverage_bonus = self.calculate_leverage_bonus()
        volume_bonus = self.calculate_volume_bonus()
        
        # 处理每个算法
        for algo in self.detected_algorithms:
            # 计算基础分
            base_score = self._calculate_algorithm_base_score(algo)
            
            # 应用加成
            total_score = base_score + leverage_bonus + volume_bonus
            
            # 确定风险等级
            algo_type = algo.get('detection_type', 'unknown')
            risk_level = self._get_algorithm_risk_level(algo_type, total_score)
            
            # 提取关键指标
            key_indicators = self._extract_key_indicators(algo)
            
            # 整理结果
            processed_results.append({
                "algorithm_name": algo_type,
                "base_score": base_score,
                "leverage_bonus": leverage_bonus,
                "volume_bonus": volume_bonus,
                "total_score": total_score,
                "level": risk_level,
                "key_indicators": key_indicators,
                "abnormal_volume": algo.get("abnormal_volume", 0)
            })
            
            final_scores.append(total_score)
        
        # 取最高分
        overall_final_score = max(final_scores) if final_scores else 0
        
        # 确定最终风险等级
        base_level = self._determine_final_risk_level(processed_results)
        
        # 应用直接升级规则
        final_level = self._check_direct_upgrade_rules(base_level, processed_results)
            
        # 生成风险描述
        reason = self.report_generator.generate_risk_reason(final_level, processed_results)
        
        return {
            "final_score": overall_final_score,
            "final_level": final_level,
            "reason": reason,
            "details": processed_results
        }
        
    def calculate_leverage_bonus(self) -> float:
        """计算杠杆风险加成分数"""
        # 获取杠杆值
        leverage = self._get_max_leverage()
        
        # 获取杠杆阈值配置
        thresholds = self.config_manager.thresholds_config.get('risk_bonus', {}).get('leverage', {})
        level1 = thresholds.get('level1', 50)
        level2 = thresholds.get('level2', 100)
        
        # 计算杠杆加成
        if leverage <= level1:
            return 0
        elif leverage <= level2:
            return 10
        else:
            return 20
        
    def calculate_volume_bonus(self) -> float:
        """计算交易量风险加成分数"""
        # 获取交易量比例
        volume_ratio = self._get_volume_ratio()
        
        # 获取交易量阈值配置
        thresholds = self.config_manager.thresholds_config.get('risk_bonus', {}).get('volume_ratio', {})
        level1 = thresholds.get('level1', 0.05)
        level2 = thresholds.get('level2', 0.15)
        level3 = thresholds.get('level3', 0.30)
        
        # 计算交易量加成
        if volume_ratio < level1:
            return 0
        elif volume_ratio < level2:
            return 15
        elif volume_ratio < level3:
            return 25
        else:
            return 35
    
    def _calculate_algorithm_base_score(self, algo: Dict) -> float:
        """计算算法基础风险分数"""
        algo_type = algo.get('detection_type', 'unknown')
        
        # 获取评分计算器
        calculator = AlgorithmRegistry.get_score_calculator(algo_type)
        if calculator:
            # 使用注册的计算器
            return calculator(algo, self.config_manager)
        
        # 使用默认评分机制
        weights = self.config_manager.get_algorithm_weights(algo_type)
        indicators = algo.get('indicators', {})
        
        if not weights or not indicators:
            return 50  # 默认中等风险
            
        # 计算加权评分
        score = 0
        for indicator_name, weight in weights.items():
            if indicator_name in indicators:
                indicator_value = indicators[indicator_name]
                # 归一化指标值(假设已经在0-1范围内)
                score += weight * indicator_value * 100
                
        return min(100, max(0, score))  # 确保分数在0-100范围内
    
    def _get_algorithm_risk_level(self, algo_type: str, score: float) -> str:
        """根据算法类型和评分确定风险等级"""
        thresholds = self.config_manager.get_risk_level_thresholds(algo_type)
        
        if not thresholds:
            # 使用默认阈值
            if score >= 85:
                return "high"
            elif score >= 60:
                return "medium"
            else:
                return "low"
                
        # 使用配置的阈值
        high_threshold = thresholds.get('high', 85)
        medium_threshold = thresholds.get('medium', 60)
        
        if score >= high_threshold:
            return "high"
        elif score >= medium_threshold:
            return "medium"
        else:
            return "low"
            
    def _determine_final_risk_level(self, processed_results: List[Dict]) -> str:
        """确定最终风险等级"""
        # 统计各风险等级的算法数量
        high_count = 0
        medium_count = 0
        low_count = 0
        
        for result in processed_results:
            level = result.get('level', 'low')
            if level == 'high':
                high_count += 1
            elif level == 'medium':
                medium_count += 1
            else:
                low_count += 1
                
        # 获取升级规则配置
        upgrade_config = self.config_manager.thresholds_config.get('combo_upgrade', {})
        medium_to_high = upgrade_config.get('medium_to_high', 2)
        low_to_medium = upgrade_config.get('low_to_medium', 3)
        
        # 应用组合风险升级规则
        if high_count > 0:
            return "high"  # 规则1: 任何高风险算法→整体高风险
        elif medium_count >= medium_to_high:
            return "high"  # 规则2: 两个及以上中风险算法→整体高风险
        elif medium_count > 0:
            return "medium"  # 规则3: 一个中风险算法→整体中风险
        elif low_count >= low_to_medium:
            return "medium"  # 规则4: 三个及以上低风险算法→整体中风险
        else:
            return "low"
            
    def _check_direct_upgrade_rules(self, base_level: str, processed_results: List[Dict]) -> str:
        """检查直接影响升级规则"""
        # 获取杠杆值
        leverage = self._get_max_leverage()
        
        # 获取交易量比例
        volume_ratio = self._get_volume_ratio()
        
        # 应用直接升级规则
        if leverage > 100 and base_level == 'low':
            return "medium"  # 规则1: 超高杠杆(>100倍)→至少中风险
            
        if len(processed_results) > 1 and leverage > 100:
            return "high"  # 规则2: 多算法触发且平均杠杆>100倍→高风险
            
        if volume_ratio > 0.5 and base_level == 'low':
            return "medium"  # 规则3: 异常交易量超过用户30日平均交易量的50%→至少中风险
            
        if volume_ratio > 1.0:
            return "high"  # 规则4: 异常交易量超过用户30日平均交易量的100%→高风险
            
        return base_level
        
    def _extract_key_indicators(self, algo: Dict) -> Dict:
        """提取算法关键指标"""
        algo_type = algo.get('detection_type', 'unknown')
        indicators = algo.get('indicators', {})
        
        # 获取该算法关键指标权重配置
        weights = self.config_manager.get_algorithm_weights(algo_type)
        
        key_indicators = {}
        # 根据权重排序，提取重要指标
        if weights and indicators:
            sorted_indicators = sorted(
                weights.items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            
            # 提取前3个重要指标
            for indicator_name, _ in sorted_indicators[:3]:
                if indicator_name in indicators:
                    key_indicators[indicator_name] = indicators[indicator_name]
        
        # 添加其他必要指标
        if 'abnormal_volume' in algo:
            key_indicators['volume'] = algo['abnormal_volume']
            
        if 'trade_count' in algo:
            key_indicators['trades_count'] = algo['trade_count']
            
        if 'duration' in algo:
            key_indicators['duration'] = algo['duration']
            
        if 'leverage' in algo:
            key_indicators['leverage'] = algo['leverage']
            
        return key_indicators
        
    def _extract_coin_name(self) -> str:
        """从交易数据中提取合约名称"""
        if self.user_trades is None or len(self.user_trades) == 0:
            return "unknown"
            
        # 尝试从不同可能的字段中提取
        for field in ['contract_name', 'symbol', 'pair']:
            if field in self.user_trades.columns:
                values = self.user_trades[field].unique()
                if len(values) > 0:
                    return str(values[0])
                    
        return "unknown"
        
    def _get_coin_category(self) -> str:
        """获取合约类别"""
        coin_name = self.coin_name.lower()
        
        if 'btc' in coin_name or 'bitcoin' in coin_name:
            return 'BTC'
        elif 'eth' in coin_name or 'ethereum' in coin_name:
            return 'ETH'
        elif 'sol' in coin_name or 'solana' in coin_name:
            return 'SOL'
        else:
            return 'other'
            
    def _get_volume_thresholds(self) -> Dict:
        """获取交易量阈值"""
        category = self._get_coin_category()
        
        # 基于合约类别返回不同的阈值
        if category == 'BTC':
            return {
                'low': 10000,
                'medium': 50000,
                'high': 200000
            }
        elif category == 'ETH':
            return {
                'low': 5000,
                'medium': 25000,
                'high': 100000
            }
        elif category == 'SOL':
            return {
                'low': 2000,
                'medium': 10000,
                'high': 50000
            }
        else:
            return {
                'low': 1000,
                'medium': 5000,
                'high': 20000
            }
            
    def _get_max_leverage(self) -> float:
        """获取最大杠杆值"""
        # 检查检测到的算法中是否有杠杆信息
        max_leverage = 0
        for algo in self.detected_algorithms:
            if 'leverage' in algo:
                max_leverage = max(max_leverage, float(algo['leverage']))
                
        return max_leverage
        
    def _get_volume_ratio(self) -> float:
        """计算异常交易量与用户30日平均交易量的比例"""
        # 这里简化处理，假设异常交易量是所有检测到的算法的交易量总和
        total_abnormal_volume = 0
        for algo in self.detected_algorithms:
            if 'abnormal_volume' in algo:
                total_abnormal_volume += float(algo['abnormal_volume'])
                
        # 假设用户30日平均交易量，实际应用中应从用户历史数据获取
        user_avg_volume = self._get_user_avg_volume()
        
        if user_avg_volume <= 0:
            return 1.0  # 如果没有历史数据，假设是高风险
            
        return total_abnormal_volume / user_avg_volume
        
    def _get_user_avg_volume(self) -> float:
        """获取用户30日平均交易量，实际实现应查询用户历史数据"""
        # 示例实现，实际应查询数据库
        if self.user_trades is None or len(self.user_trades) == 0:
            return 10000  # 默认值
            
        # 获取合约类别对应的中等阈值作为参考值
        category = self._get_coin_category()
        thresholds = self._get_volume_thresholds()
        
        return thresholds.get('medium', 10000) 
"""
用户行为分析触发器
负责在合约风险分析完成后，触发用户行为分析

职责：
1. 获取涉及的用户列表
2. 获取用户的完整持仓数据
3. 触发用户行为分析
4. 处理进度回调和错误处理
"""

import logging
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class UserAnalysisTrigger:
    """用户行为分析触发器"""
    
    def __init__(self):
        """初始化触发器"""
        pass
    
    def trigger_user_behavior_analysis(self,
                                     task_id: str,
                                     result_id: int,
                                     complete_positions: Optional[Dict] = None,
                                     progress_callback: Optional[Callable] = None) -> bool:
        """
        触发用户行为分析

        参数:
            task_id: 任务ID
            result_id: 风险分析结果ID
            complete_positions: 完整持仓数据（可选，如果提供则直接使用）
            progress_callback: 进度回调函数

        返回:
            bool: 是否成功
        """
        try:
            logger.info(f"========== 开始触发用户行为分析 ==========")
            logger.info(f"task_id: {task_id}, result_id: {result_id}")
            
            # 进度回调
            if progress_callback:
                progress_callback({
                    'stage': '用户行为分析',
                    'percentage': 95,
                    'message': '正在基于风险分析结果进行用户行为分析...'
                })
            
            # 1. 获取涉及的用户列表
            affected_users = self._get_affected_users(result_id)
            logger.info(f"发现 {len(affected_users)} 个用户需要进行行为分析")
            
            if not affected_users:
                logger.info("没有用户需要进行行为分析")
                return True
            
            # 2. 获取用户的完整持仓数据
            if complete_positions:
                # 如果提供了完整持仓数据，直接转换
                logger.info(f"使用提供的完整持仓数据: {len(complete_positions)} 个")
                position_data_list = self._convert_complete_positions_to_position_data(complete_positions, task_id)
                user_positions = self._group_positions_by_user(position_data_list)
            else:
                # 否则从数据库获取
                user_positions = self._get_user_positions_from_task(task_id)
            logger.info(f"获取到 {len(user_positions)} 个用户的持仓数据")
            
            # 3. 执行用户行为分析 - 显示累积进度
            total_users = len(affected_users)
            success_count = 0
            failed_count = 0
            no_data_count = 0
            processed_count = 0
            
            # 根据用户数量动态调整进度输出频率
            if total_users <= 50:
                progress_interval = 10
            elif total_users <= 200:
                progress_interval = 25
            else:
                progress_interval = 50
            
            logger.info(f"🚀 开始用户行为分析，共 {total_users} 个用户...")
            
            for user_id in affected_users:
                processed_count += 1
                
                try:
                    if user_id in user_positions:
                        success = self._analyze_single_user(user_id, user_positions[user_id])
                        if success:
                            success_count += 1
                        else:
                            failed_count += 1
                    else:
                        no_data_count += 1
                except Exception as e:
                    failed_count += 1
                    logger.debug(f"用户 {user_id} 行为分析异常: {str(e)}")
                    continue
                
                # 根据用户数量动态调整进度输出频率
                if processed_count % progress_interval == 0 or processed_count == total_users:
                    progress_percentage = (processed_count / total_users) * 100
                    logger.info(f"📊 用户行为分析进度: {processed_count}/{total_users} ({progress_percentage:.1f}%) "
                               f"- 成功={success_count}, 失败={failed_count}, 无数据={no_data_count}")
            
            logger.info(f"🎯 用户行为分析完成: 总用户数={total_users}, "
                       f"成功={success_count}, 失败={failed_count}, 无数据={no_data_count}")
            
            # 进度回调
            if progress_callback:
                progress_callback({
                    'stage': '分析完成',
                    'percentage': 100,
                    'message': f'用户行为分析完成，成功分析 {success_count} 个用户'
                })
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"触发用户行为分析失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def _get_affected_users(self, result_id: int) -> List[str]:
        """获取受影响的用户列表 - 🚀 修复：获取所有有持仓数据的用户，而不仅仅是异常用户"""
        try:
            from database.duckdb_manager import DuckDBManager

            db_manager = DuckDBManager()

            # 🚀 修复：从position_analysis表获取所有用户，而不是只从contract_risk_details获取异常用户
            # 首先尝试从最新的任务获取用户列表
            sql = """
            SELECT DISTINCT member_id
            FROM position_analysis
            WHERE task_id = (
                SELECT task_id
                FROM position_analysis
                ORDER BY created_at DESC
                LIMIT 1
            )
            AND member_id IS NOT NULL
            AND member_id != ''
            """

            results = db_manager.execute_query(sql, [])
            user_list = [result[0] for result in results if result[0]]

            logger.info(f"🚀 修复：从position_analysis表获取到 {len(user_list)} 个用户（包含所有用户，不仅仅是异常用户）")

            # 如果没有获取到用户，回退到原来的逻辑
            if not user_list:
                logger.warning("从position_analysis表未获取到用户，回退到contract_risk_details表")
                fallback_sql = """
                SELECT DISTINCT member_id
                FROM contract_risk_details
                WHERE algorithm_result_id = ?
                AND member_id IS NOT NULL
                AND member_id != ''
                """

                results = db_manager.execute_query(fallback_sql, [result_id])
                user_list = [result[0] for result in results if result[0]]
                logger.info(f"从contract_risk_details表获取到 {len(user_list)} 个用户")

            return user_list

        except Exception as e:
            logger.error(f"获取受影响用户列表失败: {str(e)}")
            return []
    
    def _get_user_positions_from_task(self, task_id: str) -> Dict:
        """从任务相关的数据中获取用户持仓数据"""
        try:
            from database.duckdb_manager import DuckDBManager
            
            db_manager = DuckDBManager()
            
            # 查询position_analysis表获取持仓数据
            sql = """
            SELECT
                member_id,
                position_id,
                contract_name,
                primary_side,
                open_time,
                close_time,
                duration_minutes,
                total_open_amount,
                total_close_amount,
                total_pnl,
                total_commission,
                net_pnl,
                leverage,
                market_orders_open,
                limit_orders_open,
                market_orders_close,
                limit_orders_close,
                cross_margin_positions,
                isolated_margin_positions
            FROM position_analysis
            WHERE task_id = ?
            ORDER BY member_id, open_time
            """
            
            results = db_manager.execute_query(sql, [task_id])
            
            # 按用户分组持仓数据
            user_positions = {}
            for row in results:
                member_id = row[0]
                if member_id not in user_positions:
                    user_positions[member_id] = []
                
                position_data = {
                    'position_id': row[1],
                    'contract_name': row[2],
                    'primary_side': row[3],
                    'open_time': row[4],
                    'close_time': row[5],
                    'duration_minutes': row[6],
                    'total_open_amount': row[7],
                    'total_close_amount': row[8],
                    'total_pnl': row[9],
                    'total_commission': row[10],
                    'net_pnl': row[11],
                    'leverage': row[12],
                    'market_orders_open': row[13],
                    'limit_orders_open': row[14],
                    'market_orders_close': row[15],
                    'limit_orders_close': row[16],
                    'cross_margin_positions': row[17],
                    'isolated_margin_positions': row[18]
                }
                user_positions[member_id].append(position_data)
            
            logger.info(f"从task_id {task_id} 获取到 {len(user_positions)} 个用户的持仓数据")
            return user_positions
            
        except Exception as e:
            logger.error(f"获取用户持仓数据失败: {str(e)}")
            return {}
    
    def _analyze_single_user(self, user_id: str, positions) -> bool:
        """分析单个用户 - 🚀 修复：支持PositionData对象和字典两种输入格式"""
        try:
            # 导入用户行为分析器
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

            from modules.user_analysis.services.user_behavior_analyzer import UserBehaviorAnalyzer
            from modules.user_analysis.models.user_behavior_models import PositionData

            # 🚀 修复：检查输入数据类型并相应处理
            position_data_list = []

            for pos in positions:
                try:
                    # 如果已经是PositionData对象，直接使用
                    if isinstance(pos, PositionData):
                        position_data_list.append(pos)
                    # 如果是字典，转换为PositionData对象
                    elif isinstance(pos, dict):
                        position_data = PositionData(
                            position_id=pos['position_id'],
                            member_id=user_id,
                            contract_name=pos['contract_name'],
                            primary_side=int(pos['primary_side']),
                            open_time=pos['open_time'],
                            close_time=pos['close_time'],
                            duration_minutes=float(pos['duration_minutes'] or 0),
                            total_open_amount=float(pos['total_open_amount'] or 0),
                            total_close_amount=float(pos['total_close_amount'] or 0),
                            avg_open_price=float(pos.get('avg_open_price', 0)),
                            avg_close_price=float(pos.get('avg_close_price', 0)),
                            total_pnl=float(pos['total_pnl'] or 0),
                            total_commission=float(pos['total_commission'] or 0),
                            net_pnl=float(pos['net_pnl'] or 0),
                            leverage=float(pos['leverage'] or 1),
                            # 🔧 修复：添加订单类型字段
                            market_orders_open=int(pos.get('market_orders_open', 0)),
                            limit_orders_open=int(pos.get('limit_orders_open', 0)),
                            market_orders_close=int(pos.get('market_orders_close', 0)),
                            limit_orders_close=int(pos.get('limit_orders_close', 0)),
                            # 🚀 新增：添加仓位模式字段
                            cross_margin_positions=int(pos.get('cross_margin_positions', 0)),
                            isolated_margin_positions=int(pos.get('isolated_margin_positions', 0))
                        )
                        position_data_list.append(position_data)
                    else:
                        logger.debug(f"未知的持仓数据类型: {type(pos)}")
                        continue

                except Exception as e:
                    logger.debug(f"转换持仓数据失败: {str(e)}")
                    continue
            
            if not position_data_list:
                return False
            
            # 创建分析器并执行分析
            analyzer = UserBehaviorAnalyzer()
            analysis_result = analyzer.analyze_user_behavior(user_id, position_data_list)

            if analysis_result is None:
                return False

            # 🚀 关键修复：保存分析结果到数据库
            # 导入合约分析器的保存方法
            from modules.contract_risk_analysis.services.contract_analyzer import CTContractAnalyzer

            # 创建临时分析器实例来调用保存方法
            temp_analyzer = CTContractAnalyzer()
            temp_analyzer._save_user_behavior_result(user_id, analysis_result)

            logger.debug(f"✅ 用户 {user_id} 行为分析结果已保存到数据库")
            return True
            
        except Exception as e:
            logger.error(f"分析用户 {user_id} 失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def _convert_complete_positions_to_position_data(self, complete_positions: Dict, task_id: str) -> List[Dict]:
        """
        将CompletePosition对象转换为PositionData对象
        包含完整的手续费计算和数据转换逻辑
        """
        try:
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

            from modules.user_analysis.models.user_behavior_models import PositionData
            from datetime import datetime
            import pandas as pd

            position_data_list = []
            converted_count = 0
            skipped_count = 0

            logger.info(f"开始转换 {len(complete_positions)} 个完整持仓数据...")

            for position_id, complete_position in complete_positions.items():
                try:
                    # 只分析已完成的持仓
                    if not complete_position.is_completed or not complete_position.first_close_time:
                        skipped_count += 1
                        continue

                    # 🚀 修复：使用CompletePosition中的真实手续费数据
                    total_commission = self._calculate_real_commission(complete_position)

                    # 安全转换函数
                    def safe_datetime(dt_value):
                        if dt_value is None:
                            return datetime.now()
                        if isinstance(dt_value, str):
                            try:
                                return pd.to_datetime(dt_value)
                            except:
                                return datetime.now()
                        return dt_value

                    def safe_float(value, default=0.0):
                        if value is None:
                            return default
                        try:
                            if hasattr(value, 'item'):
                                return float(value.item())
                            return float(value)
                        except (ValueError, TypeError):
                            return default

                    def safe_int(value, default=0):
                        if value is None:
                            return default
                        try:
                            if hasattr(value, 'item'):
                                return int(value.item())
                            return int(value)
                        except (ValueError, TypeError):
                            return default

                    def safe_str(value, default=""):
                        if value is None:
                            return default
                        return str(value)

                    # 计算净盈利（盈利减去手续费）
                    real_profit = safe_float(getattr(complete_position, 'real_profit', 0.0))
                    net_pnl = real_profit - total_commission

                    position_data = PositionData(
                        position_id=safe_str(complete_position.position_id),
                        member_id=safe_str(complete_position.member_id),
                        contract_name=safe_str(complete_position.contract_name),
                        primary_side=safe_int(complete_position.primary_side, 1),
                        open_time=safe_datetime(complete_position.first_open_time),
                        close_time=(safe_datetime(complete_position.first_close_time) if complete_position.first_close_time
                                   else safe_datetime(complete_position.first_open_time) + timedelta(minutes=15) if complete_position.first_open_time
                                   else datetime.now()),
                        duration_minutes=safe_float(getattr(complete_position, 'total_duration_minutes', getattr(complete_position, 'duration_minutes', 0))),
                        total_open_amount=safe_float(complete_position.total_open_amount),
                        total_close_amount=safe_float(complete_position.total_close_amount),
                        avg_open_price=safe_float(getattr(complete_position, 'avg_open_price', 0)),
                        avg_close_price=safe_float(getattr(complete_position, 'avg_close_price', 0)),
                        total_pnl=real_profit,
                        total_commission=safe_float(total_commission),
                        net_pnl=safe_float(net_pnl),
                        leverage=safe_float(getattr(complete_position, 'leverage', 1.0), 1.0),
                        task_id=safe_str(task_id),
                        # 🚀 修复：从CompletePosition对象中获取真实的订单类型统计
                        market_orders_open=safe_int(getattr(complete_position, 'market_orders_open', 0)),
                        limit_orders_open=safe_int(getattr(complete_position, 'limit_orders_open', 0)),
                        market_orders_close=safe_int(getattr(complete_position, 'market_orders_close', 0)),
                        limit_orders_close=safe_int(getattr(complete_position, 'limit_orders_close', 0)),
                        # 🚀 新增：从CompletePosition对象中获取仓位模式统计
                        cross_margin_positions=safe_int(getattr(complete_position, 'cross_margin_positions', 0)),
                        isolated_margin_positions=safe_int(getattr(complete_position, 'isolated_margin_positions', 0))
                    )

                    position_data_list.append(position_data)
                    converted_count += 1

                    # 每转换1000个数据记录一次进度
                    if converted_count % 1000 == 0:
                        logger.info(f"已转换 {converted_count} 个持仓数据...")

                except Exception as e:
                    logger.warning(f"转换持仓数据 {position_id} 失败: {str(e)}")
                    skipped_count += 1
                    continue

            logger.info(f"数据转换完成 - 成功转换: {converted_count}, 跳过: {skipped_count}")
            return position_data_list

        except Exception as e:
            logger.error(f"转换完整持仓数据失败: {str(e)}")
            return []

    def _calculate_real_commission(self, complete_position) -> float:
        """计算真实手续费"""
        total_commission = 0.0

        try:
            if hasattr(complete_position, 'total_fee') and complete_position.total_fee:
                # 使用已聚合的真实手续费
                total_commission = float(complete_position.total_fee)
                logger.debug(f"使用聚合手续费: {total_commission}")
            elif hasattr(complete_position, 'orders') and complete_position.orders:
                # 备选：汇总所有订单的真实手续费
                for order in complete_position.orders:
                    if hasattr(order, 'fee_usdt') and order.fee_usdt:
                        total_commission += float(order.fee_usdt)
                    elif hasattr(order, 'fee') and order.fee:
                        total_commission += float(order.fee)
                logger.debug(f"从订单计算手续费: {total_commission}")
            else:
                # 最后备选：估算手续费
                estimated_fee = float(complete_position.total_open_amount) * 0.0005
                total_commission = estimated_fee
                logger.warning(f"持仓 {complete_position.position_id} 使用估算手续费: {estimated_fee}")
        except Exception as e:
            logger.warning(f"计算手续费失败: {str(e)}, 使用默认值0")
            total_commission = 0.0

        return total_commission

    def _group_positions_by_user(self, position_data_list: List) -> Dict:
        """按用户分组持仓数据"""
        user_positions = {}

        for position_data in position_data_list:
            user_id = position_data.member_id
            if user_id not in user_positions:
                user_positions[user_id] = []

            # 转换为字典格式以保持兼容性
            position_dict = {
                'position_id': position_data.position_id,
                'contract_name': position_data.contract_name,
                'primary_side': position_data.primary_side,
                'open_time': position_data.open_time,
                'close_time': position_data.close_time,
                'duration_minutes': position_data.duration_minutes,
                'total_open_amount': position_data.total_open_amount,
                'total_close_amount': position_data.total_close_amount,
                'total_pnl': position_data.total_pnl,
                'total_commission': position_data.total_commission,
                'net_pnl': position_data.net_pnl,
                'leverage': position_data.leverage,
                # 🚀 关键修复：添加订单类型字段，防止数据丢失
                'market_orders_open': getattr(position_data, 'market_orders_open', 0),
                'limit_orders_open': getattr(position_data, 'limit_orders_open', 0),
                'market_orders_close': getattr(position_data, 'market_orders_close', 0),
                'limit_orders_close': getattr(position_data, 'limit_orders_close', 0),
                # 🚀 关键修复：添加保证金模式字段，防止数据丢失
                'cross_margin_positions': getattr(position_data, 'cross_margin_positions', 0),
                'isolated_margin_positions': getattr(position_data, 'isolated_margin_positions', 0)
            }
            user_positions[user_id].append(position_dict)

        logger.info(f"按用户分组完成: {len(user_positions)} 个用户")
        return user_positions

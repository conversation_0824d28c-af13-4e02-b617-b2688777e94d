#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量处理配置加载器
版本: 1.0
创建时间: 2025-08-01

负责加载和管理增量处理相关的配置参数
"""

import os
import yaml
import logging
from dataclasses import dataclass, field
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

@dataclass
class ToleranceConfig:
    """容差配置"""
    absolute: float = 0.0001  # 绝对容差 (USDT)
    relative: float = 0.001   # 相对容差 (0.1%)

@dataclass
class MatchingConfig:
    """匹配配置"""
    tolerance: ToleranceConfig = field(default_factory=ToleranceConfig)
    max_time_gap_hours: int = 24  # 最大时间间隔（小时）
    enable_fuzzy_matching: bool = True  # 启用模糊匹配

@dataclass
class CompletionCheckConfig:
    """完整性检查配置"""
    deal_vol_tolerance: ToleranceConfig = field(default_factory=ToleranceConfig)
    min_position_amount: float = 1.0  # 最小持仓金额
    max_position_duration_hours: int = 168  # 最大持仓时长（小时）
    min_completion_ratio: float = 0.95  # 最小完整度阈值

@dataclass
class WaitingTableConfig:
    """等待表配置"""
    max_waiting_days: int = 30  # 最大等待天数
    max_check_count: int = 100  # 最大检查次数
    cleanup_interval_hours: int = 24  # 清理间隔（小时）

@dataclass
class PerformanceConfig:
    """性能配置"""
    batch_size: int = 1000  # 批处理大小
    max_memory_usage_mb: int = 512  # 最大内存使用（MB）
    enable_parallel_processing: bool = False  # 启用并行处理
    max_workers: int = 4  # 最大工作线程数

@dataclass
class DuplicateHandlingConfig:
    """重复数据处理配置"""
    default_strategy: str = "smart_update"  # 默认策略: skip, replace, smart_update, merge
    update_threshold: float = 0.7  # 更新阈值
    enable_deduplication: bool = True  # 启用去重

@dataclass
class ErrorHandlingConfig:
    """错误处理配置"""
    max_retry_count: int = 3  # 最大重试次数
    retry_delay_seconds: int = 1  # 重试延迟（秒）
    enable_fallback: bool = True  # 启用降级处理
    log_level: str = "INFO"  # 日志级别

@dataclass
class ValidationConfig:
    """验证配置"""
    required_fields: list = field(default_factory=lambda: [
        'position_id', 'member_id', 'contract_name', 'side', 'deal_vol_usdt'
    ])
    # 🚀 修复：时间字段支持多种名称
    time_fields: list = field(default_factory=lambda: [
        'timestamp', 'create_time', 'order_create_time'
    ])
    enable_data_quality_check: bool = True  # 启用数据质量检查
    max_null_ratio: float = 0.1  # 最大空值比例

@dataclass
class OptimizationConfig:
    """优化配置"""
    enable_caching: bool = True  # 启用缓存
    cache_ttl_minutes: int = 60  # 缓存TTL（分钟）
    enable_indexing: bool = True  # 启用索引优化

@dataclass
class DatabaseConfig:
    """数据库配置"""
    connection_timeout: int = 30  # 连接超时（秒）
    query_timeout: int = 300  # 查询超时（秒）
    max_connections: int = 10  # 最大连接数

@dataclass
class BasicSettingsConfig:
    """基础设置配置"""
    enable_incremental_processing: bool = True  # 启用增量处理
    default_processing_mode: str = "incremental"  # 默认处理模式
    log_level: str = "INFO"  # 日志级别

@dataclass
class IncrementalProcessingConfig:
    """增量处理完整配置"""
    basic_settings: BasicSettingsConfig = field(default_factory=BasicSettingsConfig)
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    matching: MatchingConfig = field(default_factory=MatchingConfig)
    completion_check: CompletionCheckConfig = field(default_factory=CompletionCheckConfig)
    waiting_table: WaitingTableConfig = field(default_factory=WaitingTableConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    duplicate_handling: DuplicateHandlingConfig = field(default_factory=DuplicateHandlingConfig)
    error_handling: ErrorHandlingConfig = field(default_factory=ErrorHandlingConfig)
    validation: ValidationConfig = field(default_factory=ValidationConfig)
    optimization: OptimizationConfig = field(default_factory=OptimizationConfig)

class ConfigLoader:
    """配置加载器"""
    
    def __init__(self):
        self._config_cache = {}
    
    def load_config(self, config_path: str) -> IncrementalProcessingConfig:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            IncrementalProcessingConfig: 配置对象
        """
        if config_path in self._config_cache:
            return self._config_cache[config_path]
        
        try:
            if not os.path.exists(config_path):
                logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
                config = IncrementalProcessingConfig()
            else:
                with open(config_path, 'r', encoding='utf-8') as f:
                    yaml_data = yaml.safe_load(f)
                
                config = self._parse_config(yaml_data)
                logger.info(f"成功加载配置文件: {config_path}")
            
            self._config_cache[config_path] = config
            return config
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return IncrementalProcessingConfig()
    
    def _parse_config(self, yaml_data: Dict[str, Any]) -> IncrementalProcessingConfig:
        """解析YAML配置数据"""
        config = IncrementalProcessingConfig()
        
        # 解析各个配置段
        if 'basic_settings' in yaml_data:
            config.basic_settings = self._parse_basic_settings(yaml_data['basic_settings'])
        
        if 'database' in yaml_data:
            config.database = self._parse_database_config(yaml_data['database'])
        
        if 'matching' in yaml_data:
            config.matching = self._parse_matching_config(yaml_data['matching'])
        
        if 'completion_check' in yaml_data:
            config.completion_check = self._parse_completion_check_config(yaml_data['completion_check'])
        
        if 'waiting_table' in yaml_data:
            config.waiting_table = self._parse_waiting_table_config(yaml_data['waiting_table'])
        
        if 'performance' in yaml_data:
            config.performance = self._parse_performance_config(yaml_data['performance'])
        
        if 'duplicate_handling' in yaml_data:
            config.duplicate_handling = self._parse_duplicate_handling_config(yaml_data['duplicate_handling'])
        
        if 'error_handling' in yaml_data:
            config.error_handling = self._parse_error_handling_config(yaml_data['error_handling'])
        
        if 'validation' in yaml_data:
            config.validation = self._parse_validation_config(yaml_data['validation'])
        
        if 'optimization' in yaml_data:
            config.optimization = self._parse_optimization_config(yaml_data['optimization'])
        
        return config
    
    def _parse_basic_settings(self, data: Dict) -> BasicSettingsConfig:
        """解析基础设置配置"""
        return BasicSettingsConfig(
            enable_incremental_processing=data.get('enable_incremental_processing', True),
            default_processing_mode=data.get('default_processing_mode', 'incremental'),
            log_level=data.get('log_level', 'INFO')
        )
    
    def _parse_database_config(self, data: Dict) -> DatabaseConfig:
        """解析数据库配置"""
        return DatabaseConfig(
            connection_timeout=data.get('connection_timeout', 30),
            query_timeout=data.get('query_timeout', 300),
            max_connections=data.get('max_connections', 10)
        )
    
    def _parse_matching_config(self, data: Dict) -> MatchingConfig:
        """解析匹配配置"""
        tolerance_data = data.get('tolerance', {})
        tolerance = ToleranceConfig(
            absolute=tolerance_data.get('absolute', 0.0001),
            relative=tolerance_data.get('relative', 0.001)
        )
        
        return MatchingConfig(
            tolerance=tolerance,
            max_time_gap_hours=data.get('max_time_gap_hours', 24),
            enable_fuzzy_matching=data.get('enable_fuzzy_matching', True)
        )
    
    def _parse_completion_check_config(self, data: Dict) -> CompletionCheckConfig:
        """解析完整性检查配置"""
        tolerance_data = data.get('deal_vol_tolerance', {})
        tolerance = ToleranceConfig(
            absolute=tolerance_data.get('absolute', 0.0001),
            relative=tolerance_data.get('relative', 0.001)
        )
        
        return CompletionCheckConfig(
            deal_vol_tolerance=tolerance,
            min_position_amount=data.get('min_position_amount', 1.0),
            max_position_duration_hours=data.get('max_position_duration_hours', 168),
            min_completion_ratio=data.get('min_completion_ratio', 0.95)
        )
    
    def _parse_waiting_table_config(self, data: Dict) -> WaitingTableConfig:
        """解析等待表配置"""
        return WaitingTableConfig(
            max_waiting_days=data.get('max_waiting_days', 30),
            max_check_count=data.get('max_check_count', 100),
            cleanup_interval_hours=data.get('cleanup_interval_hours', 24)
        )
    
    def _parse_performance_config(self, data: Dict) -> PerformanceConfig:
        """解析性能配置"""
        return PerformanceConfig(
            batch_size=data.get('batch_size', 1000),
            max_memory_usage_mb=data.get('max_memory_usage_mb', 512),
            enable_parallel_processing=data.get('enable_parallel_processing', False),
            max_workers=data.get('max_workers', 4)
        )
    
    def _parse_duplicate_handling_config(self, data: Dict) -> DuplicateHandlingConfig:
        """解析重复数据处理配置"""
        return DuplicateHandlingConfig(
            default_strategy=data.get('default_strategy', 'smart_update'),
            update_threshold=data.get('update_threshold', 0.7),
            enable_deduplication=data.get('enable_deduplication', True)
        )
    
    def _parse_error_handling_config(self, data: Dict) -> ErrorHandlingConfig:
        """解析错误处理配置"""
        return ErrorHandlingConfig(
            max_retry_count=data.get('max_retry_count', 3),
            retry_delay_seconds=data.get('retry_delay_seconds', 1),
            enable_fallback=data.get('enable_fallback', True),
            log_level=data.get('log_level', 'INFO')
        )
    
    def _parse_validation_config(self, data: Dict) -> ValidationConfig:
        """解析验证配置"""
        return ValidationConfig(
            required_fields=data.get('required_fields', [
                'position_id', 'member_id', 'contract_name', 'side', 'deal_vol_usdt'
            ]),
            time_fields=data.get('time_fields', [
                'timestamp', 'create_time', 'order_create_time'
            ]),
            enable_data_quality_check=data.get('enable_data_quality_check', True),
            max_null_ratio=data.get('max_null_ratio', 0.1)
        )
    
    def _parse_optimization_config(self, data: Dict) -> OptimizationConfig:
        """解析优化配置"""
        return OptimizationConfig(
            enable_caching=data.get('enable_caching', True),
            cache_ttl_minutes=data.get('cache_ttl_minutes', 60),
            enable_indexing=data.get('enable_indexing', True)
        )

# 全局配置加载器实例
config_loader = ConfigLoader()

import os
import json
import logging
from typing import Dict, Any, Optional

# 配置日志记录
logger = logging.getLogger(__name__)

class ConfigManager:
    """
    配置管理器
    用于管理各算法的配置参数
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        参数:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        self.config_path = config_path or os.path.join(os.path.dirname(__file__), 'config/algorithms.json')
        self.configs = self._load_configs()
        
        # 添加阈值配置
        self.thresholds_config = {
            "risk_bonus": {
                "leverage": {
                    "level1": 50,
                    "level2": 100
                },
                "volume_ratio": {
                    "level1": 0.05,
                    "level2": 0.15,
                    "level3": 0.30
                }
            },
            "risk_levels": {
                "high_frequency_trading": {
                    "high": 85,
                    "medium": 60
                },
                "suspected_wash_trading": {
                    "high": 80,
                    "medium": 55
                },
                "funding_rate_arbitrage": {
                    "high": 90,
                    "medium": 65
                },
                "regular_brush_trading": {
                    "high": 85,
                    "medium": 60
                }
            },
            "combo_upgrade": {
                "high_medium_threshold": 1,
                "medium_low_threshold": 2
            }
        }
        
        # 添加报告模板配置
        self.report_templates = {
            "high_frequency_trading_reason": "检测到高频交易，{risk_level}风险。交易量{volume}USDT，交易次数{trades_count}次，风险评分{score}。",
            "suspected_wash_trading_reason": "检测到疑似洗盘交易，{risk_level}风险。交易量{volume}USDT，交易匹配度{match_degree}，盈亏平衡度{profit_balance}，风险评分{score}。",
            "funding_rate_arbitrage_reason": "检测到资金费率套利，{risk_level}风险。交易量{volume}USDT，资金费率相关性{funding_correlation}，风险评分{score}。",
            "regular_brush_trading_reason": "检测到规律性刷量，{risk_level}风险。交易量{volume}USDT，时间规律性{time_regularity}，风险评分{score}。"
        }
        
    def _load_configs(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        返回:
            配置字典
        """
        # 确保配置目录存在
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        
        # 如果配置文件不存在，创建默认配置
        if not os.path.exists(self.config_path):
            default_configs = self._create_default_configs()
            self._save_configs(default_configs)
            return default_configs
            
        # 尝试加载配置
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}", exc_info=True)
            # 创建并返回默认配置
            default_configs = self._create_default_configs()
            self._save_configs(default_configs)
            return default_configs
            
    def _save_configs(self, configs: Dict[str, Any]) -> bool:
        """
        保存配置到文件
        
        参数:
            configs: 配置字典
            
        返回:
            是否保存成功
        """
        try:
            with open(self.config_path, 'w') as f:
                json.dump(configs, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {str(e)}", exc_info=True)
            return False
            
    def _create_default_configs(self) -> Dict[str, Any]:
        """
        创建默认配置
        
        返回:
            默认配置字典
        """
        return {
            "high_frequency_trading": {
                "min_trades": 15,
                "max_trade_interval_seconds": 30,
                "min_volume": 1000,
                "window_minutes": 60,
                "sensitivity": 1.0
            },
            "wash_trading": {
                "min_trades": 10,
                "price_reversion_threshold": 0.8,
                "min_trade_match_degree": 0.7,
                "min_profit_balance": 0.8,
                "sensitivity": 1.0
            },
            "funding_rate_arbitrage": {
                "min_trades": 5,
                "time_window_minutes": 60,
                "min_funding_correlation": 0.6,
                "sensitivity": 1.0
            },
            "regular_brush_trading": {
                "min_trades": 15,
                "min_short_position_ratio": 0.7,
                "max_avg_position_time_minutes": 5,
                "min_time_regularity": 0.7,
                "sensitivity": 1.0
            }
        }
        
    def get_algorithm_config(self, algorithm_name: str) -> Dict[str, Any]:
        """
        获取算法配置
        
        参数:
            algorithm_name: 算法名称
            
        返回:
            算法配置字典，如果不存在则返回空字典
        """
        return self.configs.get(algorithm_name, {})
        
    def update_algorithm_config(self, algorithm_name: str, new_config: Dict[str, Any]) -> bool:
        """
        更新算法配置
        
        参数:
            algorithm_name: 算法名称
            new_config: 新配置字典
            
        返回:
            是否更新成功
        """
        # 更新配置
        if algorithm_name in self.configs:
            self.configs[algorithm_name].update(new_config)
        else:
            self.configs[algorithm_name] = new_config
            
        # 保存到文件
        return self._save_configs(self.configs)
        
    def set_sensitivity(self, algorithm_name: str, sensitivity: float) -> bool:
        """
        设置算法敏感度
        
        参数:
            algorithm_name: 算法名称
            sensitivity: 敏感度值 (0.1-2.0，越大越敏感)
            
        返回:
            是否设置成功
        """
        if algorithm_name not in self.configs:
            self.configs[algorithm_name] = {"sensitivity": sensitivity}
        else:
            self.configs[algorithm_name]["sensitivity"] = sensitivity
            
        return self._save_configs(self.configs)
        
    def get_sensitivity(self, algorithm_name: str) -> float:
        """
        获取算法敏感度
        
        参数:
            algorithm_name: 算法名称
            
        返回:
            敏感度值，默认为1.0
        """
        if algorithm_name in self.configs and "sensitivity" in self.configs[algorithm_name]:
            return self.configs[algorithm_name]["sensitivity"]
        return 1.0
        
    def get_algorithm_weights(self, algorithm_name: str) -> Dict[str, float]:
        """
        获取算法指标权重配置
        
        参数:
            algorithm_name: 算法名称
            
        返回:
            权重配置字典
        """
        default_weights = {
            "high_frequency_trading": {"trades_per_minute": 0.6, "volume_ratio": 0.4},
            "suspected_wash_trading": {"match_degree": 0.5, "profit_balance": 0.5},
            "funding_rate_arbitrage": {"funding_correlation": 0.7, "profit_ratio": 0.3},
            "regular_brush_trading": {"time_regularity": 0.5, "position_ratio": 0.5}
        }
        
        return default_weights.get(algorithm_name, {})
        
    def get_risk_level_thresholds(self, algorithm_name: str) -> Dict[str, float]:
        """
        获取风险等级阈值配置
        
        参数:
            algorithm_name: 算法名称
            
        返回:
            风险等级阈值配置字典
        """
        return self.thresholds_config.get('risk_levels', {}).get(algorithm_name, {})
        
    def get_template(self, template_key: str) -> str:
        """
        获取报告模板
        
        参数:
            template_key: 模板键名
            
        返回:
            报告模板字符串，如果不存在则返回空字符串
        """
        return self.report_templates.get(template_key, "") 
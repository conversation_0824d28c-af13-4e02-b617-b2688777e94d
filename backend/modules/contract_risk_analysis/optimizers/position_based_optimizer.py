"""
基于positionId的完整订单分析优化器
解决现有系统的核心问题：
1. profit计算错误 - 直接使用原始profit字段
2. 对敲检测低效 - 首笔开仓预筛选
3. 高频检测误判 - 区分正常加仓与异常高频
4. 数据利用不充分 - 构建完整订单生命周期
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class CompletePosition:
    """完整订单（仓位）数据结构"""
    position_id: str
    member_id: str
    contract_name: str

    # 开仓信息聚合
    first_open_time: datetime
    last_open_time: datetime
    total_open_amount: float
    total_open_volume: float
    avg_open_price: float
    open_trades_count: int
    primary_side: int  # 主要方向（1=多，3=空）

    # 平仓信息聚合
    first_close_time: Optional[datetime]
    last_close_time: Optional[datetime]
    total_close_amount: float
    total_close_volume: float
    avg_close_price: float
    close_trades_count: int

    # 订单特征
    is_completed: bool  # 是否完全平仓
    total_duration_minutes: float  # 总持仓时长
    real_profit: float  # 真实盈亏（来自原始profit字段）
    calculated_profit: float  # 计算盈亏（价差×数量）

    # 交易行为特征
    is_quick_trade: bool  # 是否快进快出（<5分钟）
    is_scalping: bool  # 是否刷单行为
    add_position_count: int  # 加仓次数
    reduce_position_count: int  # 减仓次数

    # 风险指标
    risk_score: float  # 风险评分
    abnormal_flags: List[str]  # 异常标记

    # 🚀 新增：杠杆和手续费信息（有默认值的字段放在最后）
    leverage: float = 1.0  # 杠杆倍数，默认1倍
    total_fee: float = 0.0  # 总手续费（来自fee_usdt字段）

    # 🚀 新增：订单类型统计（基于liquidity字段：taker=市价单，maker=限价单）
    market_orders_open: int = 0    # 开仓市价单数量
    limit_orders_open: int = 0     # 开仓限价单数量
    market_orders_close: int = 0   # 平仓市价单数量
    limit_orders_close: int = 0    # 平仓限价单数量

    # 🚀 修复：仓位模式标识（基于open_type字段：2=全仓，1=逐仓）
    cross_margin_positions: int = 0   # 是否为全仓模式（1=是，0=否）
    isolated_margin_positions: int = 0  # 是否为逐仓模式（1=是，0=否）


class PositionBasedOptimizer:
    """基于positionId的完整订单分析器"""
    
    def __init__(self):
        self.complete_positions: Dict[str, CompletePosition] = {}
        self.user_positions_map: Dict[str, List[str]] = defaultdict(list)
        self.contract_positions_map: Dict[str, List[str]] = defaultdict(list)
        self.time_window_index: Dict[str, List[str]] = defaultdict(list)
        
        # 🚀 第四代对敲检测配置参数 (硬编码配置)
        # 经过多轮优化和测试，这些参数提供了最佳的检测效果
        self.config = {
            # 基础检测参数
            'quick_trade_threshold_minutes': 5,    # 快进快出阈值（分钟）
            'scalping_trades_threshold': 10,       # 刷单交易次数阈值
            'high_frequency_threshold': 20,        # 高频交易阈值（笔/小时）

            # 🎯 对敲检测核心参数
            'wash_trading_time_window': 15,        # 对敲时间窗口（秒）- 同账户和跨账户统一
            'amount_match_tolerance': 0.05,        # 金额匹配容差（5%）- 相对容差
            'profit_hedge_threshold': 0.7,         # 盈亏对冲阈值（0.7）- 关键特征
            'wash_score_threshold': 0.7,           # 对敲综合评分阈值（0.7）
        }
    
    def build_complete_positions(self, df: pd.DataFrame) -> Dict[str, CompletePosition]:
        """
        构建完整的订单画像
        核心：将散乱的交易记录重构为完整的订单生命周期
        """
        logger.info(f"开始构建完整订单画像，数据量: {len(df)} 条记录")

        if df.empty:
            return {}

        # 🚀 修复：支持多种时间字段名称
        # 确保必要字段存在，时间字段支持 timestamp 或 create_time
        required_fields = ['position_id', 'member_id', 'contract_name', 'side', 'deal_vol_usdt']
        time_field = None

        # 检查时间字段
        if 'timestamp' in df.columns:
            time_field = 'timestamp'
        elif 'create_time' in df.columns:
            time_field = 'create_time'
        elif 'order_create_time' in df.columns:
            time_field = 'order_create_time'

        if time_field is None:
            logger.error("缺少时间字段: 需要 'timestamp'、'create_time' 或 'order_create_time' 中的任意一个")
            return {}

        # 检查其他必要字段
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            logger.error(f"缺少必要字段: {missing_fields}")
            return {}

        # 预处理：统一时间字段名称并确保是datetime类型
        df_processed = df.copy()

        # 将时间字段统一命名为 timestamp
        if time_field != 'timestamp':
            df_processed['timestamp'] = df_processed[time_field]
            logger.info(f"时间字段 '{time_field}' 已重命名为 'timestamp'")

        # 确保timestamp字段是datetime类型 - 🚀 统一时间格式处理
        if not pd.api.types.is_datetime64_any_dtype(df_processed['timestamp']):
            try:
                # 🚀 统一时间格式处理：优先处理标准格式 2025/7/23 21:42:17
                df_processed['timestamp'] = pd.to_datetime(df_processed['timestamp'], format='mixed', errors='coerce')
                logger.info(f"时间字段转换完成，有效时间数: {df_processed['timestamp'].notna().sum()}")
            except Exception as e:
                logger.warning(f"时间字段转换失败: {e}")
                # 如果转换失败，尝试使用当前时间作为默认值
                df_processed['timestamp'] = pd.Timestamp.now()
        
        # 按positionId分组
        position_groups = df_processed.groupby('position_id')
        
        for position_id, group in position_groups:
            try:
                # 按时间排序
                group_sorted = group.sort_values('timestamp')
                
                # 基本信息
                member_id = str(group_sorted.iloc[0]['member_id'])
                contract_name = str(group_sorted.iloc[0]['contract_name'])
                
                # 分离开仓和平仓记录
                open_trades = group_sorted[group_sorted['side'].isin([1, 3])]  # 1=开多, 3=开空
                close_trades = group_sorted[group_sorted['side'].isin([2, 4])]  # 2=平空, 4=平多
                
                # 处理开仓信息
                has_real_open_trades = len(open_trades) > 0  # 🚀 标记是否有真实开仓记录

                if has_real_open_trades:
                    first_open_time = open_trades.iloc[0]['timestamp']
                    last_open_time = open_trades.iloc[-1]['timestamp']
                    total_open_amount = open_trades['deal_vol_usdt'].sum()
                    total_open_volume = open_trades.get('deal_vol', open_trades['deal_vol_usdt']).sum()
                    
                    # 计算加权平均开仓价
                    if 'deal_avg_price' in open_trades.columns:
                        weights = open_trades['deal_vol_usdt']
                        avg_open_price = (open_trades['deal_avg_price'] * weights).sum() / weights.sum()
                    else:
                        avg_open_price = 0.0
                    
                    open_trades_count = len(open_trades)
                    primary_side = open_trades.iloc[0]['side']  # 主要方向取第一笔
                    add_position_count = max(0, len(open_trades) - 1)  # 除首次开仓外的加仓次数
                else:
                    # 🚀 新逻辑：没有开仓记录时，不进行推算，直接跳过
                    logger.debug(f"Position {position_id}: 没有开仓记录，跳过处理（不进行推算）")

                    # 如果有平仓记录但没有开仓记录，记录日志但不处理
                    if len(close_trades) > 0:
                        logger.debug(f"Position {position_id}: 只有平仓记录，等待增量模式从历史数据补全")
                    else:
                        logger.debug(f"Position {position_id}: 既没有开仓也没有平仓记录，跳过处理")

                    # 跳过这个position，不构建CompletePosition对象
                    continue
                
                # 处理平仓信息
                if len(close_trades) > 0:
                    first_close_time = close_trades.iloc[0]['timestamp']
                    last_close_time = close_trades.iloc[-1]['timestamp']
                    total_close_amount = close_trades['deal_vol_usdt'].sum()
                    total_close_volume = close_trades.get('deal_vol', close_trades['deal_vol_usdt']).sum()
                    
                    # 计算加权平均平仓价
                    if 'deal_avg_price' in close_trades.columns:
                        weights = close_trades['deal_vol_usdt']
                        avg_close_price = (close_trades['deal_avg_price'] * weights).sum() / weights.sum()
                    else:
                        avg_close_price = 0.0
                    
                    close_trades_count = len(close_trades)
                    reduce_position_count = close_trades_count
                    # 🚀 修复：位置优化器只做基础判断，真正的完整性判断交给增量处理器
                    # 这里只标记是否有真实的开仓和平仓记录，不做数量匹配判断
                    is_completed = has_real_open_trades  # 有真实开仓记录且有平仓记录
                else:
                    # 没有平仓记录，标记为未完成
                    first_close_time = None
                    last_close_time = None
                    total_close_amount = 0.0
                    total_close_volume = 0.0
                    avg_close_price = 0.0
                    close_trades_count = 0
                    reduce_position_count = 0
                    is_completed = False  # 没有平仓记录认为未完成
                
                # 计算持仓时长 - 🚀 修复：确保时间字段是datetime对象
                try:
                    if is_completed and first_close_time:
                        # 确保时间字段是datetime对象
                        close_time = last_close_time
                        open_time = first_open_time

                        if isinstance(close_time, str):
                            close_time = pd.to_datetime(close_time)
                        if isinstance(open_time, str):
                            open_time = pd.to_datetime(open_time)

                        total_duration_minutes = (close_time - open_time).total_seconds() / 60
                    else:
                        # 未完成订单，计算到最后一笔交易的时长
                        last_trade_time = group_sorted.iloc[-1]['timestamp']
                        open_time = first_open_time

                        if isinstance(open_time, str):
                            open_time = pd.to_datetime(open_time)
                        if isinstance(last_trade_time, str):
                            last_trade_time = pd.to_datetime(last_trade_time)

                        total_duration_minutes = (last_trade_time - open_time).total_seconds() / 60
                except Exception as e:
                    logger.warning(f"计算持仓时长失败: {e}, 使用默认值")
                    total_duration_minutes = 0.0
                
                # 关键：真实盈亏计算 - 优先使用原始profit字段，备选calculated_profit
                real_profit = 0.0

                # 🚀 修复：添加调试日志，检查可用字段
                logger.debug(f"Position {position_id} 可用字段: {list(group_sorted.columns)}")

                # 方案1：优先使用原始profit字段
                if 'profit' in group_sorted.columns:
                    profit_values = group_sorted['profit'].dropna()
                    if len(profit_values) > 0:
                        real_profit = profit_values.sum()
                        logger.debug(f"Position {position_id} 使用原始profit字段: {real_profit}")
                    else:
                        logger.debug(f"Position {position_id} profit字段存在但全为空")


                # 🚀 新增：杠杆信息处理
                leverage = 1.0  # 默认值
                if 'leverage' in group_sorted.columns:
                    # 取第一笔交易的杠杆值（通常一个position的杠杆是固定的）
                    leverage_values = group_sorted['leverage'].dropna()
                    if len(leverage_values) > 0:
                        leverage = float(leverage_values.iloc[0])
                        # 验证杠杆值的合理性
                        if leverage <= 0 or leverage > 1000:
                            logger.warning(f"持仓 {position_id} 杠杆值异常: {leverage}，使用默认值1.0")
                            leverage = 1.0

                # 🚀 新增：真实手续费处理
                total_fee = 0.0  # 默认值
                if 'fee_usdt' in group_sorted.columns:
                    # 聚合所有相关交易的手续费
                    fee_values = group_sorted['fee_usdt'].fillna(0)
                    total_fee = float(fee_values.sum())
                    if total_fee < 0:
                        logger.warning(f"持仓 {position_id} 手续费为负值: {total_fee}，使用0")
                        total_fee = 0.0
                elif 'fee' in group_sorted.columns:
                    # 备选：使用fee字段
                    fee_values = group_sorted['fee'].fillna(0)
                    total_fee = float(fee_values.sum())
                    if total_fee < 0:
                        total_fee = 0.0

                # 🚀 修复：计算盈亏（用于对比和备选）
                calculated_profit = 0.0
                if is_completed and avg_open_price > 0 and avg_close_price > 0:
                    if primary_side == 1:  # 多头
                        calculated_profit = (avg_close_price - avg_open_price) * total_close_volume
                    else:  # 空头
                        calculated_profit = (avg_open_price - avg_close_price) * total_close_volume

                    logger.debug(f"Position {position_id} calculated_profit: {calculated_profit} "
                               f"(方向:{primary_side}, 开仓价:{avg_open_price}, 平仓价:{avg_close_price}, 量:{total_close_volume})")
                
                # 交易行为特征
                is_quick_trade = total_duration_minutes < self.config['quick_trade_threshold_minutes']
                is_scalping = (open_trades_count + close_trades_count) >= self.config['scalping_trades_threshold']
                
                # 风险评分（简单版本）
                risk_flags = []
                risk_score = 0.0

                if is_quick_trade:
                    risk_flags.append('quick_trade')
                    risk_score += 0.3

                if is_scalping:
                    risk_flags.append('scalping')
                    risk_score += 0.4

                if add_position_count > 5:
                    risk_flags.append('frequent_add_position')
                    risk_score += 0.2

                # 🚀 新增：统计订单类型（基于liquidity字段：taker=市价单，maker=限价单）
                market_orders_open = 0
                limit_orders_open = 0
                market_orders_close = 0
                limit_orders_close = 0

                # 统计开仓订单类型
                if len(open_trades) > 0 and 'liquidity' in open_trades.columns:
                    for _, trade in open_trades.iterrows():
                        liquidity = str(trade.get('liquidity', '')).lower()
                        if liquidity == 'taker':
                            market_orders_open += 1
                        elif liquidity == 'maker':
                            limit_orders_open += 1
                        # 如果liquidity字段为空或其他值，不统计

                # 统计平仓订单类型
                if len(close_trades) > 0 and 'liquidity' in close_trades.columns:
                    for _, trade in close_trades.iterrows():
                        liquidity = str(trade.get('liquidity', '')).lower()
                        if liquidity == 'taker':
                            market_orders_close += 1
                        elif liquidity == 'maker':
                            limit_orders_close += 1
                        # 如果liquidity字段为空或其他值，不统计

                # 🚀 修复：确定仓位模式（一个position只能有一种模式）
                is_cross_margin = 0  # 是否为全仓模式（1=是，0=否）
                is_isolated_margin = 0  # 是否为逐仓模式（1=是，0=否）

                # 检查仓位模式：从第一个有效的open_type值确定整个position的模式
                all_trades = pd.concat([open_trades, close_trades]) if len(open_trades) > 0 and len(close_trades) > 0 else (open_trades if len(open_trades) > 0 else close_trades)
                if len(all_trades) > 0 and 'open_type' in all_trades.columns:
                    # 获取第一个非空的open_type值来确定position的模式
                    for _, trade in all_trades.iterrows():
                        open_type = trade.get('open_type')
                        if pd.notna(open_type) and open_type != '':
                            if open_type == 2:
                                is_cross_margin = 1
                                break
                            elif open_type == 1:
                                is_isolated_margin = 1
                                break

                # 构建完整订单对象
                complete_position = CompletePosition(
                    position_id=str(position_id),
                    member_id=member_id,
                    contract_name=contract_name,
                    first_open_time=first_open_time,
                    last_open_time=last_open_time,
                    total_open_amount=total_open_amount,
                    total_open_volume=total_open_volume,
                    avg_open_price=avg_open_price,
                    open_trades_count=open_trades_count,
                    primary_side=primary_side,
                    first_close_time=first_close_time,
                    last_close_time=last_close_time,
                    total_close_amount=total_close_amount,
                    total_close_volume=total_close_volume,
                    avg_close_price=avg_close_price,
                    close_trades_count=close_trades_count,
                    is_completed=is_completed,
                    total_duration_minutes=total_duration_minutes,
                    real_profit=real_profit,
                    calculated_profit=calculated_profit,
                    is_quick_trade=is_quick_trade,
                    is_scalping=is_scalping,
                    add_position_count=add_position_count,
                    reduce_position_count=reduce_position_count,
                    risk_score=risk_score,
                    abnormal_flags=risk_flags,
                    leverage=leverage,  # 🚀 新增：杠杆字段
                    total_fee=total_fee,  # 🚀 新增：真实手续费字段
                    # 🚀 新增：订单类型统计字段
                    market_orders_open=market_orders_open,
                    limit_orders_open=limit_orders_open,
                    market_orders_close=market_orders_close,
                    limit_orders_close=limit_orders_close,
                    # 🚀 修复：仓位模式标识字段（每个position只有一种模式）
                    cross_margin_positions=is_cross_margin,
                    isolated_margin_positions=is_isolated_margin
                )
                
                # 存储完整订单
                self.complete_positions[str(position_id)] = complete_position

                # 🚀 新增：如果订单不完整，保存到等待表供增量模式使用
                if not is_completed:
                    self._save_incomplete_position_to_waiting_table(complete_position)

                # 构建索引
                self._build_indexes(complete_position)
                
            except Exception as e:
                logger.warning(f"构建订单 {position_id} 失败: {str(e)}")
                continue

        logger.info(f"完整订单构建完成，共 {len(self.complete_positions)} 个订单")

        # 统计不完整订单数量
        incomplete_count = sum(1 for pos in self.complete_positions.values() if not pos.is_completed)
        if incomplete_count > 0:
            logger.info(f"📋 其中 {incomplete_count} 个不完整订单已保存到等待表，供增量模式使用")

        return self.complete_positions

    def get_config_info(self) -> Dict[str, Any]:
        """
        获取第四代对敲检测器的配置信息

        返回:
            配置信息字典，包含所有参数说明
        """
        return {
            'version': '第四代PositionBasedOptimizer',
            'description': '基于完整订单生命周期的对敲检测器',
            'config': self.config.copy(),
            'features': [
                '统一时间窗口（同账户和跨账户都是15秒）',
                '简化金额匹配（统一5%相对容差）',
                '盈亏对冲分析（关键特征检测）',
                '完整订单生命周期分析',
                '高效的单次遍历算法'
            ],
            'advantages': [
                '消除了多套配置参数的混乱',
                '提高了检测精度和性能',
                '减少了误报率',
                '简化了维护工作'
            ]
        }
    
    def _build_indexes(self, position: CompletePosition):
        """构建索引以提升查询性能"""
        # 用户索引
        self.user_positions_map[position.member_id].append(position.position_id)
        
        # 合约索引
        self.contract_positions_map[position.contract_name].append(position.position_id)
        
        # 时间窗口索引（按小时）
        hour_key = position.first_open_time.strftime('%Y-%m-%d %H')
        self.time_window_index[hour_key].append(position.position_id)
    
    def optimized_wash_trading_detection(self) -> List[Dict]:
        """
        优化的对敲检测：基于完整订单的首笔开仓预筛选
        使用高效的单次遍历+二选一判断逻辑
        """
        logger.info("🚀 开始第四代对敲检测 (PositionBasedOptimizer)")
        logger.info("📊 配置参数: 时间窗口=15秒, 金额容差=5%, 盈亏对冲阈值=0.7, 对敲评分阈值=0.7")
        wash_trading_pairs = []
        
        # 第一步：首笔开仓预筛选
        # 按合约分组，只考虑完整订单的首次开仓
        contract_groups = defaultdict(list)
        for pos_id, position in self.complete_positions.items():
            if position.is_completed:  # 只检测已完成的订单
                contract_groups[position.contract_name].append(position)
        
        for contract_name, positions in contract_groups.items():
            logger.debug(f"检测合约 {contract_name}，订单数: {len(positions)}")
            
            # 🚀 高效的单次遍历检测
            for i, pos_a in enumerate(positions):
                for j, pos_b in enumerate(positions[i+1:], i+1):
                    
                    # 预筛选：方向必须相反才可能对敲
                    if pos_a.primary_side == pos_b.primary_side:
                        continue
                    
                    # 时间窗口检查 - 🚀 修复：确保时间字段是datetime对象
                    try:
                        # 转换时间字段为datetime对象
                        time_a = pos_a.first_open_time
                        time_b = pos_b.first_open_time

                        if isinstance(time_a, str):
                            time_a = pd.to_datetime(time_a)
                        if isinstance(time_b, str):
                            time_b = pd.to_datetime(time_b)

                        time_diff = abs((time_a - time_b).total_seconds())
                    except Exception as e:
                        logger.warning(f"时间字段转换失败: {e}, 跳过此对比")
                        continue
                    if time_diff > self.config['wash_trading_time_window']:
                        continue
                    
                    # 金额匹配检查
                    if not self._amounts_match(pos_a.total_open_amount, pos_b.total_open_amount):
                        continue
                    
                    # 🚀 关键：二选一判断逻辑
                    is_same_account = (pos_a.member_id == pos_b.member_id)
                    
                    # 深度分析完整订单特征
                    pair_analysis = self._analyze_wash_trading_pair(pos_a, pos_b, is_same_account)
                    
                    if pair_analysis['is_wash_trading']:
                        wash_trading_pairs.append(pair_analysis)
        
        # 统计结果
        same_account_count = sum(1 for pair in wash_trading_pairs if pair.get('is_same_account'))
        cross_account_count = len(wash_trading_pairs) - same_account_count
        
        logger.info(f"对敲检测完成，发现 {len(wash_trading_pairs)} 对可疑交易 "
                   f"(同账户: {same_account_count}, 跨账户: {cross_account_count})")
        return wash_trading_pairs
    
    def _amounts_match(self, amount_a: float, amount_b: float) -> bool:
        """检查金额是否匹配"""
        if amount_a == 0 or amount_b == 0:
            return False
        
        diff_ratio = abs(amount_a - amount_b) / max(amount_a, amount_b)
        return diff_ratio <= self.config['amount_match_tolerance']
    
    def _analyze_wash_trading_pair(self, pos_a: CompletePosition, pos_b: CompletePosition, is_same_account: bool) -> Dict:
        """深度分析对敲交易对的完整特征"""
        
        # 盈亏对冲分析（关键特征！）
        total_profit = pos_a.real_profit + pos_b.real_profit
        profit_sum = abs(pos_a.real_profit) + abs(pos_b.real_profit)
        
        if profit_sum > 0:
            profit_hedge_score = 1.0 - abs(total_profit) / profit_sum
        else:
            profit_hedge_score = 0.0
        
        # 时间模式分析 - 🚀 修复：确保时间字段是datetime对象
        try:
            time_a = pos_a.first_open_time
            time_b = pos_b.first_open_time

            if isinstance(time_a, str):
                time_a = pd.to_datetime(time_a)
            if isinstance(time_b, str):
                time_b = pd.to_datetime(time_b)

            time_diff = abs((time_a - time_b).total_seconds())
            time_match_score = 1.0 - min(time_diff / self.config['wash_trading_time_window'], 1.0)
        except Exception as e:
            logger.warning(f"时间分析失败: {e}, 使用默认评分")
            time_diff = float('inf')
            time_match_score = 0.0
        
        # 金额匹配度
        amount_diff = abs(pos_a.total_open_amount - pos_b.total_open_amount)
        max_amount = max(pos_a.total_open_amount, pos_b.total_open_amount)
        amount_match_score = 1.0 - (amount_diff / max_amount) if max_amount > 0 else 0.0
        
        # 持仓时长相似性
        duration_diff = abs(pos_a.total_duration_minutes - pos_b.total_duration_minutes)
        max_duration = max(pos_a.total_duration_minutes, pos_b.total_duration_minutes, 1)
        duration_similarity = 1.0 - min(duration_diff / max_duration, 1.0)
        
        # 综合评分
        wash_score = (time_match_score * 0.3 + 
                     amount_match_score * 0.3 + 
                     profit_hedge_score * 0.3 + 
                     duration_similarity * 0.1)
        
        is_wash_trading = (wash_score > self.config['wash_score_threshold'] and 
                          profit_hedge_score > self.config['profit_hedge_threshold'])
        
        # 🚀 新增：明确的检测方法标记
        detection_method = 'same_account_wash_trading' if is_same_account else 'cross_account_wash_trading'
        
        # 构建详细交易对信息（用于详细存储）
        trade_pair_detail = self._build_trade_pair_detail(pos_a, pos_b, time_diff)
        
        # 🚀 统一的结果格式，确保与存储管理器兼容
        result = {
            'is_wash_trading': is_wash_trading,
            'detection_type': 'wash_trading',
            'detection_method': detection_method,  # 🚀 关键：明确标记同账户或跨账户
            
            # 用户信息 - 兼容存储管理器期望的字段名
            'member_id': pos_a.member_id,  # 主用户ID
            'user_id': pos_a.member_id,    # 兼容字段
            'opponent_member_id': pos_b.member_id if not is_same_account else pos_a.member_id,
            'counterparty_ids': [pos_b.member_id] if not is_same_account else [],
            
            # 用户详细信息
            'user_a': {
                'member_id': pos_a.member_id,
                'position_id': pos_a.position_id,
                'side': pos_a.primary_side,
                'amount': pos_a.total_open_amount,
                'real_profit': pos_a.real_profit,
                'duration_minutes': pos_a.total_duration_minutes,
                'open_time': pos_a.first_open_time.isoformat() if pos_a.first_open_time else None,
                'close_time': pos_a.last_close_time.isoformat() if pos_a.last_close_time else None
            },
            'user_b': {
                'member_id': pos_b.member_id,
                'position_id': pos_b.position_id,
                'side': pos_b.primary_side,
                'amount': pos_b.total_open_amount,
                'real_profit': pos_b.real_profit,
                'duration_minutes': pos_b.total_duration_minutes,
                'open_time': pos_b.first_open_time.isoformat() if pos_b.first_open_time else None,
                'close_time': pos_b.last_close_time.isoformat() if pos_b.last_close_time else None
            },
            
            # 合约和风险信息
            'contract_name': pos_a.contract_name,
            'wash_score': wash_score,
            'profit_hedge_score': profit_hedge_score,
            'time_match_score': time_match_score,
            'amount_match_score': amount_match_score,
            'duration_similarity': duration_similarity,
            'total_profit': total_profit,
            'time_diff_seconds': time_diff,
            'abnormal_volume': pos_a.total_open_amount + pos_b.total_open_amount,
            'severity': 'High' if wash_score > 0.8 else 'Medium',
            'reason': f'盈亏对冲评分: {profit_hedge_score:.2f}, 综合评分: {wash_score:.2f}',
            'first_open_time': min(pos_a.first_open_time, pos_b.first_open_time),
            
            # 详细交易对信息
            'trade_pair_detail': trade_pair_detail,
            
            # 🚀 新增：账户类型标记（便于后续处理）
            'account_type': 'same_account' if is_same_account else 'cross_account',
            'is_same_account': is_same_account
        }
        
        return result
    
    def _build_trade_pair_detail(self, pos_a: CompletePosition, pos_b: CompletePosition, time_diff: float) -> Dict:
        """构建详细的交易对信息，用于详细存储"""
        
        # 计算平仓时间差 - 🚀 修复：确保时间字段是datetime对象
        close_time_gap = 0
        if pos_a.last_close_time and pos_b.last_close_time:
            try:
                # 确保时间字段是datetime对象
                time_a = pos_a.last_close_time
                time_b = pos_b.last_close_time

                # 如果是字符串，转换为datetime
                if isinstance(time_a, str):
                    time_a = pd.to_datetime(time_a)
                if isinstance(time_b, str):
                    time_b = pd.to_datetime(time_b)

                close_time_gap = abs((time_a - time_b).total_seconds())
            except Exception as e:
                logger.warning(f"计算平仓时间差失败: {e}")
                close_time_gap = 0
        
        # 构建符合WashTradingStorageManager期望格式的详细信息
        trade_pair_detail = {
            'pair_index': 1,  # 单个交易对
            'contract_name': pos_a.contract_name,
            'user_a': {
                'member_id': pos_a.member_id,
                'position_id': pos_a.position_id,
                'open_time': pos_a.first_open_time.isoformat() if pos_a.first_open_time else None,
                'open_side': pos_a.primary_side,
                'open_amount': pos_a.total_open_amount,
                'close_time': pos_a.last_close_time.isoformat() if pos_a.last_close_time else None,
                'close_side': 4 if pos_a.primary_side == 1 else 2,  # 1开多->4平多, 3开空->2平空
                'close_amount': pos_a.total_close_amount,
                'profit': pos_a.real_profit
            },
            'user_b': {
                'member_id': pos_b.member_id,
                'position_id': pos_b.position_id,
                'open_time': pos_b.first_open_time.isoformat() if pos_b.first_open_time else None,
                'open_side': pos_b.primary_side,
                'open_amount': pos_b.total_open_amount,
                'close_time': pos_b.last_close_time.isoformat() if pos_b.last_close_time else None,
                'close_side': 4 if pos_b.primary_side == 1 else 2,  # 1开多->4平多, 3开空->2平空
                'close_amount': pos_b.total_close_amount,
                'profit': pos_b.real_profit
            },
            'time_gaps': {
                'open_gap_seconds': int(time_diff),
                'close_gap_seconds': int(close_time_gap)
            },
            'total_amount': pos_a.total_open_amount + pos_b.total_open_amount,
            'net_profit': pos_a.real_profit + pos_b.real_profit,
            'risk_level': 'High' if self._calculate_wash_score(pos_a, pos_b) > 0.8 else 'Medium',
            'risk_score': self._calculate_wash_score(pos_a, pos_b)
        }
        
        return trade_pair_detail
    
    def _calculate_wash_score(self, pos_a: CompletePosition, pos_b: CompletePosition) -> float:
        """计算对敲评分（简化版本，用于风险等级评估）"""
        # 简化的评分计算，复用现有逻辑 - 🚀 修复：确保时间字段是datetime对象
        try:
            time_a = pos_a.first_open_time
            time_b = pos_b.first_open_time

            if isinstance(time_a, str):
                time_a = pd.to_datetime(time_a)
            if isinstance(time_b, str):
                time_b = pd.to_datetime(time_b)

            time_diff = abs((time_a - time_b).total_seconds())
            time_match_score = 1.0 - min(time_diff / self.config['wash_trading_time_window'], 1.0)
        except Exception as e:
            logger.warning(f"对敲评分时间计算失败: {e}, 使用默认评分")
            time_match_score = 0.0
        
        amount_diff = abs(pos_a.total_open_amount - pos_b.total_open_amount)
        max_amount = max(pos_a.total_open_amount, pos_b.total_open_amount)
        amount_match_score = 1.0 - (amount_diff / max_amount) if max_amount > 0 else 0.0
        
        return (time_match_score + amount_match_score) / 2
    
    def optimized_high_frequency_detection(self) -> List[Dict]:
        """
        优化的高频检测：基于完整订单，过滤正常加仓
        """
        logger.info("开始优化高频检测")
        high_freq_results = []
        
        # 按用户+合约分组
        user_contract_groups = defaultdict(list)
        for position in self.complete_positions.values():
            key = f"{position.member_id}_{position.contract_name}"
            user_contract_groups[key].append(position)
        
        for key, positions in user_contract_groups.items():
            member_id, contract_name = key.split('_', 1)
            
            # 按小时分组统计完整订单频率
            hourly_positions = defaultdict(list)
            for position in positions:
                hour_key = position.first_open_time.strftime('%Y-%m-%d %H')
                hourly_positions[hour_key].append(position)
            
            # 检测异常高频
            for hour_key, hour_positions in hourly_positions.items():
                if len(hour_positions) >= self.config['high_frequency_threshold']:
                    
                    # 关键：过滤正常的分批建仓（加仓次数多的订单）
                    abnormal_positions = [
                        pos for pos in hour_positions 
                        if pos.add_position_count <= 2  # 加仓次数少，可能是异常
                    ]
                    
                    # 计算快进快出比例
                    quick_trades = [pos for pos in hour_positions if pos.is_quick_trade]
                    quick_trades_ratio = len(quick_trades) / len(hour_positions)
                    
                    if len(abnormal_positions) >= self.config['high_frequency_threshold'] * 0.7:
                        high_freq_results.append({
                            'detection_type': 'high_frequency',
                            'member_id': member_id,
                            'contract_name': contract_name,
                            'hour': hour_key,
                            'total_positions': len(hour_positions),
                            'abnormal_positions': len(abnormal_positions),
                            'quick_trades_count': len(quick_trades),
                            'quick_trades_ratio': quick_trades_ratio,
                            'abnormal_volume': sum(pos.total_open_amount for pos in abnormal_positions),
                            'total_volume': sum(pos.total_open_amount for pos in hour_positions),
                            'severity': 'High' if quick_trades_ratio > 0.8 else 'Medium',
                            'reason': f'小时内异常订单 {len(abnormal_positions)} 笔，快进快出比例 {quick_trades_ratio:.1%}',
                            'detection_method': 'complete_position_frequency'
                        })
        
        logger.info(f"高频检测完成，发现 {len(high_freq_results)} 个异常用户")
        return high_freq_results
    
    def optimized_arbitrage_detection(self) -> List[Dict]:
        """优化的套利检测：仅使用基于时间模式的资金费率套利检测"""
        logger.info("开始优化套利检测")
        arbitrage_results = []
        
        funding_arbitrage_results = self._detect_funding_rate_arbitrage()
        arbitrage_results.extend(funding_arbitrage_results)
        
        logger.info(f"资金费率套利检测完成，发现 {len(arbitrage_results)} 个套利用户")
        return arbitrage_results
    

    
    def _detect_funding_rate_arbitrage(self) -> List[Dict]:
        """基于时间模式的资金费率套利检测"""
        from ..algorithms.funding_rate_arbitrage_realistic import RealisticFundingArbitrageDetector
        
        # 将完整订单转换为DataFrame格式供检测器使用
        position_data = []
        for position in self.complete_positions.values():
            # 开仓记录
            position_data.append({
                'position_id': position.position_id,
                'member_id': position.member_id,
                'contract_name': position.contract_name,
                'side': position.primary_side,
                'timestamp': position.first_open_time,
                'deal_vol_usdt': position.total_open_amount
            })
            
            # 平仓记录（如果有）
            if position.is_completed and position.first_close_time:
                close_side = 4 if position.primary_side == 1 else 2  # 1->4(平多), 3->2(平空)
                position_data.append({
                    'position_id': position.position_id,
                    'member_id': position.member_id,
                    'contract_name': position.contract_name,
                    'side': close_side,
                    'timestamp': position.first_close_time,
                    'deal_vol_usdt': position.total_close_amount
                })
        
        if not position_data:
            return []
        
        # 创建DataFrame
        df = pd.DataFrame(position_data)
        
        # 使用资金费率套利检测器
        detector = RealisticFundingArbitrageDetector()
        funding_results = detector.detect(df)
        
        # 转换结果格式以保持一致性
        converted_results = []
        for result in funding_results:
            converted_results.append({
                'detection_type': 'funding_arbitrage',  # 统一类型名称
                'member_id': result['member_id'],
                'contract_name': result['contract_name'],
                'total_profit': 0.0,  # 资金费率套利不关注盈亏
                'total_positions': result['total_positions'],
                'pattern_matched_positions': result['pattern_matched_positions'],
                'pattern_ratio': result['pattern_ratio'],
                'contracts_involved': result['contracts_involved'],
                'funding_cycle_distribution': result['funding_cycle_distribution'],
                'abnormal_volume': result['abnormal_volume'],
                'severity': result['severity'],
                'reason': result['reason'],
                'detection_method': result['detection_method'],
                'pattern_analysis': result['pattern_analysis']
            })
        
        return converted_results
    
    def get_comprehensive_analysis(self) -> Dict[str, Any]:
        """获取综合分析结果"""
        # 执行所有检测
        wash_results = self.optimized_wash_trading_detection()
        high_freq_results = self.optimized_high_frequency_detection()
        arbitrage_results = self.optimized_arbitrage_detection()
        
        # 合并所有结果
        all_results = wash_results + high_freq_results + arbitrage_results
        
        # 🚀 新增：对敲结果详细分类统计
        same_account_wash_results = [r for r in wash_results if r.get('account_type') == 'same_account']
        cross_account_wash_results = [r for r in wash_results if r.get('account_type') == 'cross_account']
        
        # 统计信息
        stats = {
            'total_positions': len(self.complete_positions),
            'completed_positions': sum(1 for pos in self.complete_positions.values() if pos.is_completed),
            'quick_trades': sum(1 for pos in self.complete_positions.values() if pos.is_quick_trade),
            'total_real_profit': sum(pos.real_profit for pos in self.complete_positions.values()),
            'wash_trading_pairs': len(wash_results),
            'same_account_wash_pairs': len(same_account_wash_results),  # 🚀 新增
            'cross_account_wash_pairs': len(cross_account_wash_results),  # 🚀 新增
            'high_frequency_users': len(high_freq_results),
            'arbitrage_users': len(arbitrage_results),
            'total_abnormal_volume': sum(result.get('abnormal_volume', 0) for result in all_results)
        }
        
        return {
            'results': all_results,
            'statistics': stats,
            'detection_summary': {
                'wash_trading': len(wash_results),
                'same_account_wash_trading': len(same_account_wash_results),  # 🚀 新增
                'cross_account_wash_trading': len(cross_account_wash_results),  # 🚀 新增
                'high_frequency': len(high_freq_results),
                'arbitrage': len(arbitrage_results),
                'total_detections': len(all_results)
            },
            # 🚀 新增：详细的对敲分析结果
            'wash_trading_details': {
                'same_account_pairs': same_account_wash_results,
                'cross_account_pairs': cross_account_wash_results,
                'same_account_summary': {
                    'total_pairs': len(same_account_wash_results),
                    'high_risk_pairs': len([r for r in same_account_wash_results if r.get('severity') == 'High']),
                    'medium_risk_pairs': len([r for r in same_account_wash_results if r.get('severity') == 'Medium']),
                    'total_volume': sum(r.get('abnormal_volume', 0) for r in same_account_wash_results)
                },
                'cross_account_summary': {
                    'total_pairs': len(cross_account_wash_results),
                    'high_risk_pairs': len([r for r in cross_account_wash_results if r.get('severity') == 'High']),
                    'medium_risk_pairs': len([r for r in cross_account_wash_results if r.get('severity') == 'Medium']),
                    'total_volume': sum(r.get('abnormal_volume', 0) for r in cross_account_wash_results)
                }
            }
        }
    
    def clear_cache(self):
        """清理缓存"""
        self.complete_positions.clear()
        self.user_positions_map.clear()
        self.contract_positions_map.clear()
        self.time_window_index.clear()

    def _save_incomplete_position_to_waiting_table(self, position: CompletePosition):
        """
        保存不完整订单到等待表，供增量模式使用

        🚀 关键修复：普通模式也要保存不完整订单，否则增量模式没有历史数据可匹配
        """
        try:
            # 只保存有开仓数据的不完整订单（等待表设计要求）
            if position.first_open_time is None or position.total_open_amount <= 0:
                logger.debug(f"跳过订单 {position.position_id}: 缺少开仓数据，无法存储到等待表")
                return

            # 导入数据库管理器
            from database.duckdb_manager import DuckDBManager as DatabaseManager
            from datetime import datetime

            db_manager = DatabaseManager()

            # 准备插入数据
            insert_sql = """
            INSERT OR REPLACE INTO incomplete_positions_waiting
            (position_id, member_id, contract_name, primary_side, first_open_time,
             total_open_amount, open_trades_count, avg_open_price, total_open_volume,
             waiting_since, last_check_time, check_count, source_task_id, data_version,
             created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = [
                str(position.position_id),
                str(position.member_id),
                str(position.contract_name),
                int(position.primary_side),
                position.first_open_time,
                float(position.total_open_amount),
                int(position.open_trades_count),
                float(position.avg_open_price),
                float(position.total_open_volume),
                datetime.now(),  # waiting_since
                datetime.now(),  # last_check_time
                0,  # check_count
                'normal_mode',  # source_task_id
                '1.0',  # data_version
                datetime.now(),  # created_at
                datetime.now()   # updated_at
            ]

            db_manager.execute_sql(insert_sql, params)
            logger.debug(f"✅ 不完整订单 {position.position_id} 已保存到等待表")

        except Exception as e:
            logger.warning(f"保存不完整订单到等待表失败 {position.position_id}: {e}")
            # 不影响主流程，继续处理

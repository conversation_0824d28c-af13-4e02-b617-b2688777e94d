# 对敲交易检测模块接口变动记录

## 重构时间
2024年当前时间 - 模块化架构重构

## 重构概述
将原有的单一文件 `wash_trading_optimized.py` (2959行) 拆分为模块化架构，提高代码可维护性并新增详细信息收集功能。

## 架构变化

### 原架构 (Legacy)
```
wash_trading_optimized.py (2959行)
└── WashTradingDetector类 (包含所有功能)
```

### 新架构 (Modular)
```
wash_trading/
├── __init__.py
├── detector.py              # 核心检测逻辑
├── detail_collector.py      # 详细信息收集
├── storage_manager.py       # 数据库存储管理
└── result_formatter.py      # 结果格式化

wash_trading_optimized.py    # 向后兼容接口
```

## 接口兼容性

### ✅ 保持不变的接口
- `WashTradingDetector.__init__(config_manager, cache_dir)` - 构造函数
- `WashTradingDetector.detect(df, progress_callback)` - 主检测方法
- 返回值基本格式保持兼容（原有字段不变）

### 🆕 新增接口
- `save_results_with_details(results, db_connection)` - 保存详细信息到数据库
- `get_trade_pair_details(result_id, db_connection)` - 获取交易对详情
- `format_trade_pair_details(trade_pairs, contract_name)` - 格式化显示
- `get_detailed_analysis_info()` - 获取架构信息

### 🔄 增强的返回值
检测结果现在可能包含以下新字段（仅当新架构可用时）：
- `has_details: bool` - 是否有详细信息
- `trade_pairs_details: List[Dict]` - 交易对详情列表
- `summary: Dict` - 汇总统计信息

## 数据库变化

### 新增表结构
```sql
-- 对敲交易详情表
CREATE TABLE wash_trading_pairs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    result_id BIGINT NOT NULL,
    pair_index INT NOT NULL,
    
    -- 用户A信息
    user_a_id VARCHAR(50) NOT NULL,
    user_a_position_id VARCHAR(100),
    user_a_open_time DATETIME,
    user_a_open_side TINYINT,
    user_a_open_amount DECIMAL(15,2),
    user_a_close_time DATETIME,
    user_a_close_side TINYINT,
    user_a_close_amount DECIMAL(15,2),
    user_a_profit DECIMAL(15,2),
    
    -- 用户B信息
    user_b_id VARCHAR(50) NOT NULL,
    user_b_position_id VARCHAR(100),
    user_b_open_time DATETIME,
    user_b_open_side TINYINT,
    user_b_open_amount DECIMAL(15,2),
    user_b_close_time DATETIME,
    user_b_close_side TINYINT,
    user_b_close_amount DECIMAL(15,2),
    user_b_profit DECIMAL(15,2),
    
    -- 分析数据
    open_time_diff_seconds INT,
    close_time_diff_seconds INT,
    total_amount DECIMAL(15,2),
    net_profit DECIMAL(15,2),
    
    -- 索引
    INDEX idx_result_id (result_id),
    INDEX idx_user_a_id (user_a_id),
    INDEX idx_user_b_id (user_b_id)
);
```

## 使用示例

### 基本使用（兼容原有代码）
```python
from modules.contract_risk_analysis.algorithms.wash_trading_optimized import WashTradingDetector

detector = WashTradingDetector()
results = detector.detect(df)  # 原有接口保持不变
```

### 新功能使用
```python
# 检测并获取详细信息
detector = WashTradingDetector()
results = detector.detect(df)

# 检查是否支持新功能
info = detector.get_detailed_analysis_info()
if info['supports_detailed_info']:
    # 保存详细信息到数据库
    detector.save_results_with_details(results, db_connection)
    
    # 获取详细信息
    trade_pairs = detector.get_trade_pair_details(result_id, db_connection)
    
    # 格式化显示
    formatted_details = detector.format_trade_pair_details(trade_pairs, contract_name)
    print(formatted_details)
```

## 前端集成指导

### API接口建议
后端需要添加以下API端点来支持前端详细信息显示：

```python
# 获取对敲交易详情
@app.route('/api/wash-trading/details/<int:result_id>')
def get_wash_trading_details(result_id):
    detector = WashTradingDetector()
    if detector.get_detailed_analysis_info()['supports_detailed_info']:
        trade_pairs = detector.get_trade_pair_details(result_id, db_connection)
        return jsonify(trade_pairs)
    return jsonify([])

# 格式化显示详情
@app.route('/api/wash-trading/format/<int:result_id>')
def format_wash_trading_details(result_id):
    detector = WashTradingDetector()
    trade_pairs = detector.get_trade_pair_details(result_id, db_connection)
    formatted = detector.format_trade_pair_details(trade_pairs, contract_name)
    return jsonify({'formatted_text': formatted})
```

## 回退机制
如果新架构无法加载（缺少依赖或配置错误），系统会自动回退到原始实现：
- 所有原有功能保持正常
- 新增方法返回空值或警告信息
- 不影响现有业务逻辑

## 测试验证
```bash
# 验证架构加载
python3 -c "
from modules.contract_risk_analysis.algorithms.wash_trading_optimized import WashTradingDetector
detector = WashTradingDetector()
print('架构信息:', detector.get_detailed_analysis_info())
"
```

期望输出：
```json
{
    "architecture": "new",
    "supports_detailed_info": true,
    "supports_database_storage": true,
    "supports_formatting": true,
    "features": [
        "详细交易对信息收集",
        "数据库存储管理", 
        "用户友好的格式化显示",
        "时间差和利润分析",
        "交易对汇总统计"
    ]
}
```

## 文件清单

### 新增文件
- `backend/modules/contract_risk_analysis/algorithms/wash_trading/__init__.py`
- `backend/modules/contract_risk_analysis/algorithms/wash_trading/detector.py`
- `backend/modules/contract_risk_analysis/algorithms/wash_trading/detail_collector.py`
- `backend/modules/contract_risk_analysis/algorithms/wash_trading/storage_manager.py`
- `backend/modules/contract_risk_analysis/algorithms/wash_trading/result_formatter.py`
- `backend/modules/contract_risk_analysis/sql/wash_trading_pairs.sql`
- `backend/modules/contract_risk_analysis/INTERFACE_CHANGES.md`

### 修改文件
- `backend/modules/contract_risk_analysis/algorithms/wash_trading_optimized.py` - 转为向后兼容接口

## 迁移建议
1. **立即可用**：新接口向后兼容，现有代码无需修改
2. **渐进升级**：可以逐步使用新功能，如详细信息收集
3. **数据库准备**：需要执行SQL脚本创建新表
4. **前端集成**：可以选择性集成新的详细显示功能 
"""
基于真实业务逻辑的资金费率套利检测器
根据position_id构建完整订单，分析开平仓时间模式来识别资金费率套利行为

核心逻辑：
1. 基于position_id构建完整订单
2. 开仓时间：每4小时前3分钟 (例如3:57-4:00, 7:57-8:00, 11:57-12:00等)
3. 平仓时间：整点后3分钟 (例如4:00-4:03, 8:00-8:03, 12:00-12:03等)
4. 资金费率时间点：0, 4, 8, 12, 16, 20点
5. 当用户订单中2/3以上符合此模式时，判定为资金费率套利
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class FundingArbitragePosition:
    """资金费率套利订单数据结构"""
    position_id: str
    member_id: str
    contract_name: str
    
    # 开仓信息
    open_time: datetime
    open_side: int  # 1=开多, 3=开空
    open_amount: float
    
    # 平仓信息
    close_time: Optional[datetime]
    close_side: int  # 2=平空, 4=平多
    close_amount: float
    
    # 是否完整订单
    is_complete: bool
    
    # 套利模式匹配
    matches_funding_pattern: bool
    open_in_window: bool  # 开仓是否在窗口内
    close_in_window: bool  # 平仓是否在窗口内
    funding_cycle_hour: int  # 对应的资金费率周期小时


class RealisticFundingArbitrageDetector:
    """基于真实业务逻辑的资金费率套利检测器"""
    
    def __init__(self):
        """初始化检测器"""
        # 资金费率时间点 (每4小时)
        self.funding_hours = [0, 4, 8, 12, 16, 20]
        
        # 时间窗口配置
        self.open_window_minutes = 3  # 开仓窗口：前3分钟
        self.close_window_minutes = 3  # 平仓窗口：后3分钟
        
        # 检测阈值
        self.min_positions = 3  # 最少订单数
        self.pattern_threshold = 0.67  # 模式匹配阈值 (2/3，使用0.67避免浮点数精度问题)
        
        # 统计信息
        self.statistics = {
            'total_positions': 0,
            'complete_positions': 0,
            'pattern_matched_positions': 0,
            'funding_arbitrage_users': 0
        }
    
    def detect(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        检测资金费率套利行为
        
        参数:
            df: 交易数据DataFrame
            
        返回:
            检测结果列表
        """
        results = []
        
        if df is None or df.empty:
            return results
        
        # 验证必要字段
        required_fields = ['position_id', 'member_id', 'contract_name', 'side', 'timestamp', 'deal_vol_usdt']
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            logger.warning(f"资金费率套利检测缺少必要字段: {missing_fields}")
            return results
        
        # 确保时间格式正确
        if not pd.api.types.is_datetime64_dtype(df['timestamp']):
            df = df.copy()
            df.loc[:, 'timestamp'] = pd.to_datetime(df['timestamp'])
        
        logger.info(f"开始基于真实业务逻辑的资金费率套利检测，数据量: {len(df)} 条记录")
        
        # 1. 构建完整订单
        complete_positions = self._build_complete_positions(df)
        self.statistics['total_positions'] = len(complete_positions)
        
        # 2. 分析每个用户的套利模式
        user_analysis = self._analyze_user_patterns(complete_positions)
        
        # 3. 生成检测结果
        for member_id, analysis in user_analysis.items():
            if analysis['is_funding_arbitrage']:
                result = self._create_detection_result(member_id, analysis)
                results.append(result)
        
        self.statistics['funding_arbitrage_users'] = len(results)
        
        logger.info(f"资金费率套利检测完成，发现 {len(results)} 个套利用户，"
                   f"总订单: {self.statistics['total_positions']}, "
                   f"完整订单: {self.statistics['complete_positions']}, "
                   f"模式匹配: {self.statistics['pattern_matched_positions']}")
        
        return results
    
    def _build_complete_positions(self, df: pd.DataFrame) -> Dict[str, FundingArbitragePosition]:
        """
        基于position_id构建完整订单
        
        参数:
            df: 交易数据
            
        返回:
            完整订单字典 {position_id: FundingArbitragePosition}
        """
        complete_positions = {}
        
        # 按position_id分组
        for position_id, group in df.groupby('position_id'):
            try:
                # 按时间排序
                group_sorted = group.sort_values('timestamp')
                
                # 基本信息
                member_id = str(group_sorted.iloc[0]['member_id'])
                contract_name = str(group_sorted.iloc[0]['contract_name'])
                
                # 分离开仓和平仓记录
                open_trades = group_sorted[group_sorted['side'].isin([1, 3])]  # 1=开多, 3=开空
                close_trades = group_sorted[group_sorted['side'].isin([2, 4])]  # 2=平空, 4=平多
                
                # 必须有开仓记录
                if len(open_trades) == 0:
                    continue
                
                # 获取开仓信息（取第一笔开仓）
                first_open = open_trades.iloc[0]
                open_time = first_open['timestamp']
                open_side = first_open['side']
                open_amount = open_trades['deal_vol_usdt'].sum()
                
                # 获取平仓信息
                close_time = None
                close_side = 0
                close_amount = 0.0
                is_complete = False
                
                if len(close_trades) > 0:
                    last_close = close_trades.iloc[-1]
                    close_time = last_close['timestamp']
                    close_side = last_close['side']
                    close_amount = close_trades['deal_vol_usdt'].sum()
                    is_complete = True
                
                # 分析是否符合资金费率套利模式
                pattern_analysis = self._analyze_position_pattern(open_time, close_time, is_complete)
                
                # 创建完整订单对象
                position = FundingArbitragePosition(
                    position_id=str(position_id),
                    member_id=member_id,
                    contract_name=contract_name,
                    open_time=open_time,
                    open_side=open_side,
                    open_amount=open_amount,
                    close_time=close_time,
                    close_side=close_side,
                    close_amount=close_amount,
                    is_complete=is_complete,
                    matches_funding_pattern=pattern_analysis['matches_pattern'],
                    open_in_window=pattern_analysis['open_in_window'],
                    close_in_window=pattern_analysis['close_in_window'],
                    funding_cycle_hour=pattern_analysis['funding_cycle_hour']
                )
                
                complete_positions[str(position_id)] = position
                
                if is_complete:
                    self.statistics['complete_positions'] += 1
                if pattern_analysis['matches_pattern']:
                    self.statistics['pattern_matched_positions'] += 1
                    
            except Exception as e:
                logger.warning(f"构建订单 {position_id} 失败: {str(e)}")
                continue
        
        return complete_positions
    
    def _analyze_position_pattern(self, open_time: datetime, close_time: Optional[datetime], is_complete: bool) -> Dict:
        """
        分析订单是否符合资金费率套利模式
        
        参数:
            open_time: 开仓时间
            close_time: 平仓时间
            is_complete: 是否完整订单
            
        返回:
            模式分析结果
        """
        result = {
            'matches_pattern': False,
            'open_in_window': False,
            'close_in_window': False,
            'funding_cycle_hour': -1
        }
        
        # 分析开仓时间模式
        open_analysis = self._check_open_time_pattern(open_time)
        result['open_in_window'] = open_analysis['in_window']
        result['funding_cycle_hour'] = open_analysis['funding_hour']
        
        # 分析平仓时间模式（仅对完整订单）
        if is_complete and close_time:
            close_analysis = self._check_close_time_pattern(close_time, open_analysis['funding_hour'])
            result['close_in_window'] = close_analysis['in_window']
            
            # 完整订单：开仓和平仓都符合模式
            result['matches_pattern'] = result['open_in_window'] and result['close_in_window']
        else:
            # 未完成订单：只检查开仓模式
            result['matches_pattern'] = result['open_in_window']
        
        return result
    
    def _check_open_time_pattern(self, open_time: datetime) -> Dict:
        """
        检查开仓时间是否符合模式
        开仓模式：每4小时前3分钟 (例如3:57-4:00, 7:57-8:00)
        
        参数:
            open_time: 开仓时间
            
        返回:
            开仓时间分析结果
        """
        hour = open_time.hour
        minute = open_time.minute
        
        # 检查是否在任何一个资金费率时间点的开仓窗口内
        in_window = False
        matched_funding_hour = -1
        
        for funding_hour in self.funding_hours:
            # 计算开仓窗口 (资金费率前3分钟)
            if funding_hour == 0:
                # 0点的开仓窗口是23:57-0:00
                if (hour == 23 and minute >= 57) or (hour == 0 and minute == 0):
                    in_window = True
                    matched_funding_hour = 0
                    break
            else:
                # 其他时间点的开仓窗口 (例如4点是3:57-4:00)
                window_start_hour = funding_hour - 1
                if (hour == window_start_hour and minute >= 57) or \
                   (hour == funding_hour and minute == 0):
                    in_window = True
                    matched_funding_hour = funding_hour
                    break
        
        # 如果没有匹配到，找到下一个资金费率时间点
        if not in_window:
            for funding_hour in self.funding_hours:
                if hour < funding_hour:
                    matched_funding_hour = funding_hour
                    break
            if matched_funding_hour == -1:
                matched_funding_hour = 0  # 下一个是第二天的0点
        
        return {
            'in_window': in_window,
            'funding_hour': matched_funding_hour
        }
    
    def _check_close_time_pattern(self, close_time: datetime, funding_hour: int) -> Dict:
        """
        检查平仓时间是否符合模式
        平仓模式：整点后3分钟 (例如4:00-4:03, 8:00-8:03)
        
        参数:
            close_time: 平仓时间
            funding_hour: 对应的资金费率小时
            
        返回:
            平仓时间分析结果
        """
        hour = close_time.hour
        minute = close_time.minute
        
        # 检查是否在对应的资金费率时间点后的窗口内
        in_window = (hour == funding_hour and 0 <= minute <= self.close_window_minutes)
        
        return {
            'in_window': in_window
        }
    
    def _analyze_user_patterns(self, complete_positions: Dict[str, FundingArbitragePosition]) -> Dict[str, Dict]:
        """
        分析每个用户的套利模式
        
        参数:
            complete_positions: 完整订单字典
            
        返回:
            用户分析结果 {member_id: analysis}
        """
        user_analysis = defaultdict(lambda: {
            'total_positions': 0,
            'pattern_matched_positions': 0,
            'pattern_ratio': 0.0,
            'is_funding_arbitrage': False,
            'positions': [],
            'contracts': set(),
            'funding_cycles': defaultdict(int)
        })
        
        # 按用户分组分析
        for position in complete_positions.values():
            member_id = position.member_id
            analysis = user_analysis[member_id]
            
            analysis['total_positions'] += 1
            analysis['positions'].append(position)
            analysis['contracts'].add(position.contract_name)
            
            if position.matches_funding_pattern:
                analysis['pattern_matched_positions'] += 1
                analysis['funding_cycles'][position.funding_cycle_hour] += 1
        
        # 计算模式匹配率并判断是否为套利
        for member_id, analysis in user_analysis.items():
            if analysis['total_positions'] >= self.min_positions:
                analysis['pattern_ratio'] = analysis['pattern_matched_positions'] / analysis['total_positions']
                analysis['is_funding_arbitrage'] = analysis['pattern_ratio'] > self.pattern_threshold
            
            # 转换set为list便于序列化
            analysis['contracts'] = list(analysis['contracts'])
        
        return dict(user_analysis)
    
    def _create_detection_result(self, member_id: str, analysis: Dict) -> Dict[str, Any]:
        """
        创建检测结果
        
        参数:
            member_id: 用户ID
            analysis: 用户分析结果
            
        返回:
            检测结果
        """
        # 计算总交易量
        total_volume = sum(pos.open_amount for pos in analysis['positions'])
        
        # 获取主要合约
        contract_volumes = defaultdict(float)
        for pos in analysis['positions']:
            contract_volumes[pos.contract_name] += pos.open_amount
        
        main_contract = max(contract_volumes.keys(), key=lambda x: contract_volumes[x])
        
        # 分析资金费率周期分布
        cycle_distribution = dict(analysis['funding_cycles'])
        
        # 计算风险等级
        if analysis['pattern_ratio'] >= 0.9:
            severity = 'high'
        elif analysis['pattern_ratio'] >= 0.8:
            severity = 'medium'
        else:
            severity = 'low'
        
        # 构建检测结果
        return {
            'detection_type': 'funding_arbitrage',  # 统一类型名称
            'member_id': member_id,
            'contract_name': main_contract,
            'total_positions': analysis['total_positions'],
            'pattern_matched_positions': analysis['pattern_matched_positions'],
            'pattern_ratio': float(analysis['pattern_ratio']),
            'reason': f'资金费率套利模式检测：{analysis["pattern_matched_positions"]}/{analysis["total_positions"]}个订单符合时间模式 ({analysis["pattern_ratio"]*100:.1f}%)',
            'severity': severity,
            'abnormal_volume': float(total_volume),
            'contracts_involved': analysis['contracts'],
            'funding_cycle_distribution': cycle_distribution,
            'pattern_analysis': {
                'open_window_matches': sum(1 for pos in analysis['positions'] if pos.open_in_window),
                'close_window_matches': sum(1 for pos in analysis['positions'] if pos.close_in_window and pos.is_complete),
                'complete_positions': sum(1 for pos in analysis['positions'] if pos.is_complete),
                'most_active_cycle': max(cycle_distribution.keys(), key=lambda x: cycle_distribution[x]) if cycle_distribution else -1
            },
            'detection_method': 'position_time_pattern'
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        return self.statistics.copy() 
"""
代理商分析数据过滤配置管理器
"""
import yaml
import os
import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class AgentDataFilterConfig:
    """代理商分析数据过滤配置管理器"""
    
    def __init__(self, config_path: str = None):
        if config_path is None:
            # 默认配置文件路径
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
                'config', 'agent_data_filter_config.yml'
            )
        
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                return self._get_default_config()
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"成功加载数据过滤配置: {self.config_path}")
                return config
                
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'data_quality_filters': {
                'invalid_device_ids': {
                    'enabled': True,
                    'patterns': [
                        'unknowndeviceid', 'unknown_device_id', 'unknown-device-id',
                        'null', 'undefined', '', 'nan', 'none'
                    ],
                    'case_sensitive': False
                },
                'internal_operations': {
                    'enabled': True,
                    'internal_ips': ['***********'],
                    'internal_user_ids': ['477c982d08675c1923e81f7594a02aa3433f2d364694aa882da9c81d42c34475'],
                    'internal_bd_names': ['Ourbit_Internal', '内部运营', '测试团队']
                }
            },
            'default_settings': {
                'exclude_invalid_devices': True,
                'exclude_internal_operations': True
            },
            'filtering_stats': {
                'enabled': True,
                'log_level': 'INFO',
                'show_detailed_stats': True
            }
        }
    
    def get_invalid_device_patterns(self) -> List[str]:
        """获取无效设备ID模式列表"""
        config = self.config.get('data_quality_filters', {}).get('invalid_device_ids', {})
        if not config.get('enabled', True):
            return []
        
        patterns = config.get('patterns', [])
        case_sensitive = config.get('case_sensitive', False)
        
        if not case_sensitive:
            patterns = [pattern.lower() for pattern in patterns]
        
        return patterns
    
    def get_internal_ips(self) -> List[str]:
        """获取内部运营IP列表"""
        config = self.config.get('data_quality_filters', {}).get('internal_operations', {})
        if not config.get('enabled', True):
            return []
        
        return config.get('internal_ips', [])
    
    def get_internal_user_ids(self) -> List[str]:
        """获取内部运营用户ID列表"""
        config = self.config.get('data_quality_filters', {}).get('internal_operations', {})
        if not config.get('enabled', True):
            return []
        
        return config.get('internal_user_ids', [])
    
    def get_internal_bd_names(self) -> List[str]:
        """获取内部运营BD名称列表"""
        config = self.config.get('data_quality_filters', {}).get('internal_operations', {})
        if not config.get('enabled', True):
            return []
        
        return config.get('internal_bd_names', [])
    
    def is_invalid_device_filtering_enabled(self) -> bool:
        """检查是否启用无效设备ID过滤"""
        return self.config.get('data_quality_filters', {}).get('invalid_device_ids', {}).get('enabled', True)
    
    def is_internal_operations_filtering_enabled(self) -> bool:
        """检查是否启用内部运营数据过滤"""
        return self.config.get('data_quality_filters', {}).get('internal_operations', {}).get('enabled', True)
    
    def get_default_exclude_invalid_devices(self) -> bool:
        """获取默认的排除无效设备ID设置"""
        return self.config.get('default_settings', {}).get('exclude_invalid_devices', True)
    
    def get_default_exclude_internal_operations(self) -> bool:
        """获取默认的排除内部运营数据设置"""
        return self.config.get('default_settings', {}).get('exclude_internal_operations', True)
    
    def is_filtering_stats_enabled(self) -> bool:
        """检查是否启用过滤统计"""
        return self.config.get('filtering_stats', {}).get('enabled', True)
    
    def get_filtering_log_level(self) -> str:
        """获取过滤日志级别"""
        return self.config.get('filtering_stats', {}).get('log_level', 'INFO')
    
    def should_show_detailed_stats(self) -> bool:
        """是否显示详细统计信息"""
        return self.config.get('filtering_stats', {}).get('show_detailed_stats', True)
    
    def reload_config(self):
        """重新加载配置"""
        self.config = self._load_config()
        logger.info("配置已重新加载")

# 创建全局配置实例
filter_config = AgentDataFilterConfig()

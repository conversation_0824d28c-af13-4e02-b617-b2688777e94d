"""
代理关系分析模块API
"""
from flask import Blueprint, request, jsonify, send_file
import pandas as pd
import os
import tempfile
import logging
from datetime import datetime
from werkzeug.utils import secure_filename

from ..processors.data_processor import (
    preprocess_data, 
    enhance_data_with_level_analysis, 
    analyze_relationships_in_shared_data,
    generate_level_analysis_report,
    filter_bd_relationships,
    build_bd_pyramid_structure,
    generate_pyramid_statistics
)
from ..processors.parallel_processor import ParallelProcessor
from ..services.relationship_analyzer import analyze_bd_statistics
from ..services.filter import filter_data, filter_ranking, classify_data_by_bd
from ..services.level_analyzer import (
    AgentLevelAnalyzer,
    UserDataRepository,
    LevelRelationshipAnalyzer,
    LevelRiskEvaluator
)
# ========== 鉴权导入 ==========
from core.utils.decorators import login_required, admin_required
from core.security.csrf_protection import csrf_protect

agent_bp = Blueprint('agent', __name__)

# 配置日志
logger = logging.getLogger(__name__)

# 创建并行处理器实例
parallel_processor = ParallelProcessor(
    enable_parallel=False,
    max_workers=4,
    min_data_size=5000,
    min_groups_size=100
)

# 允许的文件类型
ALLOWED_EXTENSIONS = {'xlsx', 'xls', 'csv'}

def allowed_file(filename):
    """检查文件是否为允许的类型"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@admin_required  # 只有管理员可以上传文件
@csrf_protect
@agent_bp.route('/upload', methods=['POST'])
def upload_agent_data():
    """上传代理关系数据进行分析"""
    from database.repositories.task_repository import task_repository
    import uuid
    
    if 'file' not in request.files:
        return jsonify({'error': '未检测到文件'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': '仅支持xlsx、xls、csv文件'}), 400
    
    # 检查是否使用异步模式
    use_async = request.form.get('async', 'true').lower() == 'true'
    
    if use_async:
        # 异步模式：创建任务并返回任务ID
        task_id = str(uuid.uuid4())
        
        # 保存文件到临时位置
        import tempfile
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
        file.save(temp_file.name)
        
        # 创建任务数据
        task_data = {
            'file_path': temp_file.name,
            'filename': file.filename,
            'enable_level_analysis': request.form.get('enable_level_analysis', 'true').lower() == 'true',
            'exclude_invalid_devices': request.form.get('exclude_invalid_devices', 'true').lower() == 'true',
            'exclude_internal_operations': request.form.get('exclude_internal_operations', 'true').lower() == 'true'
        }
        
        # 创建DuckDB任务
        task_repository.create_task(task_id, 'agent_analysis', file.filename)
        
        # 启动异步处理
        import threading
        thread = threading.Thread(target=process_agent_analysis_task, args=(task_id, task_data))
        thread.start()
        
        return jsonify({
            'task_id': task_id,
            'status': 'pending',
            'message': '代理关系分析任务已创建，将自动存储用户关系数据到数据库'
        })
    else:
        # 同步模式：直接处理并返回结果（保持向后兼容）
        # 获取数据过滤参数
        exclude_invalid_devices = request.form.get('exclude_invalid_devices', 'true').lower() == 'true'
        exclude_internal_operations = request.form.get('exclude_internal_operations', 'true').lower() == 'true'

        try:
            # 读取数据
            if file.filename.endswith('.csv'):
                df = pd.read_csv(file.stream, encoding='utf-8',
                               dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})
            else:
                df = pd.read_excel(file.stream, engine='openpyxl',
                                 dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})

            # 数据预处理（包含数据质量过滤）
            df = preprocess_data(df, '', exclude_invalid_devices, exclude_internal_operations)
            
            # 是否启用层级分析
            enable_level_analysis = request.form.get('enable_level_analysis', 'true').lower() == 'true'
            
            # 层级分析增强（如果启用）
            if enable_level_analysis and not df.empty:
                df = enhance_data_with_level_analysis(df, enable_risk_evaluation=False)
            
            # 使用并行处理器查找共享关系
            device_shared = parallel_processor.find_shared_relationships_parallel(df, 'device_id')
            ip_shared = parallel_processor.find_shared_relationships_parallel(df, 'ip')
            both_shared = parallel_processor.find_both_shared_parallel(device_shared, ip_shared)
            
            # 如果启用了层级分析，则增强关系数据
            if enable_level_analysis and not df.empty:
                user_repository = UserDataRepository(df.copy())
                device_shared = analyze_relationships_in_shared_data(device_shared, user_repository)
                ip_shared = analyze_relationships_in_shared_data(ip_shared, user_repository)
                both_shared = analyze_relationships_in_shared_data(both_shared, user_repository)
            
            # 生成BD团队统计数据
            bd_statistics = parallel_processor.analyze_bd_statistics_parallel(device_shared, ip_shared, both_shared)
            
            # 生成层级分析报告（如果启用）
            level_analysis_report = None
            if enable_level_analysis and not df.empty:
                level_analysis_report = generate_level_analysis_report(df)
            
            response_data = {
                'status': 'success',
                'device_shared': device_shared,
                'ip_shared': ip_shared,
                'both_shared': both_shared,
                'device_ranking': bd_statistics['device'],
                'ip_ranking': bd_statistics['ip'],
                'both_ranking': bd_statistics['both'],
                'summary': {
                    'device_shared_count': len(device_shared),
                    'ip_shared_count': len(ip_shared),
                    'both_shared_count': len(both_shared)
                }
            }
            
            # 添加层级分析报告（如果有）
            if level_analysis_report:
                response_data['level_analysis'] = level_analysis_report
            
            return jsonify(response_data)
            
        except Exception as e:
            return jsonify({'error': f'分析失败: {str(e)}'}), 500

@login_required  # 需要登录才能搜索数据
@csrf_protect
@agent_bp.route('/search', methods=['POST'])
def search_relationships():
    """搜索和过滤代理关系数据"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有提供数据'}), 400

        # 获取筛选条件
        search_term = data.get('search_term', '').lower()
        filter_bd = data.get('filter_bd', '')
        filter_unassigned_bd = data.get('filter_unassigned_bd', False)
        filter_ourbit_internal = data.get('filter_ourbit_internal', False)
        task_id = data.get('task_id')

        # 优化：如果有任务ID，直接从数据库查询，避免前端传输大量数据
        if task_id:
            logger.info(f"使用任务ID {task_id} 进行数据库搜索")

            from database.repositories.task_repository import task_repository

            # 从数据库获取筛选后的数据
            # 增加页面大小以支持大数据集
            max_page_size = 1000000  # 100万条记录的限制

            device_data = task_repository.get_shared_relationships(
                task_id=task_id,
                relationship_type='device',
                page=1,
                page_size=max_page_size,  # 增加页面大小
                bd_filter=filter_bd,
                search_term=search_term
            )

            ip_data = task_repository.get_shared_relationships(
                task_id=task_id,
                relationship_type='ip',
                page=1,
                page_size=max_page_size,
                bd_filter=filter_bd,
                search_term=search_term
            )

            both_data = task_repository.get_shared_relationships(
                task_id=task_id,
                relationship_type='both',
                page=1,
                page_size=max_page_size,
                bd_filter=filter_bd,
                search_term=search_term
            )

            # 格式化数据以匹配前端期望的结构（与get_task_result中的逻辑一致）
            def format_search_relationships(relationships, rel_type):
                formatted = []
                for rel in relationships:
                    formatted_rel = {
                        'user_a_mid': rel.get('user_a_mid', ''),
                        'user_a_member_id': rel.get('user_a_member_id', ''),
                        'user_a_name': rel.get('user_a_name', ''),
                        'user_a_bd': rel.get('user_a_bd', ''),
                        'user_a_level': rel.get('user_a_level', ''),
                        'user_a_time': rel.get('user_a_time', ''),
                        'user_b_mid': rel.get('user_b_mid', ''),
                        'user_b_member_id': rel.get('user_b_member_id', ''),
                        'user_b_name': rel.get('user_b_name', ''),
                        'user_b_bd': rel.get('user_b_bd', ''),
                        'user_b_level': rel.get('user_b_level', ''),
                        'user_b_time': rel.get('user_b_time', ''),
                        'same_bd': rel.get('same_bd', False),
                        'match_count': rel.get('match_count', 1),
                        'shared_value': rel.get('shared_value', '')
                    }

                    # 根据关系类型设置特定字段
                    if rel_type == 'device':
                        formatted_rel['shared_device_id'] = rel.get('shared_value', '')
                        formatted_rel['shared_ip'] = ''
                    elif rel_type == 'ip':
                        formatted_rel['shared_device_id'] = ''
                        formatted_rel['shared_ip'] = rel.get('shared_value', '')
                    elif rel_type == 'both':
                        # 对于both类型，shared_value是"device_id,ip"格式
                        shared_value = rel.get('shared_value', '')
                        logger.debug(f"搜索API处理both类型数据，shared_value: '{shared_value}'")

                        if shared_value:
                            shared_parts = shared_value.split(',')
                            if len(shared_parts) >= 2:
                                formatted_rel['shared_device_id'] = shared_parts[0].strip()
                                formatted_rel['shared_ip'] = shared_parts[1].strip()
                                logger.debug(f"搜索API成功解析both数据: device_id='{shared_parts[0].strip()}', ip='{shared_parts[1].strip()}'")
                            else:
                                logger.warning(f"搜索API both类型数据格式异常，shared_value: '{shared_value}', 分割后长度: {len(shared_parts)}")
                                formatted_rel['shared_device_id'] = shared_value
                                formatted_rel['shared_ip'] = ''
                        else:
                            logger.warning(f"搜索API both类型数据shared_value为空")
                            formatted_rel['shared_device_id'] = ''
                            formatted_rel['shared_ip'] = ''

                    formatted.append(formatted_rel)

                return formatted

            # 应用格式化
            device_shared = format_search_relationships(device_data['relationships'], 'device')
            ip_shared = format_search_relationships(ip_data['relationships'], 'ip')
            both_shared = format_search_relationships(both_data['relationships'], 'both')

            logger.info(f"任务ID模式数据统计: 设备{len(device_shared)}条, IP{len(ip_shared)}条, 两者{len(both_shared)}条")
            logger.info(f"数据库分页信息: 设备总数{device_data.get('pagination', {}).get('total', 0)}, IP总数{ip_data.get('pagination', {}).get('total', 0)}, 两者总数{both_data.get('pagination', {}).get('total', 0)}")

            # 对于ranking数据，我们需要重新计算或从缓存获取
            # 这里简化处理，返回空的ranking数据
            device_ranking = []
            ip_ranking = []
            both_ranking = []

        else:
            # 回退到原来的方式：从前端获取数据
            device_shared = data.get('device_shared', [])
            ip_shared = data.get('ip_shared', [])
            both_shared = data.get('both_shared', [])
            device_ranking = data.get('device_ranking', [])
            ip_ranking = data.get('ip_ranking', [])
            both_ranking = data.get('both_ranking', [])

        # 过滤数据 - 如果是任务ID模式，数据库已经完成了基本筛选
        if task_id:
            logger.info(f"任务ID模式：跳过前端筛选器，直接使用数据库筛选结果")
            # 任务ID模式：数据库已经完成了search_term和filter_bd的筛选
            # 直接使用数据库返回的结果，不再进行额外筛选
            filtered_device = device_shared
            filtered_ip = ip_shared
            filtered_both = both_shared

            # 只有在明确需要额外筛选时才应用前端筛选器
            if filter_unassigned_bd or filter_ourbit_internal:
                logger.info(f"应用额外的前端筛选条件: filter_unassigned_bd={filter_unassigned_bd}, filter_ourbit_internal={filter_ourbit_internal}")
                filtered_device = parallel_processor.filter_data_parallel(
                    device_shared, '', '', filter_unassigned_bd, filter_ourbit_internal, 'all'
                )
                filtered_ip = parallel_processor.filter_data_parallel(
                    ip_shared, '', '', filter_unassigned_bd, filter_ourbit_internal, 'all'
                )
                filtered_both = parallel_processor.filter_data_parallel(
                    both_shared, '', '', filter_unassigned_bd, filter_ourbit_internal, 'all'
                )
        else:
            logger.info(f"原始数据模式：使用完整的前端数据筛选")
            # 原来的方式：完整的前端数据筛选
            filtered_device = parallel_processor.filter_data_parallel(
                device_shared, search_term, filter_bd, filter_unassigned_bd, filter_ourbit_internal, 'all'
            )
            filtered_ip = parallel_processor.filter_data_parallel(
                ip_shared, search_term, filter_bd, filter_unassigned_bd, filter_ourbit_internal, 'all'
            )
            filtered_both = parallel_processor.filter_data_parallel(
                both_shared, search_term, filter_bd, filter_unassigned_bd, filter_ourbit_internal, 'all'
            )
        
        # 手动分类数据 - 确保只显示与指定BD相关的关系
        if filter_bd:
            logger.info(f"使用BD筛选: {filter_bd}")
            # 如果指定了BD筛选，手动进行更精确的分类
            device_same_bd = []
            device_diff_bd = []
            for item in filtered_device:
                user_a_bd = item.get('user_a_bd')
                user_b_bd = item.get('user_b_bd')

                # 确保至少有一方是指定的BD
                if user_a_bd == filter_bd or user_b_bd == filter_bd:
                    if user_a_bd == user_b_bd == filter_bd:
                        # 双方都是指定BD - 同一BD团队
                        device_same_bd.append(item)
                    else:
                        # 只有一方是指定BD - 不同BD团队
                        device_diff_bd.append(item)
            
            ip_same_bd = []
            ip_diff_bd = []
            for item in filtered_ip:
                user_a_bd = item.get('user_a_bd')
                user_b_bd = item.get('user_b_bd')
                
                if user_a_bd == filter_bd or user_b_bd == filter_bd:
                    if user_a_bd == user_b_bd == filter_bd:
                        ip_same_bd.append(item)
                    else:
                        ip_diff_bd.append(item)
            
            both_same_bd = []
            both_diff_bd = []
            for item in filtered_both:
                user_a_bd = item.get('user_a_bd')
                user_b_bd = item.get('user_b_bd')
                
                if user_a_bd == filter_bd or user_b_bd == filter_bd:
                    if user_a_bd == user_b_bd == filter_bd:
                        both_same_bd.append(item)
                    else:
                        both_diff_bd.append(item)

            logger.info(f"BD筛选分类结果: 设备同BD{len(device_same_bd)}, 设备异BD{len(device_diff_bd)}, IP同BD{len(ip_same_bd)}, IP异BD{len(ip_diff_bd)}, 两者同BD{len(both_same_bd)}, 两者异BD{len(both_diff_bd)}")
        else:
            logger.info("没有BD筛选，使用原有的分类逻辑")
            # 没有BD筛选，使用原有的分类逻辑
            device_same_bd, device_diff_bd = classify_data_by_bd(filtered_device, filter_bd)
            ip_same_bd, ip_diff_bd = classify_data_by_bd(filtered_ip, filter_bd)
            both_same_bd, both_diff_bd = classify_data_by_bd(filtered_both, filter_bd)

        logger.info(f"最终分类结果: 设备同BD{len(device_same_bd)}, 设备异BD{len(device_diff_bd)}, IP同BD{len(ip_same_bd)}, IP异BD{len(ip_diff_bd)}, 两者同BD{len(both_same_bd)}, 两者异BD{len(both_diff_bd)}")
        
        # 过滤排名数据
        filtered_device_ranking = filter_ranking(device_ranking, filter_unassigned_bd, filter_ourbit_internal, filter_bd)
        filtered_ip_ranking = filter_ranking(ip_ranking, filter_unassigned_bd, filter_ourbit_internal, filter_bd)
        filtered_both_ranking = filter_ranking(both_ranking, filter_unassigned_bd, filter_ourbit_internal, filter_bd)
        
        return jsonify({
            'status': 'success',
            'device_same_bd': device_same_bd,
            'device_diff_bd': device_diff_bd,
            'ip_same_bd': ip_same_bd,
            'ip_diff_bd': ip_diff_bd,
            'both_same_bd': both_same_bd,
            'both_diff_bd': both_diff_bd,
            'device_ranking': filtered_device_ranking,
            'ip_ranking': filtered_ip_ranking,
            'both_ranking': filtered_both_ranking,
            'summary': {
                'device_shared_count': len(filtered_device),
                'ip_shared_count': len(filtered_ip),
                'both_shared_count': len(filtered_both)
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'搜索失败: {str(e)}'}), 500

@login_required  # 需要登录才能下载结果
@csrf_protect
@agent_bp.route('/download', methods=['POST'])
def download_results():
    """下载分析结果"""
    try:
        data = request.get_json()
        
        # 获取筛选条件
        search_term = data.get('search_term', '').lower()
        filter_bd = data.get('filter_bd', '')
        filter_unassigned_bd = data.get('filter_unassigned_bd', False)
        filter_ourbit_internal = data.get('filter_ourbit_internal', False)
        
        # 🔄 兼容性处理：检查数据来源
        device_shared = data.get('device_shared', [])
        ip_shared = data.get('ip_shared', [])
        both_shared = data.get('both_shared', [])
        
        # 如果前端传递的数据为空但有任务ID，尝试从新表中获取数据
        if (not device_shared and not ip_shared and not both_shared):
            # 检查是否有task_id参数或在URL中
            task_id = data.get('task_id') or request.args.get('task_id')
            
            if task_id:
                try:
                    from database.repositories.task_repository import task_repository
                    
                    logger.info(f"从数据库获取任务 {task_id} 的完整数据")
                    
                    # 获取所有关系数据（不分页，用于导出）
                    max_export_size = 1000000  # 100万条记录的导出限制

                    device_data = task_repository.get_shared_relationships(
                        task_id=task_id,
                        relationship_type='device',
                        page=1,
                        page_size=max_export_size,  # 增加导出页面大小
                        bd_filter=filter_bd,
                        search_term=search_term
                    )

                    ip_data = task_repository.get_shared_relationships(
                        task_id=task_id,
                        relationship_type='ip',
                        page=1,
                        page_size=max_export_size,
                        bd_filter=filter_bd,
                        search_term=search_term
                    )

                    both_data = task_repository.get_shared_relationships(
                        task_id=task_id,
                        relationship_type='both',
                        page=1,
                        page_size=max_export_size,
                        bd_filter=filter_bd,
                        search_term=search_term
                    )
                    
                    # 格式化为导出格式
                    def format_for_export(relationships, rel_type):
                        formatted = []
                        for rel in relationships:
                            formatted_rel = {
                                'shared_type': '共享设备' if rel_type == 'device' else ('共享IP' if rel_type == 'ip' else '两者都共享'),
                                'user_a_mid': rel.get('user_a_mid', ''),
                                'user_a_member_id': rel.get('user_a_member_id', ''),
                                'user_a_name': rel.get('user_a_name', ''),
                                'user_a_bd': rel.get('user_a_bd', ''),
                                'user_a_level': rel.get('user_a_level', ''),
                                'user_a_time': rel.get('user_a_time', ''),
                                'user_b_mid': rel.get('user_b_mid', ''),
                                'user_b_member_id': rel.get('user_b_member_id', ''),
                                'user_b_name': rel.get('user_b_name', ''),
                                'user_b_bd': rel.get('user_b_bd', ''),
                                'user_b_level': rel.get('user_b_level', ''),
                                'user_b_time': rel.get('user_b_time', ''),
                                'same_bd': rel.get('same_bd', False),
                                'match_count': rel.get('match_count', 1)
                            }
                            
                            if rel_type == 'device':
                                formatted_rel['shared_device_id'] = rel.get('shared_value', '')
                                formatted_rel['shared_ip'] = ''
                            elif rel_type == 'ip':
                                formatted_rel['shared_device_id'] = ''
                                formatted_rel['shared_ip'] = rel.get('shared_value', '')
                            elif rel_type == 'both':
                                shared_parts = rel.get('shared_value', '').split(',')
                                if len(shared_parts) >= 2:
                                    formatted_rel['shared_device_id'] = shared_parts[0]
                                    formatted_rel['shared_ip'] = shared_parts[1]
                                else:
                                    formatted_rel['shared_device_id'] = ''
                                    formatted_rel['shared_ip'] = ''
                            
                            formatted.append(formatted_rel)
                        
                        return formatted
                    
                    # 替换原始数据
                    device_shared = format_for_export(device_data['relationships'], 'device')
                    ip_shared = format_for_export(ip_data['relationships'], 'ip')
                    both_shared = format_for_export(both_data['relationships'], 'both')
                    
                    logger.info(f"成功获取数据: 设备{len(device_shared)}条, IP{len(ip_shared)}条, 两者{len(both_shared)}条")
                    
                except Exception as db_error:
                    logger.warning(f"从数据库获取数据失败: {str(db_error)}")
        
        # 合并所有数据
        all_data = []
        
        # 添加设备共享数据
        for item in device_shared:
            item['shared_type'] = '共享设备'
            all_data.append(item)
        
        # 添加IP共享数据
        for item in ip_shared:
            item['shared_type'] = '共享IP'
            all_data.append(item)
        
        # 添加两者都共享数据
        for item in both_shared:
            item['shared_type'] = '两者都共享'
            all_data.append(item)
        
        # 过滤数据
        filtered_data = parallel_processor.filter_data_parallel(
            all_data, search_term, filter_bd, filter_unassigned_bd, filter_ourbit_internal
        )
        
        if not filtered_data:
            return jsonify({'error': '没有符合条件的数据可导出'}), 400
        
        # 格式化数据
        df_results = []
        for item in filtered_data:
            df_results.append({
                '共享类型': item.get('shared_type', ''),
                'BD团队是否相同': '相同' if item.get('same_bd', False) else '不同',
                '用户A_ID': item.get('user_a_mid', ''),
                '用户A_名称': item.get('user_a_name', ''),
                '用户A_BD团队': item.get('user_a_bd', ''),
                '用户A_等级': item.get('user_a_level', ''),
                '用户A_登录时间': item.get('user_a_time', ''),
                '用户B_ID': item.get('user_b_mid', ''),
                '用户B_名称': item.get('user_b_name', ''),
                '用户B_BD团队': item.get('user_b_bd', ''),
                '用户B_等级': item.get('user_b_level', ''),
                '用户B_登录时间': item.get('user_b_time', ''),
                '共享设备ID': item.get('shared_device_id', item.get('shared_value', '')),
                '共享IP': item.get('shared_ip', ''),
                '匹配次数': item.get('match_count', 0)
            })
        
        # 转换为DataFrame并保存
        result_df = pd.DataFrame(df_results)
        temp_csv = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')
        result_df.to_csv(temp_csv.name, index=False, encoding='utf-8-sig')
        
        return send_file(
            temp_csv.name, 
            as_attachment=True,
            download_name=f'代理关系分析_{datetime.now().strftime("%Y%m%d%H%M%S")}.csv',
            mimetype='text/csv'
        )
        
    except Exception as e:
        return jsonify({'error': f'导出失败: {str(e)}'}), 500

@agent_bp.route('/performance-test', methods=['POST'])
@admin_required  # 性能测试需要管理员权限
def performance_test():
    """性能测试接口"""
    if 'file' not in request.files:
        return jsonify({'error': '未检测到文件'}), 400
    
    file = request.files['file']
    if not file.filename.endswith('.csv'):
        return jsonify({'error': '仅支持CSV文件'}), 400
    
    level_column = request.form.get('level_column', '')
    
    try:
        # 读取数据并创建副本用于测试
        df = pd.read_csv(file.stream, encoding='utf-8', 
                        dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})
        df_copy = df.copy()
        
        # 预处理数据（使用默认的数据质量过滤）
        df = preprocess_data(df, level_column)
        df_copy = preprocess_data(df_copy, level_column)
        
        # 创建单线程和多线程处理器
        single_thread_processor = ParallelProcessor(
            enable_parallel=False, max_workers=1,
            min_data_size=999999999, min_groups_size=999999999
        )
        
        multi_thread_processor = ParallelProcessor(
            enable_parallel=True, max_workers=8,
            min_data_size=0, min_groups_size=0
        )
        
        # 执行性能测试...
        # (这里简化了代码，实际应该包含完整的性能测试逻辑)
        
        return jsonify({
            'status': 'success',
            'message': '性能测试完成',
            'data_size': len(df)
        })
        
    except Exception as e:
        return jsonify({'error': f'性能测试失败: {str(e)}'}), 500


# ========== 层级分析专用接口 ==========

@agent_bp.route('/level-analysis', methods=['POST'])
@admin_required  # 层级分析需要管理员权限
def analyze_user_levels():
    """用户层级分析专用接口"""
    if 'file' not in request.files:
        return jsonify({'error': '未检测到文件'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': '仅支持xlsx、xls、csv文件'}), 400
    
    try:
        # 读取数据
        if file.filename.endswith('.csv'):
            df = pd.read_csv(file.stream, encoding='utf-8', 
                           dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})
        else:
            df = pd.read_excel(file.stream, engine='openpyxl',
                             dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})
        
        # 数据预处理（使用默认的数据质量过滤）
        df = preprocess_data(df)

        # 层级分析增强
        df = enhance_data_with_level_analysis(df, enable_risk_evaluation=False)
        
        # 生成分析报告
        analysis_report = generate_level_analysis_report(df)
        
        # 高风险用户筛选
        high_risk_users = df[df.get('risk_level', '') == 'high'].to_dict('records') if 'risk_level' in df.columns else []
        
        # BD关系过滤
        bd_filtered_df = filter_bd_relationships(df, exclude_direct_customers=True)
        
        return jsonify({
            'status': 'success',
            'analysis_report': analysis_report,
            'high_risk_users': high_risk_users,
            'bd_filtered_count': len(bd_filtered_df),
            'total_users_analyzed': len(df),
            'level_categories': AgentLevelAnalyzer().get_level_categories()
        })
        
    except Exception as e:
        return jsonify({'error': f'层级分析失败: {str(e)}'}), 500


@agent_bp.route('/level-analysis/user/<user_id>', methods=['GET'])
@login_required  # 需要登录才能查看用户分析
def analyze_single_user(user_id):
    """分析单个用户的层级"""
    try:
        # 🚨 严格模式：不返回模拟数据，直接抛出异常
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"❌ 缺少真实用户数据源，无法分析用户 {user_id}")
        logger.error("❌ 需要补充以下真实数据：")
        logger.error("   1. digital_id: 用户数字ID")
        logger.error("   2. user_agent_level: 用户代理等级")
        logger.error("   3. recommender_digital_id: 推荐人数字ID")
        logger.error("   4. top_kol_digital_id: 顶级KOL数字ID")
        logger.error("   5. agent_flag: 代理标识")
        logger.error("   6. member_id: 会员ID")
        logger.error("❌ 数据来源：应从用户管理系统或代理关系表获取")
        
        raise ValueError(
            f"❌ 无法获取用户 {user_id} 的真实数据！\n"
            "请确保用户数据源包含完整的代理关系信息。\n"
            "如果您的系统没有相关数据表，请联系技术团队建立数据源。"
        )
        
        # 创建分析器
        level_analyzer = AgentLevelAnalyzer()
        risk_evaluator = LevelRiskEvaluator()
        
        # 分析层级
        level_result = level_analyzer.analyze(user_data)
        
        # 评估风险
        risk_result = risk_evaluator.evaluate_user_risk(user_data, level_result)
        
        return jsonify({
            'status': 'success',
            'user_id': user_id,
            'level_analysis': level_result,
            'risk_evaluation': risk_result
        })
        
    except Exception as e:
        return jsonify({'error': f'用户层级分析失败: {str(e)}'}), 500


@agent_bp.route('/level-analysis/relationship', methods=['POST'])
@login_required  # 需要登录才能分析用户关系
def analyze_user_relationship():
    """分析两个用户之间的层级关系"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有提供数据'}), 400
        
        user_a_data = data.get('user_a')
        user_b_data = data.get('user_b')
        
        if not user_a_data or not user_b_data:
            return jsonify({'error': '需要提供两个用户的数据'}), 400
        
        # 创建分析器
        level_analyzer = AgentLevelAnalyzer()
        relationship_analyzer = LevelRelationshipAnalyzer(level_analyzer)
        risk_evaluator = LevelRiskEvaluator()
        
        # 分析用户层级
        level_a_result = level_analyzer.analyze(user_a_data)
        level_b_result = level_analyzer.analyze(user_b_data)
        
        # 分析关系
        relationship_result = relationship_analyzer.analyze_relationship(
            level_a_result.get('level_number', 0),
            level_b_result.get('level_number', 0),
            user_a_data,
            user_b_data
        )
        
        # 评估关系风险
        relationship_risk = risk_evaluator.evaluate_relationship_risk(relationship_result)
        
        return jsonify({
            'status': 'success',
            'user_a_level': level_a_result,
            'user_b_level': level_b_result,
            'relationship_analysis': relationship_result,
            'relationship_risk': relationship_risk
        })
        
    except Exception as e:
        return jsonify({'error': f'关系分析失败: {str(e)}'}), 500


@agent_bp.route('/level-analysis/bd-graph', methods=['POST'])
@admin_required  # BD关系图生成需要管理员权限
def generate_bd_graph_data():
    """生成BD关系图数据"""
    if 'file' not in request.files:
        return jsonify({'error': '未检测到文件'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': '仅支持xlsx、xls、csv文件'}), 400
    
    try:
        # 读取数据
        if file.filename.endswith('.csv'):
            df = pd.read_csv(file.stream, encoding='utf-8',
                           dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})
        else:
            df = pd.read_excel(file.stream, engine='openpyxl',
                             dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})
        
        # 数据预处理（使用默认的数据质量过滤）
        df = preprocess_data(df)

        # 层级分析增强
        df = enhance_data_with_level_analysis(df, enable_risk_evaluation=False)
        
        # 根据记忆中的规则过滤BD关系图数据
        # BD关系图中，只显示BD推荐体系的用户（BD、1级代理、2级代理、3级代理），直客用户应被过滤掉
        bd_graph_df = filter_bd_relationships(df, exclude_direct_customers=True)
        
        # 构建图节点和边
        nodes = []
        edges = []
        
        for _, row in bd_graph_df.iterrows():
            user_data = row.to_dict()
            
            # 创建节点
            node = {
                'id': user_data.get('digital_id', ''),
                'label': user_data.get('user_name', user_data.get('digital_id', '')),
                'level_type': user_data.get('analyzed_level_type', '未知'),
                'level_number': user_data.get('analyzed_level_number', 0),
                'bd_name': user_data.get('bd_name', ''),
                'risk_level': user_data.get('risk_level', 'unknown'),
                'risk_score': user_data.get('risk_score', 0.0),
                'agent_flag': user_data.get('agent_flag', ''),
                'user_agent_level': user_data.get('user_agent_level', 0)
            }
            nodes.append(node)
            
            # 创建边（推荐关系）
            recommender_id = user_data.get('recommender_digital_id')
            if recommender_id and str(recommender_id).strip():
                edge = {
                    'source': recommender_id,
                    'target': user_data.get('digital_id', ''),
                    'relationship_type': 'recommendation',
                    'label': '推荐'
                }
                edges.append(edge)
        
        # 去重节点
        unique_nodes = {}
        for node in nodes:
            unique_nodes[node['id']] = node
        nodes = list(unique_nodes.values())
        
        # 统计信息
        level_stats = {}
        for node in nodes:
            level_type = node['level_type']
            level_stats[level_type] = level_stats.get(level_type, 0) + 1
        
        return jsonify({
            'status': 'success',
            'graph_data': {
                'nodes': nodes,
                'edges': edges
            },
            'statistics': {
                'total_nodes': len(nodes),
                'total_edges': len(edges),
                'level_distribution': level_stats,
                'bd_system_users': len(bd_graph_df),
                'original_users': len(df)
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'BD关系图数据生成失败: {str(e)}'}), 500


@agent_bp.route('/level-analysis/risk-report', methods=['POST'])
@admin_required  # 风险报告生成需要管理员权限
def generate_risk_report():
    """生成风险评估报告"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有提供数据'}), 400
        
        users_data = data.get('users', [])
        if not users_data:
            return jsonify({'error': '没有提供用户数据'}), 400
        
        # 创建分析器
        level_analyzer = AgentLevelAnalyzer()
        risk_evaluator = LevelRiskEvaluator()
        
        # 批量分析
        level_results = []
        risk_results = []
        
        for user_data in users_data:
            level_result = level_analyzer.analyze(user_data)
            risk_result = risk_evaluator.evaluate_user_risk(user_data, level_result)
            
            level_results.append(level_result)
            risk_results.append(risk_result)
        
        # 统计信息
        risk_stats = risk_evaluator.get_risk_statistics(risk_results)
        
        # 高风险用户
        high_risk_users = risk_evaluator.filter_high_risk_users(risk_results, 'medium')
        
        return jsonify({
            'status': 'success',
            'risk_statistics': risk_stats,
            'high_risk_users': high_risk_users,
            'total_analyzed': len(users_data)
        })
        
    except Exception as e:
        return jsonify({'error': f'风险报告生成失败: {str(e)}'}), 500

@agent_bp.route('/level-analysis/bd-pyramid-status', methods=['GET'])
@login_required
def check_bd_pyramid_status():
    """检查是否有可用的BD金字塔数据"""
    try:
        # 检查是否有已处理的代理关系分析任务结果
        from database.repositories.task_repository import task_repository
        import os
        import json
        from datetime import datetime
        
        # 从DuckDB获取代理分析任务
        tasks = task_repository.get_tasks_by_type('agent_analysis')
        
        # 检查是否有已完成的任务
        for task in tasks:
            if task.get('status') == 'completed':
                return jsonify({
                    'status': 'success',
                    'has_cached_data': True,
                    'task_id': task.get('task_id'),
                    'data_summary': {
                        'device_shared_count': 0,  # 暂时设为0，可以后续从结果数据中计算
                        'ip_shared_count': 0,
                        'both_shared_count': 0
                    },
                    'last_updated': task.get('updated_at'),
                    'message': '发现可用的代理关系数据'
                })
        
        return jsonify({
            'status': 'success',
            'has_cached_data': False,
            'message': '没有可用的BD金字塔数据，请先在代理关系分析页面上传数据'
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'has_cached_data': False,
            'error': f'检查BD数据状态失败: {str(e)}'
        }), 500

@agent_bp.route('/level-analysis/bd-pyramid-from-cache', methods=['POST'])
@login_required  # 需要登录才能生成BD金字塔
def generate_bd_pyramid_from_cache():
    """基于已缓存的代理关系数据生成BD金字塔结构"""
    try:
        request_data = request.get_json()
        task_id = request_data.get('task_id')
        include_direct_customers = request_data.get('include_direct_customers', False)
        
        if not task_id:
            return jsonify({'error': '缺少task_id参数'}), 400
        
        # 从DuckDB获取数据
        from database.repositories.task_repository import task_repository
        import os
        import json
        
        # 从DuckDB获取任务数据
        task = task_repository.get_task(task_id)
        
        if not task or task.get('status') != 'completed':
            return jsonify({'error': '未找到有效的任务数据'}), 404
        
        # 返回简化的BD金字塔结构（因为没有详细的关系数据）
        return jsonify({
            'status': 'success',
            'bd_pyramid_data': [],
            'message': 'DuckDB模式下暂不支持从缓存生成BD金字塔，请重新上传数据'
        })
        
    except Exception as e:
        return jsonify({'error': f'从缓存生成BD金字塔失败: {str(e)}'}), 500

@agent_bp.route('/level-analysis/bd-pyramid', methods=['POST'])
@admin_required  # BD金字塔生成需要管理员权限
def generate_bd_pyramid_json():
    """生成BD为起点的金字塔链路图JSON格式"""
    if 'file' not in request.files:
        return jsonify({'error': '未检测到文件'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': '仅支持xlsx、xls、csv文件'}), 400
    
    # 获取是否包含直客树的参数
    include_direct_customers = request.form.get('include_direct_customers', 'false').lower() == 'true'
    # 获取是否包含孤立直客统计的参数
    include_isolated_dc_stats = request.form.get('include_isolated_dc_stats', 'false').lower() == 'true'
    
    try:
        # 读取数据
        if file.filename.endswith('.csv'):
            df = pd.read_csv(file.stream, encoding='utf-8',
                           dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})
        else:
            df = pd.read_excel(file.stream, engine='openpyxl',
                             dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})
        
        # 数据预处理（使用默认的数据质量过滤）
        df = preprocess_data(df)

        # 层级分析增强
        df = enhance_data_with_level_analysis(df, enable_risk_evaluation=False)
        
        # 根据是否包含直客来决定过滤策略
        if include_direct_customers:
            # 包含直客模式：不排除直客，且包含直客体系用户
            bd_graph_df = filter_bd_relationships(df, exclude_direct_customers=False, include_direct_customers=True)
        else:
            # 传统模式：排除所有直客
            bd_graph_df = filter_bd_relationships(df, exclude_direct_customers=True, include_direct_customers=False)
        
        # 构建金字塔链路图（传递直客树参数）
        pyramid_data = build_bd_pyramid_structure(
            bd_graph_df, 
            include_direct_customer_trees=include_direct_customers,
            include_isolated_dc_stats=include_isolated_dc_stats
        )
        
        # 生成统计信息
        statistics = generate_pyramid_statistics(pyramid_data)
        
        # 🆕 自动存储用户和关系数据到数据库（统一数据管理）
        try:
            from database.repositories.user_repository import user_repository
            import uuid
            
            logger.info("开始存储用户数据到数据库")
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 提取用户数据
            users = []
            relationships = []
            
            for _, row in df.iterrows():
                digital_id = row.get('digital_id', '')
                if not digital_id:
                    continue
                
                # 构建用户数据
                user_data = {
                    'digital_id': digital_id,
                    'member_id': row.get('member_id', ''),
                    'user_name': row.get('user_name', ''),
                    'agent_flag': row.get('agent_flag', ''),
                    'analyzed_level_number': row.get('analyzed_level_number', 0),
                    'analyzed_level_type': row.get('analyzed_level_type', '未知'),
                    'bd_name': row.get('bd_name', ''),
                    'device_count': 1 if row.get('device_id') else 0,
                    'risk_score': row.get('risk_score', 0),
                    'relation_count': 0  # 后续可以计算
                }
                users.append(user_data)
                
                # 构建关系数据
                recommender_id = row.get('recommender_digital_id', '')
                if recommender_id and str(recommender_id) != 'nan' and recommender_id.strip():
                    relationship_data = {
                        'parent_digital_id': recommender_id,
                        'child_digital_id': digital_id,
                        'relationship_type': 'direct_agent',
                        'level_diff': 1
                    }
                    relationships.append(relationship_data)
            
            # 批量插入用户数据
            if users:
                user_repository.batch_insert_users(users)
                logger.info(f"成功存储{len(users)}个用户到数据库")
            
            # 批量插入关系数据
            if relationships:
                user_repository.batch_insert_relationships(relationships)
                logger.info(f"成功存储{len(relationships)}个关系到数据库")
            
            # 🚨 严格模式：不构建模拟BD统计数据
            logger.error("❌ 缺少真实BD统计数据源")
            
            bd_statistics = None  # 不提供模拟数据
            
            # 更新结果数据，添加数据库存储信息
            result_data = {
                'status': 'success',
                'pyramid_data': pyramid_data,
                'statistics': statistics,
                'bd_pyramid_data': pyramid_data,
                'filename': file.filename,
                'created_at': datetime.now().isoformat(),
                'task_type': 'bd_pyramid_analysis',
                'total_members': len(df),
                'database_storage': {
                    'users_stored': len(users),
                    'relationships_stored': len(relationships),
                    'storage_status': 'success'
                }
            }
            
            # 添加层级分析报告（如果有）
            level_analysis_report = generate_level_analysis_report(df)
            if level_analysis_report:
                result_data['level_analysis'] = level_analysis_report
            
        except Exception as db_error:
            logger.warning(f"存储到数据库失败: {str(db_error)}")
            
            # 🚨 严格模式：数据库存储失败时也不提供模拟数据
            logger.error("❌ 数据库存储失败，且缺少真实BD统计数据源")
            bd_statistics = None  # 不提供模拟数据
            
            result_data = {
                'status': 'success',
                'pyramid_data': pyramid_data,
                'statistics': statistics,
                'bd_pyramid_data': pyramid_data,
                'filename': file.filename,
                'created_at': datetime.now().isoformat(),
                'task_type': 'bd_pyramid_analysis',
                'total_members': len(df),
                'database_storage': {
                    'users_stored': 0,
                    'relationships_stored': 0,
                    'storage_status': 'failed',
                    'error': str(db_error)
                }
            }
        
        return jsonify(result_data)
        
    except Exception as e:
        return jsonify({'error': f'BD金字塔链路图生成失败: {str(e)}'}), 500

@agent_bp.route('/tasks', methods=['GET'])
@login_required  # 需要登录才能查看任务列表
def get_completed_tasks():
    """获取已完成的代理商关系分析任务列表"""
    try:
        from database.repositories.task_repository import task_repository
        
        # 从DuckDB获取代理分析任务
        tasks = task_repository.get_tasks_by_type('agent_analysis')
        
        completed_tasks = []
        
        for task in tasks:
            if task.get('status') == 'completed':
                completed_tasks.append({
                    'task_id': task.get('task_id'),
                    'filename': task.get('filename', '未知文件'),
                    'total_members': 0,  # 暂时设为0，可以后续从结果数据中计算
                    'created_at': task.get('created_at'),
                    'status': task.get('status')
                })
        
        return jsonify({
            'status': 'success',
            'tasks': completed_tasks,
            'total': len(completed_tasks)
        })
        
    except Exception as e:
        return jsonify({'error': f'获取任务列表失败: {str(e)}'}), 500

@agent_bp.route('/result/<task_id>', methods=['GET'])
@login_required  # 需要登录才能查看任务结果
def get_task_result(task_id):
    """获取指定任务的结果"""
    try:
        from database.repositories.task_repository import task_repository
        
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 1000))  # 代理商数据通常较大，使用更大的分页
        
        # 从DuckDB获取任务信息
        task = task_repository.get_task(task_id)
        
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        # 检查任务状态
        if task.get('status') == 'failed':
            return jsonify({
                'task_id': task_id,
                'status': 'failed', 
                'error': task.get('message', '任务处理失败'),
                'message': task.get('message', '任务处理失败')
            }), 400
        
        if task.get('status') == 'processing':
            return jsonify({
                'task_id': task_id,
                'status': 'processing',
                'progress': task.get('progress', 0),
                'message': task.get('message', '任务处理中...')
            }), 202
        
        # 获取任务结果摘要
        result_summary = task.get('result_summary', {})
        
        # 真正的分页模式 - 不再一次性加载大量数据
        if result_summary.get('data_storage') == 'optimized_table':
            try:
                logger.info(f"为任务 {task_id} 返回分页信息")
                
                # 获取各类型关系数据的总数（仅统计信息，不加载数据）
                device_stats = task_repository.get_relationship_statistics(task_id, 'device')
                ip_stats = task_repository.get_relationship_statistics(task_id, 'ip')
                both_stats = task_repository.get_relationship_statistics(task_id, 'both')
                
                # 对于代理商分析，一次性返回所有数据，让前端进行分页显示
                # 这样用户可以在筛选后看到完整的数据集
                
                # 获取所有数据（不分页）
                device_data = task_repository.get_shared_relationships(
                    task_id=task_id, 
                    relationship_type='device',
                    page=1, 
                    page_size=999999  # 获取所有数据
                )
                
                ip_data = task_repository.get_shared_relationships(
                    task_id=task_id, 
                    relationship_type='ip',
                    page=1, 
                    page_size=999999  # 获取所有数据
                )
                
                both_data = task_repository.get_shared_relationships(
                    task_id=task_id, 
                    relationship_type='both',
                    page=1, 
                    page_size=999999  # 获取所有数据
                )
                
                # 格式化为前端期望的格式
                def format_relationships(relationships, rel_type):
                    formatted = []
                    for rel in relationships:
                        formatted_rel = {
                            'user_a_mid': rel.get('user_a_mid', ''),
                            'user_a_member_id': rel.get('user_a_member_id', ''),
                            'user_a_name': rel.get('user_a_name', ''),
                            'user_a_bd': rel.get('user_a_bd', ''),
                            'user_a_level': rel.get('user_a_level', ''),
                            'user_a_time': rel.get('user_a_time', ''),
                            'user_b_mid': rel.get('user_b_mid', ''),
                            'user_b_member_id': rel.get('user_b_member_id', ''),
                            'user_b_name': rel.get('user_b_name', ''),
                            'user_b_bd': rel.get('user_b_bd', ''),
                            'user_b_level': rel.get('user_b_level', ''),
                            'user_b_time': rel.get('user_b_time', ''),
                            'same_bd': rel.get('same_bd', False),
                            'match_count': rel.get('match_count', 1),
                            'shared_value': rel.get('shared_value', '')
                        }
                        
                        # 根据关系类型设置特定字段
                        if rel_type == 'device':
                            formatted_rel['shared_device_id'] = rel.get('shared_value', '')
                            formatted_rel['shared_ip'] = ''
                        elif rel_type == 'ip':
                            formatted_rel['shared_device_id'] = ''
                            formatted_rel['shared_ip'] = rel.get('shared_value', '')
                        elif rel_type == 'both':
                            # 对于both类型，shared_value是"device_id,ip"格式
                            shared_value = rel.get('shared_value', '')
                            logger.debug(f"处理both类型数据，shared_value: '{shared_value}'")

                            if shared_value:
                                shared_parts = shared_value.split(',')
                                if len(shared_parts) >= 2:
                                    formatted_rel['shared_device_id'] = shared_parts[0].strip()
                                    formatted_rel['shared_ip'] = shared_parts[1].strip()
                                    logger.debug(f"成功解析both数据: device_id='{shared_parts[0].strip()}', ip='{shared_parts[1].strip()}'")
                                else:
                                    logger.warning(f"both类型数据格式异常，shared_value: '{shared_value}', 分割后长度: {len(shared_parts)}")
                                    formatted_rel['shared_device_id'] = shared_value
                                    formatted_rel['shared_ip'] = ''
                            else:
                                logger.warning(f"both类型数据shared_value为空")
                                formatted_rel['shared_device_id'] = ''
                                formatted_rel['shared_ip'] = ''
                        
                        formatted.append(formatted_rel)
                    
                    return formatted
                
                # 构建分页模式的结果格式
                paginated_result = result_summary.copy()
                paginated_result.update({
                    'device_shared': format_relationships(device_data['relationships'], 'device'),
                    'ip_shared': format_relationships(ip_data['relationships'], 'ip'),
                    'both_shared': format_relationships(both_data['relationships'], 'both'),
                    'device_ranking': result_summary.get('ranking_summary', {}).get('top_device_bds', []),
                    'ip_ranking': result_summary.get('ranking_summary', {}).get('top_ip_bds', []),
                    'both_ranking': result_summary.get('ranking_summary', {}).get('top_both_bds', []),
                    'pagination': {
                        'device': {
                            'total_records': device_stats.get('total_count', 0),
                            'loaded_records': len(device_data['relationships'])
                        },
                        'ip': {
                            'total_records': ip_stats.get('total_count', 0),
                            'loaded_records': len(ip_data['relationships'])
                        },
                        'both': {
                            'total_records': both_stats.get('total_count', 0),
                            'loaded_records': len(both_data['relationships'])
                        }
                    },
                    '_pagination_mode': 'client_side',
                    '_note': f'客户端分页模式：已加载所有数据，前端负责分页显示。设备{len(device_data["relationships"])}条，IP{len(ip_data["relationships"])}条，两者{len(both_data["relationships"])}条。'
                })
                
                logger.info(f"返回第{page}页数据: 设备{len(device_data['relationships'])}条, IP{len(ip_data['relationships'])}条, 两者{len(both_data['relationships'])}条")
                
                result_summary = paginated_result
                
            except Exception as page_error:
                logger.warning(f"加载分页数据失败: {str(page_error)}")
                # 如果分页加载失败，仍返回原始摘要
        
        # 构建返回结果
        result = {
            'task_id': task_id,
            'task_type': 'agent_analysis',
            'status': task.get('status'),
            'message': task.get('message', ''),
            'created_at': task.get('created_at'),
            'updated_at': task.get('updated_at'),
            'result': result_summary,
            'page': page,
            'page_size': page_size,
            'error': None
        }
        
        return jsonify(result)
            
    except Exception as e:
        return jsonify({'error': f'获取任务结果失败: {str(e)}'}), 500

@agent_bp.route('/result/<task_id>/relationships/<relationship_type>', methods=['GET'])
@login_required  # 需要登录才能查看关系数据
def get_task_relationships_by_type(task_id, relationship_type):
    """按需获取指定类型的代理商关系数据（支持分页）"""
    try:
        from database.repositories.task_repository import task_repository
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        bd_filter = request.args.get('bd_filter', '')
        search_term = request.args.get('search', '')
        same_bd_only = request.args.get('same_bd_only', 'false').lower() == 'true'
        
        # 验证关系类型
        if relationship_type not in ['device', 'ip', 'both']:
            return jsonify({'error': f'无效的关系类型: {relationship_type}'}), 400
        
        # 验证任务是否存在
        task = task_repository.get_task(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        # 检查任务状态
        if task.get('status') != 'completed':
            return jsonify({'error': '任务尚未完成'}), 400
        
        # 获取指定类型的关系数据
        relationships_data = task_repository.get_shared_relationships(
            task_id=task_id,
            relationship_type=relationship_type,
            page=page,
            page_size=page_size,
            bd_filter=bd_filter,
            search_term=search_term
        )
        
        # 格式化数据以兼容前端
        formatted_relationships = []
        for rel in relationships_data['relationships']:
            formatted_rel = {
                'user_a_mid': rel.get('user_a_mid', ''),
                'user_a_member_id': rel.get('user_a_member_id', ''),
                'user_a_name': rel.get('user_a_name', ''),
                'user_a_bd': rel.get('user_a_bd', ''),
                'user_a_level': rel.get('user_a_level', ''),
                'user_a_time': rel.get('user_a_time', ''),
                'user_b_mid': rel.get('user_b_mid', ''),
                'user_b_member_id': rel.get('user_b_member_id', ''),
                'user_b_name': rel.get('user_b_name', ''),
                'user_b_bd': rel.get('user_b_bd', ''),
                'user_b_level': rel.get('user_b_level', ''),
                'user_b_time': rel.get('user_b_time', ''),
                'same_bd': rel.get('same_bd', False),
                'match_count': rel.get('match_count', 1),
                'shared_value': rel.get('shared_value', ''),
                'relationship_type': relationship_type
            }
            
            # 根据关系类型设置相应的共享字段
            if relationship_type == 'device':
                formatted_rel['shared_device_id'] = rel.get('shared_value', '')
            elif relationship_type == 'ip':
                formatted_rel['shared_ip'] = rel.get('shared_value', '')
            elif relationship_type == 'both':
                # 对于both类型，shared_value是"device_id,ip"格式
                shared_parts = rel.get('shared_value', '').split(',')
                if len(shared_parts) >= 2:
                    formatted_rel['shared_device_id'] = shared_parts[0]
                    formatted_rel['shared_ip'] = shared_parts[1]
            
            formatted_relationships.append(formatted_rel)
        
        # 如果需要按same_bd过滤，进行客户端过滤
        if same_bd_only:
            formatted_relationships = [rel for rel in formatted_relationships if rel.get('same_bd', False)]
        
        return jsonify({
            'status': 'success',
            'task_id': task_id,
            'relationship_type': relationship_type,
            'relationships': formatted_relationships,
            'pagination': relationships_data['pagination'],
            'filters': {
                'bd_filter': bd_filter,
                'search_term': search_term,
                'same_bd_only': same_bd_only
            }
        })
        
    except Exception as e:
        logger.error(f"获取关系数据失败: {str(e)}")
        return jsonify({'error': f'获取关系数据失败: {str(e)}'}), 500

@agent_bp.route('/result/<task_id>/shared-relationships', methods=['GET'])
@login_required  # 需要登录才能查看共享关系数据
def get_task_shared_relationships(task_id):
    """按需获取任务的共享关系详情（兼容性接口）"""
    try:
        from database.repositories.task_repository import task_repository
        
        # 获取查询参数
        relationship_type = request.args.get('type', '')  # 'device', 'ip', 'both', 或空字符串获取全部
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))  # 详细数据用较小的分页
        bd_filter = request.args.get('bd_filter', '')
        search_term = request.args.get('search', '')
        
        # 验证任务是否存在
        task = task_repository.get_task(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        # 检查任务状态
        if task.get('status') != 'completed':
            return jsonify({'error': '任务尚未完成'}), 400
        
        # 获取共享关系数据
        relationships_data = task_repository.get_shared_relationships(
            task_id=task_id,
            relationship_type=relationship_type if relationship_type else None,
            page=page,
            page_size=page_size,
            bd_filter=bd_filter,
            search_term=search_term
        )
        
        # 格式化返回数据，兼容前端期望的格式
        formatted_relationships = []
        for rel in relationships_data['relationships']:
            formatted_rel = {
                'user_a_mid': rel.get('user_a_mid', ''),
                'user_a_member_id': rel.get('user_a_member_id', ''),
                'user_a_name': rel.get('user_a_name', ''),
                'user_a_bd': rel.get('user_a_bd', ''),
                'user_a_level': rel.get('user_a_level', ''),
                'user_a_time': rel.get('user_a_time', ''),
                'user_b_mid': rel.get('user_b_mid', ''),
                'user_b_member_id': rel.get('user_b_member_id', ''),
                'user_b_name': rel.get('user_b_name', ''),
                'user_b_bd': rel.get('user_b_bd', ''),
                'user_b_level': rel.get('user_b_level', ''),
                'user_b_time': rel.get('user_b_time', ''),
                'same_bd': rel.get('same_bd', False),
                'match_count': rel.get('match_count', 1),
                'shared_value': rel.get('shared_value', ''),
                'relationship_type': rel.get('relationship_type', '')
            }
            
            # 根据关系类型设置相应的共享字段
            if rel.get('relationship_type') == 'device':
                formatted_rel['shared_device_id'] = rel.get('shared_value', '')
            elif rel.get('relationship_type') == 'ip':
                formatted_rel['shared_ip'] = rel.get('shared_value', '')
            elif rel.get('relationship_type') == 'both':
                # 对于both类型，shared_value是"device_id,ip"格式
                shared_parts = rel.get('shared_value', '').split(',')
                if len(shared_parts) >= 2:
                    formatted_rel['shared_device_id'] = shared_parts[0]
                    formatted_rel['shared_ip'] = shared_parts[1]
            
            formatted_relationships.append(formatted_rel)
        
        return jsonify({
            'status': 'success',
            'task_id': task_id,
            'relationships': formatted_relationships,
            'pagination': relationships_data['pagination'],
            'filters': {
                'relationship_type': relationship_type,
                'bd_filter': bd_filter,
                'search_term': search_term
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'获取共享关系数据失败: {str(e)}'}), 500

@login_required
@csrf_protect
@agent_bp.route('/batch-query', methods=['POST'])
def batch_query():
    """批量查询用户关系接口"""
    try:
        from ..services.batch_query_service import batch_query_service

        data = request.get_json()
        if not data:
            return jsonify({'error': '没有提供数据'}), 400

        user_ids = data.get('user_ids', [])
        include_device = data.get('include_device', True)
        include_ip = data.get('include_ip', True)
        output_format = data.get('output_format', 'separate')
        color_coding = data.get('color_coding', 'both')

        # 调用批量查询服务
        result = batch_query_service.create_batch_query_task(
            user_ids, include_device, include_ip, output_format, color_coding
        )

        return jsonify(result)

    except Exception as e:
        logger.error(f"批量查询失败: {str(e)}")
        return jsonify({'error': f'批量查询失败: {str(e)}'}), 500

@agent_bp.route('/batch-query-status/<task_id>', methods=['GET'])
@login_required
def batch_query_status(task_id):
    """检查批量查询任务状态"""
    try:
        from ..services.batch_query_service import batch_query_service

        result = batch_query_service.get_task_status(task_id)
        return jsonify(result)

    except Exception as e:
        logger.error(f"检查批量查询状态失败: {str(e)}")
        return jsonify({'error': f'检查状态失败: {str(e)}'}), 500

@agent_bp.route('/batch-query-download/<task_id>', methods=['GET'])
@login_required
def batch_query_download(task_id):
    """下载批量查询结果"""
    try:
        from ..services.batch_query_service import batch_query_service
        from flask import send_file
        from datetime import datetime

        # 获取文件路径
        file_path = batch_query_service.get_download_file(task_id)

        # 生成下载文件名
        filename = f"batch_query_result_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        return send_file(
            file_path,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"下载批量查询结果失败: {str(e)}")
        return jsonify({'error': f'下载失败: {str(e)}'}), 500



@agent_bp.route('/export-data/<task_id>', methods=['GET'])
@admin_required  # 数据导出需要管理员权限
def export_task_data(task_id):
    """导出指定任务的数据（用于其他模块使用）"""
    try:
        from database.repositories.task_repository import task_repository

        # 从DuckDB获取任务数据
        task = task_repository.get_task(task_id)

        if not task:
            return jsonify({'error': '任务不存在'}), 404

        # 构建导出数据格式
        export_data = {
            'status': 'success',
            'task_id': task_id,
            'data': task.get('result_summary', {}),
            'meta': {
                'data_source': 'agent_relationship_analysis',
                'export_time': datetime.now().isoformat(),
                'format_version': '1.0'
            }
        }

        return jsonify(export_data)

    except Exception as e:
        return jsonify({'error': f'导出数据失败: {str(e)}'}), 500

@agent_bp.route('/unified-data', methods=['GET'])
@login_required
def get_unified_data():
    """统一数据接口 - 根据查询参数返回不同的数据组合"""
    try:
        from database.repositories.user_repository import user_repository
        
        # 获取查询参数
        data_type = request.args.get('type', 'all')  # all, bd_pyramid, agent_relations, users
        bd_filter = request.args.get('bd_filter', '')  # 按BD筛选
        include_relations = request.args.get('include_relations', 'true').lower() == 'true'
        include_statistics = request.args.get('include_statistics', 'true').lower() == 'true'
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 1000))
        
        result = {
            'status': 'success',
            'data_type': data_type,
            'filters': {
                'bd_filter': bd_filter,
                'include_relations': include_relations,
                'include_statistics': include_statistics
            },
            'pagination': {
                'page': page,
                'page_size': page_size
            }
        }
        
        # 根据数据类型返回不同的数据组合
        if data_type in ['all', 'users']:
            # 获取用户数据
            users = user_repository.get_users_with_pagination(page, page_size, bd_filter)
            result['users'] = users
            
        if data_type in ['all', 'agent_relations'] and include_relations:
            # 获取代理关系数据
            relationships = user_repository.get_relationships_with_pagination(page, page_size, bd_filter)
            result['relationships'] = relationships
            
        if data_type in ['all', 'bd_pyramid']:
            # 获取BD金字塔数据
            bd_data = user_repository.get_bd_pyramid_data(bd_filter)
            result['bd_pyramid'] = bd_data
            
        if include_statistics:
            # 获取统计信息
            stats = user_repository.get_data_statistics(bd_filter)
            result['statistics'] = stats
            
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'获取统一数据失败: {str(e)}'}), 500

@agent_bp.route('/data-sources', methods=['GET'])
@login_required  # 需要登录才能查看数据源信息
def get_data_sources():
    """获取可用的数据源信息"""
    try:
        from database.repositories.user_repository import user_repository
        from database.repositories.task_repository import task_repository
        
        # 获取用户表统计
        user_stats = user_repository.get_table_statistics()
        
        # 获取任务列表
        tasks = task_repository.get_completed_tasks()
        
        # 获取BD列表
        bd_list = user_repository.get_bd_list()
        
        result = {
            'status': 'success',
            'data_sources': {
                'database': {
                    'users_count': user_stats.get('total_users', 0),
                    'relationships_count': user_stats.get('total_relationships', 0),
                    'bd_count': len(bd_list),
                    'last_updated': user_stats.get('last_updated')
                },
                'tasks': {
                    'total_tasks': len(tasks),
                    'completed_tasks': len([t for t in tasks if t.get('status') == 'completed']),
                    'latest_task': tasks[0] if tasks else None
                }
            },
            'available_bds': bd_list,
            'supported_data_types': ['all', 'bd_pyramid', 'agent_relations', 'users'],
            'api_endpoints': {
                'unified_data': '/api/agent/unified-data',
                'bd_pyramid': '/api/agent/level-analysis/bd-pyramid-from-cache',
                'agent_relations': '/api/agent/result/<task_id>',
                'user_details': '/api/user/analysis/<member_id>'
            }
        }
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'获取数据源信息失败: {str(e)}'}), 500

def _determine_level_type(level_number):
    """根据层级数字确定层级类型"""
    if level_number == 1:
        return 'BD'
    elif level_number == 2:
        return '1级代理'
    elif level_number == 3:
        return '2级代理'
    elif level_number == 4:
        return '3级代理'
    elif level_number >= 11:
        return '直客'
    else:
        return '未知'

def _determine_agent_flag(level_number):
    """根据层级数字确定代理标识"""
    if level_number == 1:
        return 'BD'
    elif level_number >= 11:
        return '直客'
    elif 2 <= level_number <= 4:
        return f'{level_number-1}级代理'
    else:
        return '用户'

















def process_agent_analysis_task(task_id, task_data):
    """处理代理商分析任务"""
    import pandas as pd
    import os
    from database.repositories.task_repository import task_repository
    
    try:
        # 更新任务状态为处理中
        task_repository.update_task_status(task_id, 'processing', 0, "正在处理代理关系分析...")
        
        # 获取任务数据
        file_path = task_data['file_path']
        enable_level_analysis = task_data['enable_level_analysis']
        exclude_invalid_devices = task_data.get('exclude_invalid_devices', True)
        exclude_internal_operations = task_data.get('exclude_internal_operations', True)

        # 读取数据
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path, encoding='utf-8',
                           dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})
        else:
            df = pd.read_excel(file_path, engine='openpyxl',
                             dtype={'digital_id': str, 'recommender_digital_id': str, 'top_kol_digital_id': str})

        # 数据预处理（包含数据质量过滤）
        df = preprocess_data(df, '', exclude_invalid_devices, exclude_internal_operations)
        
        # 层级分析增强（如果启用）
        if enable_level_analysis and not df.empty:
            df = enhance_data_with_level_analysis(df, enable_risk_evaluation=False)
        
        # 使用并行处理器查找共享关系
        device_shared = parallel_processor.find_shared_relationships_parallel(df, 'device_id')
        ip_shared = parallel_processor.find_shared_relationships_parallel(df, 'ip')
        both_shared = parallel_processor.find_both_shared_parallel(device_shared, ip_shared)
        
        # 如果启用了层级分析，则增强关系数据
        if enable_level_analysis and not df.empty:
            user_repository = UserDataRepository(df.copy())
            device_shared = analyze_relationships_in_shared_data(device_shared, user_repository)
            ip_shared = analyze_relationships_in_shared_data(ip_shared, user_repository)
            both_shared = analyze_relationships_in_shared_data(both_shared, user_repository)
        
        # 生成BD团队统计数据
        bd_statistics = parallel_processor.analyze_bd_statistics_parallel(device_shared, ip_shared, both_shared)
        
        # 生成层级分析报告（如果启用）
        level_analysis_report = None
        if enable_level_analysis and not df.empty:
            level_analysis_report = generate_level_analysis_report(df)
        
        # 🎯 简洁存储策略：成功就用优化存储，失败就报错让用户重试
        logger.info(f"开始将{len(device_shared) + len(ip_shared) + len(both_shared)}条关系数据存储到优化表中")
        
        try:
            # 分批处理，避免一次性处理过多数据
            batch_size = 1000
            total_count = 0
            
            # 分批处理设备共享关系
            for i in range(0, len(device_shared), batch_size):
                batch = device_shared[i:i + batch_size]
                batch_data = []
                for rel in batch:
                    rel_data = rel.copy()
                    rel_data['relationship_type'] = 'device'
                    # 🎯 现在并行处理器会返回明确的字段名，fallback到shared_value
                    rel_data['shared_value'] = rel.get('shared_device_id', rel.get('shared_value', ''))
                    batch_data.append(rel_data)
                
                if batch_data:
                    task_repository.batch_insert_shared_relationships(task_id, batch_data)
                    total_count += len(batch_data)
                    logger.debug(f"分批存储设备共享关系: {total_count}条")
            
            # 分批处理IP共享关系
            for i in range(0, len(ip_shared), batch_size):
                batch = ip_shared[i:i + batch_size]
                batch_data = []
                for rel in batch:
                    rel_data = rel.copy()
                    rel_data['relationship_type'] = 'ip'
                    # 🎯 现在并行处理器会返回明确的字段名，fallback到shared_value
                    rel_data['shared_value'] = rel.get('shared_ip', rel.get('shared_value', ''))
                    batch_data.append(rel_data)
                
                if batch_data:
                    task_repository.batch_insert_shared_relationships(task_id, batch_data)
                    total_count += len(batch_data)
                    logger.debug(f"分批存储IP共享关系: {total_count}条")
            
            # 分批处理两者都共享的关系
            for i in range(0, len(both_shared), batch_size):
                batch = both_shared[i:i + batch_size]
                batch_data = []
                for rel in batch:
                    rel_data = rel.copy()
                    rel_data['relationship_type'] = 'both'
                    rel_data['shared_value'] = f"{rel.get('shared_device_id', '')},{rel.get('shared_ip', '')}"
                    batch_data.append(rel_data)
                
                if batch_data:
                    task_repository.batch_insert_shared_relationships(task_id, batch_data)
                    total_count += len(batch_data)
                    logger.debug(f"分批存储双重共享关系: {total_count}条")
            
            logger.info(f"存储成功，总共存储了{total_count}条关系记录")
            
            # 构建轻量级结果摘要
            result_summary = {
                'status': 'success',
                'summary': {
                    'device_shared_count': len(device_shared),
                    'ip_shared_count': len(ip_shared),
                    'both_shared_count': len(both_shared),
                    'total_relationships': total_count
                },
                'ranking_summary': {
                    'top_device_bds': bd_statistics['device'],
                    'top_ip_bds': bd_statistics['ip'],
                    'top_both_bds': bd_statistics['both']
                },
                'filename': task_data.get('filename', '未知文件'),
                'created_at': datetime.now().isoformat(),
                'task_type': 'agent_analysis',
                'total_members': len(df) if not df.empty else 0,
                'data_storage': 'optimized_table'
            }
            
            # 添加层级分析报告摘要（如果有）
            if level_analysis_report:
                result_summary['level_analysis_summary'] = {
                    'total_analyzed': level_analysis_report.get('total_analyzed', 0),
                    'bd_count': len(level_analysis_report.get('bd_statistics', {})),
                    'agent_count': level_analysis_report.get('agent_statistics', {}).get('total_agents', 0)
                }
            
        except Exception as storage_error:
            # 💥 存储失败：直接让任务失败，提醒用户重新处理
            error_msg = f"数据存储失败，请重新上传处理。错误详情: {str(storage_error)}"
            logger.error(f"存储失败: {error_msg}")
            task_repository.update_task_status(task_id, 'failed', 0, error_msg)
            raise Exception(error_msg)
        
        # 保存任务结果
        task_repository.update_task_result(task_id, result_summary)
        
        # ⭐ 新增：保存分析结果到agent_analysis表（供用户分析模块使用）
        try:
            from database.repositories.agent_repository import agent_repository
            
            # 构建完整的分析结果数据
            full_result_data = {
                'device_shared': device_shared,
                'ip_shared': ip_shared,
                'both_shared': both_shared,
                'bd_statistics': bd_statistics,
                'level_analysis_report': level_analysis_report if level_analysis_report else {}
            }
            
            # 保存到agent_analysis表
            save_success = agent_repository.save_analysis_result(
                task_id=task_id,
                analysis_type='agent_relationship',
                filename=task_data.get('filename', '未知文件'),
                total_users=len(df) if not df.empty else 0,
                device_shared_count=len(device_shared),
                ip_shared_count=len(ip_shared),
                both_shared_count=len(both_shared),
                result_data=full_result_data
            )
            
            if save_success:
                logger.info("成功保存分析结果到agent_analysis表")
            else:
                logger.error("保存到agent_analysis表失败")
                
        except Exception as save_error:
            logger.warning(f"保存到agent_analysis表异常: {str(save_error)}")
        
        # 完成任务
        task_repository.update_task_status(task_id, 'completed', 100, f"代理关系分析完成，处理了 {len(df)} 条记录")

        # 🆕 自动存储用户和关系数据到数据库（统一数据管理）
        try:
            from database.repositories.user_repository import user_repository
            
            logger.info("开始存储用户数据到数据库")
            
            # 提取用户数据
            users = []
            relationships = []
            
            for _, row in df.iterrows():
                digital_id = row.get('digital_id', '')
                if not digital_id:
                    continue
                
                # 构建用户数据
                user_data = {
                    'digital_id': digital_id,
                    'member_id': row.get('member_id', ''),
                    'user_name': row.get('user_name', ''),
                    'agent_flag': row.get('agent_flag', ''),
                    'analyzed_level_number': row.get('analyzed_level_number', 0),
                    'analyzed_level_type': row.get('analyzed_level_type', '未知'),
                    'bd_name': row.get('bd_name', ''),
                    'device_count': 1 if row.get('device_id') else 0,
                    'risk_score': row.get('risk_score', 0),
                    'relation_count': 0  # 后续可以计算
                }
                users.append(user_data)
                
                # 构建关系数据
                recommender_id = row.get('recommender_digital_id', '')
                if recommender_id and str(recommender_id) != 'nan' and recommender_id.strip():
                    relationship_data = {
                        'parent_digital_id': recommender_id,
                        'child_digital_id': digital_id,
                        'relationship_type': 'direct_agent',
                        'level_diff': 1
                    }
                    relationships.append(relationship_data)
            
            # 批量插入用户数据
            if users:
                user_repository.batch_insert_users(users)
                logger.info(f"成功存储{len(users)}个用户到数据库")
            
            # 批量插入关系数据
            if relationships:
                user_repository.batch_insert_relationships(relationships)
                logger.info(f"成功存储{len(relationships)}个关系到数据库")
            
        except Exception as db_error:
            logger.warning(f"存储到数据库失败: {str(db_error)}")
        
        # 清理临时文件
        try:
            os.unlink(file_path)
        except:
            pass
        
    except Exception as e:
        # 清理临时文件
        try:
            if 'file_path' in task_data:
                os.unlink(task_data['file_path'])
        except:
            pass
        
        # 更新任务状态为失败
        error_msg = f"代理关系分析失败: {str(e)}"
        task_repository.update_task_status(task_id, 'failed', 0, error_msg)
        raise
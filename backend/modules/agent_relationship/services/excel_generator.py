"""
Excel生成器模块
负责生成批量查询的Excel报告，包括颜色标注功能
"""

import logging
import tempfile
import pandas as pd
from io import BytesIO
from datetime import datetime

logger = logging.getLogger(__name__)

class ExcelGenerator:
    """Excel生成器类"""
    
    def __init__(self):
        self.logger = logger
    
    def generate_batch_query_excel(self, user_ids, device_results, ip_results, include_device, include_ip, output_format, color_coding='both'):
        """生成批量查询Excel文件"""
        try:
            # 生成Excel文件 - 按用户提供的ID顺序输出
            output = BytesIO()
            
            # 创建按用户ID顺序的汇总表
            summary_data = self._create_ordered_summary(user_ids, device_results, ip_results, include_device, include_ip)
            
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # 主要的汇总查询表
                if summary_data:
                    df_summary = pd.DataFrame(summary_data)
                    df_summary.to_excel(writer, sheet_name='汇总查询', index=False)
                    
                    # 添加颜色标注
                    if color_coding != 'none':
                        self._apply_color_coding(writer.book['汇总查询'], df_summary, user_ids, color_coding)
                
                if output_format == 'separate':
                    # 分别输出模式 - 额外的详细表
                    if device_results:
                        device_same_bd, device_diff_bd = self._categorize_relationships(device_results)
                        if device_same_bd:
                            df_device_same = pd.DataFrame(device_same_bd)
                            df_device_same.to_excel(writer, sheet_name='同BD设备共享', index=False)
                        if device_diff_bd:
                            df_device_diff = pd.DataFrame(device_diff_bd)
                            df_device_diff.to_excel(writer, sheet_name='跨BD设备共享', index=False)
                    
                    if ip_results:
                        ip_same_bd, ip_diff_bd = self._categorize_relationships(ip_results)
                        if ip_same_bd:
                            df_ip_same = pd.DataFrame(ip_same_bd)
                            df_ip_same.to_excel(writer, sheet_name='同BD IP共享', index=False)
                        if ip_diff_bd:
                            df_ip_diff = pd.DataFrame(ip_diff_bd)
                            df_ip_diff.to_excel(writer, sheet_name='跨BD IP共享', index=False)
                    
                    # 统计信息
                    stats_data = []
                    if device_results:
                        device_same_bd, device_diff_bd = self._categorize_relationships(device_results)
                        stats_data.extend([
                            {'数据类型': '同BD设备共享', '记录数': len(device_same_bd)},
                            {'数据类型': '跨BD设备共享', '记录数': len(device_diff_bd)}
                        ])
                    if ip_results:
                        ip_same_bd, ip_diff_bd = self._categorize_relationships(ip_results)
                        stats_data.extend([
                            {'数据类型': '同BD IP共享', '记录数': len(ip_same_bd)},
                            {'数据类型': '跨BD IP共享', '记录数': len(ip_diff_bd)}
                        ])
                    
                    if stats_data:
                        df_stats = pd.DataFrame(stats_data)
                        df_stats.to_excel(writer, sheet_name='统计信息', index=False)
            
            # 保存文件到临时目录
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
            temp_file.write(output.getvalue())
            temp_file.close()
            
            self.logger.info(f"Excel文件生成完成: {temp_file.name}")
            return temp_file.name
            
        except Exception as e:
            self.logger.error(f"生成Excel文件失败: {str(e)}")
            raise
    
    def _create_ordered_summary(self, user_ids, device_results, ip_results, include_device, include_ip):
        """创建按用户ID顺序的汇总表"""
        summary_data = []
        
        # 将关系数据按用户ID索引
        device_by_user = {}
        ip_by_user = {}
        
        if include_device and device_results:
            for rel in device_results:
                user_a = rel.get('user_a_mid', '')
                user_b = rel.get('user_b_mid', '')
                
                if user_a not in device_by_user:
                    device_by_user[user_a] = []
                if user_b not in device_by_user:
                    device_by_user[user_b] = []
                    
                device_by_user[user_a].append(rel)
                device_by_user[user_b].append(rel)
        
        if include_ip and ip_results:
            for rel in ip_results:
                user_a = rel.get('user_a_mid', '')
                user_b = rel.get('user_b_mid', '')
                
                if user_a not in ip_by_user:
                    ip_by_user[user_a] = []
                if user_b not in ip_by_user:
                    ip_by_user[user_b] = []
                    
                ip_by_user[user_a].append(rel)
                ip_by_user[user_b].append(rel)
        
        # 按用户提供的ID顺序生成汇总数据
        for user_id in user_ids:
            # 获取该用户的设备共享关系
            user_device_rels = device_by_user.get(user_id, [])
            # 获取该用户的IP共享关系  
            user_ip_rels = ip_by_user.get(user_id, [])
            
            # 收集同设备的其他用户ID
            same_device_ids = set()
            cross_device_ids = set()
            
            for rel in user_device_rels:
                other_user = rel.get('user_b_mid') if rel.get('user_a_mid') == user_id else rel.get('user_a_mid')
                if other_user and other_user != user_id:
                    if rel.get('same_bd', False):
                        same_device_ids.add(other_user)
                    else:
                        # 跨BD格式：BD名+用户ID
                        other_bd = rel.get('user_b_bd') if rel.get('user_a_mid') == user_id else rel.get('user_a_bd')
                        cross_device_ids.add(f"{other_bd or '未分配'}+{other_user}")
            
            # 收集同IP的其他用户ID
            same_ip_ids = set()
            cross_ip_ids = set()
            
            for rel in user_ip_rels:
                other_user = rel.get('user_b_mid') if rel.get('user_a_mid') == user_id else rel.get('user_a_mid')
                if other_user and other_user != user_id:
                    if rel.get('same_bd', False):
                        same_ip_ids.add(other_user)
                    else:
                        # 跨BD格式：BD名+用户ID
                        other_bd = rel.get('user_b_bd') if rel.get('user_a_mid') == user_id else rel.get('user_a_bd')
                        cross_ip_ids.add(f"{other_bd or '未分配'}+{other_user}")
            
            # 合并同BD和跨BD的ID
            all_device_ids = list(same_device_ids) + list(cross_device_ids)
            all_ip_ids = list(same_ip_ids) + list(cross_ip_ids)

            # 计算同时满足设备和IP共享的用户ID（取交集）
            # 需要从原始用户ID中提取（去除BD前缀）
            device_user_ids = set()
            for device_id in all_device_ids:
                # 提取用户ID（去除BD前缀）
                if '+' in device_id:
                    user_part = device_id.split('+')[-1]
                else:
                    user_part = device_id
                device_user_ids.add(user_part)

            ip_user_ids = set()
            for ip_id in all_ip_ids:
                # 提取用户ID（去除BD前缀）
                if '+' in ip_id:
                    user_part = ip_id.split('+')[-1]
                else:
                    user_part = ip_id
                ip_user_ids.add(user_part)

            # 找出同时在设备和IP关系中的用户ID
            both_user_ids = device_user_ids.intersection(ip_user_ids)

            # 为同时满足的用户ID构建显示格式（保持原有的BD前缀格式）
            both_display_ids = []
            for user_id_only in both_user_ids:
                # 从设备关系中找到对应的显示格式
                for device_id in all_device_ids:
                    if device_id.endswith(user_id_only) and ('+' not in device_id or device_id.split('+')[-1] == user_id_only):
                        both_display_ids.append(device_id)
                        break
                else:
                    # 如果在设备关系中没找到，从IP关系中找
                    for ip_id in all_ip_ids:
                        if ip_id.endswith(user_id_only) and ('+' not in ip_id or ip_id.split('+')[-1] == user_id_only):
                            both_display_ids.append(ip_id)
                            break

            # 添加到汇总数据
            summary_data.append({
                'bd': user_id,  # 使用用户ID作为bd列
                '同设备': ', '.join(sorted(all_device_ids)) if all_device_ids else '',
                '同IP': ', '.join(sorted(all_ip_ids)) if all_ip_ids else '',
                '同设备同IP': ', '.join(sorted(both_display_ids)) if both_display_ids else ''
            })
        
        return summary_data
    
    def _categorize_relationships(self, relationships):
        """将关系数据分类为同BD和跨BD"""
        same_bd = []
        diff_bd = []
        
        for rel in relationships:
            is_same_bd = rel.get('same_bd', False)
            shared_value = rel.get('shared_value', '')
            
            base_data = {
                '用户A_ID': rel.get('user_a_mid', ''),
                '用户A_名称': rel.get('user_a_name', ''),
                '用户B_ID': rel.get('user_b_mid', ''),
                '用户B_名称': rel.get('user_b_name', ''),
                '匹配次数': rel.get('match_count', 1),
                '用户A_最后活跃': rel.get('user_a_time', ''),
                '用户B_最后活跃': rel.get('user_b_time', '')
            }
            
            if is_same_bd:
                # 同BD：只显示共享值
                base_data['BD关系'] = '同BD'
                base_data['共享标识'] = shared_value
                same_bd.append(base_data)
            else:
                # 跨BD：显示BD+共享值
                user_a_bd = rel.get('user_a_bd', '') or '未分配'
                user_b_bd = rel.get('user_b_bd', '') or '未分配'
                base_data['BD关系'] = '跨BD'
                base_data['用户A_BD'] = user_a_bd
                base_data['用户B_BD'] = user_b_bd
                base_data['共享标识'] = f"{user_a_bd}+{user_b_bd}+{shared_value}"
                diff_bd.append(base_data)
        
        return same_bd, diff_bd

    def _apply_color_coding(self, worksheet, df_summary, user_ids, color_coding='both'):
        """为Excel工作表应用颜色标注，标识关系群体"""
        try:
            from openpyxl.styles import PatternFill

            # 预定义颜色列表（使用较浅的颜色便于阅读）
            colors = [
                'FFE6E6',  # 浅红色
                'E6F3FF',  # 浅蓝色
                'E6FFE6',  # 浅绿色
                'FFFFE6',  # 浅黄色
                'F0E6FF',  # 浅紫色
                'FFE6F0',  # 浅粉色
                'E6FFF0',  # 浅青色
                'FFF0E6',  # 浅橙色
                'F0F0F0',  # 浅灰色
                'E6E6FF',  # 浅靛色
                'FFE6CC',  # 浅桃色
                'CCFFE6',  # 浅薄荷色
                'E6CCFF',  # 浅薰衣草色
                'FFCCCC',  # 浅珊瑚色
                'CCFFFF',  # 浅天蓝色
            ]

            # 分析关系群体
            groups = self._find_relationship_groups(df_summary, user_ids, color_coding)

            self.logger.info(f"发现 {len(groups)} 个关系群体")

            # 为每个群体分配颜色
            group_colors = {}
            for i, group in enumerate(groups):
                if len(group) > 1:  # 只为有多个成员的群体分配颜色
                    color = colors[i % len(colors)]
                    group_colors[i] = color
                    self.logger.info(f"群体 {i+1}: {len(group)} 个用户，颜色: {color}")

            # 应用颜色到Excel单元格
            for row_idx, user_id in enumerate(user_ids, start=2):  # 从第2行开始（第1行是标题）
                # 找到该用户所属的群体
                user_group = None
                for group_idx, group in enumerate(groups):
                    if user_id in group:
                        user_group = group_idx
                        break

                # 如果用户属于某个群体且群体有颜色，则应用颜色
                if user_group is not None and user_group in group_colors:
                    color = group_colors[user_group]
                    fill = PatternFill(start_color=color, end_color=color, fill_type='solid')

                    # 为整行应用颜色
                    for col in range(1, 5):  # A, B, C, D列
                        cell = worksheet.cell(row=row_idx, column=col)
                        cell.fill = fill

            # 在工作表中添加颜色说明
            self._add_color_legend(worksheet, groups, group_colors)

        except Exception as e:
            self.logger.error(f"应用颜色标注失败: {str(e)}")

    def _find_relationship_groups(self, df_summary, user_ids, color_coding='both'):
        """分析用户关系，找出关系群体"""
        try:
            import pandas as pd

            # 构建关系图
            relationships = {}

            for idx, row in df_summary.iterrows():
                user_id = row['bd']
                device_relations = str(row['同设备']) if pd.notna(row['同设备']) else ''
                ip_relations = str(row['同IP']) if pd.notna(row['同IP']) else ''
                both_relations = str(row['同设备同IP']) if pd.notna(row['同设备同IP']) else ''

                # 根据颜色标注模式选择关系类型
                if color_coding == 'device':
                    # 只基于设备关系
                    target_relations = device_relations
                elif color_coding == 'ip':
                    # 只基于IP关系
                    target_relations = ip_relations
                elif color_coding == 'both':
                    # 基于同时满足设备和IP的关系
                    target_relations = both_relations
                else:
                    # 默认使用设备+IP的并集
                    target_relations = device_relations + ', ' + ip_relations if device_relations and ip_relations else (device_relations or ip_relations)

                # 解析目标关系
                related_users = set()
                if target_relations:
                    for relation in target_relations.split(', '):
                        relation = relation.strip()
                        if relation:
                            # 提取用户ID（去除BD名前缀）
                            if '+' in relation:
                                user_part = relation.split('+')[-1]
                            else:
                                user_part = relation
                            related_users.add(user_part)

                relationships[user_id] = related_users

                self.logger.debug(f"用户 {user_id} ({color_coding}模式): 关系 {len(related_users)} 个")

            # 构建双向关系图（确保关系是对称的）
            bidirectional_relationships = {}
            for user_id in user_ids:
                bidirectional_relationships[user_id] = set()

            # 添加双向关系
            for user_id, related_users in relationships.items():
                if user_id in user_ids:
                    for related_user in related_users:
                        if related_user in user_ids:  # 只考虑输入列表中的用户
                            # 添加双向关系
                            bidirectional_relationships[user_id].add(related_user)
                            bidirectional_relationships[related_user].add(user_id)

            # 使用DFS算法找出连通分量（关系群体）
            groups = []
            visited = set()

            def dfs(user, current_group):
                if user in visited:
                    return
                visited.add(user)
                current_group.add(user)

                # 遍历该用户的所有双向关系
                for related_user in bidirectional_relationships.get(user, set()):
                    if related_user not in visited:
                        dfs(related_user, current_group)

            # 对每个用户进行DFS
            for user_id in user_ids:
                if user_id not in visited:
                    current_group = set()
                    dfs(user_id, current_group)
                    if current_group:
                        groups.append(current_group)

            # 记录群体分析结果
            self.logger.info(f"关系分析完成: 总用户 {len(user_ids)}, 发现群体 {len(groups)}")
            for i, group in enumerate(groups):
                if len(group) > 1:
                    self.logger.info(f"群体 {i+1}: {len(group)} 个用户 - {sorted(list(group))[:5]}{'...' if len(group) > 5 else ''}")

            # 按群体大小排序（大群体优先）
            groups.sort(key=len, reverse=True)

            return groups

        except Exception as e:
            self.logger.error(f"分析关系群体失败: {str(e)}")
            return []

    def _add_color_legend(self, worksheet, groups, group_colors):
        """在工作表中添加颜色说明"""
        try:
            from openpyxl.styles import PatternFill, Font, Alignment

            # 在右侧添加图例
            legend_start_col = 5  # E列开始
            legend_start_row = 2

            # 添加图例标题
            title_cell = worksheet.cell(row=legend_start_row, column=legend_start_col)
            title_cell.value = "关系群体图例"
            title_cell.font = Font(bold=True, size=12)
            title_cell.alignment = Alignment(horizontal='center')

            # 添加每个群体的说明
            row = legend_start_row + 2
            for group_idx, group in enumerate(groups):
                if len(group) > 1 and group_idx in group_colors:
                    color = group_colors[group_idx]
                    fill = PatternFill(start_color=color, end_color=color, fill_type='solid')

                    # 颜色示例单元格
                    color_cell = worksheet.cell(row=row, column=legend_start_col)
                    color_cell.fill = fill
                    color_cell.value = f"群体{group_idx + 1}"
                    color_cell.alignment = Alignment(horizontal='center')

                    # 群体成员说明
                    members_cell = worksheet.cell(row=row, column=legend_start_col + 1)
                    members_text = f"{len(group)}个用户: {', '.join(sorted(list(group))[:5])}"
                    if len(group) > 5:
                        members_text += "..."
                    members_cell.value = members_text

                    row += 1

            # 调整列宽
            worksheet.column_dimensions['E'].width = 15
            worksheet.column_dimensions['F'].width = 30

        except Exception as e:
            self.logger.error(f"添加颜色说明失败: {str(e)}")

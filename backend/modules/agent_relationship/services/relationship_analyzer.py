"""
数据分析模块 - 提供数据统计和分析功能
"""

# 简单的缓存装饰器
def memoize(func):
    cache = {}
    def wrapper(*args, **kwargs):
        key = str(args) + str(sorted(kwargs.items()))
        if key not in cache:
            cache[key] = func(*args, **kwargs)
        return cache[key]
    return wrapper

@memoize
def analyze_bd_statistics(device_shared, ip_shared, both_shared):
    """
    分析每个BD团队下共享关系的统计数据
    
    参数:
        device_shared (list): 共享设备关系列表
        ip_shared (list): 共享IP关系列表
        both_shared (list): 同时共享设备和IP的关系列表
        
    返回:
        dict: BD团队统计数据
    """
    # 初始化结果字典
    bd_stats = {
        'device': {},  # 设备共享统计
        'ip': {},      # IP共享统计
        'both': {}     # 两者都共享统计
    }
    
    # 性能优化：使用字典推导式和集合操作
    
    # 分析共享设备
    if device_shared:
        # 预处理：按BD团队分组
        device_by_bd = {}
        for item in device_shared:
            bd_name = item.get('user_a_bd', '') or '未分配'
            user_pair = tuple(sorted([item.get('user_a_mid', ''), item.get('user_b_mid', '')]))
            
            if bd_name not in device_by_bd:
                device_by_bd[bd_name] = set()
            
            device_by_bd[bd_name].add(user_pair)
        
        # 转换为结果格式
        bd_stats['device'] = {bd: len(pairs) for bd, pairs in device_by_bd.items()}
            
    # 分析共享IP
    if ip_shared:
        # 预处理：按BD团队分组
        ip_by_bd = {}
        for item in ip_shared:
            bd_name = item.get('user_a_bd', '') or '未分配'
            user_pair = tuple(sorted([item.get('user_a_mid', ''), item.get('user_b_mid', '')]))
            
            if bd_name not in ip_by_bd:
                ip_by_bd[bd_name] = set()
            
            ip_by_bd[bd_name].add(user_pair)
        
        # 转换为结果格式
        bd_stats['ip'] = {bd: len(pairs) for bd, pairs in ip_by_bd.items()}
    
    # 分析两者都共享
    if both_shared:
        # 预处理：按BD团队分组
        both_by_bd = {}
        for item in both_shared:
            bd_name = item.get('user_a_bd', '') or '未分配'
            user_pair = tuple(sorted([item.get('user_a_mid', ''), item.get('user_b_mid', '')]))
            
            if bd_name not in both_by_bd:
                both_by_bd[bd_name] = set()
            
            both_by_bd[bd_name].add(user_pair)
        
        # 转换为结果格式
        bd_stats['both'] = {bd: len(pairs) for bd, pairs in both_by_bd.items()}
    
    # 转换为排序后的列表结果
    result = {
        'device': [],
        'ip': [],
        'both': []
    }
    
    # 设备共享排名
    for bd_name, count in bd_stats['device'].items():
        result['device'].append({
            'bd_name': bd_name,
            'count': count
        })
    result['device'] = sorted(result['device'], key=lambda x: x['count'], reverse=True)
    
    # IP共享排名
    for bd_name, count in bd_stats['ip'].items():
        result['ip'].append({
            'bd_name': bd_name,
            'count': count
        })
    result['ip'] = sorted(result['ip'], key=lambda x: x['count'], reverse=True)
    
    # 两者都共享排名
    for bd_name, count in bd_stats['both'].items():
        result['both'].append({
            'bd_name': bd_name,
            'count': count
        })
    result['both'] = sorted(result['both'], key=lambda x: x['count'], reverse=True)
    
    return result 
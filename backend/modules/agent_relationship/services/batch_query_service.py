"""
批量查询服务模块
负责处理用户批量查询请求，生成Excel报告
"""

import logging
import uuid
import threading
import tempfile
import pandas as pd
from io import BytesIO
from datetime import datetime

logger = logging.getLogger(__name__)

class BatchQueryService:
    """批量查询服务类"""
    
    def __init__(self):
        self.logger = logger
    
    def create_batch_query_task(self, user_ids, include_device=True, include_ip=True, output_format='separate', color_coding='both'):
        """创建批量查询任务"""
        try:
            from database.repositories.task_repository import task_repository
            
            if not user_ids:
                raise ValueError('用户ID列表不能为空')
            
            if len(user_ids) > 5000:
                raise ValueError('用户ID数量不能超过5000个')
            
            if not include_device and not include_ip:
                raise ValueError('请至少选择一种查询类型')
            
            self.logger.info(f"开始批量查询 {len(user_ids)} 个用户的关系数据")
            
            # 创建批量查询任务
            batch_task_id = str(uuid.uuid4())
            task_repository.create_task(batch_task_id, 'batch_query', f'批量查询_{len(user_ids)}个用户')
            
            # 启动异步处理
            thread = threading.Thread(target=self._process_batch_query_task, args=(batch_task_id, {
                'user_ids': user_ids,
                'include_device': include_device,
                'include_ip': include_ip,
                'output_format': output_format,
                'color_coding': color_coding
            }))
            thread.start()
            
            return {
                'status': 'success',
                'task_id': batch_task_id,
                'message': f'批量查询任务已创建，正在处理 {len(user_ids)} 个用户'
            }
            
        except Exception as e:
            self.logger.error(f"创建批量查询任务失败: {str(e)}")
            raise
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        try:
            from database.repositories.task_repository import task_repository
            
            task = task_repository.get_task(task_id)
            if not task:
                raise ValueError('任务不存在')
            
            status = task.get('status', 'pending')
            
            if status == 'completed':
                # 构建下载URL
                download_url = f'/api/agent/batch-query-download/{task_id}'
                return {
                    'status': 'completed',
                    'download_url': download_url,
                    'message': '批量查询完成'
                }
            elif status == 'failed':
                return {
                    'status': 'failed',
                    'message': task.get('error_message', '查询失败')
                }
            else:
                return {
                    'status': 'processing',
                    'message': task.get('progress_message', '正在处理中...')
                }
                
        except Exception as e:
            self.logger.error(f"检查批量查询状态失败: {str(e)}")
            raise
    
    def get_download_file(self, task_id):
        """获取下载文件路径"""
        try:
            from database.repositories.task_repository import task_repository
            
            task = task_repository.get_task(task_id)
            if not task:
                raise ValueError('任务不存在')
            
            if task.get('status') != 'completed':
                raise ValueError('任务尚未完成')
            
            # 获取结果文件路径
            result_summary = task.get('result_summary', {})
            if isinstance(result_summary, str):
                import json
                result_summary = json.loads(result_summary)
            
            file_path = result_summary.get('file_path')
            
            if not file_path:
                raise ValueError('结果文件路径不存在')
            
            import os
            if not os.path.exists(file_path):
                raise ValueError('结果文件不存在')
            
            return file_path
            
        except Exception as e:
            self.logger.error(f"获取下载文件失败: {str(e)}")
            raise
    
    def _process_batch_query_task(self, task_id, task_data):
        """处理批量查询任务"""
        try:
            from database.repositories.task_repository import task_repository
            
            # 更新任务状态
            task_repository.update_task_status(task_id, 'processing', 0, '正在处理批量查询...')
            
            user_ids = task_data['user_ids']
            include_device = task_data['include_device']
            include_ip = task_data['include_ip']
            output_format = task_data['output_format']
            color_coding = task_data.get('color_coding', 'both')
            
            self.logger.info(f"开始处理批量查询任务 {task_id}，查询 {len(user_ids)} 个用户")
            
            # 查询用户关系数据
            device_results = []
            ip_results = []
            
            if include_device:
                task_repository.update_task_status(task_id, 'processing', 25, '正在查询设备共享关系...')
                device_results = self._query_user_relationships(user_ids, 'device')
            
            if include_ip:
                task_repository.update_task_status(task_id, 'processing', 50, '正在查询IP共享关系...')
                ip_results = self._query_user_relationships(user_ids, 'ip')
            
            task_repository.update_task_status(task_id, 'processing', 75, '正在生成Excel文件...')
            
            # 生成Excel文件
            file_path = self._generate_excel_file(user_ids, device_results, ip_results, include_device, include_ip, output_format, color_coding)
            
            # 更新任务状态为完成
            task_repository.update_task_status(
                task_id, 
                'completed', 
                100, 
                f'批量查询完成，共查询到 {len(device_results) + len(ip_results)} 条关系'
            )
            
            # 单独更新结果摘要
            task_repository.update_task_result_summary(task_id, {
                'file_path': file_path,
                'device_count': len(device_results),
                'ip_count': len(ip_results),
                'total_users': len(user_ids)
            })
            
            self.logger.info(f"批量查询任务 {task_id} 完成")
            
        except Exception as e:
            self.logger.error(f"批量查询任务 {task_id} 失败: {str(e)}")
            from database.repositories.task_repository import task_repository
            task_repository.update_task_status(task_id, 'failed', 0, f'批量查询失败: {str(e)}')
    
    def _query_user_relationships(self, user_ids, relationship_type):
        """查询用户关系数据"""
        try:
            # 构建查询条件 - 使用参数化查询避免SQL注入
            placeholders = ', '.join(['?' for _ in user_ids])
            sql = f"""
            SELECT DISTINCT
                user_a_mid, user_a_name, user_a_bd, user_a_level, user_a_time,
                user_b_mid, user_b_name, user_b_bd, user_b_level, user_b_time,
                shared_value, same_bd, match_count
            FROM shared_relationships 
            WHERE relationship_type = ?
            AND (user_a_mid IN ({placeholders}) OR user_b_mid IN ({placeholders}))
            ORDER BY user_a_mid, user_b_mid
            """
            
            # 构建参数列表：relationship_type + user_ids + user_ids
            params = [relationship_type] + user_ids + user_ids
            
            self.logger.debug(f"执行SQL查询: {sql}")
            self.logger.debug(f"参数: {params[:10]}...")  # 只显示前10个参数避免日志过长
            
            from database.duckdb_manager import DuckDBManager
            db_manager = DuckDBManager()
            results = db_manager.execute_sql(sql, params)
            
            self.logger.info(f"查询到 {len(results)} 条 {relationship_type} 关系数据")
            return results
            
        except Exception as e:
            self.logger.error(f"查询用户关系失败: {str(e)}")
            return []
    
    def _generate_excel_file(self, user_ids, device_results, ip_results, include_device, include_ip, output_format, color_coding='both'):
        """生成Excel文件"""
        try:
            from .excel_generator import ExcelGenerator

            generator = ExcelGenerator()
            return generator.generate_batch_query_excel(
                user_ids, device_results, ip_results,
                include_device, include_ip, output_format, color_coding
            )
            
        except Exception as e:
            self.logger.error(f"生成Excel文件失败: {str(e)}")
            raise

# 创建服务实例
batch_query_service = BatchQueryService()

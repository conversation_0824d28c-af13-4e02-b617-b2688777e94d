#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户数据仓库模块
提供用户数据查询、缓存等功能
"""

import logging
from typing import Dict, Any, Optional, List
import pandas as pd
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class UserDataRepository:
    """用户数据仓库，用于查询用户信息"""
    
    def __init__(self, user_database: Optional[Dict[str, Any]] = None, 
                 enable_cache: bool = True, cache_ttl: int = 3600):
        """
        初始化用户数据仓库
        
        Args:
            user_database: 用户数据库或数据源
            enable_cache: 是否启用缓存
            cache_ttl: 缓存生存时间（秒）
        """
        # 避免DataFrame的布尔判断问题
        if user_database is None:
            self.user_database = {}
        else:
            self.user_database = user_database
        self.enable_cache = enable_cache
        self.cache_ttl = cache_ttl
        self._cache = {}
        self._cache_timestamps = {}
        self.logger = logger
        
        # 支持从DataFrame构建数据库
        if isinstance(user_database, pd.DataFrame):
            self._build_from_dataframe(user_database)
    
    def _build_from_dataframe(self, df: pd.DataFrame):
        """从DataFrame构建用户数据库"""
        try:
            self.user_database = {}
            
            # 确保必要的ID列存在
            id_columns = ['digital_id', 'member_id']
            for col in id_columns:
                if col in df.columns:
                    df[col] = df[col].astype(str)
            
            # 以digital_id为主键构建数据库
            for _, row in df.iterrows():
                user_data = row.to_dict()
                
                # 使用digital_id作为主键
                digital_id = user_data.get('digital_id')
                if digital_id and str(digital_id).strip():
                    self.user_database[str(digital_id)] = user_data
                
                # 如果有member_id且与digital_id不同，也建立映射
                member_id = user_data.get('member_id')
                if (member_id and str(member_id).strip() and 
                    str(member_id) != str(digital_id)):
                    self.user_database[str(member_id)] = user_data
                    
            self.logger.info(f"从DataFrame构建用户数据库，共{len(self.user_database)}条记录")
            
        except Exception as e:
            self.logger.error(f"从DataFrame构建用户数据库失败: {e}")
            self.user_database = {}
    
    def get_user_by_digital_id(self, digital_id: str) -> Optional[Dict[str, Any]]:
        """
        通过数字ID获取用户信息
        
        Args:
            digital_id: 用户数字ID
            
        Returns:
            dict: 用户信息字典，如果不存在则返回None
        """
        if not digital_id or not str(digital_id).strip():
            return None
        
        digital_id = str(digital_id).strip()
        
        # 检查缓存
        if self.enable_cache:
            cached_result = self._get_from_cache(f"digital_{digital_id}")
            if cached_result is not None:
                return cached_result
        
        try:
            user_data = self.user_database.get(digital_id)
            
            # 缓存结果
            if self.enable_cache:
                self._set_cache(f"digital_{digital_id}", user_data)
            
            return user_data
            
        except Exception as e:
            self.logger.error(f"查询用户数据失败 {digital_id}: {e}")
            return None
    
    def get_user_by_member_id(self, member_id: str) -> Optional[Dict[str, Any]]:
        """
        根据会员ID获取用户数据（用于兼容性）
        
        参数:
            member_id: 用户会员ID
            
        返回:
            dict: 用户数据字典，如果不存在则返回None
        """
        if not member_id or not str(member_id).strip():
            return None
        
        member_id = str(member_id).strip()
        
        # 检查缓存
        if self.enable_cache:
            cached_result = self._get_from_cache(f"member_{member_id}")
            if cached_result is not None:
                return cached_result
        
        try:
            # 首先尝试直接查找
            user_data = self.user_database.get(member_id)
            
            # 如果没找到，遍历查找匹配的member_id
            if not user_data:
                for user_info in self.user_database.values():
                    if user_info.get('member_id') == member_id:
                        user_data = user_info
                        break
            
            # 缓存结果
            if self.enable_cache:
                self._set_cache(f"member_{member_id}", user_data)
            
            return user_data
            
        except Exception as e:
            self.logger.error(f"查询用户数据失败 {member_id}: {e}")
            return None
    
    def batch_get_users(self, user_ids: List[str], 
                       id_type: str = 'digital_id') -> Dict[str, Dict[str, Any]]:
        """
        批量获取用户数据
        
        Args:
            user_ids: 用户ID列表
            id_type: ID类型，'digital_id' 或 'member_id'
            
        Returns:
            dict: {user_id: user_data} 映射
        """
        results = {}
        
        for user_id in user_ids:
            if id_type == 'digital_id':
                user_data = self.get_user_by_digital_id(user_id)
            else:
                user_data = self.get_user_by_member_id(user_id)
            
            if user_data:
                results[user_id] = user_data
        
        return results
    
    def get_users_by_bd(self, bd_name: str) -> List[Dict[str, Any]]:
        """
        获取指定BD下的所有用户
        
        Args:
            bd_name: BD团队名称
            
        Returns:
            list: 用户数据列表
        """
        try:
            results = []
            for user_data in self.user_database.values():
                user_bd = (user_data.get('bd_name') or 
                          user_data.get('top_kol_bd_name') or '')
                if user_bd == bd_name:
                    results.append(user_data)
            return results
        except Exception as e:
            self.logger.error(f"查询BD用户失败 {bd_name}: {e}")
            return []
    
    def get_users_by_level(self, level: int) -> List[Dict[str, Any]]:
        """
        获取指定层级的所有用户
        
        Args:
            level: 用户层级
            
        Returns:
            list: 用户数据列表
        """
        try:
            results = []
            for user_data in self.user_database.values():
                user_level = user_data.get('user_agent_level', 0)
                if user_level == level:
                    results.append(user_data)
            return results
        except Exception as e:
            self.logger.error(f"查询层级用户失败 {level}: {e}")
            return []
    
    def _get_from_cache(self, key: str) -> Optional[Dict[str, Any]]:
        """从缓存获取数据"""
        if key not in self._cache:
            return None
        
        # 检查是否过期
        timestamp = self._cache_timestamps.get(key)
        if timestamp and (datetime.now() - timestamp).seconds > self.cache_ttl:
            del self._cache[key]
            del self._cache_timestamps[key]
            return None
        
        return self._cache[key]
    
    def _set_cache(self, key: str, value: Optional[Dict[str, Any]]):
        """设置缓存"""
        self._cache[key] = value
        self._cache_timestamps[key] = datetime.now()
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self._cache_timestamps.clear()
        self.logger.info("用户数据缓存已清空")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取数据仓库统计信息"""
        return {
            'total_users': len(self.user_database),
            'cache_size': len(self._cache) if self.enable_cache else 0,
            'cache_enabled': self.enable_cache,
            'cache_ttl': self.cache_ttl
        }
    
    def update_user_data(self, user_id: str, user_data: Dict[str, Any]):
        """更新用户数据"""
        try:
            self.user_database[user_id] = user_data
            
            # 清除相关缓存
            if self.enable_cache:
                cache_keys_to_remove = [
                    key for key in self._cache.keys() 
                    if user_id in key
                ]
                for key in cache_keys_to_remove:
                    if key in self._cache:
                        del self._cache[key]
                    if key in self._cache_timestamps:
                        del self._cache_timestamps[key]
                        
            self.logger.info(f"更新用户数据: {user_id}")
            
        except Exception as e:
            self.logger.error(f"更新用户数据失败 {user_id}: {e}")
    
    def add_users_from_dataframe(self, df: pd.DataFrame):
        """从DataFrame添加用户数据"""
        try:
            added_count = 0
            
            # 确保必要的ID列存在
            id_columns = ['digital_id', 'member_id']
            for col in id_columns:
                if col in df.columns:
                    df[col] = df[col].astype(str)
            
            for _, row in df.iterrows():
                user_data = row.to_dict()
                digital_id = user_data.get('digital_id')
                
                if digital_id and str(digital_id).strip():
                    self.user_database[str(digital_id)] = user_data
                    added_count += 1
            
            # 清空缓存
            if self.enable_cache:
                self.clear_cache()
            
            self.logger.info(f"从DataFrame添加{added_count}条用户数据")
            
        except Exception as e:
            self.logger.error(f"从DataFrame添加用户数据失败: {e}")


# 工厂函数
def create_repository_from_dataframe(df: pd.DataFrame, **kwargs) -> UserDataRepository:
    """
    从DataFrame创建用户数据仓库的工厂函数
    
    Args:
        df: 包含用户数据的DataFrame
        **kwargs: 其他参数传递给UserDataRepository
        
    Returns:
        UserDataRepository: 用户数据仓库实例
    """
    return UserDataRepository(user_database=df, **kwargs)


def create_mock_repository() -> UserDataRepository:
    """
    创建测试用的模拟数据仓库
    
    Returns:
        UserDataRepository: 包含测试数据的仓库实例
    """
    mock_data = {
        "1001": {
            "digital_id": "1001",
            "member_id": "1001",
            "user_agent_level": 1,
            "recommender_digital_id": None,
            "top_kol_digital_id": None,
            "agent_flag": "代理",
            "bd_name": "BD-A"
        },
        "2001": {
            "digital_id": "2001",
            "member_id": "2001",
            "user_agent_level": 2,
            "recommender_digital_id": "1001",
            "top_kol_digital_id": "1001",
            "agent_flag": "代理",
            "bd_name": "BD-A"
        },
        "3001": {
            "digital_id": "3001",
            "member_id": "3001",
            "user_agent_level": 3,
            "recommender_digital_id": "2001",
            "top_kol_digital_id": "1001",
            "agent_flag": "代理",
            "bd_name": "BD-A"
        },
        "5001": {
            "digital_id": "5001",
            "member_id": "5001",
            "user_agent_level": 0,
            "recommender_digital_id": None,
            "top_kol_digital_id": None,
            "agent_flag": "直客",
            "bd_name": "BD-B"
        }
    }
    
    return UserDataRepository(mock_data) 
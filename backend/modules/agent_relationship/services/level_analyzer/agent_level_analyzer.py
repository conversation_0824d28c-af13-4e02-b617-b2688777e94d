#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
代理层级关系判断模块
根据用户数据判断代理层级关系
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class AgentLevelAnalyzer:
    """代理层级关系分析器"""
    
    def __init__(self, user_data_repository=None):
        """
        初始化代理层级分析器
        
        Args:
            user_data_repository: 用户数据仓库，用于查询用户信息
        """
        self.user_data_repository = user_data_repository
        self.logger = logger
    
    def analyze(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析用户代理层级
        
        Args:
            user_data: 包含用户信息的字典，至少包含以下字段：
                - user_agent_level: 代理等级
                - recommender_digital_id: 推荐人数字ID
                - top_kol_digital_id: 顶级KOL数字ID
                - agent_flag: 代理标识，表示是否为直客
                - digital_id: 用户自身的数字ID
                - member_id: 用户会员ID
        
        Returns:
            dict: 包含以下字段的字典：
                - level_type: 层级类型（'BD', '1级代理', '2级代理', '3级代理', '纯直客', '1级直客代理', '2级直客代理', '3级直客代理'）
                - level_number: 层级数字（1-4表示BD到3级代理，11-14表示纯直客到3级直客代理）
                - description: 层级描述
                - analysis_method: 分析方法说明
        """
        try:
            # 检查是否为直客
            if user_data.get('agent_flag') == '直客':
                return self._analyze_direct_customer(user_data)
            
            # 判断BD级别 (1级)
            try:
                user_level = user_data.get('user_agent_level')
                if user_level is not None:
                    level_num = int(user_level) if user_level != '' else 0
                    if level_num == 1:
                        return {
                            'level_type': 'BD',
                            'level_number': 1,
                            'description': 'BD级别 (1级)',
                            'analysis_method': 'user_agent_level判断'
                        }
            except (ValueError, TypeError):
                pass
            
            # 判断1级代理 (2级)
            try:
                user_level = user_data.get('user_agent_level')
                if user_level is not None:
                    level_num = int(user_level) if user_level != '' else 0
                    if level_num == 2:
                        return {
                            'level_type': '1级代理',
                            'level_number': 2,
                            'description': '1级代理 (2级) - 通过user_agent_level判断',
                            'analysis_method': 'user_agent_level判断'
                        }
            except (ValueError, TypeError):
                pass
            
            recommender_id = user_data.get('recommender_digital_id')
            top_kol_id = user_data.get('top_kol_digital_id')
            
            # 判断是否为1级代理 (通过推荐关系)
            if (recommender_id and top_kol_id and 
                recommender_id == top_kol_id):
                return {
                    'level_type': '1级代理',
                    'level_number': 2,
                    'description': '1级代理 (2级) - 推荐人ID等于顶级KOL ID',
                    'analysis_method': '推荐关系判断'
                }
            
            # 判断2级代理 (3级)
            try:
                user_level = user_data.get('user_agent_level')
                if user_level is not None:
                    level_num = int(user_level) if user_level != '' else 0
                    if level_num == 3:
                        return {
                            'level_type': '2级代理',
                            'level_number': 3,
                            'description': '2级代理 (3级) - 通过user_agent_level判断',
                            'analysis_method': 'user_agent_level判断'
                        }
            except (ValueError, TypeError):
                pass
            
            # 判断是否为2级代理 (通过推荐关系链)
            if (recommender_id and top_kol_id and 
                recommender_id != top_kol_id and 
                self._is_recommender_first_level_agent(recommender_id, top_kol_id)):
                return {
                    'level_type': '2级代理',
                    'level_number': 3,
                    'description': '2级代理 (3级) - 推荐人为1级代理',
                    'analysis_method': '推荐关系链判断'
                }
            
            # 判断3级代理 (4级)
            try:
                user_level = user_data.get('user_agent_level', 0)
                level_num = int(user_level) if user_level != '' else 0
                if 4 <= level_num <= 9:
                    return {
                        'level_type': '3级代理',
                        'level_number': 4,
                        'description': '3级代理 (4级) - 通过user_agent_level判断',
                        'analysis_method': 'user_agent_level判断'
                    }
            except (ValueError, TypeError):
                # 如果无法转换为数字，跳过这个判断
                pass
            
            # 判断是否为3级代理 (通过排除法)
            if recommender_id and top_kol_id and recommender_id != top_kol_id:
                return {
                    'level_type': '3级代理',
                    'level_number': 4,
                    'description': '3级代理 (4级) - 不满足1级和2级代理条件',
                    'analysis_method': '排除法判断'
                }
            
            # 默认返回
            return {
                'level_type': '未知',
                'level_number': 0,
                'description': '无法确定层级关系',
                'analysis_method': '默认值'
            }
            
        except Exception as e:
            self.logger.error(f"层级分析失败: {e}")
            return {
                'level_type': '分析失败',
                'level_number': 0,
                'description': f'层级分析异常: {str(e)}',
                'analysis_method': '异常处理'
            }
    
    def _analyze_direct_customer(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析直客的层级"""
        try:
            recommender_id = user_data.get('recommender_digital_id')
            top_kol_id = user_data.get('top_kol_digital_id')
            
            # 纯直客
            if not recommender_id and not top_kol_id:
                return {
                    'level_type': '纯直客',
                    'level_number': 11,
                    'description': '纯直客 - 无推荐人和顶级KOL',
                    'analysis_method': '直客分析'
                }
            
            # 1级直客代理
            if not recommender_id and top_kol_id:
                return {
                    'level_type': '1级直客代理',
                    'level_number': 12,
                    'description': '1级直客代理 - 无推荐人但有顶级KOL',
                    'analysis_method': '直客分析'
                }
            
            # 根据推荐人的代理级别来确定直客代理级别
            if recommender_id and not top_kol_id:
                # 获取推荐人信息
                if not self.user_data_repository:
                    return {
                        'level_type': '1级直客代理',
                        'level_number': 12,
                        'description': '1级直客代理 (默认) - 无法查询推荐人信息',
                        'analysis_method': '直客分析-默认'
                    }
                
                recommender_data = self.user_data_repository.get_user_by_digital_id(recommender_id)
                if not recommender_data:
                    return {
                        'level_type': '1级直客代理',
                        'level_number': 12,
                        'description': '1级直客代理 (默认) - 推荐人信息不存在',
                        'analysis_method': '直客分析-默认'
                    }
                
                # 根据推荐人的user_agent_level判断直客代理级别
                recommender_level = recommender_data.get('user_agent_level', 0)
                
                try:
                    # 确保类型安全的比较
                    level_num = int(recommender_level) if recommender_level != '' else 0
                    if level_num == 1:  # BD推荐
                        level = 1
                    elif level_num == 2:  # 1级代理推荐
                        level = 2
                    elif level_num == 3:  # 2级代理推荐
                        level = 3
                    else:  # 其他情况
                        level = 3  # 最高为3级直客代理
                except (ValueError, TypeError):
                    # 如果无法转换为数字，使用默认值
                    level = 1
                
                level_type = f'{level}级直客代理'
                level_number = 11 + level
                
                return {
                    'level_type': level_type,
                    'level_number': level_number,
                    'description': f'{level}级直客代理 - 根据推荐人级别判断',
                    'analysis_method': '直客分析-推荐人'
                }
            
            # 默认为纯直客
            return {
                'level_type': '纯直客',
                'level_number': 11,
                'description': '纯直客 (默认) - 不符合其他直客类型条件',
                'analysis_method': '直客分析-默认'
            }
            
        except Exception as e:
            self.logger.error(f"直客分析失败: {e}")
            return {
                'level_type': '直客分析失败',
                'level_number': 11,
                'description': f'直客分析异常: {str(e)}',
                'analysis_method': '异常处理'
            }
    
    def _is_recommender_first_level_agent(self, recommender_id: str, top_kol_id: str) -> bool:
        """
        判断推荐人是否为1级代理
        
        Args:
            recommender_id: 推荐人ID
            top_kol_id: 顶级KOL ID
            
        Returns:
            bool: 如果推荐人是1级代理则返回True，否则返回False
        """
        if not self.user_data_repository:
            return False
        
        try:
            # 查询推荐人信息
            recommender_data = self.user_data_repository.get_user_by_digital_id(recommender_id)
            if not recommender_data:
                return False
            
            # 判断推荐人是否为1级代理
            recommender_recommender_id = recommender_data.get('recommender_digital_id')
            recommender_top_kol_id = recommender_data.get('top_kol_digital_id')
            
            return (recommender_recommender_id and recommender_top_kol_id and 
                    recommender_recommender_id == recommender_top_kol_id)
        except Exception as e:
            self.logger.error(f"推荐人层级查询失败: {e}")
            return False
    
    def get_level_categories(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有层级类别定义
        
        Returns:
            dict: 层级类别映射
        """
        return {
            # 代理体系 (1-4)
            'agent_system': {
                1: {'type': 'BD', 'name': 'BD'},
                2: {'type': '1级代理', 'name': '1级代理'},
                3: {'type': '2级代理', 'name': '2级代理'},
                4: {'type': '3级代理', 'name': '3级代理'},
            },
            # 直客体系 (11-14)
            'direct_system': {
                11: {'type': '纯直客', 'name': '纯直客'},
                12: {'type': '1级直客代理', 'name': '1级直客代理'},
                13: {'type': '2级直客代理', 'name': '2级直客代理'},
                14: {'type': '3级直客代理', 'name': '3级直客代理'},
            }
        }
    
    def is_agent_system(self, level_number: int) -> bool:
        """判断是否为代理体系"""
        return 1 <= level_number <= 4
    
    def is_direct_system(self, level_number: int) -> bool:
        """判断是否为直客体系"""
        return 11 <= level_number <= 14
    
    def get_system_type(self, level_number: int) -> str:
        """获取体系类型"""
        if self.is_agent_system(level_number):
            return 'agent_system'
        elif self.is_direct_system(level_number):
            return 'direct_system'
        else:
            return 'unknown' 
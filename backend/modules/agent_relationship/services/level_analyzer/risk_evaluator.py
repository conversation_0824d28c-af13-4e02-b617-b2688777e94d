#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
层级风险评估模块
对用户层级和关系进行风险评估，提供风险等级和建议
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class LevelRiskEvaluator:
    """层级风险评估器"""
    
    def __init__(self):
        """初始化风险评估器"""
        self.logger = logger
        
        # 风险权重配置
        self.risk_weights = {
            'level_abnormality': 0.3,      # 层级异常
            'relationship_risk': 0.25,     # 关系风险
            'system_crossing': 0.2,        # 跨体系
            'recommendation_anomaly': 0.15, # 推荐异常
            'data_consistency': 0.1        # 数据一致性
        }
        
        # 风险阈值
        self.risk_thresholds = {
            'low': 0.3,
            'medium': 0.6,
            'high': 0.8
        }
    
    def evaluate_user_risk(self, user_data: Dict[str, Any], 
                          level_analysis: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        评估单个用户的风险
        
        Args:
            user_data: 用户数据
            level_analysis: 层级分析结果（可选）
            
        Returns:
            dict: 风险评估结果
        """
        try:
            risk_factors = {}
            total_risk_score = 0.0
            
            # 1. 层级异常评估
            level_risk = self._evaluate_level_abnormality(user_data, level_analysis)
            risk_factors['level_abnormality'] = level_risk
            total_risk_score += level_risk * self.risk_weights['level_abnormality']
            
            # 2. 推荐关系异常评估
            recommendation_risk = self._evaluate_recommendation_anomaly(user_data)
            risk_factors['recommendation_anomaly'] = recommendation_risk
            total_risk_score += recommendation_risk * self.risk_weights['recommendation_anomaly']
            
            # 3. 数据一致性评估
            consistency_risk = self._evaluate_data_consistency(user_data)
            risk_factors['data_consistency'] = consistency_risk
            total_risk_score += consistency_risk * self.risk_weights['data_consistency']
            
            # 确保风险分数在0-1范围内
            total_risk_score = min(max(total_risk_score, 0.0), 1.0)
            
            # 确定风险等级
            risk_level = self._determine_risk_level(total_risk_score)
            
            return {
                'user_id': user_data.get('digital_id') or user_data.get('member_id'),
                'total_risk_score': total_risk_score,
                'risk_level': risk_level,
                'risk_factors': risk_factors,
                'risk_description': self._generate_risk_description(risk_factors, risk_level),
                'recommendations': self._generate_recommendations(risk_factors, risk_level),
                'evaluation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"用户风险评估失败: {e}")
            return self._get_error_evaluation(user_data, str(e))
    
    def _evaluate_level_abnormality(self, user_data: Dict[str, Any], 
                                   level_analysis: Optional[Dict[str, Any]] = None) -> float:
        """评估层级异常风险"""
        try:
            risk_score = 0.0
            
            # 检查是否有层级分析结果
            if level_analysis:
                analysis_method = level_analysis.get('analysis_method', '')
                level_type = level_analysis.get('level_type', '')
                
                # 基于分析方法的风险评估
                if '排除法' in analysis_method:
                    risk_score += 0.3  # 排除法判断存在不确定性
                elif '异常处理' in analysis_method:
                    risk_score += 0.5  # 异常处理表示存在问题
                elif '默认' in analysis_method:
                    risk_score += 0.2  # 默认值表示数据不完整
                
                # 特殊层级类型风险
                if '未知' in level_type or '分析失败' in level_type:
                    risk_score += 0.4
            
            # 检查user_agent_level的合理性
            user_agent_level = user_data.get('user_agent_level', 0)
            if user_agent_level is not None:
                try:
                    # 确保转换为数字进行比较
                    level_num = int(user_agent_level) if user_agent_level != '' else 0
                    if level_num < 0 or level_num > 10:
                        risk_score += 0.3  # 超出合理范围
                    elif level_num == 0:
                        risk_score += 0.1  # 可能是数据缺失
                except (ValueError, TypeError):
                    # 如果无法转换为数字，说明数据异常
                    risk_score += 0.2
            
            return min(risk_score, 1.0)
            
        except Exception as e:
            self.logger.error(f"层级异常评估失败: {e}")
            return 0.3  # 默认中等风险
    
    def _evaluate_recommendation_anomaly(self, user_data: Dict[str, Any]) -> float:
        """评估推荐关系异常风险"""
        try:
            risk_score = 0.0
            
            recommender_id = user_data.get('recommender_digital_id')
            top_kol_id = user_data.get('top_kol_digital_id')
            agent_flag = user_data.get('agent_flag', '')
            
            # 直客的推荐关系检查
            if agent_flag == '直客':
                if recommender_id and top_kol_id and recommender_id == top_kol_id:
                    risk_score += 0.2  # 直客但有相同的推荐人和KOL，可能异常
            
            # 代理的推荐关系检查
            elif agent_flag == '代理' or agent_flag != '直客':
                if not recommender_id and not top_kol_id:
                    risk_score += 0.3  # 代理但无推荐关系，可能异常
                elif recommender_id == user_data.get('digital_id'):
                    risk_score += 0.5  # 自己推荐自己，明显异常
            
            # 检查推荐ID的格式（简单检查）
            if recommender_id:
                try:
                    if len(str(recommender_id)) < 3:  # 太短的ID可能异常
                        risk_score += 0.1
                except:
                    risk_score += 0.2
            
            return min(risk_score, 1.0)
            
        except Exception as e:
            self.logger.error(f"推荐关系异常评估失败: {e}")
            return 0.2
    
    def _evaluate_data_consistency(self, user_data: Dict[str, Any]) -> float:
        """评估数据一致性风险"""
        try:
            risk_score = 0.0
            
            # 检查必要字段的存在性
            required_fields = ['digital_id', 'agent_flag']
            missing_count = 0
            
            for field in required_fields:
                if not user_data.get(field):
                    missing_count += 1
            
            risk_score += missing_count * 0.15
            
            # 检查ID字段的一致性
            digital_id = user_data.get('digital_id')
            member_id = user_data.get('member_id')
            
            if digital_id and member_id:
                # 如果两个ID都存在但差异很大，可能存在问题
                try:
                    if str(digital_id) != str(member_id):
                        # 简单的一致性检查
                        if len(str(digital_id)) != len(str(member_id)):
                            risk_score += 0.1
                except:
                    risk_score += 0.1
            
            # 检查agent_flag的有效性
            agent_flag = user_data.get('agent_flag', '')
            valid_flags = ['直客', '代理', 'KOL', '代理带来的用户']
            if agent_flag and agent_flag not in valid_flags:
                risk_score += 0.15
            
            return min(risk_score, 1.0)
            
        except Exception as e:
            self.logger.error(f"数据一致性评估失败: {e}")
            return 0.2
    
    def _determine_risk_level(self, risk_score: float) -> str:
        """确定风险等级"""
        if risk_score >= self.risk_thresholds['high']:
            return 'high'
        elif risk_score >= self.risk_thresholds['medium']:
            return 'medium'
        elif risk_score >= self.risk_thresholds['low']:
            return 'low'
        else:
            return 'very_low'
    
    def _generate_risk_description(self, risk_factors: Dict[str, float], risk_level: str) -> str:
        """生成风险描述"""
        descriptions = []
        
        # 分析主要风险因子
        high_risk_factors = {k: v for k, v in risk_factors.items() if v >= 0.3}
        
        if risk_level == 'high':
            descriptions.append("存在高风险，需要重点关注")
        elif risk_level == 'medium':
            descriptions.append("存在中等风险，建议进一步核实")
        elif risk_level == 'low':
            descriptions.append("存在轻微风险，建议关注")
        else:
            descriptions.append("风险较低")
        
        # 具体风险描述
        if high_risk_factors.get('level_abnormality', 0) >= 0.3:
            descriptions.append("层级判断存在异常")
        if high_risk_factors.get('recommendation_anomaly', 0) >= 0.3:
            descriptions.append("推荐关系存在异常")
        if high_risk_factors.get('data_consistency', 0) >= 0.3:
            descriptions.append("数据一致性存在问题")
        
        return "；".join(descriptions)
    
    def _generate_recommendations(self, risk_factors: Dict[str, float], risk_level: str) -> List[str]:
        """生成风险处理建议"""
        recommendations = []
        
        if risk_level == 'high':
            recommendations.append("立即进行人工核实")
            recommendations.append("暂停相关业务操作")
        elif risk_level == 'medium':
            recommendations.append("加强监控")
            recommendations.append("进行抽查核实")
        
        # 基于具体风险因子的建议
        if risk_factors.get('level_abnormality', 0) >= 0.3:
            recommendations.append("核实用户真实身份和层级关系")
        
        if risk_factors.get('recommendation_anomaly', 0) >= 0.3:
            recommendations.append("验证推荐关系的真实性")
        
        if risk_factors.get('data_consistency', 0) >= 0.3:
            recommendations.append("完善用户基础数据")
        
        if not recommendations:
            recommendations.append("继续正常监控")
        
        return recommendations
    
    def _get_error_evaluation(self, user_data: Dict[str, Any], error_msg: str) -> Dict[str, Any]:
        """获取错误评估结果"""
        return {
            'user_id': user_data.get('digital_id') or user_data.get('member_id'),
            'total_risk_score': 0.5,
            'risk_level': 'unknown',
            'risk_factors': {},
            'risk_description': f'风险评估失败: {error_msg}',
            'recommendations': ['请联系技术人员检查'],
            'evaluation_time': datetime.now().isoformat(),
            'error': error_msg
        }
    
    def batch_evaluate_users(self, users_data: List[Dict[str, Any]], 
                            level_analyses: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """批量评估用户风险"""
        results = []
        
        for i, user_data in enumerate(users_data):
            try:
                level_analysis = None
                if level_analyses and i < len(level_analyses):
                    level_analysis = level_analyses[i]
                
                evaluation_result = self.evaluate_user_risk(user_data, level_analysis)
                results.append(evaluation_result)
                
            except Exception as e:
                self.logger.error(f"批量风险评估失败 {i}: {e}")
                results.append(self._get_error_evaluation(user_data, str(e)))
        
        return results
    
    def evaluate_relationship_risk(self, relationship_data: Dict[str, Any]) -> Dict[str, Any]:
        """评估关系风险"""
        try:
            risk_score = relationship_data.get('risk_factor', 0.3)
            
            # 额外的关系风险评估
            if relationship_data.get('system_cross', False):
                risk_score += 0.1  # 跨体系额外风险
            
            # 检查层级差异，确保类型安全
            level_difference = relationship_data.get('level_difference', 0)
            try:
                diff_num = int(level_difference) if level_difference != '' else 0
                if diff_num > 3:
                    risk_score += 0.1  # 层级差异过大
            except (ValueError, TypeError):
                # 如果无法转换为数字，说明数据异常
                risk_score += 0.05
            
            # 确保风险分数在0-1范围内
            risk_score = min(max(risk_score, 0.0), 1.0)
            
            risk_level = self._determine_risk_level(risk_score)
            
            return {
                'relationship_risk_score': risk_score,
                'relationship_risk_level': risk_level,
                'risk_description': self._get_relationship_risk_description(relationship_data, risk_level),
                'evaluation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"关系风险评估失败: {e}")
            return {
                'relationship_risk_score': 0.5,
                'relationship_risk_level': 'unknown',
                'risk_description': f'关系风险评估失败: {str(e)}',
                'evaluation_time': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _get_relationship_risk_description(self, relationship_data: Dict[str, Any], risk_level: str) -> str:
        """获取关系风险描述"""
        rel_type = relationship_data.get('relationship_type', '未知关系')
        
        if risk_level == 'high':
            return f"{rel_type}存在高风险，需要重点监控"
        elif risk_level == 'medium':
            return f"{rel_type}存在中等风险，建议关注"
        elif risk_level == 'low':
            return f"{rel_type}存在轻微风险"
        else:
            return f"{rel_type}风险较低"
    
    def get_risk_statistics(self, evaluations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取风险统计信息"""
        try:
            total_count = len(evaluations)
            if total_count == 0:
                return {'total_count': 0}
            
            # 按风险等级统计
            risk_level_counts = {'very_low': 0, 'low': 0, 'medium': 0, 'high': 0, 'unknown': 0}
            total_risk_score = 0.0
            
            for evaluation in evaluations:
                risk_level = evaluation.get('risk_level', 'unknown')
                risk_level_counts[risk_level] = risk_level_counts.get(risk_level, 0) + 1
                total_risk_score += evaluation.get('total_risk_score', 0.0)
            
            average_risk_score = total_risk_score / total_count if total_count > 0 else 0.0
            
            return {
                'total_count': total_count,
                'risk_level_distribution': risk_level_counts,
                'average_risk_score': average_risk_score,
                'high_risk_count': risk_level_counts.get('high', 0),
                'high_risk_ratio': risk_level_counts.get('high', 0) / total_count if total_count > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"风险统计失败: {e}")
            return {'error': str(e)}
    
    def filter_high_risk_users(self, evaluations: List[Dict[str, Any]], 
                              risk_level_threshold: str = 'medium') -> List[Dict[str, Any]]:
        """筛选高风险用户"""
        try:
            threshold_map = {'very_low': 0, 'low': 1, 'medium': 2, 'high': 3, 'unknown': -1}
            threshold_value = threshold_map.get(risk_level_threshold, 2)
            
            return [
                evaluation for evaluation in evaluations
                if threshold_map.get(evaluation.get('risk_level', 'unknown'), -1) >= threshold_value
            ]
            
        except Exception as e:
            self.logger.error(f"高风险用户筛选失败: {e}")
            return [] 
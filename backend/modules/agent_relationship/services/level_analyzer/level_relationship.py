#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
层级关系分析模块
分析用户间的层级关系，提供关系类型判断和统计功能
"""

import logging
from typing import Dict, Any, List, Tuple, Optional
from .agent_level_analyzer import AgentLevelAnalyzer

logger = logging.getLogger(__name__)


class LevelRelationshipAnalyzer:
    """层级关系分析器"""
    
    def __init__(self, level_analyzer: Optional[AgentLevelAnalyzer] = None):
        """
        初始化层级关系分析器
        
        Args:
            level_analyzer: 层级分析器实例
        """
        self.level_analyzer = level_analyzer
        self.logger = logger
    
    def analyze_relationship(self, level_a: int, level_b: int, 
                           user_a_data: Optional[Dict] = None,
                           user_b_data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        分析两个用户之间的层级关系
        
        Args:
            level_a: 用户A的层级数字
            level_b: 用户B的层级数字
            user_a_data: 用户A的详细数据（可选）
            user_b_data: 用户B的详细数据（可选）
            
        Returns:
            dict: 包含关系分析结果的字典
        """
        try:
            relationship_type = self._determine_relationship_type(level_a, level_b)
            risk_factor = self._calculate_relationship_risk(level_a, level_b, relationship_type)
            
            # 分析推荐关系链（如果有用户数据）
            recommendation_analysis = None
            if user_a_data and user_b_data and self.level_analyzer:
                recommendation_analysis = self._analyze_recommendation_chain(user_a_data, user_b_data)
            
            return {
                'relationship_type': relationship_type,
                'level_difference': abs(level_a - level_b),
                'risk_factor': risk_factor,
                'system_cross': self._is_cross_system(level_a, level_b),
                'hierarchy_direction': self._get_hierarchy_direction(level_a, level_b),
                'recommendation_analysis': recommendation_analysis,
                'description': self._get_relationship_description(level_a, level_b, relationship_type)
            }
            
        except Exception as e:
            self.logger.error(f"层级关系分析失败: {e}")
            return self._get_error_result(str(e))
    
    def _determine_relationship_type(self, level_a: int, level_b: int) -> str:
        """确定关系类型"""
        if not level_a or not level_b:
            return "未知关系"
        
        # 同级关系
        if level_a == level_b:
            return "同级关系"
        
        # 相邻级别关系
        if abs(level_a - level_b) == 1:
            return "相邻级关系"
        
        # 跨体系关系（代理体系 vs 直客体系）
        if self._is_cross_system(level_a, level_b):
            return "跨体系关系"
        
        # 跨级关系
        if abs(level_a - level_b) > 1:
            return "跨级关系"
        
        return "其他关系"
    
    def _is_cross_system(self, level_a: int, level_b: int) -> bool:
        """判断是否为跨体系关系"""
        agent_system_range = range(1, 5)  # 1-4
        direct_system_range = range(11, 15)  # 11-14
        
        return ((level_a in agent_system_range and level_b in direct_system_range) or
                (level_b in agent_system_range and level_a in direct_system_range))
    
    def _get_hierarchy_direction(self, level_a: int, level_b: int) -> str:
        """获取层级方向"""
        if level_a == level_b:
            return "平级"
        elif level_a < level_b:
            return "A为上级"
        else:
            return "B为上级"
    
    def _calculate_relationship_risk(self, level_a: int, level_b: int, relationship_type: str) -> float:
        """计算关系风险因子"""
        base_risk = 0.3
        
        # 根据关系类型调整风险
        risk_adjustments = {
            "同级关系": 0.1,
            "相邻级关系": 0.05,
            "跨体系关系": 0.2,
            "跨级关系": 0.15,
            "其他关系": 0.1
        }
        
        risk_adjustment = risk_adjustments.get(relationship_type, 0.1)
        
        # BD级别特殊处理（更高风险）
        if level_a == 1 or level_b == 1:
            risk_adjustment += 0.1
        
        # 跨体系额外风险
        if self._is_cross_system(level_a, level_b):
            risk_adjustment += 0.1
        
        return min(base_risk + risk_adjustment, 1.0)
    
    def _analyze_recommendation_chain(self, user_a_data: Dict, user_b_data: Dict) -> Dict[str, Any]:
        """分析推荐关系链"""
        try:
            # 获取推荐关系信息
            a_recommender = user_a_data.get('recommender_digital_id')
            b_recommender = user_b_data.get('recommender_digital_id')
            a_top_kol = user_a_data.get('top_kol_digital_id')
            b_top_kol = user_b_data.get('top_kol_digital_id')
            
            analysis = {
                'has_common_recommender': a_recommender and a_recommender == b_recommender,
                'has_common_top_kol': a_top_kol and a_top_kol == b_top_kol,
                'direct_recommendation': False,
                'recommendation_distance': None
            }
            
            # 检查直接推荐关系
            a_id = user_a_data.get('digital_id')
            b_id = user_b_data.get('digital_id')
            
            if a_recommender == b_id:
                analysis['direct_recommendation'] = True
                analysis['recommendation_distance'] = 1
                analysis['direction'] = 'B推荐A'
            elif b_recommender == a_id:
                analysis['direct_recommendation'] = True
                analysis['recommendation_distance'] = 1
                analysis['direction'] = 'A推荐B'
            
            # 分析推荐距离（简化版本）
            if not analysis['direct_recommendation'] and analysis['has_common_top_kol']:
                analysis['recommendation_distance'] = self._estimate_recommendation_distance(
                    user_a_data, user_b_data
                )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"推荐关系链分析失败: {e}")
            return {'error': str(e)}
    
    def _estimate_recommendation_distance(self, user_a_data: Dict, user_b_data: Dict) -> Optional[int]:
        """估算推荐距离（简化实现）"""
        try:
            # 基于层级差异估算距离
            level_a = user_a_data.get('user_agent_level', 0)
            level_b = user_b_data.get('user_agent_level', 0)
            
            if level_a and level_b:
                return abs(level_a - level_b)
            
            return None
            
        except Exception:
            return None
    
    def _get_relationship_description(self, level_a: int, level_b: int, relationship_type: str) -> str:
        """获取关系描述"""
        level_names = {
            1: 'BD', 2: '1级代理', 3: '2级代理', 4: '3级代理',
            11: '纯直客', 12: '1级直客代理', 13: '2级直客代理', 14: '3级直客代理'
        }
        
        name_a = level_names.get(level_a, f'未知级别({level_a})')
        name_b = level_names.get(level_b, f'未知级别({level_b})')
        
        base_desc = f"{name_a} 与 {name_b} 的{relationship_type}"
        
        # 添加具体说明
        if relationship_type == "同级关系":
            return f"{base_desc}，风险程度较低"
        elif relationship_type == "相邻级关系":
            return f"{base_desc}，存在上下级可能"
        elif relationship_type == "跨体系关系":
            return f"{base_desc}，代理体系与直客体系交叉，需重点关注"
        elif relationship_type == "跨级关系":
            return f"{base_desc}，层级跨度较大，存在异常可能"
        else:
            return base_desc
    
    def _get_error_result(self, error_msg: str) -> Dict[str, Any]:
        """获取错误结果"""
        return {
            'relationship_type': '分析失败',
            'level_difference': 0,
            'risk_factor': 0.5,
            'system_cross': False,
            'hierarchy_direction': '未知',
            'recommendation_analysis': None,
            'description': f'关系分析失败: {error_msg}',
            'error': error_msg
        }
    
    def batch_analyze_relationships(self, user_pairs: List[Tuple[Dict, Dict]]) -> List[Dict[str, Any]]:
        """批量分析用户关系"""
        results = []
        
        for user_a_data, user_b_data in user_pairs:
            try:
                # 首先分析层级
                if self.level_analyzer:
                    level_a_result = self.level_analyzer.analyze(user_a_data)
                    level_b_result = self.level_analyzer.analyze(user_b_data)
                    
                    level_a = level_a_result.get('level_number', 0)
                    level_b = level_b_result.get('level_number', 0)
                else:
                    level_a = user_a_data.get('user_agent_level', 0)
                    level_b = user_b_data.get('user_agent_level', 0)
                
                # 分析关系
                relationship_result = self.analyze_relationship(
                    level_a, level_b, user_a_data, user_b_data
                )
                
                # 添加用户信息
                relationship_result.update({
                    'user_a_id': user_a_data.get('digital_id') or user_a_data.get('member_id'),
                    'user_b_id': user_b_data.get('digital_id') or user_b_data.get('member_id'),
                    'user_a_level': level_a,
                    'user_b_level': level_b
                })
                
                results.append(relationship_result)
                
            except Exception as e:
                self.logger.error(f"批量关系分析失败: {e}")
                results.append(self._get_error_result(str(e)))
        
        return results
    
    def get_relationship_statistics(self, relationships: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取关系统计信息"""
        try:
            total_count = len(relationships)
            if total_count == 0:
                return {'total_count': 0}
            
            # 按关系类型统计
            type_counts = {}
            risk_levels = {'low': 0, 'medium': 0, 'high': 0}
            cross_system_count = 0
            
            for rel in relationships:
                rel_type = rel.get('relationship_type', '未知')
                type_counts[rel_type] = type_counts.get(rel_type, 0) + 1
                
                # 风险等级统计
                risk_factor = rel.get('risk_factor', 0.3)
                if risk_factor >= 0.7:
                    risk_levels['high'] += 1
                elif risk_factor >= 0.5:
                    risk_levels['medium'] += 1
                else:
                    risk_levels['low'] += 1
                
                # 跨体系统计
                if rel.get('system_cross', False):
                    cross_system_count += 1
            
            return {
                'total_count': total_count,
                'type_distribution': type_counts,
                'risk_distribution': risk_levels,
                'cross_system_count': cross_system_count,
                'cross_system_ratio': cross_system_count / total_count if total_count > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"关系统计失败: {e}")
            return {'error': str(e)}
    
    def filter_high_risk_relationships(self, relationships: List[Dict[str, Any]], 
                                     risk_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """筛选高风险关系"""
        try:
            return [
                rel for rel in relationships 
                if rel.get('risk_factor', 0) >= risk_threshold
            ]
        except Exception as e:
            self.logger.error(f"高风险关系筛选失败: {e}")
            return [] 
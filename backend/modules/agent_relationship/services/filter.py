"""
数据过滤模块 - 提供数据过滤和分类功能
"""

def filter_data(data, search_term, filter_bd, filter_unassigned_bd, filter_ourbit_internal, filter_mode='all'):
    """
    过滤数据函数
    
    参数:
        data (list): 要过滤的数据列表
        search_term (str): 搜索关键词
        filter_bd (str): BD团队过滤条件
        filter_unassigned_bd (bool): 是否过滤未分配BD的数据
        filter_ourbit_internal (bool): 是否过滤Ourbit_Internal的数据
        filter_mode (str): 筛选模式 - 'all'(所有), 'same_bd'(同一BD), 'diff_bd'(不同BD)
        
    返回:
        list: 过滤后的数据列表
    """
    if not data:
        return []
    
    # 性能优化：如果没有任何过滤条件，直接返回原始数据
    if not search_term and not filter_bd and not filter_unassigned_bd and not filter_ourbit_internal:
        return data
    
    result = []
    for item in data:
        # 过滤未分配BD的数据
        if filter_unassigned_bd and (not item.get('user_a_bd') or item.get('user_a_bd') == '未分配' or 
                                      not item.get('user_b_bd') or item.get('user_b_bd') == '未分配'):
            continue
        
        # 过滤Ourbit_Internal的数据
        if filter_ourbit_internal and (item.get('user_a_bd') == 'Ourbit_Internal' or item.get('user_b_bd') == 'Ourbit_Internal'):
            continue
        
        # BD团队过滤 - 根据筛选模式进行不同的处理
        if filter_bd:
            user_a_bd = item.get('user_a_bd')
            user_b_bd = item.get('user_b_bd')
            
            if filter_bd == '未分配':
                # 处理未分配BD的情况
                if filter_mode == 'same_bd':
                    # 同一BD模式：双方都是未分配
                    if not (
                        (not user_a_bd or user_a_bd == '未分配') and 
                        (not user_b_bd or user_b_bd == '未分配')
                    ):
                        continue
                elif filter_mode == 'diff_bd':
                    # 不同BD模式：至少有一方是未分配，但不能双方都是未分配
                    is_a_unassigned = (not user_a_bd or user_a_bd == '未分配')
                    is_b_unassigned = (not user_b_bd or user_b_bd == '未分配')
                    if not ((is_a_unassigned or is_b_unassigned) and not (is_a_unassigned and is_b_unassigned)):
                        continue
                else:
                    # 全部模式：至少有一方是未分配
                    if not ((not user_a_bd or user_a_bd == '未分配') or (not user_b_bd or user_b_bd == '未分配')):
                        continue
            else:
                # 处理具体BD团队的情况
                if filter_mode == 'same_bd':
                    # 同一BD模式：双方都属于指定BD团队
                    if not (user_a_bd == filter_bd and user_b_bd == filter_bd):
                        continue
                elif filter_mode == 'diff_bd':
                    # 不同BD模式：至少有一方属于指定BD团队，但不能双方都是同一BD团队
                    has_target_bd = (user_a_bd == filter_bd or user_b_bd == filter_bd)
                    both_same_bd = (user_a_bd == user_b_bd and user_a_bd == filter_bd)
                    if not (has_target_bd and not both_same_bd):
                        continue
                else:
                    # 全部模式：至少有一方属于指定BD团队
                    if not (user_a_bd == filter_bd or user_b_bd == filter_bd):
                        continue
        
        # 搜索过滤
        if search_term:
            search_fields = [
                # 用户ID
                str(item.get('user_a_mid') or ''),
                str(item.get('user_b_mid') or ''),
                # 用户名称
                str(item.get('user_a_name') or ''),
                str(item.get('user_b_name') or ''),
                # BD团队
                str(item.get('user_a_bd') or ''),
                str(item.get('user_b_bd') or ''),
                # 用户层级
                str(item.get('user_a_level') or ''),
                str(item.get('user_b_level') or ''),
                # 共享设备ID
                str(item.get('shared_device_id') or item.get('shared_value') or ''),
                # 共享IP
                str(item.get('shared_ip') or (item.get('shared_value') if item.get('shared_type') == '共享IP' else '') or '')
            ]
            
            if not any(search_term in field.lower() for field in search_fields):
                continue
        
        result.append(item)
    
    return result

def filter_ranking(ranking_data, filter_unassigned_bd, filter_ourbit_internal, filter_bd=None):
    """
    过滤排名数据
    
    参数:
        ranking_data (list): 排名数据列表
        filter_unassigned_bd (bool): 是否过滤未分配BD的数据
        filter_ourbit_internal (bool): 是否过滤Ourbit_Internal的数据
        filter_bd (str): BD团队过滤条件，如果提供则只显示该BD团队的排名
        
    返回:
        list: 过滤后的排名数据列表
    """
    if not ranking_data:
        return []
    
    result = []
    for item in ranking_data:
        # 过滤未分配
        if filter_unassigned_bd and (not item.get('bd_name') or item.get('bd_name') == '未分配'):
            continue
        
        # 过滤Ourbit_Internal
        if filter_ourbit_internal and item.get('bd_name') == 'Ourbit_Internal':
            continue
        
        # BD团队过滤 - 如果指定了特定BD团队，只显示该团队的排名
        if filter_bd:
            if filter_bd == '未分配':
                # 只显示未分配BD的排名
                if item.get('bd_name') != '未分配' and item.get('bd_name'):
                    continue
            else:
                # 只显示指定BD团队的排名
                if item.get('bd_name') != filter_bd:
                    continue
        
        result.append(item)
    
    # 按共享数量降序排序
    return sorted(result, key=lambda x: x.get('count', 0), reverse=True)

def classify_data_by_bd(data, filter_bd):
    """
    根据BD团队分类数据
    
    参数:
        data (list): 数据列表（已经过BD筛选）
        filter_bd (str): BD团队过滤条件
        
    返回:
        tuple: (同一BD团队数据列表, 不同BD团队数据列表)
    """
    same_bd = []
    diff_bd = []
    
    for item in data:
        # 判断是否同一团队
        is_same_bd = (
            item.get('user_a_bd') == item.get('user_b_bd') and 
            item.get('user_a_bd') and 
            item.get('user_a_bd') != '未分配'
        )
        
        # 由于数据已经经过filter_data筛选，如果有BD过滤，所有数据都应该属于指定的BD团队
        # 所以这里只需要根据是否同一团队进行分类
        if is_same_bd:
            same_bd.append(item)
        else:
            diff_bd.append(item)
    
    return same_bd, diff_bd 
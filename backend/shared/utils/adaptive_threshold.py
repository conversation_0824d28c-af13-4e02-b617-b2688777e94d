"""
自适应阈值管理器
"""
from typing import Dict, Any, Optional

class AdaptiveThreshold:
    """自适应阈值管理器"""
    
    def __init__(self, cache_dir: Optional[str] = None):
        self.cache_dir = cache_dir
        self.thresholds = {}
        
    def get_threshold(self, metric_name: str, contract_name: str, default_value: float) -> float:
        """获取自适应阈值"""
        key = f"{metric_name}_{contract_name}"
        return self.thresholds.get(key, default_value)
        
    def update_threshold(self, metric_name: str, contract_name: str, value: float):
        """更新阈值"""
        key = f"{metric_name}_{contract_name}"
        self.thresholds[key] = value 
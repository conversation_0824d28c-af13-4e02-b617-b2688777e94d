"""
共享工具模块
"""
from typing import Dict, Any, Callable

class AlgorithmRegistry:
    """算法注册中心"""
    _algorithms = {}
    
    @classmethod
    def register_algorithm(cls, name: str, algorithm: Callable):
        """注册算法"""
        cls._algorithms[name] = algorithm
        
    @classmethod
    def get_algorithm(cls, name: str):
        """获取算法"""
        return cls._algorithms.get(name)

class FieldMapper:
    """字段映射器"""
    
    def __init__(self):
        self.field_map = {}
        
    def map_field(self, source: str, target: str):
        """映射字段"""
        self.field_map[source] = target
        
    def get_mapped_field(self, field: str) -> str:
        """获取映射后的字段名"""
        return self.field_map.get(field, field) 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新存储结构初始化脚本
版本: 2.0 - 拆分存储优化版本
"""

import sys
import os
import logging
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

from database.algorithm_storage_manager import AlgorithmStorageManager
from database.duckdb_manager import DuckDBManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def initialize_new_storage():
    """初始化新的存储结构"""
    
    logger.info("开始新存储结构初始化")
    
    try:
        # 0. 首先初始化基础数据库结构
        print("\n0️⃣ 初始化基础数据库结构...")
        db = DuckDBManager()
        db.initialize_database()
        print("   ✅ 基础表结构创建成功")
        
        # 1. 创建存储管理器
        storage_manager = AlgorithmStorageManager()
        
        print("\n1️⃣ 初始化新表结构...")
        success = storage_manager.initialize_tables()
        if success:
            print("   ✅ 新表结构创建成功")
        else:
            print("   ❌ 新表结构创建失败")
            return False
        
        # 2. 验证表创建
        print("\n2️⃣ 验证表结构...")
        with db.get_connection() as conn:
            # 检查基础表
            basic_tables = ['users', 'user_relationships', 'tasks', 'contract_risk_analysis', 'agent_analysis']
            print("   📊 基础表检查:")
            for table in basic_tables:
                try:
                    count = conn.execute(f'SELECT COUNT(*) FROM {table}').fetchone()[0]
                    print(f"      ✅ {table}: {count} 条记录")
                except Exception as e:
                    print(f"      ❌ {table}: 创建失败 - {e}")
                    return False
            
            # 检查新创建的表
            new_tables = [
                'algorithm_results',
                'wash_trading_results',
                'same_account_wash_trading',
                'cross_account_wash_trading',
                'high_frequency_trading_details',
                'funding_rate_arbitrage_details'
            ]
            
            print("   🆕 新存储表检查:")
            missing_tables = []
            for table in new_tables:
                try:
                    count = conn.execute(f'SELECT COUNT(*) FROM {table}').fetchone()[0]
                    print(f"      ✅ {table}: {count} 条记录")
                except Exception as e:
                    print(f"      ❌ {table}: 不存在或有错误 - {e}")
                    missing_tables.append(table)
            
            if missing_tables:
                print(f"\n❌ 以下表未能正确创建: {missing_tables}")
                return False
        
        # 3. 检查旧表状态  
        print("\n3️⃣ 检查旧表状态...")
        print("   💡 cross_bd_wash_trading表已清理，跨账户对敲功能已迁移到cross_account_wash_trading表")
        
        # 4. 显示使用指南
        print("\n4️⃣ 初始化完成")
        show_usage_guide()
        
        return True
        
    except Exception as e:
        logger.error(f"初始化失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    
    print("""
==============================================
🎉 新存储结构初始化成功！
==============================================

📋 下一步操作:

1. 测试新存储结构:
   ```bash
   cd backend
   python -c "
   from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
   from database.duckdb_manager import DuckDBManager
   
   adapter = ContractDataAdapter(DuckDBManager())
   adapter.enable_new_storage()  # 启用新存储
   print('新存储结构已启用')
   "
   ```

2. 数据迁移 (如果需要):
   ```bash
   python database/migrate_existing_data.py
   ```

3. 性能测试:
   ```bash
   python database/performance_test.py
   ```

4. 在生产环境中启用:
   # 在需要使用新存储的地方调用:
   adapter.enable_new_storage()

==============================================
🛡️ 安全回滚:
==============================================

如果出现问题，可以随时回滚:
```python
adapter.disable_new_storage()  # 回退到旧存储
```

原有数据完全保留，零风险切换！

==============================================
📊 表结构说明:
==============================================

✅ 新增表:
├── algorithm_results              - 通用算法结果表
├── wash_trading_results          - 对敲交易统一管理表
├── same_account_wash_trading     - 同账户对敲详情表
├── cross_account_wash_trading    - 跨账户对敲详情表
├── high_frequency_trading_details - 高频交易详情表
└── funding_rate_arbitrage_details - 资金费率套利详情表

🔄 修改表:
├── contract_risk_analysis        - 保留JSON存储，作为兼容备份

✅ 保留表:
├── users, user_relationships, tasks, agent_analysis
└── shared_relationships          - 完全不变

==============================================
""")

def verify_schema():
    """验证数据库模式"""
    print("\n🔍 验证数据库模式...")
    
    db = DuckDBManager()
    with db.get_connection() as conn:
        # 检查所有表
        tables = conn.execute('SHOW TABLES').fetchall()
        print(f"\n📊 当前数据库共有 {len(tables)} 个表:")
        
        for table in sorted(tables):
            table_name = table[0]
            count = conn.execute(f'SELECT COUNT(*) FROM {table_name}').fetchone()[0]
            
            # 获取表的字段数
            desc = conn.execute(f'DESCRIBE {table_name}').fetchall()
            field_count = len(desc)
            

            print(f"  {marker} {table_name:<35} | {count:>8} 条记录 | {field_count:>2} 字段")

if __name__ == '__main__':
    try:
        success = initialize_new_storage()
        
        if success:
            verify_schema()
            print("\n🎉 初始化完成！系统已准备好使用新存储结构。")
        else:
            print("\n❌ 初始化失败，请检查错误日志。")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        sys.exit(1) 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库路径验证器
提供数据库路径验证、清理和监控功能
"""

import os
import logging
import duckdb
from typing import List, Dict, Tuple, Optional
from pathlib import Path
from config.database_config import db_config, validate_db_path

logger = logging.getLogger(__name__)

class DatabaseValidator:
    """数据库验证器"""
    
    def __init__(self):
        self.config = db_config
        self.data_dir = self.config.DB_CONFIG['DATA_DIR']
    
    def validate_and_fix_db_path(self, db_path: str) -> Tuple[str, List[str]]:
        """
        验证并修复数据库路径
        
        Args:
            db_path: 要验证的数据库路径
            
        Returns:
            tuple: (修正后的路径, 警告信息列表)
        """
        warnings = []
        
        if not db_path:
            warnings.append("数据库路径为空，使用默认路径")
            return self.config.get_main_db_path(), warnings
        
        # 验证路径
        is_valid, error_msg = validate_db_path(db_path)
        
        if not is_valid:
            warnings.append(f"路径验证失败: {error_msg}")
            warnings.append("使用默认路径")
            return self.config.get_main_db_path(), warnings
        
        return db_path, warnings
    
    def scan_database_files(self) -> Dict[str, Dict]:
        """
        扫描数据目录中的所有数据库文件
        
        Returns:
            dict: 数据库文件信息
        """
        if not os.path.exists(self.data_dir):
            return {}
        
        db_files = {}
        
        for filename in os.listdir(self.data_dir):
            file_path = os.path.join(self.data_dir, filename)
            
            # 只检查数据库文件
            if filename.endswith(('.db', '.duckdb', '.sqlite', '.sqlite3')):
                file_info = self._analyze_db_file(file_path)
                db_files[filename] = file_info
        
        return db_files
    
    def _analyze_db_file(self, file_path: str) -> Dict:
        """分析数据库文件"""
        file_info = {
            'path': file_path,
            'size': os.path.getsize(file_path),
            'modified': os.path.getmtime(file_path),
            'is_valid': False,
            'is_empty': False,
            'table_count': 0,
            'record_count': 0,
            'extension': os.path.splitext(file_path)[1],
            'issues': []
        }
        
        # 检查文件扩展名
        if not file_path.endswith('.duckdb'):
            file_info['issues'].append(f"错误的文件扩展名: {file_info['extension']}")
        
        # 检查文件大小
        if file_info['size'] < 1024:  # 小于1KB
            file_info['is_empty'] = True
            file_info['issues'].append("文件过小，可能是空数据库")
        
        # 尝试连接数据库获取更多信息
        try:
            with duckdb.connect(file_path) as conn:
                # 获取表数量
                tables = conn.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'main'").fetchall()
                file_info['table_count'] = len(tables)
                
                # 如果有表，计算总记录数
                if tables:
                    total_records = 0
                    for table in tables:
                        try:
                            count = conn.execute(f"SELECT COUNT(*) FROM {table[0]}").fetchone()[0]
                            total_records += count
                        except:
                            pass
                    file_info['record_count'] = total_records
                    file_info['is_valid'] = True
                else:
                    file_info['issues'].append("数据库中没有表")
                    
        except Exception as e:
            file_info['issues'].append(f"无法连接数据库: {str(e)}")
        
        return file_info
    
    def find_problematic_files(self) -> List[Dict]:
        """查找有问题的数据库文件"""
        db_files = self.scan_database_files()
        problematic = []
        
        for filename, info in db_files.items():
            if info['issues'] or info['is_empty']:
                problematic.append({
                    'filename': filename,
                    'path': info['path'],
                    'issues': info['issues'],
                    'size': info['size'],
                    'can_delete': self._can_safely_delete(info)
                })
        
        return problematic
    
    def _can_safely_delete(self, file_info: Dict) -> bool:
        """判断文件是否可以安全删除"""
        # 如果是主数据库，不能删除
        if self.config.is_main_database(file_info['path']):
            return False
        
        # 如果文件很小且没有数据，可以删除
        if file_info['is_empty'] and file_info['record_count'] == 0:
            return True
        
        # 如果扩展名错误且文件很小，可以删除
        if not file_info['path'].endswith('.duckdb') and file_info['size'] < 50000:
            return True
        
        return False
    
    def cleanup_invalid_files(self, dry_run: bool = True) -> Dict[str, List[str]]:
        """
        清理无效的数据库文件
        
        Args:
            dry_run: 是否只是预览，不实际删除
            
        Returns:
            dict: 清理结果
        """
        result = {
            'deleted': [],
            'skipped': [],
            'errors': []
        }
        
        problematic_files = self.find_problematic_files()
        
        for file_info in problematic_files:
            filename = file_info['filename']
            file_path = file_info['path']
            
            if not file_info['can_delete']:
                result['skipped'].append(f"{filename} (不能安全删除)")
                continue
            
            if dry_run:
                result['deleted'].append(f"{filename} (预览模式)")
            else:
                try:
                    os.remove(file_path)
                    result['deleted'].append(filename)
                    logger.info(f"已删除无效数据库文件: {filename}")
                except OSError as e:
                    error_msg = f"{filename}: {str(e)}"
                    result['errors'].append(error_msg)
                    logger.error(f"删除文件失败: {error_msg}")
        
        return result
    
    def generate_report(self) -> str:
        """生成数据库文件报告"""
        db_files = self.scan_database_files()
        problematic = self.find_problematic_files()
        
        report = []
        report.append("=== 数据库文件扫描报告 ===\n")
        
        # 总体统计
        report.append(f"扫描目录: {self.data_dir}")
        report.append(f"发现数据库文件: {len(db_files)} 个")
        report.append(f"有问题的文件: {len(problematic)} 个\n")
        
        # 详细信息
        if db_files:
            report.append("=== 文件详情 ===")
            for filename, info in db_files.items():
                status = "✅ 正常" if not info['issues'] else "❌ 有问题"
                size_mb = info['size'] / 1024 / 1024
                
                report.append(f"\n📁 {filename}")
                report.append(f"   状态: {status}")
                report.append(f"   大小: {size_mb:.2f} MB")
                report.append(f"   表数量: {info['table_count']}")
                report.append(f"   记录数: {info['record_count']}")
                
                if info['issues']:
                    report.append("   问题:")
                    for issue in info['issues']:
                        report.append(f"     - {issue}")
        
        # 清理建议
        if problematic:
            report.append("\n=== 清理建议 ===")
            for file_info in problematic:
                action = "可以删除" if file_info['can_delete'] else "需要手动检查"
                report.append(f"• {file_info['filename']}: {action}")
                for issue in file_info['issues']:
                    report.append(f"  - {issue}")
        
        return "\n".join(report)


# 创建全局验证器实例
db_validator = DatabaseValidator()

# 导出常用函数
validate_and_fix_db_path = db_validator.validate_and_fix_db_path
scan_database_files = db_validator.scan_database_files
cleanup_invalid_files = db_validator.cleanup_invalid_files
generate_db_report = db_validator.generate_report

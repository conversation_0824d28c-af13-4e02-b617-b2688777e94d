"""
用户行为分析数据仓库
提供统一的用户行为数据查询接口，替代原有的视图和临时表
"""

import logging
from typing import List, Dict, Optional
from datetime import datetime, date
from database.duckdb_manager import DuckDBManager

logger = logging.getLogger(__name__)

class UserBehaviorRepository:
    """用户行为分析数据仓库"""
    
    def __init__(self):
        self.db_manager = DuckDBManager()
    
    def get_user_behavior_summary(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """
        获取用户行为分析摘要列表
        替代原有的 user_behavior_summary_view 视图
        """
        try:
            sql = """
            SELECT 
                member_id,
                analysis_date,
                trader_type,
                professional_score,
                fund_scale_category,
                total_volume,
                total_trades,
                win_rate,
                profit_factor,
                avg_leverage,
                total_analyzed_coins,
                avg_coin_win_rate,
                abnormal_ratio,
                created_at
            FROM user_trading_profiles
            ORDER BY analysis_date DESC, professional_score DESC
            LIMIT ? OFFSET ?
            """
            
            return self.db_manager.fetch_all(sql, [limit, offset])
            
        except Exception as e:
            logger.error(f"获取用户行为摘要失败: {e}")
            return []
    
    def get_professional_traders_stats(self, analysis_date: Optional[date] = None) -> List[Dict]:
        """
        获取专业交易员统计数据
        替代原有的 professional_traders_stats_view 视图
        """
        try:
            where_clause = ""
            params = []
            
            if analysis_date:
                where_clause = "WHERE analysis_date = ?"
                params.append(analysis_date)
            
            sql = f"""
            SELECT 
                analysis_date,
                COUNT(*) as total_users,
                SUM(CASE WHEN trader_type = '专业交易员' THEN 1 ELSE 0 END) as professional_count,
                SUM(CASE WHEN trader_type = '半专业交易员' THEN 1 ELSE 0 END) as semi_professional_count,
                SUM(CASE WHEN trader_type = '普通散户' THEN 1 ELSE 0 END) as retail_count,
                SUM(CASE WHEN trader_type = '新用户' THEN 1 ELSE 0 END) as new_user_count,
                AVG(professional_score) as avg_professional_score,
                AVG(win_rate) as avg_win_rate,
                AVG(profit_factor) as avg_profit_factor
            FROM user_trading_profiles
            {where_clause}
            GROUP BY analysis_date
            ORDER BY analysis_date DESC
            """
            
            return self.db_manager.fetch_all(sql, params)
            
        except Exception as e:
            logger.error(f"获取专业交易员统计失败: {e}")
            return []
    
    def get_user_behavior_by_id(self, member_id: str) -> Optional[Dict]:
        """获取指定用户的最新行为分析数据"""
        try:
            sql = """
            SELECT * FROM user_trading_profiles
            WHERE member_id = ?
            ORDER BY analysis_date DESC, created_at DESC
            LIMIT 1
            """
            
            results = self.db_manager.fetch_all(sql, [member_id])
            return results[0] if results else None
            
        except Exception as e:
            logger.error(f"获取用户 {member_id} 行为数据失败: {e}")
            return None
    
    def get_users_by_trader_type(self, trader_type: str, limit: int = 50) -> List[Dict]:
        """根据交易者类型获取用户列表"""
        try:
            sql = """
            SELECT 
                member_id, trader_type, professional_score, 
                fund_scale_category, total_volume, win_rate,
                analysis_date, created_at
            FROM user_trading_profiles
            WHERE trader_type = ?
            ORDER BY professional_score DESC, total_volume DESC
            LIMIT ?
            """
            
            return self.db_manager.fetch_all(sql, [trader_type, limit])
            
        except Exception as e:
            logger.error(f"获取交易者类型 {trader_type} 的用户列表失败: {e}")
            return []
    
    def get_high_professional_score_users(self, min_score: float = 60.0, limit: int = 100) -> List[Dict]:
        """获取高专业度评分用户"""
        try:
            sql = """
            SELECT 
                member_id, trader_type, professional_score,
                fund_scale_category, total_volume, total_trades,
                win_rate, profit_factor, analysis_date
            FROM user_trading_profiles
            WHERE professional_score >= ?
            ORDER BY professional_score DESC, total_volume DESC
            LIMIT ?
            """
            
            return self.db_manager.fetch_all(sql, [min_score, limit])
            
        except Exception as e:
            logger.error(f"获取高专业度评分用户失败: {e}")
            return []
    
    def get_users_by_fund_scale(self, fund_scale_category: str, limit: int = 100) -> List[Dict]:
        """根据资金规模获取用户列表"""
        try:
            sql = """
            SELECT 
                member_id, fund_scale_category, real_trading_volume,
                trader_type, professional_score, total_volume,
                analysis_date, created_at
            FROM user_trading_profiles
            WHERE fund_scale_category = ?
            ORDER BY real_trading_volume DESC, professional_score DESC
            LIMIT ?
            """
            
            return self.db_manager.fetch_all(sql, [fund_scale_category, limit])
            
        except Exception as e:
            logger.error(f"获取资金规模 {fund_scale_category} 的用户列表失败: {e}")
            return []
    
    def get_abnormal_trading_users(self, min_abnormal_ratio: float = 0.1, limit: int = 100) -> List[Dict]:
        """获取异常交易用户"""
        try:
            sql = """
            SELECT 
                member_id, abnormal_ratio, abnormal_volume,
                wash_trading_volume, high_frequency_volume,
                risk_events_count, trader_type, professional_score,
                analysis_date
            FROM user_trading_profiles
            WHERE abnormal_ratio >= ?
            ORDER BY abnormal_ratio DESC, risk_events_count DESC
            LIMIT ?
            """
            
            return self.db_manager.fetch_all(sql, [min_abnormal_ratio, limit])
            
        except Exception as e:
            logger.error(f"获取异常交易用户失败: {e}")
            return []
    
    def get_user_count_by_date(self, start_date: Optional[date] = None, end_date: Optional[date] = None) -> List[Dict]:
        """获取按日期统计的用户数量"""
        try:
            where_conditions = []
            params = []
            
            if start_date:
                where_conditions.append("analysis_date >= ?")
                params.append(start_date)
            
            if end_date:
                where_conditions.append("analysis_date <= ?")
                params.append(end_date)
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            sql = f"""
            SELECT 
                analysis_date,
                COUNT(*) as total_users,
                COUNT(DISTINCT member_id) as unique_users,
                AVG(professional_score) as avg_professional_score,
                AVG(total_volume) as avg_total_volume
            FROM user_trading_profiles
            {where_clause}
            GROUP BY analysis_date
            ORDER BY analysis_date DESC
            """
            
            return self.db_manager.fetch_all(sql, params)
            
        except Exception as e:
            logger.error(f"获取用户数量统计失败: {e}")
            return []
    
    def search_users(self, keyword: str, limit: int = 50) -> List[Dict]:
        """搜索用户（支持用户ID模糊匹配）"""
        try:
            sql = """
            SELECT 
                member_id, trader_type, professional_score,
                fund_scale_category, total_volume, win_rate,
                analysis_date, created_at
            FROM user_trading_profiles
            WHERE member_id LIKE ?
            ORDER BY professional_score DESC, total_volume DESC
            LIMIT ?
            """
            
            search_pattern = f"%{keyword}%"
            return self.db_manager.fetch_all(sql, [search_pattern, limit])
            
        except Exception as e:
            logger.error(f"搜索用户失败: {e}")
            return []

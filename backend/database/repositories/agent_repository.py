"""
代理关系分析结果数据仓库
"""
import json
import logging
from datetime import datetime
from database.duckdb_manager import db_manager

# 创建logger
logger = logging.getLogger(__name__)

class AgentRepository:
    def __init__(self):
        self.db_manager = db_manager
    
    def save_analysis_result(self, task_id, analysis_type, filename, total_users, 
                           device_shared_count, ip_shared_count, both_shared_count, result_data):
        """保存代理关系分析结果"""
        try:
            # 将result_data转换为JSON字符串
            result_json = json.dumps(result_data, ensure_ascii=False)
            
            # 先检查是否已存在
            existing = self.get_analysis_result(task_id)
            
            if existing:
                # 更新现有记录
                sql = """
                    UPDATE agent_analysis 
                    SET analysis_type = ?, filename = ?, total_users = ?, 
                        device_shared_count = ?, ip_shared_count = ?, both_shared_count = ?, 
                        result_data = ?, updated_at = ?
                    WHERE task_id = ?
                """
                params = [
                    analysis_type, filename, total_users, device_shared_count,
                    ip_shared_count, both_shared_count, result_json,
                    datetime.now().isoformat(), task_id
                ]
            else:
                # 插入新记录
                sql = """
                    INSERT INTO agent_analysis 
                    (task_id, analysis_type, filename, total_users, device_shared_count, 
                     ip_shared_count, both_shared_count, result_data, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = [
                    task_id, analysis_type, filename, total_users, device_shared_count,
                    ip_shared_count, both_shared_count, result_json,
                    datetime.now().isoformat(), datetime.now().isoformat()
                ]
            
            self.db_manager.execute_sql(sql, params)
            return True
            
        except Exception as e:
            logger.error(f"保存代理关系分析结果失败: {e}")
            return False
    
    def get_analysis_result(self, task_id):
        """获取代理关系分析结果"""
        try:
            sql = "SELECT * FROM agent_analysis WHERE task_id = ?"
            results = self.db_manager.execute_sql(sql, [task_id])
            
            if results:
                result_dict = results[0]  # execute_sql已经返回字典列表
                
                # 解析JSON数据
                if result_dict.get('result_data'):
                    try:
                        result_dict['result_data'] = json.loads(result_dict['result_data'])
                    except json.JSONDecodeError:
                        result_dict['result_data'] = {}
                
                return result_dict
            
            return None
            
        except Exception as e:
            logger.error(f"获取代理关系分析结果失败: {e}")
            return None
    
    def get_all_analysis_results(self, limit=100, offset=0):
        """获取所有代理关系分析结果"""
        try:
            sql = """
                SELECT task_id, analysis_type, filename, total_users, device_shared_count,
                       ip_shared_count, both_shared_count, created_at, updated_at
                FROM agent_analysis 
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """
            
            results = self.db_manager.execute_sql(sql, [limit, offset])
            return results  # execute_sql已经返回字典列表
            
        except Exception as e:
            logger.error(f"获取代理关系分析结果列表失败: {e}")
            return []
    
    def delete_analysis_result(self, task_id):
        """删除代理关系分析结果"""
        try:
            sql = "DELETE FROM agent_analysis WHERE task_id = ?"
            self.db_manager.execute_sql(sql, [task_id])
            return True
            
        except Exception as e:
            logger.error(f"删除代理关系分析结果失败: {e}")
            return False
    
    def get_statistics(self):
        """获取代理关系分析统计信息"""
        try:
            sql = """
                SELECT 
                    COUNT(*) as total_analyses,
                    SUM(total_users) as total_users_analyzed,
                    SUM(device_shared_count) as total_device_shared,
                    SUM(ip_shared_count) as total_ip_shared,
                    SUM(both_shared_count) as total_both_shared,
                    AVG(CAST(device_shared_count AS FLOAT) / NULLIF(total_users, 0) * 100) as avg_device_share_rate
                FROM agent_analysis
            """
            
            results = self.db_manager.execute_sql(sql)
            
            if results:
                return results[0]  # execute_sql已经返回字典列表
            
            return {}
            
        except Exception as e:
            logger.error(f"获取代理关系分析统计信息失败: {e}")
            return {}

# 创建全局实例
agent_repository = AgentRepository() 
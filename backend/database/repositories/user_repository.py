"""
用户数据仓库
处理用户和用户关系的数据库操作
"""
from typing import List, Dict, Optional, Any
from ..duckdb_manager import db_manager
import json
import logging

logger = logging.getLogger(__name__)

class UserRepository:
    """用户数据仓库"""
    
    def batch_insert_users(self, users: List[Dict[str, Any]]) -> None:
        """批量插入用户数据"""
        if not users:
            return
        
        sql = """
        INSERT OR IGNORE INTO users (
            digital_id, member_id, user_name, agent_flag, analyzed_level_number,
            analyzed_level_type, bd_name, device_count, risk_level, risk_score, relation_count
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params_list = []
        for user in users:
            params_list.append([
                user.get('digital_id'),
                user.get('member_id'),
                user.get('user_name'),
                user.get('agent_flag'),
                user.get('analyzed_level_number'),
                user.get('analyzed_level_type'),
                user.get('bd_name'),
                user.get('device_count', 0),
                user.get('risk_level'),
                user.get('risk_score'),
                user.get('relation_count', 0)
            ])
        
        # 分批处理，避免一次插入过多数据导致锁定时间过长
        batch_size = 1000
        total_processed = 0
        
        for i in range(0, len(params_list), batch_size):
            batch = params_list[i:i + batch_size]
            try:
                db_manager.execute_many(sql, batch)
                total_processed += len(batch)
                logger.info(f"成功插入第{i//batch_size + 1}批用户数据: {len(batch)}条")
            except Exception as e:
                logger.error(f"插入第{i//batch_size + 1}批用户数据失败: {e}")
                # 继续处理下一批，而不是完全失败
                continue
        
        logger.info(f"批量插入用户完成: 总计{total_processed}/{len(params_list)}个用户")
    
    def batch_insert_relationships(self, relationships: List[Dict[str, Any]]) -> None:
        """批量插入用户关系数据"""
        if not relationships:
            return
        
        # 先获取当前最大ID
        max_id_sql = "SELECT COALESCE(MAX(id), 0) as max_id FROM user_relationships"
        max_id_result = db_manager.execute_sql(max_id_sql)
        current_max_id = max_id_result[0]['max_id'] if max_id_result else 0
        
        sql = """
        INSERT OR IGNORE INTO user_relationships (
            id, parent_digital_id, child_digital_id, relationship_type, level_diff
        ) VALUES (?, ?, ?, ?, ?)
        """
        
        params_list = []
        for i, rel in enumerate(relationships):
            params_list.append([
                current_max_id + i + 1,  # 手动生成ID
                rel.get('parent_digital_id'),
                rel.get('child_digital_id'),
                rel.get('relationship_type'),
                rel.get('level_diff', 1)
            ])
        
        db_manager.execute_many(sql, params_list)
        logger.info(f"批量插入{len(relationships)}个用户关系")
    
    def get_bd_users(self) -> List[Dict[str, Any]]:
        """获取所有BD用户"""
        sql = """
        SELECT * FROM users 
        WHERE agent_flag = 'BD' OR agent_flag LIKE '%BD%'
        ORDER BY bd_name, user_name
        """
        return db_manager.execute_sql(sql)
    
    def get_users_by_bd(self, bd_name: str) -> List[Dict[str, Any]]:
        """获取指定BD下的所有用户"""
        sql = """
        SELECT * FROM users 
        WHERE bd_name = ?
        ORDER BY analyzed_level_number, user_name
        """
        return db_manager.execute_sql(sql, [bd_name])
    
    def get_user_hierarchy(self, digital_id: str) -> Dict[str, Any]:
        """获取用户的完整层级结构"""
        # 获取用户基本信息
        user_sql = "SELECT * FROM users WHERE digital_id = ?"
        user_result = db_manager.execute_sql(user_sql, [digital_id])
        if not user_result:
            return None
        
        user_data = user_result[0]
        
        # 获取直接下级
        children_sql = """
        SELECT u.* FROM users u
        JOIN user_relationships r ON u.digital_id = r.child_digital_id
        WHERE r.parent_digital_id = ?
        ORDER BY u.analyzed_level_number, u.user_name
        """
        children_data = db_manager.execute_sql(children_sql, [digital_id])
        
        # 递归获取每个下级的层级结构
        children = []
        for child_data in children_data:
            child_hierarchy = self.get_user_hierarchy(child_data['digital_id'])
            if child_hierarchy:
                children.append(child_hierarchy)
        
        # 构建符合链路分析期望的数据结构
        return {
            'user_info': user_data,  # 将用户信息嵌套在user_info中
            'children': children
        }
    
    def get_bd_statistics(self) -> Dict[str, Any]:
        """获取BD统计信息"""
        stats_sql = """
        SELECT 
            bd_name,
            COUNT(*) as total_users,
            COUNT(CASE WHEN agent_flag = 'BD' THEN 1 END) as bd_count,
            COUNT(CASE WHEN agent_flag = '1级代理' THEN 1 END) as level1_count,
            COUNT(CASE WHEN agent_flag = '2级代理' THEN 1 END) as level2_count,
            COUNT(CASE WHEN agent_flag = '3级代理' THEN 1 END) as level3_count,
            COUNT(CASE WHEN agent_flag = '直客' THEN 1 END) as direct_count,
            AVG(risk_score) as avg_risk_score,
            SUM(device_count) as total_devices
        FROM users 
        WHERE bd_name IS NOT NULL
        GROUP BY bd_name
        ORDER BY total_users DESC
        """
        return db_manager.execute_sql(stats_sql)
    
    def search_users(self, keyword: str, agent_flag: Optional[str] = None) -> List[Dict[str, Any]]:
        """搜索用户"""
        base_sql = """
        SELECT * FROM users 
        WHERE (user_name LIKE ? OR member_id LIKE ? OR digital_id LIKE ?)
        """
        params = [f'%{keyword}%', f'%{keyword}%', f'%{keyword}%']
        
        if agent_flag:
            base_sql += " AND agent_flag = ?"
            params.append(agent_flag)
        
        base_sql += " ORDER BY user_name LIMIT 100"
        
        return db_manager.execute_sql(base_sql, params)
    
    def get_high_risk_users(self, risk_threshold: float = 70.0) -> List[Dict[str, Any]]:
        """获取高风险用户"""
        sql = """
        SELECT * FROM users 
        WHERE risk_score >= ?
        ORDER BY risk_score DESC
        """
        return db_manager.execute_sql(sql, [risk_threshold])
    
    def get_user_by_member_id(self, member_id: str) -> Optional[Dict[str, Any]]:
        """根据member_id获取用户"""
        sql = "SELECT * FROM users WHERE member_id = ?"
        result = db_manager.execute_sql(sql, [member_id])
        return result[0] if result else None
    
    def update_user_risk_score(self, digital_id: str, risk_score: float) -> None:
        """更新用户风险评分"""
        sql = """
        UPDATE users 
        SET risk_score = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE digital_id = ?
        """
        db_manager.execute_sql(sql, [risk_score, digital_id])

    # 🆕 统一数据管理方法
    def get_users_with_pagination(self, page: int = 1, page_size: int = 1000, bd_filter: str = '') -> Dict[str, Any]:
        """分页获取用户数据"""
        offset = (page - 1) * page_size
        
        base_sql = "SELECT * FROM users"
        count_sql = "SELECT COUNT(*) as total FROM users"
        params = []
        
        if bd_filter:
            where_clause = " WHERE bd_name = ?"
            base_sql += where_clause
            count_sql += where_clause
            params.append(bd_filter)
        
        # 获取总数
        total_result = db_manager.execute_sql(count_sql, params)
        total = total_result[0]['total'] if total_result else 0
        
        # 获取分页数据
        base_sql += " ORDER BY bd_name, analyzed_level_number, user_name LIMIT ? OFFSET ?"
        params.extend([page_size, offset])
        
        users = db_manager.execute_sql(base_sql, params)
        
        return {
            'users': users,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total,
                'total_pages': (total + page_size - 1) // page_size
            }
        }
    
    def get_relationships_with_pagination(self, page: int = 1, page_size: int = 1000, bd_filter: str = '') -> Dict[str, Any]:
        """分页获取关系数据"""
        offset = (page - 1) * page_size
        
        base_sql = """
        SELECT r.*, 
               p.user_name as parent_name, p.bd_name as parent_bd,
               c.user_name as child_name, c.bd_name as child_bd
        FROM user_relationships r
        LEFT JOIN users p ON r.parent_digital_id = p.digital_id
        LEFT JOIN users c ON r.child_digital_id = c.digital_id
        """
        
        count_sql = """
        SELECT COUNT(*) as total
        FROM user_relationships r
        LEFT JOIN users p ON r.parent_digital_id = p.digital_id
        LEFT JOIN users c ON r.child_digital_id = c.digital_id
        """
        
        params = []
        
        if bd_filter:
            where_clause = " WHERE p.bd_name = ? OR c.bd_name = ?"
            base_sql += where_clause
            count_sql += where_clause
            params.extend([bd_filter, bd_filter])
        
        # 获取总数
        total_result = db_manager.execute_sql(count_sql, params)
        total = total_result[0]['total'] if total_result else 0
        
        # 获取分页数据
        base_sql += " ORDER BY p.bd_name, r.level_diff LIMIT ? OFFSET ?"
        params.extend([page_size, offset])
        
        relationships = db_manager.execute_sql(base_sql, params)
        
        return {
            'relationships': relationships,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total,
                'total_pages': (total + page_size - 1) // page_size
            }
        }
    
    def get_bd_pyramid_data_optimized(self, bd_filter: str = '') -> Dict[str, Any]:
        """获取BD金字塔数据 - 优化版本（非递归，高性能）"""
        try:
            logger.debug("开始BD金字塔数据获取")
            
            # 1. 一次性获取所有用户数据
            user_sql = "SELECT * FROM users"
            params = []
            if bd_filter:
                user_sql += " WHERE bd_name = ?"
                params.append(bd_filter)
            
            all_users = db_manager.execute_sql(user_sql, params)
            logger.debug(f"获取到 {len(all_users)} 个用户")
            
            # 2. 一次性获取所有关系数据
            rel_sql = "SELECT * FROM user_relationships"
            all_relationships = db_manager.execute_sql(rel_sql)
            logger.debug(f"获取到 {len(all_relationships)} 个关系")
            
            # 3. 构建用户字典和关系映射
            user_dict = {user['digital_id']: user for user in all_users}
            children_map = {}  # parent_id -> [child_ids]
            
            for rel in all_relationships:
                parent_id = rel['parent_digital_id']
                child_id = rel['child_digital_id']
                
                if parent_id not in children_map:
                    children_map[parent_id] = []
                children_map[parent_id].append(child_id)
            
            # 4. 找到BD根节点 - 修复识别逻辑
            bd_roots = []
            for user in all_users:
                # 修正BD根节点识别：使用analyzed_level_number = 1，而不是agent_flag = 'BD'
                if user.get('analyzed_level_number') == 1:
                    if not bd_filter or user.get('bd_name') == bd_filter:
                        # 确保用户有合适的显示名称
                        if not user.get('user_name') or user.get('user_name').strip() == '':
                            # 如果没有用户名，使用digital_id或bd_name作为显示名称
                            display_name = user.get('bd_name') or f"用户_{user.get('digital_id', 'Unknown')}"
                            user['user_name'] = display_name
                        bd_roots.append(user)
            
            logger.debug(f"找到 {len(bd_roots)} 个BD根节点")
            
            # 5. 使用非递归方式构建金字塔
            bd_trees = []
            for bd_root in bd_roots:
                tree = self._build_tree_non_recursive(bd_root, user_dict, children_map)
                if tree:
                    bd_trees.append(tree)
            
            result = {
                'bd_trees': bd_trees,
                'total_bd_count': len(bd_trees),
                'bd_filter': bd_filter
            }
            
            logger.info(f"成功构建 {len(bd_trees)} 个BD金字塔")
            return result
            
        except Exception as e:
            logger.error(f"BD金字塔数据获取失败: {str(e)}")
            return {'bd_trees': [], 'total_bd_count': 0, 'bd_filter': bd_filter}
    
    def _build_tree_non_recursive(self, root_user: Dict[str, Any], user_dict: Dict[str, Dict], children_map: Dict[str, List]) -> Dict[str, Any]:
        """使用非递归方式构建用户层级树"""
        try:
            # 使用队列进行广度优先遍历，避免递归栈溢出
            from collections import deque
            
            # 构建根节点
            root_tree = {
                'user_info': root_user,
                'children': []
            }
            
            # 队列存储 (tree_node, user_id, depth)
            queue = deque([(root_tree, root_user['digital_id'], 0)])
            visited = set()  # 防止循环引用
            max_depth = 10   # 限制最大深度，防止无限循环
            
            while queue:
                current_tree, current_user_id, depth = queue.popleft()
                
                # 防止循环引用和深度过深
                if current_user_id in visited or depth >= max_depth:
                    continue
                
                visited.add(current_user_id)
                
                # 获取子节点
                child_ids = children_map.get(current_user_id, [])
                
                for child_id in child_ids:
                    if child_id in user_dict and child_id not in visited:
                        child_user = user_dict[child_id]
                        child_tree = {
                            'user_info': child_user,
                            'children': []
                        }
                        current_tree['children'].append(child_tree)
                        
                        # 将子节点加入队列继续处理
                        queue.append((child_tree, child_id, depth + 1))
            
            return root_tree
            
        except Exception as e:
            logger.warning(f"构建树结构失败: {str(e)}")
            return {
                'user_info': root_user,
                'children': []
            }

    def get_bd_pyramid_data(self, bd_filter: str = '') -> Dict[str, Any]:
        """获取BD金字塔数据 - 使用优化版本"""
        return self.get_bd_pyramid_data_optimized(bd_filter)
    
    def get_data_statistics(self, bd_filter: str = '') -> Dict[str, Any]:
        """获取数据统计信息"""
        base_where = ""
        params = []
        
        if bd_filter:
            base_where = " WHERE bd_name = ?"
            params.append(bd_filter)
        
        # 用户统计
        user_stats_sql = f"""
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN agent_flag = 'BD' THEN 1 END) as bd_count,
            COUNT(CASE WHEN agent_flag = '1级代理' THEN 1 END) as level1_count,
            COUNT(CASE WHEN agent_flag = '2级代理' THEN 1 END) as level2_count,
            COUNT(CASE WHEN agent_flag = '3级代理' THEN 1 END) as level3_count,
            COUNT(CASE WHEN agent_flag = '直客' THEN 1 END) as direct_count,
            AVG(risk_score) as avg_risk_score,
            MAX(risk_score) as max_risk_score,
            SUM(device_count) as total_devices
        FROM users{base_where}
        """
        
        user_stats = db_manager.execute_sql(user_stats_sql, params)
        
        # 关系统计
        rel_stats_sql = f"""
        SELECT COUNT(*) as total_relationships
        FROM user_relationships r
        LEFT JOIN users u ON r.parent_digital_id = u.digital_id
        {base_where.replace('bd_name', 'u.bd_name') if base_where else ''}
        """
        
        rel_stats = db_manager.execute_sql(rel_stats_sql, params)
        
        return {
            'users': user_stats[0] if user_stats else {},
            'relationships': rel_stats[0] if rel_stats else {},
            'bd_filter': bd_filter
        }
    
    def get_table_statistics(self) -> Dict[str, Any]:
        """获取表统计信息"""
        user_count_sql = "SELECT COUNT(*) as count FROM users"
        rel_count_sql = "SELECT COUNT(*) as count FROM user_relationships"
        
        user_count = db_manager.execute_sql(user_count_sql)
        rel_count = db_manager.execute_sql(rel_count_sql)
        
        return {
            'total_users': user_count[0]['count'] if user_count else 0,
            'total_relationships': rel_count[0]['count'] if rel_count else 0,
            'last_updated': None  # 可以从表的元数据获取
        }
    
    def get_bd_list(self) -> List[str]:
        """获取所有BD名称列表"""
        sql = """
        SELECT DISTINCT bd_name 
        FROM users 
        WHERE bd_name IS NOT NULL AND bd_name != ''
        ORDER BY bd_name
        """
        result = db_manager.execute_sql(sql)
        return [row['bd_name'] for row in result] if result else []

# 创建全局用户仓库实例
user_repository = UserRepository() 
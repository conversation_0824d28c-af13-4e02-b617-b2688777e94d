-- 权限管理系统数据库结构设计
-- 实现细粒度的角色权限控制

-- 1. 权限资源表 - 定义系统中所有可控制的资源
CREATE TABLE IF NOT EXISTS auth_permissions (
    id INTEGER PRIMARY KEY,
    resource_name VARCHAR(100) NOT NULL UNIQUE,  -- 资源名称，如 'user_management', 'system_config'
    resource_type VARCHAR(50) NOT NULL,          -- 资源类型：module, page, api, data
    parent_id INTEGER,                           -- 父资源ID，支持层级结构
    display_name VARCHAR(200) NOT NULL,          -- 显示名称
    description TEXT,                            -- 权限描述
    is_active BOOLEAN DEFAULT true,              -- 是否启用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES auth_permissions(id)
);

-- 2. 操作类型表 - 定义对资源可执行的操作
CREATE TABLE IF NOT EXISTS auth_actions (
    id INTEGER PRIMARY KEY,
    action_name VARCHAR(50) NOT NULL UNIQUE,     -- 操作名称：create, read, update, delete, execute
    display_name VARCHAR(100) NOT NULL,          -- 显示名称
    description TEXT,                            -- 操作描述
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 角色表 - 扩展现有角色系统
CREATE TABLE IF NOT EXISTS auth_roles (
    id INTEGER PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,       -- 角色名称：admin, viewer, editor, auditor
    display_name VARCHAR(100) NOT NULL,          -- 显示名称
    description TEXT,                            -- 角色描述
    is_system_role BOOLEAN DEFAULT false,        -- 是否为系统内置角色
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 角色权限关联表 - 角色与权限的多对多关系
CREATE TABLE IF NOT EXISTS auth_role_permissions (
    id INTEGER PRIMARY KEY,
    role_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    action_id INTEGER NOT NULL,
    is_granted BOOLEAN DEFAULT true,              -- 是否授予权限
    is_inherited BOOLEAN DEFAULT false,           -- 是否为继承权限
    granted_by INTEGER,                          -- 授权人ID
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,                        -- 权限过期时间
    FOREIGN KEY (role_id) REFERENCES auth_roles(id),
    FOREIGN KEY (permission_id) REFERENCES auth_permissions(id),
    FOREIGN KEY (action_id) REFERENCES auth_actions(id),
    FOREIGN KEY (granted_by) REFERENCES auth_users(id),
    UNIQUE(role_id, permission_id, action_id)
);

-- 5. 用户角色关联表 - 用户与角色的多对多关系
CREATE TABLE IF NOT EXISTS auth_user_roles (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    assigned_by INTEGER,                         -- 分配人ID
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,                        -- 角色过期时间
    is_active BOOLEAN DEFAULT true,
    FOREIGN KEY (user_id) REFERENCES auth_users(id),
    FOREIGN KEY (role_id) REFERENCES auth_roles(id),
    FOREIGN KEY (assigned_by) REFERENCES auth_users(id),
    UNIQUE(user_id, role_id)
);

-- 6. 用户特殊权限表 - 用户级别的特殊权限覆盖
CREATE TABLE IF NOT EXISTS auth_user_permissions (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    action_id INTEGER NOT NULL,
    is_granted BOOLEAN DEFAULT true,              -- 是否授予权限
    granted_by INTEGER,                          -- 授权人ID
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,                        -- 权限过期时间
    reason TEXT,                                 -- 授权原因
    FOREIGN KEY (user_id) REFERENCES auth_users(id),
    FOREIGN KEY (permission_id) REFERENCES auth_permissions(id),
    FOREIGN KEY (action_id) REFERENCES auth_actions(id),
    FOREIGN KEY (granted_by) REFERENCES auth_users(id),
    UNIQUE(user_id, permission_id, action_id)
);

-- 7. 权限组表 - 权限的逻辑分组
CREATE TABLE IF NOT EXISTS auth_permission_groups (
    id INTEGER PRIMARY KEY,
    group_name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. 权限组关联表
CREATE TABLE IF NOT EXISTS auth_permission_group_items (
    id INTEGER PRIMARY KEY,
    group_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    sort_order INTEGER DEFAULT 0,
    FOREIGN KEY (group_id) REFERENCES auth_permission_groups(id),
    FOREIGN KEY (permission_id) REFERENCES auth_permissions(id),
    UNIQUE(group_id, permission_id)
);

-- 插入基础数据

-- 插入基础操作类型
INSERT OR IGNORE INTO auth_actions (action_name, display_name, description) VALUES
('create', '创建', '创建新资源的权限'),
('read', '查看', '查看资源的权限'),
('update', '编辑', '修改资源的权限'),
('delete', '删除', '删除资源的权限'),
('execute', '执行', '执行特定操作的权限'),
('export', '导出', '导出数据的权限'),
('import', '导入', '导入数据的权限'),
('approve', '审批', '审批操作的权限');

-- 插入基础角色
INSERT OR IGNORE INTO auth_roles (role_name, display_name, description, is_system_role) VALUES
('super_admin', '超级管理员', '拥有系统所有权限的最高管理员', true),
('admin', '管理员', '系统管理员，拥有大部分管理权限', true),
('editor', '编辑员', '可以编辑内容但权限有限的用户', true),
('viewer', '查看者', '只能查看内容的普通用户', true),
('auditor', '审计员', '专门负责审计和监控的用户', true);

-- 插入权限组
INSERT OR IGNORE INTO auth_permission_groups (group_name, display_name, description, sort_order) VALUES
('user_management', '用户管理', '用户账户和权限管理相关功能', 1),
('system_management', '系统管理', '系统配置和监控相关功能', 2),
('data_analysis', '数据分析', '数据分析和报告相关功能', 3),
('security_audit', '安全审计', '安全审计和日志相关功能', 4);

-- 插入基础权限资源
INSERT OR IGNORE INTO auth_permissions (resource_name, resource_type, display_name, description) VALUES
-- 用户管理模块
('user_management', 'module', '用户管理', '用户管理模块的访问权限'),
('user_list', 'page', '用户列表', '查看用户列表页面'),
('user_create', 'api', '创建用户', '创建新用户的API权限'),
('user_update', 'api', '更新用户', '更新用户信息的API权限'),
('user_delete', 'api', '删除用户', '删除用户的API权限'),
('user_reset_password', 'api', '重置密码', '重置用户密码的权限'),

-- 系统管理模块
('system_management', 'module', '系统管理', '系统管理模块的访问权限'),
('system_dashboard', 'page', '系统控制台', '访问系统控制台页面'),
('system_config', 'page', '系统配置', '系统配置页面访问权限'),
('activity_logs', 'page', '操作日志', '查看操作日志页面'),
('session_management', 'page', '会话管理', '会话管理页面访问权限'),

-- 数据分析模块
('data_analysis', 'module', '数据分析', '数据分析模块的访问权限'),
('agent_analysis', 'page', '代理分析', '代理关系分析页面'),
('contract_analysis', 'page', '合约分析', '合约分析页面'),
('link_analysis', 'page', '链路分析', '链路分析页面'),
('user_analysis', 'page', '用户分析', '用户行为分析页面'),

-- API权限
('api_user_management', 'api', '用户管理API', '用户管理相关API权限'),
('api_system_management', 'api', '系统管理API', '系统管理相关API权限'),
('api_data_export', 'api', '数据导出API', '数据导出相关API权限'),
('api_data_import', 'api', '数据导入API', '数据导入相关API权限');

-- 建立权限组关联
INSERT OR IGNORE INTO auth_permission_group_items (group_id, permission_id, sort_order) 
SELECT 
    pg.id,
    p.id,
    ROW_NUMBER() OVER (PARTITION BY pg.id ORDER BY p.id)
FROM auth_permission_groups pg
CROSS JOIN auth_permissions p
WHERE 
    (pg.group_name = 'user_management' AND p.resource_name LIKE 'user_%') OR
    (pg.group_name = 'system_management' AND p.resource_name LIKE 'system_%') OR
    (pg.group_name = 'data_analysis' AND p.resource_name LIKE '%_analysis') OR
    (pg.group_name = 'security_audit' AND p.resource_name IN ('activity_logs', 'session_management'));

-- 为超级管理员角色分配所有权限
INSERT OR IGNORE INTO auth_role_permissions (role_id, permission_id, action_id, is_granted)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    a.id as action_id,
    true as is_granted
FROM auth_roles r
CROSS JOIN auth_permissions p
CROSS JOIN auth_actions a
WHERE r.role_name = 'super_admin';

-- 为管理员角色分配基础管理权限
INSERT OR IGNORE INTO auth_role_permissions (role_id, permission_id, action_id, is_granted)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    a.id as action_id,
    true as is_granted
FROM auth_roles r
CROSS JOIN auth_permissions p
CROSS JOIN auth_actions a
WHERE r.role_name = 'admin' 
AND p.resource_name IN ('user_management', 'system_management', 'system_dashboard', 'user_list', 'activity_logs')
AND a.action_name IN ('read', 'create', 'update', 'delete', 'execute');

-- 为查看者角色分配只读权限
INSERT OR IGNORE INTO auth_role_permissions (role_id, permission_id, action_id, is_granted)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    a.id as action_id,
    true as is_granted
FROM auth_roles r
CROSS JOIN auth_permissions p
CROSS JOIN auth_actions a
WHERE r.role_name = 'viewer' 
AND p.resource_type IN ('page', 'module')
AND p.resource_name LIKE '%_analysis'
AND a.action_name = 'read';

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON auth_role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON auth_role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON auth_user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON auth_user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON auth_user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_name ON auth_permissions(resource_name);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_type ON auth_permissions(resource_type);

-- 创建视图简化权限查询
CREATE VIEW IF NOT EXISTS v_user_permissions AS
SELECT DISTINCT
    u.id as user_id,
    u.username,
    p.resource_name,
    p.resource_type,
    p.display_name as permission_name,
    a.action_name,
    a.display_name as action_name_display,
    CASE 
        WHEN up.is_granted IS NOT NULL THEN up.is_granted
        ELSE rp.is_granted
    END as is_granted,
    CASE 
        WHEN up.id IS NOT NULL THEN 'user'
        ELSE 'role'
    END as permission_source
FROM auth_users u
LEFT JOIN auth_user_roles ur ON u.id = ur.user_id AND ur.is_active = true
LEFT JOIN auth_role_permissions rp ON ur.role_id = rp.role_id AND rp.is_granted = true
LEFT JOIN auth_permissions p ON rp.permission_id = p.id AND p.is_active = true
LEFT JOIN auth_actions a ON rp.action_id = a.id AND a.is_active = true
LEFT JOIN auth_user_permissions up ON u.id = up.user_id 
    AND p.id = up.permission_id 
    AND a.id = up.action_id
WHERE u.is_active = true
AND (ur.expires_at IS NULL OR ur.expires_at > CURRENT_TIMESTAMP)
AND (up.expires_at IS NULL OR up.expires_at > CURRENT_TIMESTAMP);

-- 创建权限检查函数的存储过程（DuckDB不支持存储过程，这里用注释说明逻辑）
/*
权限检查逻辑：
1. 首先检查用户特殊权限表 auth_user_permissions
2. 如果没有特殊权限，则检查用户角色权限
3. 支持权限继承和覆盖
4. 考虑权限过期时间
5. 返回最终的权限结果
*/

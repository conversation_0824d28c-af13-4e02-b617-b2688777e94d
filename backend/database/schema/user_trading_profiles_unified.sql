-- =====================================================
-- user_trading_profiles表统一创建脚本
-- 版本: 3.0 - 统一完整版本 (包含所有102个字段)
-- 创建时间: 2025-06-28
-- 用途: 数据库重建时的统一初始化脚本
-- =====================================================

-- 创建序列（PostgreSQL兼容）
CREATE SEQUENCE IF NOT EXISTS user_trading_profiles_id_seq;

-- 创建完整的user_trading_profiles表
CREATE TABLE IF NOT EXISTS user_trading_profiles (
    -- 基础字段
    id INTEGER PRIMARY KEY DEFAULT nextval('user_trading_profiles_id_seq'),
    member_id VARCHAR(64) NOT NULL,
    analysis_date DATE NOT NULL,
    analysis_period_start TIMESTAMP NOT NULL,
    analysis_period_end TIMESTAMP NOT NULL,
    
    -- 基础数据统计 (原始字段)
    total_positions INTEGER DEFAULT 0,
    completed_positions INTEGER DEFAULT 0,
    total_volume DECIMAL(20,2) DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    avg_trade_size DECIMAL(15,2) DEFAULT 0,
    total_commission DECIMAL(15,2) DEFAULT 0,
    
    -- 基础数据统计 (新增字段)
    max_trade_size DECIMAL(15,2) DEFAULT 0,
    min_trade_size DECIMAL(15,2) DEFAULT 0,
    total_pnl DECIMAL(15,2) DEFAULT 0,
    profit_trades_ratio DECIMAL(5,4) DEFAULT 0,
    loss_trades_ratio DECIMAL(5,4) DEFAULT 0,
    avg_profit_per_trade DECIMAL(15,2) DEFAULT 0,
    avg_loss_per_trade DECIMAL(15,2) DEFAULT 0,
    return_rate DECIMAL(8,4) DEFAULT 0,
    fee_ratio DECIMAL(5,4) DEFAULT 0,
    
    -- 盈亏指标
    profitable_count INTEGER DEFAULT 0,
    loss_count INTEGER DEFAULT 0,
    total_profit DECIMAL(15,2) DEFAULT 0,
    total_loss DECIMAL(15,2) DEFAULT 0,
    win_rate DECIMAL(5,4) DEFAULT 0,
    profit_loss_ratio DECIMAL(8,4) DEFAULT 0,
    profit_factor DECIMAL(8,4) DEFAULT 0,
    profit_consistency DECIMAL(5,4) DEFAULT 0,
    
    -- 时间指标
    avg_profit_duration_minutes INTEGER DEFAULT 0,
    avg_loss_duration_minutes INTEGER DEFAULT 0,
    profit_loss_duration_ratio DECIMAL(8,4) DEFAULT 0,
    total_trading_days INTEGER DEFAULT 0,
    trading_frequency DECIMAL(8,4) DEFAULT 0,
    max_holding_time INTEGER DEFAULT 0,
    min_holding_time INTEGER DEFAULT 0,
    
    -- 订单类型分布
    market_orders_ratio DECIMAL(5,4) DEFAULT 0,
    limit_orders_ratio DECIMAL(5,4) DEFAULT 0,
    open_market_orders INTEGER DEFAULT 0,
    open_limit_orders INTEGER DEFAULT 0,
    close_market_orders INTEGER DEFAULT 0,
    close_limit_orders INTEGER DEFAULT 0,

    -- 仓位模式分布
    cross_margin_positions INTEGER DEFAULT 0,     -- 全仓模式持仓数量
    isolated_margin_positions INTEGER DEFAULT 0,  -- 逐仓模式持仓数量
    cross_margin_ratio DECIMAL(5,4) DEFAULT 0,    -- 全仓模式比例
    isolated_margin_ratio DECIMAL(5,4) DEFAULT 0, -- 逐仓模式比例
    
    -- 杠杆分析
    avg_leverage DECIMAL(8,4) DEFAULT 0,
    max_leverage DECIMAL(8,4) DEFAULT 0,
    leverage_stability DECIMAL(5,4) DEFAULT 0,
    max_single_loss DECIMAL(15,2) DEFAULT 0,
    max_single_loss_ratio DECIMAL(8,6) DEFAULT 0,
    low_leverage_trades INTEGER DEFAULT 0,
    medium_leverage_trades INTEGER DEFAULT 0,
    high_leverage_trades INTEGER DEFAULT 0,
    
    -- 对冲数据统计
    hedge_positions_count INTEGER DEFAULT 0,
    concurrent_positions_count INTEGER DEFAULT 0,
    hedge_contracts TEXT,
    
    -- 交易偏好
    major_coins_ratio DECIMAL(5,4) DEFAULT 0,
    altcoins_ratio DECIMAL(5,4) DEFAULT 0,
    diversification_score DECIMAL(5,4) DEFAULT 0,
    favorite_contracts TEXT,
    peak_trading_hours TEXT,
    defi_percentage DECIMAL(5,4) DEFAULT 0,
    others_percentage DECIMAL(5,4) DEFAULT 0,
    risk_appetite_level VARCHAR(20) DEFAULT 'medium',
    volatility_preference DECIMAL(5,4) DEFAULT 0,
    
    -- 个人币种胜率分析
    coin_win_rate_analysis VARCHAR,  -- 修改：TEXT -> VARCHAR 匹配数据库实际类型
    advantage_coins VARCHAR,         -- 修改：TEXT -> VARCHAR 匹配数据库实际类型
    expert_coins VARCHAR,            -- 修改：TEXT -> VARCHAR 匹配数据库实际类型
    total_analyzed_coins INTEGER DEFAULT 0,
    avg_coin_win_rate DECIMAL(5,4) DEFAULT 0,
    
    -- 资金规模分类
    fund_scale_category VARCHAR(20) DEFAULT '数据不足',
    real_trading_volume DECIMAL(20,2) DEFAULT 0,

    -- 交易规模分布
    small_trades INTEGER DEFAULT 0,
    medium_trades INTEGER DEFAULT 0,
    large_trades INTEGER DEFAULT 0,
    small_trades_ratio DECIMAL(5,4) DEFAULT 0,
    medium_trades_ratio DECIMAL(5,4) DEFAULT 0,
    large_trades_ratio DECIMAL(5,4) DEFAULT 0,

    -- 专业度评分
    professional_score DECIMAL(5,2) DEFAULT 0,
    profitability_score DECIMAL(5,2) DEFAULT 0,
    risk_control_score DECIMAL(5,2) DEFAULT 0,
    trading_behavior_score DECIMAL(5,2) DEFAULT 0,
    market_understanding_score DECIMAL(5,2) DEFAULT 0,
    
    -- 详细评分字段
    win_rate_score DECIMAL(5,2) DEFAULT 0,
    profit_loss_ratio_score DECIMAL(5,2) DEFAULT 0,
    profit_factor_score DECIMAL(5,2) DEFAULT 0,
    profit_consistency_score DECIMAL(5,2) DEFAULT 0,
    avg_leverage_score DECIMAL(5,2) DEFAULT 0,
    max_leverage_score DECIMAL(5,2) DEFAULT 0,
    leverage_stability_score DECIMAL(5,2) DEFAULT 0,
    trading_frequency_score DECIMAL(5,2) DEFAULT 0,
    market_order_ratio_score DECIMAL(5,2) DEFAULT 0,
    duration_ratio_score DECIMAL(5,2) DEFAULT 0,
    position_consistency_score DECIMAL(5,2) DEFAULT 0,
    timing_ability_score DECIMAL(5,2) DEFAULT 0,
    risk_discipline_score DECIMAL(5,2) DEFAULT 0,
    execution_efficiency_score DECIMAL(5,2) DEFAULT 0,
    
    -- 用户分类
    trader_type VARCHAR(32) DEFAULT '数据不足',
    confidence_level DECIMAL(5,4) DEFAULT 0,
    
    -- 异常交易分析
    abnormal_volume DECIMAL(20,2) DEFAULT 0,
    abnormal_ratio DECIMAL(5,4) DEFAULT 0,
    wash_trading_volume DECIMAL(20,2) DEFAULT 0,
    high_frequency_volume DECIMAL(20,2) DEFAULT 0,
    funding_arbitrage_volume DECIMAL(20,2) DEFAULT 0,
    risk_events_count INTEGER DEFAULT 0,
    
    -- 高级分析指标
    position_consistency DECIMAL(5,4) DEFAULT 0,
    timing_ability DECIMAL(5,4) DEFAULT 0,
    risk_discipline DECIMAL(5,4) DEFAULT 0,
    execution_efficiency DECIMAL(5,4) DEFAULT 0,
    
    -- 个人分析专用字段 (新增)
    last_activity_time TIMESTAMP DEFAULT NULL,
    max_single_loss_score DECIMAL(5,2) DEFAULT 0,
    
    -- 元数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 唯一约束
    UNIQUE(member_id, analysis_date)
);



-- =====================================================
-- 创建索引
-- =====================================================

-- 基础查询索引
CREATE INDEX IF NOT EXISTS idx_user_trading_profiles_member_id ON user_trading_profiles(member_id);
CREATE INDEX IF NOT EXISTS idx_user_trading_profiles_analysis_date ON user_trading_profiles(analysis_date);

-- 业务查询索引
CREATE INDEX IF NOT EXISTS idx_user_trading_profiles_professional_score ON user_trading_profiles(professional_score);
CREATE INDEX IF NOT EXISTS idx_user_trading_profiles_trader_type ON user_trading_profiles(trader_type);
CREATE INDEX IF NOT EXISTS idx_user_trading_profiles_fund_scale ON user_trading_profiles(fund_scale_category);

-- 性能优化索引
CREATE INDEX IF NOT EXISTS idx_user_trading_profiles_total_pnl ON user_trading_profiles(total_pnl);
CREATE INDEX IF NOT EXISTS idx_user_trading_profiles_return_rate ON user_trading_profiles(return_rate);
CREATE INDEX IF NOT EXISTS idx_user_trading_profiles_risk_appetite ON user_trading_profiles(risk_appetite_level);

-- 个人分析专用索引
CREATE INDEX IF NOT EXISTS idx_user_trading_profiles_last_activity ON user_trading_profiles(last_activity_time);
CREATE INDEX IF NOT EXISTS idx_user_trading_profiles_max_single_loss_score ON user_trading_profiles(max_single_loss_score);



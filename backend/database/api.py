"""
数据库管理API
提供数据库状态查询、数据导入和管理功能
"""
from flask import Blueprint, request, jsonify
import logging
from .duckdb_manager import db_manager
from .repositories.user_repository import user_repository
from .repositories.task_repository import task_repository, contract_risk_repository
from core.utils.decorators import login_required, admin_required
from core.security.csrf_protection import csrf_protect

# 创建蓝图
db_bp = Blueprint('database', __name__)

# 创建logger
logger = logging.getLogger(__name__)

@db_bp.route('/status', methods=['GET'])
@login_required  # 需要登录才能查看数据库状态
def get_database_status():
    """获取数据库状态"""
    try:
        # 获取表统计信息
        stats = db_manager.get_table_stats()
        
        # 获取任务统计
        task_stats = task_repository.get_task_statistics()
        
        return jsonify({
            'status': 'connected',
            'table_statistics': stats,
            'task_statistics': task_stats,
            'database_path': db_manager.db_path
        })
        
    except Exception as e:
        logger.error(f"获取数据库状态失败: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@admin_required
@csrf_protect
@db_bp.route('/clear', methods=['POST'])
def clear_database():
    """清空数据库所有数据"""
    try:
        # 确认操作
        confirm = request.json.get('confirm', False)
        if not confirm:
            return jsonify({
                'error': '需要确认操作',
                'message': '请在请求中设置 confirm: true'
            }), 400
        
        # 清空所有数据
        db_manager.clear_all_data()
        
        # 获取清空后的统计
        stats = db_manager.get_table_stats()
        
        return jsonify({
            'status': 'success',
            'message': '数据库已清空',
            'table_statistics': stats
        })
        
    except Exception as e:
        logger.error(f"清空数据库失败: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@admin_required
@csrf_protect
@db_bp.route('/import/bd-data', methods=['POST'])
def import_bd_data():
    """导入BD金字塔数据"""
    try:
        data = request.get_json()
        
        if not data or 'bd_trees' not in data:
            return jsonify({
                'error': '无效的数据格式',
                'message': '需要包含 bd_trees 字段'
            }), 400
        
        bd_trees = data['bd_trees']
        
        # 解析并导入用户数据
        users = []
        relationships = []
        
        def extract_users_from_tree(node, parent_digital_id=None):
            """递归提取用户和关系数据"""
            user_info = node.get('user_info', {})
            
            if user_info.get('digital_id'):
                # 添加用户
                users.append({
                    'digital_id': user_info.get('digital_id'),
                    'member_id': user_info.get('member_id'),
                    'user_name': user_info.get('user_name'),
                    'agent_flag': user_info.get('agent_flag'),
                    'analyzed_level_number': user_info.get('analyzed_level_number', 0),
                    'analyzed_level_type': user_info.get('analyzed_level_type'),
                    'bd_name': user_info.get('bd_name'),
                    'device_count': user_info.get('device_count', 0),
                    'risk_score': user_info.get('risk_score', 0),
                    'relation_count': user_info.get('relation_count', 0)
                })
                
                # 添加关系
                if parent_digital_id:
                    relationships.append({
                        'parent_digital_id': parent_digital_id,
                        'child_digital_id': user_info.get('digital_id'),
                        'relationship_type': 'direct_agent',
                        'level_diff': 1
                    })
                
                # 递归处理子节点
                for child in node.get('children', []):
                    extract_users_from_tree(child, user_info.get('digital_id'))
        
        # 处理所有BD树
        for bd_tree in bd_trees:
            extract_users_from_tree(bd_tree)
        
        # 批量插入数据
        if users:
            user_repository.batch_insert_users(users)
        
        if relationships:
            user_repository.batch_insert_relationships(relationships)
        
        # 获取导入后的统计
        stats = db_manager.get_table_stats()
        
        return jsonify({
            'status': 'success',
            'message': f'成功导入 {len(users)} 个用户和 {len(relationships)} 个关系',
            'imported_users': len(users),
            'imported_relationships': len(relationships),
            'table_statistics': stats
        })
        
    except Exception as e:
        logger.error(f"导入BD数据失败: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@db_bp.route('/tasks/recent', methods=['GET'])
@login_required  # 需要登录才能查看最近任务
def get_recent_tasks():
    """获取最近的任务"""
    try:
        limit = int(request.args.get('limit', 20))
        tasks = task_repository.get_recent_tasks(limit)
        
        return jsonify({
            'status': 'success',
            'tasks': tasks,
            'count': len(tasks)
        })
        
    except Exception as e:
        logger.error(f"获取最近任务失败: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@admin_required
@csrf_protect
@db_bp.route('/tasks/cleanup', methods=['POST'])
def cleanup_old_tasks():
    """清理旧任务"""
    try:
        days = int(request.json.get('days', 30))
        cleaned_count = task_repository.cleanup_old_tasks(days)
        
        return jsonify({
            'status': 'success',
            'message': f'清理了 {cleaned_count} 个旧任务',
            'cleaned_count': cleaned_count
        })
        
    except Exception as e:
        logger.error(f"清理旧任务失败: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@db_bp.route('/users/search', methods=['GET'])
@login_required
def search_users():
    """搜索用户"""
    try:
        keyword = request.args.get('keyword', '')
        agent_flag = request.args.get('agent_flag')
        
        if not keyword:
            return jsonify({
                'error': '需要提供搜索关键词'
            }), 400
        
        users = user_repository.search_users(keyword, agent_flag)
        
        return jsonify({
            'status': 'success',
            'users': users,
            'count': len(users)
        })
        
    except Exception as e:
        logger.error(f"搜索用户失败: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@db_bp.route('/users/bd-statistics', methods=['GET'])
@login_required
def get_bd_statistics():
    """获取BD统计信息"""
    try:
        stats = user_repository.get_bd_statistics()
        
        return jsonify({
            'status': 'success',
            'bd_statistics': stats,
            'count': len(stats)
        })
        
    except Exception as e:
        logger.error(f"获取BD统计失败: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法结果存储管理器 - 完整版本
版本: 3.0 - 完整的数据存储和查询重构
负责新表结构的创建、数据写入和查询，完全兼容老存储系统
"""

import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
import pandas as pd

from .duckdb_manager import DuckDBManager

logger = logging.getLogger(__name__)


def json_serializable(obj):
    """
    将对象转换为JSON可序列化的格式
    处理pandas Timestamp、datetime、set、dataclass等特殊类型
    """
    if hasattr(obj, 'isoformat'):  # datetime, Timestamp等
        return obj.isoformat()
    elif hasattr(obj, 'item'):  # numpy数值类型
        return obj.item()
    elif isinstance(obj, set):  # set类型转换为列表
        return [json_serializable(item) for item in obj]
    elif isinstance(obj, frozenset):  # frozenset类型转换为列表
        return [json_serializable(item) for item in obj]
    elif hasattr(obj, '__dict__'):  # dataclass或其他对象，转换为字典
        return {k: json_serializable(v) for k, v in obj.__dict__.items()}
    elif isinstance(obj, dict):
        return {k: json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [json_serializable(item) for item in obj]
    elif pd.isna(obj):  # pandas NaN值
        return None
    else:
        return obj

# ========== 阶段1: 风险控制基础工具 ==========
class SafeDataExtractor:
    """安全的数据提取工具类"""
    
    @staticmethod
    def safe_extract_field(data: Dict, path: str, default=None, field_type=str):
        """
        安全提取嵌套JSON字段
        
        参数:
            data: 源数据字典
            path: 字段路径，用'.'分隔，如 'details.position_data.volume'
            default: 默认值
            field_type: 目标类型
        """
        try:
            value = data
            for key in path.split('.'):
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            # 类型转换
            if field_type == datetime and isinstance(value, str):
                return datetime.fromisoformat(value.replace('Z', '+00:00'))
            elif field_type == Decimal:
                return Decimal(str(value))
            elif field_type in (int, float) and value is not None:
                return field_type(value)
            else:
                return field_type(value) if value is not None else default
                
        except (KeyError, TypeError, ValueError, AttributeError) as e:
    
            return default
    
    @staticmethod
    def safe_parse_time_range(time_range_str: str) -> Tuple[Optional[datetime], Optional[datetime]]:
        """
        安全解析时间范围字符串
        
        参数:
            time_range_str: 如 "2025-05-19T23:49:39 - 2025-05-19T23:49:48"
            
        返回:
            (start_time, end_time) 元组
        """
        try:
            if not time_range_str or ' - ' not in time_range_str:
                return None, None
                
            start_str, end_str = time_range_str.split(' - ', 1)
            start_time = datetime.fromisoformat(start_str.replace('Z', '+00:00'))
            end_time = datetime.fromisoformat(end_str.replace('Z', '+00:00'))
            
            return start_time, end_time
            
        except (ValueError, AttributeError) as e:
    
            return None, None

class PerformanceMonitor:
    """性能监控工具"""
    
    @staticmethod
    def monitor_query(func):
        """查询性能监控装饰器"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                if duration > 1.0:  # 超过1秒警告
                    logger.warning(f"慢查询检测: {duration:.2f}s - {func.__name__}")
                elif duration > 0.5:  # 超过0.5秒提醒
                    logger.info(f"查询耗时: {duration:.2f}s - {func.__name__}")
                    
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"查询失败 [{duration:.2f}s]: {func.__name__} - {e}")
                raise
        return wrapper

class AlgorithmStorageManager:
    """算法结果存储管理器 - 完整版本"""
    
    def __init__(self, db_manager=None):
        self.db = db_manager if db_manager else DuckDBManager()
        self.data_extractor = SafeDataExtractor()
        self._transaction_active = False
        self.logger = logging.getLogger(__name__)
        
        # 存储配置优化设置
        self.storage_config = {
            'enable_additional_data_optimization': True,  # 启用additional_data优化
            'additional_data_threshold': 10,  # additional_data最小存储阈值（字节）
            'log_storage_optimization': True,  # 记录存储优化日志
            'fallback_to_additional_data': True  # 向后兼容：查询时回退到additional_data
        }
    
    # ========== 存储配置管理方法 ==========
    def configure_storage(self, **config_updates):
        """更新存储配置"""
        for key, value in config_updates.items():
            if key in self.storage_config:
                old_value = self.storage_config[key]
                self.storage_config[key] = value
                self.logger.info(f"存储配置更新: {key} = {old_value} → {value}")
            else:
                self.logger.warning(f"未知存储配置选项: {key}")
    
    def get_storage_config(self):
        """获取当前存储配置"""
        return self.storage_config.copy()
    
    def enable_additional_data_optimization(self):
        """启用additional_data优化"""
        self.configure_storage(enable_additional_data_optimization=True)
        self.logger.info("📈 已启用additional_data存储优化")
    
    def disable_additional_data_optimization(self):
        """禁用additional_data优化（传统模式）"""
        self.configure_storage(enable_additional_data_optimization=False)
        self.logger.info("📊 已切换到传统additional_data存储模式")
    
    def set_additional_data_threshold(self, threshold_bytes: int):
        """设置additional_data存储阈值"""
        self.configure_storage(additional_data_threshold=threshold_bytes)
        self.logger.info(f"🎯 additional_data存储阈值设置为: {threshold_bytes}字节")
    
    # ========== 事务管理方法 ==========
    def _start_transaction(self, conn):
        """开始事务"""
        try:
            if not self._transaction_active:
                conn.execute("BEGIN TRANSACTION")
                self._transaction_active = True
                self.logger.debug("事务已开始")
        except Exception as e:
            self.logger.error(f"开始事务失败: {e}")
            raise
    
    def _commit_transaction(self, conn):
        """提交事务"""
        try:
            if self._transaction_active:
                conn.execute("COMMIT")
                self._transaction_active = False
                self.logger.debug("事务已提交")
        except Exception as e:
            self.logger.error(f"提交事务失败: {e}")
            self._transaction_active = False
            raise
    
    def _rollback_transaction(self, conn):
        """回滚事务"""
        try:
            if self._transaction_active:
                conn.execute("ROLLBACK")
                self._transaction_active = False
                self.logger.warning("事务已回滚")
        except Exception as e:
            self.logger.error(f"回滚事务失败: {e}")
            self._transaction_active = False
        
    def _check_new_storage_exists(self) -> bool:
        """检查新存储结构是否存在"""
        try:
            # 检查关键的新表是否存在 - 使用DuckDB语法
            new_tables = ['algorithm_results', 'wash_trading_results', 'same_account_wash_trading', 'cross_account_wash_trading']
            with self.db.get_connection() as conn:
                for table in new_tables:
                    # 使用DuckDB的information_schema查询表
                    result = conn.execute(f"""
                        SELECT COUNT(*) as table_count 
                        FROM information_schema.tables 
                        WHERE table_name = '{table}'
                    """).fetchall()
                    if not result or result[0][0] == 0:
                
                        return False
    
            return True
        except Exception as e:
            logger.error(f"检查新存储结构失败: {e}")
            return False
    
    def initialize_tables(self) -> bool:
        """初始化所有新表结构"""
        try:
            # 读取SQL文件
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            sql_file = os.path.join(current_dir, "schema", "algorithm_tables.sql")
            
            if not os.path.exists(sql_file):
                logger.warning(f"SQL文件不存在: {sql_file}，使用内置建表语句")
                return self._create_tables_directly()
            
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 执行建表语句
            with self.db.get_connection() as conn:
                # 分割并执行每个CREATE语句
                statements = sql_content.split(';')
                for statement in statements:
                    statement = statement.strip()
                    if statement and ('CREATE TABLE' in statement.upper() or 'CREATE VIEW' in statement.upper()):
                        conn.execute(statement)
                        
            logger.info("算法结果表初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化算法结果表失败: {e}")
            return False
    
    def _create_tables_directly(self) -> bool:
        """直接创建表结构（备用方案）"""
        try:
            # 拆分SQL语句，逐个执行
            tables_sql = [
                """
            CREATE TABLE IF NOT EXISTS algorithm_results (
                id INTEGER PRIMARY KEY,
                task_id VARCHAR NOT NULL,
                algorithm_type VARCHAR NOT NULL,
                risk_level VARCHAR,
                confidence_score DECIMAL(5,2),
                trading_volume DECIMAL(20,8),
                trading_frequency INTEGER,
                indicators TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                """
            CREATE TABLE IF NOT EXISTS wash_trading_results (
                id INTEGER PRIMARY KEY,
                algorithm_result_id INTEGER NOT NULL,
                trading_type VARCHAR NOT NULL,
                participant_count INTEGER,
                total_volume DECIMAL(20,8),
                total_transactions INTEGER,
                avg_time_gap INTEGER,
                risk_score DECIMAL(5,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS same_account_wash_trading (
                id INTEGER PRIMARY KEY,
                    wash_trading_id INTEGER NOT NULL,
                    user_id VARCHAR(50) NOT NULL,
                    contract_name VARCHAR(100) NOT NULL,
                    long_position_id VARCHAR(50),
                    short_position_id VARCHAR(50),
                    long_open_time TIMESTAMP,
                    short_open_time TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS cross_account_wash_trading (
                    id INTEGER PRIMARY KEY,
                    wash_trading_id INTEGER NOT NULL,
                    user_a_id VARCHAR(50) NOT NULL,
                    user_b_id VARCHAR(50) NOT NULL,
                    contract_name VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS contract_risk_details (
                    id INTEGER PRIMARY KEY,
                    algorithm_result_id INTEGER NOT NULL,
                    member_id VARCHAR(50) NOT NULL,
                    contract_name VARCHAR(100),
                    detection_type VARCHAR(50), -- suspected_wash_trading, high_frequency_trading, etc.
                    detection_method VARCHAR(50),
                    risk_level VARCHAR(20), -- HIGH, MEDIUM, LOW
                    risk_score DECIMAL(10,4), -- 风险分数
                    abnormal_volume DECIMAL(20,8), -- 异常成交量
                    trade_count INTEGER, -- 交易次数
                    time_range VARCHAR(100), -- 时间范围
                    counterparty_ids VARCHAR(500), -- 对手方ID列表
                    additional_data TEXT DEFAULT NULL,  -- JSON格式的其他数据（可选，优化存储）
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (algorithm_result_id) REFERENCES algorithm_results(id)
                )
                """
            ]
            
            with self.db.get_connection() as conn:
                for sql in tables_sql:
                    if sql.strip():
                        conn.execute(sql.strip())
            
            logger.info("直接创建表结构成功")
            return True
            
        except Exception as e:
            logger.error(f"直接创建表失败: {e}")
            return False
    
    def store_algorithm_result(self, task_id: str, algorithm_type: str, 
                             result_data: Dict[str, Any]) -> Optional[int]:
        """存储算法结果到新的拆分表结构 - 完整版本（带事务管理）"""
        try:
            with self.db.get_connection() as conn:
                # 开始事务
                self._start_transaction(conn)
                
                # 1. 存储到通用算法结果表
                result_id = self._store_common_result(conn, task_id, algorithm_type, result_data)
                if not result_id:
                    self._rollback_transaction(conn)
                    return None
                
                # 2. 存储风险事件详情
                details_count = self._store_contract_risk_details(conn, result_id, result_data)
                self.logger.info(f"存储了 {details_count} 条风险事件详情")
                
                # 3. 根据实际风险事件的detection_type存储专门详情（阶段2实现）
                specialized_total = 0
                
                # 智能检测数据中包含的风险类型，并存储对应的专门详情
                contract_risks = result_data.get('contract_risks', [])
                detection_types = set()
                for risk in contract_risks:
                    detection_type = risk.get('detection_type', '')
                    if detection_type:
                        detection_types.add(detection_type)
                
                self.logger.info(f"检测到的风险类型: {list(detection_types)}")
                
                # 根据检测到的风险类型存储专门详情
                wash_count = 0
                hft_count = 0
                brush_count = 0
                funding_count = 0
                
                # 如果包含对敲交易相关的风险类型（已移除废弃的wash_trading_optimized）
                if any(dt in ['suspected_wash_trading', 'wash_trading'] for dt in detection_types):
                    wash_count = self._store_wash_trading_details(conn, result_id, result_data)
                    
                # 如果包含高频交易风险类型
                if any(dt in ['high_frequency_trading', 'hft', 'high_frequency'] for dt in detection_types):
                    hft_count = self._store_hft_details(conn, result_id, result_data)
                    
                # 如果包含刷量交易风险类型（注意：现已整合到对敲检测中）
                if any(dt in ['regular_brush_trading', 'brush_trading', 'volume_manipulation'] for dt in detection_types):
                    # 刷量交易数据现在存储在wash_trading_pairs表中，不需要单独存储
                    brush_count = 0
                    self.logger.info("刷量交易数据现已整合到对敲检测中，不需要单独存储")
                    
                # 如果包含资金费率套利风险类型
                if any(dt in ['funding_rate_arbitrage', 'funding_arbitrage'] for dt in detection_types):
                    funding_count = self._store_funding_details(conn, result_id, result_data)
                
                specialized_total = wash_count + hft_count + brush_count + funding_count
                self.logger.info(f"智能专门详情存储完成: 对敲{wash_count}条, 高频{hft_count}条, 刷量{brush_count}条, 套利{funding_count}条")
                
                # 提交事务
                self._commit_transaction(conn)
                self.logger.info(f"算法结果存储成功: {algorithm_type}, result_id: {result_id}")
                return result_id
                
        except Exception as e:
            self.logger.error(f"存储算法结果失败: {e}")
            return None
    
    def _store_common_result(self, conn, task_id: str, algorithm_type: str, 
                           result_data: Dict[str, Any]) -> Optional[int]:
        """存储通用算法结果"""
        try:
            # 提取通用字段
            summary = result_data.get('summary', {})
            indicators = result_data.get('indicators', {})
            
            # 生成新的ID
            max_id = conn.execute("SELECT COALESCE(MAX(id), 0) FROM algorithm_results").fetchone()[0]
            new_id = max_id + 1
            
            sql = """
            INSERT INTO algorithm_results (
                id, task_id, algorithm_type, risk_level, confidence_score,
                trading_volume, trading_frequency, indicators, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            conn.execute(sql, [
                new_id,
                task_id,
                algorithm_type,
                'medium',  # 默认风险等级
                0.8,       # 默认置信度
                summary.get('total_volume', 0),
                summary.get('total_frequency', 0),
                json.dumps(json_serializable(indicators)),
                datetime.now(),
                datetime.now()
            ])
            
            return new_id
            
        except Exception as e:
            self.logger.error(f"存储通用算法结果失败: {e}")
            return None
    
    def _store_contract_risk_details(self, conn, result_id: int, result_data: Dict[str, Any]) -> int:
        """🚀 优化版本：批量存储风险事件详情到通用详情表，返回存储的记录数"""
        stored_count = 0
        try:
            contract_risks = result_data.get('contract_risks', [])
            if not contract_risks:
                self.logger.info(f"没有风险事件需要存储，result_id: {result_id}")
                return 0
            
            # 确保表存在
            self._ensure_contract_risk_details_table(conn)
            
            self.logger.info(f"🚀 开始批量存储 {len(contract_risks)} 个风险事件详情...")
            import time
            start_time = time.time()
            
            # 🚀 批量数据准备
            batch_data = []
            
            # 获取当前最大ID（一次性获取，避免重复查询）
            max_id = conn.execute("SELECT COALESCE(MAX(id), 0) FROM contract_risk_details").fetchone()[0]
            current_id = max_id
            
            for idx, risk in enumerate(contract_risks):
                try:
                    current_id += 1
                    
                    # 使用安全的数据提取，支持驼峰命名和下划线命名
                    member_id = self.data_extractor.safe_extract_field(risk, 'member_id', 
                                                                      default=self.data_extractor.safe_extract_field(risk, 'memberId', 
                                                                      default=self.data_extractor.safe_extract_field(risk, 'user_id', '')))
                    contract_name = self.data_extractor.safe_extract_field(risk, 'contract_name', 
                                                                         default=self.data_extractor.safe_extract_field(risk, 'contractName', ''))
                    detection_type = self.data_extractor.safe_extract_field(risk, 'detection_type', '')
                    detection_method = self.data_extractor.safe_extract_field(risk, 'detection_method', '')
                    risk_level = self.data_extractor.safe_extract_field(risk, 'risk_level', 'medium')
                    
                    # 安全提取数值字段
                    risk_score = self.data_extractor.safe_extract_field(risk, 'risk_score', 
                                                                       default=self.data_extractor.safe_extract_field(risk, 'total_risk_score', 0), 
                                                                       field_type=float)
                    abnormal_volume = self.data_extractor.safe_extract_field(risk, 'abnormal_volume',
                                                                            default=self.data_extractor.safe_extract_field(risk, 'trading_volume', 0),
                                                                            field_type=float)
                    trade_count = self.data_extractor.safe_extract_field(risk, 'trade_count',
                                                                        default=self.data_extractor.safe_extract_field(risk, 'transaction_count', 0),
                                                                        field_type=int)
                    time_range = self.data_extractor.safe_extract_field(risk, 'time_range', 
                                                                       default=self.data_extractor.safe_extract_field(risk, 'timeRange', 
                                                                       default=self.data_extractor.safe_extract_field(risk, 'hour', '')))
                    
                    # 处理对手方ID列表
                    counterparty_ids = risk.get('counterparty_ids', [])
                    if not isinstance(counterparty_ids, list):
                        counterparty_ids = [counterparty_ids] if counterparty_ids else []
                    counterparty_ids_json = json.dumps(json_serializable(counterparty_ids))
                    
                    # 存储配置优化：additional_data仅在有实际数据时存储
                    excluded_fields = {
                        'member_id', 'memberId', 'user_id', 'contract_name', 'contractName', 'detection_type', 'detection_method',
                        'risk_level', 'risk_score', 'total_risk_score', 'abnormal_volume', 'abnormalVolume', 'trading_volume',
                        'trade_count', 'transaction_count', 'time_range', 'timeRange', 'hour', 'counterparty_ids'
                    }
                    additional_data = {k: v for k, v in risk.items() if k not in excluded_fields}
                    
                    # 存储配置优化：基于配置决定是否存储additional_data
                    additional_data_json = None
                    if self.storage_config['enable_additional_data_optimization']:
                        # 智能存储：只在有实际额外数据时存储
                        if additional_data and any(v for v in additional_data.values() if v is not None and v != '' and v != [] and v != {}):
                            additional_data_json = json.dumps(json_serializable(additional_data))
                            if len(additional_data_json) >= self.storage_config['additional_data_threshold']:
                                if self.storage_config['log_storage_optimization']:
                                    self.logger.debug(f"✅ 存储additional_data: {len(additional_data_json)}字节 - {member_id}")
                            else:
                                additional_data_json = None  # 太小的数据不存储
                                if self.storage_config['log_storage_optimization']:
                                    self.logger.debug(f"⚡ 跳过小数据additional_data: <{self.storage_config['additional_data_threshold']}字节 - {member_id}")
                        else:
                            if self.storage_config['log_storage_optimization']:
                                self.logger.debug(f"💾 跳过空additional_data存储，减少存储开销 - {member_id}")
                    else:
                        # 传统模式：总是存储
                        additional_data_json = json.dumps(json_serializable(additional_data))
                        if self.storage_config['log_storage_optimization']:
                            self.logger.debug(f"📁 传统模式存储additional_data - {member_id}")
                    
                    # 🚀 添加到批量数据列表
                    batch_data.append([
                        current_id, result_id, str(member_id), contract_name, detection_type,
                        detection_method, risk_level, risk_score, abnormal_volume, trade_count,
                        time_range, counterparty_ids_json, additional_data_json, datetime.now()
                    ])
                    
                except Exception as e:
                    self.logger.error(f"准备第{idx+1}个风险事件数据失败: {e}")
                    continue
            
            # 🚀 执行批量插入
            if batch_data:
                sql = """
                INSERT INTO contract_risk_details (
                    id, algorithm_result_id, member_id, contract_name, detection_type,
                    detection_method, risk_level, risk_score, abnormal_volume, trade_count,
                    time_range, counterparty_ids, additional_data, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                # 分批执行，防止单次插入数据过多
                batch_size = 1000
                total_batches = (len(batch_data) + batch_size - 1) // batch_size
                
                for batch_idx in range(total_batches):
                    start_idx = batch_idx * batch_size
                    end_idx = min(start_idx + batch_size, len(batch_data))
                    current_batch = batch_data[start_idx:end_idx]
                    
                    try:
                        conn.executemany(sql, current_batch)
                        stored_count += len(current_batch)
                        
                        if total_batches > 1:
                            progress = (batch_idx + 1) / total_batches * 100
                            self.logger.info(f"📦 批量插入进度: {batch_idx+1}/{total_batches} ({progress:.1f}%)")
                            
                    except Exception as e:
                        self.logger.error(f"第{batch_idx+1}批数据插入失败: {e}")
                        # 尝试逐条插入这一批（降级处理）
                        for params in current_batch:
                            try:
                                conn.execute(sql, params)
                                stored_count += 1
                            except Exception as single_error:
                                self.logger.warning(f"单条风险事件存储失败: {single_error}")
                                continue
            
            execution_time = time.time() - start_time
            avg_speed = len(contract_risks) / execution_time if execution_time > 0 else 0
            
            self.logger.info(f"🚀 风险事件详情批量存储完成！result_id: {result_id}, "
                           f"成功存储: {stored_count}/{len(contract_risks)}, "
                           f"耗时: {execution_time:.2f}秒, 速度: {avg_speed:.0f}条/秒")
            return stored_count
            
        except Exception as e:
            self.logger.error(f"批量存储风险事件详情失败: {e}")
            return stored_count
    
    def _ensure_contract_risk_details_table(self, conn):
        """确保通用风险事件详情表存在"""
        try:
            sql = """
            CREATE TABLE IF NOT EXISTS contract_risk_details (
                id INTEGER PRIMARY KEY,
                algorithm_result_id INTEGER NOT NULL,
                member_id VARCHAR(50) NOT NULL,
                contract_name VARCHAR(100),
                detection_type VARCHAR(50), -- suspected_wash_trading, high_frequency_trading, etc.
                detection_method VARCHAR(50),
                risk_level VARCHAR(20), -- HIGH, MEDIUM, LOW
                risk_score DECIMAL(10,4), -- 风险分数
                abnormal_volume DECIMAL(20,8), -- 异常成交量
                trade_count INTEGER, -- 交易次数
                time_range VARCHAR(100), -- 时间范围
                counterparty_ids VARCHAR(500), -- 对手方ID列表
                additional_data TEXT DEFAULT NULL,  -- JSON格式的其他数据（可选，优化存储）
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (algorithm_result_id) REFERENCES algorithm_results(id)
            )
            """
            conn.execute(sql)
        except Exception as e:
            self.logger.error(f"创建通用风险事件详情表失败: {e}")
    
    def _store_wash_trading_details(self, conn, result_id: int, result_data: Dict[str, Any]) -> int:
        """存储对敲交易专门详情 - 阶段2完整实现"""
        stored_count = 0
        try:
            contract_risks = result_data.get('contract_risks', [])
            wash_trading_risks = [
                risk for risk in contract_risks 
                if 'wash_trading' in risk.get('detection_type', '').lower()
            ]
            
            if not wash_trading_risks:
                self.logger.info(f"没有对敲交易风险需要专门存储，result_id: {result_id}")
                return 0
            
            self.logger.info(f"开始存储 {len(wash_trading_risks)} 个对敲交易专门详情")
            
            # 1. 确保wash_trading_results表存在
            self._ensure_wash_trading_results_table(conn)
            
            # 2. 创建对敲交易主记录
            wash_trading_main_id = self._create_wash_trading_main_record(conn, result_id, wash_trading_risks)
            if not wash_trading_main_id:
                return 0
            
            # 3. 分析和存储详细的对敲交易数据
            same_account_count = 0
            cross_account_count = 0
            
            for risk in wash_trading_risks:
                detection_method = risk.get('detection_method', '')
                
                # 如果detection_method为空或为通用分析方法，根据实际数据智能判断
                if not detection_method or detection_method in ['complete_position_analysis', 'comprehensive_analysis']:
                    user_a = self.data_extractor.safe_extract_field(risk, 'member_id', 
                                                                   default=self.data_extractor.safe_extract_field(risk, 'user_id', ''))
                    counterparty_ids = risk.get('counterparty_ids', [])
                    
                    # 检查对手方信息
                    opponent_member_id = risk.get('opponent_member_id', '')
                    
                    # 解析counterparty_ids
                    if isinstance(counterparty_ids, str):
                        try:
                            counterparty_ids = json.loads(counterparty_ids)
                        except:
                            counterparty_ids = []
                    
                    # 智能判断：如果有对手方且不同于主用户，则为跨账户对敲
                    has_different_counterparty = False
                    if opponent_member_id and opponent_member_id != user_a:
                        has_different_counterparty = True
                        detection_method = 'cross_account_wash_trading'
                    elif counterparty_ids and len(counterparty_ids) > 0 and user_a != counterparty_ids[0]:
                        has_different_counterparty = True
                        detection_method = 'cross_account_wash_trading'
                    else:
                        detection_method = 'same_account_wash_trading'
                    
                    self.logger.info(f"智能分类：用户{str(user_a)[:8]}...，对手方{opponent_member_id or counterparty_ids}，判定为{detection_method}")
                
                if 'same_account' in detection_method.lower():
                    # 同账户对敲
                    if self._store_same_account_wash_trading(conn, wash_trading_main_id, risk):
                        same_account_count += 1
                        stored_count += 1
                elif 'cross_account' in detection_method.lower():
                    # 跨账户对敲
                    if self._store_cross_account_wash_trading(conn, wash_trading_main_id, risk):
                        cross_account_count += 1
                        stored_count += 1
                else:
                    # 兜底：如果仍然无法识别，记录警告并跳过
                    self.logger.warning(f"无法识别的对敲类型: {detection_method}，跳过存储")
                    continue
            
            # 4. 更新主记录的统计信息
            self._update_wash_trading_main_stats(conn, wash_trading_main_id, same_account_count, cross_account_count)
            
            self.logger.info(f"对敲交易专门详情存储完成: 总计{stored_count}条 (同账户{same_account_count}条, 跨账户{cross_account_count}条)")
            return stored_count
            
        except Exception as e:
            self.logger.error(f"存储对敲交易详情失败: {e}")
            return stored_count
    
    def _ensure_wash_trading_results_table(self, conn):
        """确保对敲交易结果表存在（表由SQL文件创建）"""
        pass  # 表结构由backend/database/schema/algorithm_tables.sql定义
    
    def _create_wash_trading_main_record(self, conn, result_id: int, wash_trading_risks: List[Dict]) -> Optional[int]:
        """创建对敲交易主记录"""
        try:
            # 计算汇总统计
            total_volume = sum(
                self.data_extractor.safe_extract_field(risk, 'abnormal_volume', 0, float) 
                for risk in wash_trading_risks
            )
            total_transactions = sum(
                self.data_extractor.safe_extract_field(risk, 'trade_count', 0, int)
                for risk in wash_trading_risks
            )
            avg_risk_score = sum(
                self.data_extractor.safe_extract_field(risk, 'risk_score', 0, float)
                for risk in wash_trading_risks
            ) / len(wash_trading_risks) if wash_trading_risks else 0
            
            # 提取时间范围
            time_ranges = [risk.get('time_range', '') for risk in wash_trading_risks if risk.get('time_range')]
            detection_period = time_ranges[0] if time_ranges else ''
            
            # 生成新ID
            max_id = conn.execute("SELECT COALESCE(MAX(id), 0) FROM wash_trading_results").fetchone()[0]
            new_id = max_id + 1
            
            sql = """
            INSERT INTO wash_trading_results (
                id, algorithm_result_id, trading_type, pair_count,
                total_volume, avg_time_gap, risk_score, detection_method, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            conn.execute(sql, [
                new_id, result_id, 'wash_trading', len(wash_trading_risks),
                total_volume, 0, min(avg_risk_score/100, 0.9999), 'comprehensive_analysis', datetime.now()
            ])
            
            self.logger.info(f"创建对敲交易主记录成功，ID: {new_id}")
            return new_id
            
        except Exception as e:
            self.logger.error(f"创建对敲交易主记录失败: {e}")
            return None
    
    def _store_same_account_wash_trading(self, conn, wash_trading_id: int, risk: Dict) -> bool:
        """存储同账户对敲交易详情"""
        try:
            # 确保表存在
            self._ensure_same_account_wash_trading_table(conn)
            
            # 提取关键字段
            user_id = self.data_extractor.safe_extract_field(risk, 'member_id', 
                                                            default=self.data_extractor.safe_extract_field(risk, 'user_id', ''))
            contract_name = self.data_extractor.safe_extract_field(risk, 'contract_name', '')
            
            # 从additional_data中提取position相关信息
            additional_data = risk.get('additional_data', {})
            if isinstance(additional_data, str):
                try:
                    additional_data = json.loads(additional_data)
                except:
                    additional_data = {}
            
            long_position_id = self.data_extractor.safe_extract_field(additional_data, 'long_position_id', '')
            short_position_id = self.data_extractor.safe_extract_field(additional_data, 'short_position_id', '')
            
            # 解析时间信息
            time_range = risk.get('time_range', '')
            long_open_time, short_open_time = self.data_extractor.safe_parse_time_range(time_range)
            
            # 生成新ID
            max_id = conn.execute("SELECT COALESCE(MAX(id), 0) FROM same_account_wash_trading").fetchone()[0]
            new_id = max_id + 1
            
            sql = """
            INSERT INTO same_account_wash_trading (
                id, wash_trading_id, user_id, contract_name,
                long_position_id, short_position_id, long_open_time, short_open_time, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            conn.execute(sql, [
                new_id, wash_trading_id, str(user_id), contract_name,
                long_position_id, short_position_id, long_open_time, short_open_time, datetime.now()
            ])
            
            return True
            
        except Exception as e:
            self.logger.error(f"存储同账户对敲交易详情失败: {e}")
            return False
    
    def _ensure_same_account_wash_trading_table(self, conn):
        """确保同账户对敲交易表存在"""
        try:
            sql = """
            CREATE TABLE IF NOT EXISTS same_account_wash_trading (
                id INTEGER PRIMARY KEY,
                wash_trading_id INTEGER NOT NULL,
                user_id VARCHAR(50) NOT NULL,
                contract_name VARCHAR(100) NOT NULL,
                long_position_id VARCHAR(50),
                short_position_id VARCHAR(50),
                long_open_time TIMESTAMP,
                short_open_time TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
            conn.execute(sql)
        except Exception as e:
            self.logger.error(f"创建同账户对敲交易表失败: {e}")
    
    def _store_cross_account_wash_trading(self, conn, wash_trading_id: int, risk: Dict) -> bool:
        """存储跨账户对敲交易详情"""
        try:
            # 确保表存在
            self._ensure_cross_account_wash_trading_table(conn)
            
            # 提取关键字段
            user_a_id = self.data_extractor.safe_extract_field(risk, 'member_id',
                                                              default=self.data_extractor.safe_extract_field(risk, 'user_id', ''))
            contract_name = self.data_extractor.safe_extract_field(risk, 'contract_name', '')
            
            # 从counterparty_ids中获取对手方
            counterparty_ids = risk.get('counterparty_ids', [])
            if isinstance(counterparty_ids, str):
                try:
                    counterparty_ids = json.loads(counterparty_ids)
                except:
                    counterparty_ids = []
            
            user_b_id = counterparty_ids[0] if counterparty_ids else ''
            
            # 生成新ID
            max_id = conn.execute("SELECT COALESCE(MAX(id), 0) FROM cross_account_wash_trading").fetchone()[0]
            new_id = max_id + 1
            
            sql = """
            INSERT INTO cross_account_wash_trading (
                id, wash_trading_id, user_a_id, user_b_id, contract_name, created_at
            ) VALUES (?, ?, ?, ?, ?, ?)
            """
            
            conn.execute(sql, [
                new_id, wash_trading_id, str(user_a_id), str(user_b_id), contract_name, datetime.now()
            ])
            
            return True
            
        except Exception as e:
            self.logger.error(f"存储跨账户对敲交易详情失败: {e}")
            return False
    
    def _ensure_cross_account_wash_trading_table(self, conn):
        """确保跨账户对敲交易表存在"""
        try:
            sql = """
            CREATE TABLE IF NOT EXISTS cross_account_wash_trading (
                id INTEGER PRIMARY KEY,
                wash_trading_id INTEGER NOT NULL,
                user_a_id VARCHAR(50) NOT NULL,
                user_b_id VARCHAR(50) NOT NULL,
                contract_name VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
            conn.execute(sql)
        except Exception as e:
            self.logger.error(f"创建跨账户对敲交易表失败: {e}")
    
    def _update_wash_trading_main_stats(self, conn, wash_trading_id: int, same_account_count: int, cross_account_count: int):
        """更新对敲交易主记录的统计信息（实际表结构不支持此更新）"""
        # 由于实际表结构没有这些字段，暂时跳过
        self.logger.info(f"对敲交易统计: 同账户{same_account_count}条, 跨账户{cross_account_count}条")
    
    def _store_hft_details(self, conn, result_id: int, result_data: Dict[str, Any]) -> int:
        """存储高频交易专门详情 - 阶段2完整实现"""
        stored_count = 0
        try:
            contract_risks = result_data.get('contract_risks', [])
            hft_risks = [
                risk for risk in contract_risks 
                if risk.get('detection_type', '').lower() in ['high_frequency', 'high_frequency_trading', 'hft']
            ]
            
            if not hft_risks:
                self.logger.info(f"没有高频交易风险需要专门存储，result_id: {result_id}")
                return 0
            
            self.logger.info(f"开始存储 {len(hft_risks)} 个高频交易专门详情")
            
            # 确保高频交易详情表存在
            self._ensure_hft_details_table(conn)
            
            for risk in hft_risks:
                try:
                    # 提取高频交易特有字段
                    user_id = self.data_extractor.safe_extract_field(risk, 'member_id',
                                                                    default=self.data_extractor.safe_extract_field(risk, 'user_id', ''))
                    contract_name = self.data_extractor.safe_extract_field(risk, 'contract_name', '')
                    trade_frequency = self.data_extractor.safe_extract_field(risk, 'trade_count', 0, int)
                    trading_volume = self.data_extractor.safe_extract_field(risk, 'abnormal_volume', 0, float)
                    
                    # 从additional_data中提取更多字段
                    additional_data = risk.get('additional_data', {})
                    if isinstance(additional_data, str):
                        try:
                            additional_data = json.loads(additional_data)
                        except:
                            additional_data = {}
                    
                    avg_trade_size = self.data_extractor.safe_extract_field(additional_data, 'avg_trade_size', 0, float)
                    max_frequency_per_minute = self.data_extractor.safe_extract_field(additional_data, 'max_frequency_per_minute', 0, int)
                    trading_pattern = self.data_extractor.safe_extract_field(additional_data, 'trading_pattern', 'unknown')
                    
                    # 解析时间范围
                    time_range = risk.get('time_range', '')
                    start_time, end_time = self.data_extractor.safe_parse_time_range(time_range)
                    
                    # 计算交易密度（如果有时间范围）
                    trading_density = 0
                    if start_time and end_time and trade_frequency > 0:
                        time_diff_minutes = (end_time - start_time).total_seconds() / 60
                        if time_diff_minutes > 0:
                            trading_density = trade_frequency / time_diff_minutes
                    
                    risk_score = self.data_extractor.safe_extract_field(risk, 'risk_score', 0, float)
                    
                    # 生成新ID
                    max_id = conn.execute("SELECT COALESCE(MAX(id), 0) FROM high_frequency_trading_details").fetchone()[0]
                    new_id = max_id + 1
                    
                    sql = """
                    INSERT INTO high_frequency_trading_details (
                        id, algorithm_result_id, user_id, contract_name, trade_count,
                        avg_holding_time, max_frequency, volume_concentration,
                        time_pattern, risk_indicators, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    # 构建risk_indicators JSON
                    risk_indicators = {
                        'risk_score': risk_score,
                        'trading_volume': trading_volume,
                        'avg_trade_size': avg_trade_size,
                        'trading_density': trading_density
                    }
                    
                    conn.execute(sql, [
                        new_id, result_id, str(user_id), contract_name, trade_frequency,
                        0, max_frequency_per_minute, 0.5,  # avg_holding_time=0, volume_concentration=0.5
                        trading_pattern, json.dumps(json_serializable(risk_indicators)), datetime.now()
                    ])
                    
                    stored_count += 1
                    
                except Exception as e:
                    self.logger.error(f"存储单个高频交易详情失败: {e}")
                    continue
            
            self.logger.info(f"高频交易专门详情存储完成: {stored_count}条记录")
            return stored_count
            
        except Exception as e:
            self.logger.error(f"存储高频交易详情失败: {e}")
            return stored_count
    
    def _ensure_hft_details_table(self, conn):
        """确保高频交易详情表存在（表由SQL文件创建）"""
        pass  # 表结构由backend/database/schema/algorithm_tables.sql定义
    
    def _store_brush_trading_details(self, conn, result_id: int, result_data: Dict[str, Any]) -> int:
        """存储刷量交易专门详情 - 已废弃（现已整合到对敲检测中）"""
        self.logger.info(f"刷量交易数据现已整合到wash_trading_pairs表中，不需要单独存储到brush_trading_details表")
        self.logger.info(f"如需查询刷量交易数据，请查询wash_trading_pairs表")
        return 0
    
    def _ensure_brush_trading_details_table(self, conn):
        """确保刷量交易详情表存在 - 已废弃（现已整合到wash_trading_pairs表）"""
        # 刷量交易数据现在存储在wash_trading_pairs表中，不需要单独的brush_trading_details表
        pass
    
    def _store_funding_details(self, conn, result_id: int, result_data: Dict[str, Any]) -> int:
        """存储资金费率套利详情 - 阶段2完整实现"""
        stored_count = 0
        try:
            contract_risks = result_data.get('contract_risks', [])
            funding_risks = [
                risk for risk in contract_risks 
                if risk.get('detection_type', '').lower() in ['funding_arbitrage', 'funding_rate_arbitrage']
            ]
            
            if not funding_risks:
                self.logger.info(f"没有资金费率套利风险需要专门存储，result_id: {result_id}")
                return 0
            
            self.logger.info(f"开始存储 {len(funding_risks)} 个资金费率套利专门详情")
            
            # 确保资金费率套利详情表存在  
            self._ensure_funding_arbitrage_details_table(conn)
            
            for risk in funding_risks:
                try:
                    # 提取资金费率套利特有字段
                    user_id = self.data_extractor.safe_extract_field(risk, 'member_id',
                                                                    default=self.data_extractor.safe_extract_field(risk, 'user_id', ''))
                    contract_name = self.data_extractor.safe_extract_field(risk, 'contract_name', '')
                    trading_volume = self.data_extractor.safe_extract_field(risk, 'abnormal_volume', 0, float)
                    
                    # 从additional_data中提取套利特有字段
                    additional_data = risk.get('additional_data', {})
                    if isinstance(additional_data, str):
                        try:
                            additional_data = json.loads(additional_data)
                        except:
                            additional_data = {}
                    
                    funding_rate = self.data_extractor.safe_extract_field(additional_data, 'funding_rate', 0, float)
                    position_direction = self.data_extractor.safe_extract_field(additional_data, 'position_direction', 'unknown')
                    arbitrage_profit = self.data_extractor.safe_extract_field(additional_data, 'estimated_profit', 0, float)
                    holding_periods = self.data_extractor.safe_extract_field(additional_data, 'holding_periods', 1, int)
                    
                    # 解析时间范围
                    time_range = risk.get('time_range', '')
                    start_time, end_time = self.data_extractor.safe_parse_time_range(time_range)
                    
                    # 计算套利效率
                    arbitrage_efficiency = 0
                    if trading_volume > 0 and arbitrage_profit > 0:
                        arbitrage_efficiency = arbitrage_profit / trading_volume
                    
                    risk_score = self.data_extractor.safe_extract_field(risk, 'risk_score', 0, float)
                    
                    # 生成新ID
                    max_id = conn.execute("SELECT COALESCE(MAX(id), 0) FROM funding_rate_arbitrage_details").fetchone()[0]
                    new_id = max_id + 1
                    
                    sql = """
                    INSERT INTO funding_rate_arbitrage_details (
                        id, algorithm_result_id, user_id, contract_name, funding_rate,
                        position_size, holding_duration, estimated_profit, risk_exposure,
                        market_conditions, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    # 构建market_conditions JSON
                    market_conditions = {
                        'position_direction': position_direction,
                        'arbitrage_efficiency': arbitrage_efficiency,
                        'holding_periods': holding_periods,
                        'risk_score': risk_score
                    }
                    
                    conn.execute(sql, [
                        new_id, result_id, str(user_id), contract_name, funding_rate,
                        trading_volume, holding_periods, arbitrage_profit, risk_score/100,
                        json.dumps(json_serializable(market_conditions)), datetime.now()
                    ])
                    
                    stored_count += 1
                    
                except Exception as e:
                    self.logger.error(f"存储单个资金费率套利详情失败: {e}")
                    continue
            
            self.logger.info(f"资金费率套利专门详情存储完成: {stored_count}条记录")
            return stored_count
            
        except Exception as e:
            self.logger.error(f"存储资金费率套利详情失败: {e}")
            return stored_count
    
    def _ensure_funding_arbitrage_details_table(self, conn):
        """确保资金费率套利详情表存在（表由SQL文件创建）"""
        pass  # 表结构由backend/database/schema/algorithm_tables.sql定义
    
    def query_algorithm_results(self, task_id: str, algorithm_type: Optional[str] = None, user_id: Optional[str] = None) -> Dict:
        """查询算法结果 - 完整重构版本（支持专门表联合查询和用户过滤）"""
        try:
            with self.db.get_connection() as conn:
                # 1. 查询主结果 - 按创建时间降序排列，确保获取最新的结果
                if algorithm_type:
                    results = conn.execute(
                        "SELECT * FROM algorithm_results WHERE task_id = ? AND algorithm_type = ? ORDER BY created_at DESC",
                        [task_id, algorithm_type]
                    ).fetchall()
                else:
                    results = conn.execute(
                        "SELECT * FROM algorithm_results WHERE task_id = ? ORDER BY created_at DESC",
                        [task_id]
                    ).fetchall()
                
                if not results:
                    return {}
                
                # 2. 🚀 性能优化：如果指定了用户ID，使用优化的用户过滤查询
                if user_id:
                    return self._build_user_filtered_response(conn, results, user_id)
                else:
                    # 原有的完整查询
                    return self._build_enhanced_response(conn, results)
                
        except Exception as e:
            self.logger.error(f"查询算法结果失败: {e}")
            return {}
    
    def _build_user_filtered_response(self, conn, results: List, user_id: str) -> Dict:
        """构建用户过滤的响应 - 性能优化版本，只查询指定用户的数据"""
        try:
            if not results:
                return {}
            
            # 获取所有result_ids
            result_ids = [result[0] for result in results]
            result_ids_str = ','.join(map(str, result_ids))
            
            # 🎯 关键优化：只查询指定用户的风险事件详情
            contract_risks_sql = f"""
            SELECT * FROM contract_risk_details 
            WHERE algorithm_result_id IN ({result_ids_str})
            AND member_id = ?
            ORDER BY created_at
            """
            risk_details = conn.execute(contract_risks_sql, [user_id]).fetchall()
            
            self.logger.info(f"用户过滤查询完成 - 用户: {user_id}, 找到记录数: {len(risk_details)}")
            
            # 构建contract_risks数组（只包含该用户的数据）
            contract_risks = []
            result_id = result_ids[0]
            
            # 预先查询专门表数据（用于增强）
            specialized_data = self._get_all_specialized_data_for_enhancement(conn, result_id)
            
            for detail in risk_details:
                try:
                    # 解析数据库字段
                    risk_event = {
                        'member_id': detail[2],
                        'contract_name': detail[3],
                        'detection_type': detail[4],
                        'detection_method': detail[5],
                        'risk_level': detail[6],
                        'risk_score': float(detail[7]) if detail[7] else 0,
                        'abnormal_volume': float(detail[8]) if detail[8] else 0,
                        'trade_count': int(detail[9]) if detail[9] else 0,
                        'time_range': detail[10],
                    }
                    
                    # 解析counterparty_ids
                    try:
                        counterparty_ids = json.loads(detail[11]) if detail[11] else []
                        risk_event['counterparty_ids'] = counterparty_ids
                    except:
                        risk_event['counterparty_ids'] = []
                    
                    # 从专门表获取增强数据
                    detection_type = risk_event.get('detection_type', '')
                    member_id = risk_event.get('member_id', '')
                    contract_name = risk_event.get('contract_name', '')
                    
                    enhanced_data = self._get_enhanced_data_for_risk(
                        specialized_data, detection_type, member_id, contract_name
                    )
                    
                    if enhanced_data:
                        risk_event.update(enhanced_data)
                    else:
                        # 回退到additional_data
                        try:
                            additional_data = json.loads(detail[12]) if detail[12] else {}
                            risk_event.update(additional_data)
                        except:
                            pass
                    
                    contract_risks.append(risk_event)
                    
                except Exception as e:
                    self.logger.error(f"解析风险事件失败: {e}")
                    continue
            
            # 查询作为对手方的记录
            counterparty_risks = self._get_counterparty_risks(conn, result_ids_str, user_id, specialized_data)
            contract_risks.extend(counterparty_risks)
            
            # 构建响应
            main_result = results[0]
            indicators = {}
            try:
                indicators = json.loads(main_result[11]) if main_result[11] else {}  # 修正：indicators在索引11
            except:
                pass
            
            summary = self._calculate_summary_stats(contract_risks)
            
            response = {
                'contract_risks': contract_risks,
                'summary': summary,
                'indicators': indicators,
                'algorithm_type': main_result[2],
                'total_analyzed': len(contract_risks),
                'risks_found': len(contract_risks),
                'created_at': main_result[12],  # 修正：created_at在索引12
                'updated_at': main_result[13],  # 修正：updated_at在索引13
                'user_filtered': True,  # 标记为用户过滤结果
                'target_user_id': user_id
            }
            
            self.logger.info(f"用户过滤响应构建成功 - 用户: {user_id}, contract_risks数量: {len(contract_risks)}")
            return response
            
        except Exception as e:
            self.logger.error(f"构建用户过滤响应失败: {e}")
            return {
                'contract_risks': [],
                'summary': {},
                'indicators': {},
                'algorithm_type': results[0][2] if results else 'unknown',
                'user_filtered': True,
                'target_user_id': user_id
            }
    
    def _get_counterparty_risks(self, conn, result_ids_str: str, user_id: str, specialized_data: Dict) -> List[Dict]:
        """获取用户作为对手方的风险记录"""
        try:
            # 查询counterparty_ids包含该用户的记录
            counterparty_sql = f"""
            SELECT * FROM contract_risk_details 
            WHERE algorithm_result_id IN ({result_ids_str})
            AND counterparty_ids LIKE ?
            """
            counterparty_details = conn.execute(counterparty_sql, [f'%{user_id}%']).fetchall()
            
            counterparty_risks = []
            for detail in counterparty_details:
                try:
                    # 检查counterparty_ids是否真的包含该用户
                    counterparty_ids = json.loads(detail[11]) if detail[11] else []
                    if user_id not in [str(cid) for cid in counterparty_ids]:
                        continue
                    
                    # 构建对手方记录
                    risk_event = {
                        'member_id': user_id,  # 将member_id设为当前用户
                        'contract_name': detail[3],
                        'detection_type': detail[4],
                        'detection_method': detail[5] + '_as_counterparty',
                        'risk_level': detail[6],
                        'risk_score': float(detail[7]) if detail[7] else 0,
                        'abnormal_volume': float(detail[8]) if detail[8] else 0,
                        'trade_count': int(detail[9]) if detail[9] else 0,
                        'time_range': detail[10],
                        'counterparty_ids': [detail[2]],  # 原始用户作为对手方
                        'original_member_id': detail[2],  # 保留原始用户ID
                        'is_counterparty_record': True
                    }
                    
                    # 从专门表获取增强数据
                    detection_type = risk_event.get('detection_type', '')
                    contract_name = risk_event.get('contract_name', '')
                    
                    enhanced_data = self._get_enhanced_data_for_risk(
                        specialized_data, detection_type, user_id, contract_name
                    )
                    
                    if enhanced_data:
                        risk_event.update(enhanced_data)
                    
                    counterparty_risks.append(risk_event)
                    
                except Exception as e:
                    self.logger.error(f"解析对手方记录失败: {e}")
                    continue
            
            self.logger.info(f"找到对手方记录数: {len(counterparty_risks)}")
            return counterparty_risks
            
        except Exception as e:
            self.logger.error(f"获取对手方风险记录失败: {e}")
            return []
    
    @PerformanceMonitor.monitor_query
    def query_specialized_details(self, result_id: int, detail_type: str = None) -> Dict:
        """查询专门算法详情 - 阶段2新功能"""
        try:
            with self.db.get_connection() as conn:
                specialized_data = {}
                
                # 查询对敲交易详情
                if not detail_type or detail_type == 'wash_trading':
                    wash_trading_data = self._query_wash_trading_details(conn, result_id)
                    if wash_trading_data:
                        specialized_data['wash_trading'] = wash_trading_data
                
                # 查询高频交易详情
                if not detail_type or detail_type == 'high_frequency_trading':
                    hft_data = self._query_hft_details(conn, result_id)
                    if hft_data:
                        specialized_data['high_frequency_trading'] = hft_data
                
                # 查询刷量交易详情
                if not detail_type or detail_type == 'brush_trading':
                    brush_data = self._query_brush_trading_details(conn, result_id)
                    if brush_data:
                        specialized_data['brush_trading'] = brush_data
                
                # 查询套利交易详情
                if not detail_type or detail_type == 'funding_arbitrage':
                    funding_data = self._query_funding_arbitrage_details(conn, result_id)
                    if funding_data:
                        specialized_data['funding_arbitrage'] = funding_data
                
                return specialized_data
                
        except Exception as e:
            self.logger.error(f"查询专门算法详情失败: {e}")
            return {}
    
    def _query_wash_trading_details(self, conn, result_id: int) -> Dict:
        """查询对敲交易专门详情"""
        try:
            # 查询主记录
            main_records = conn.execute(
                "SELECT * FROM wash_trading_results WHERE algorithm_result_id = ?",
                [result_id]
            ).fetchall()
            
            if not main_records:
                return {}
            
            wash_trading_data = []
            for main_record in main_records:
                wash_trading_id = main_record[0]  # id字段
                
                # 查询同账户对敲详情
                same_account_details = conn.execute(
                    "SELECT * FROM same_account_wash_trading WHERE wash_trading_id = ?",
                    [wash_trading_id]
                ).fetchall()
                
                # 查询跨账户对敲详情
                cross_account_details = conn.execute(
                    "SELECT * FROM cross_account_wash_trading WHERE wash_trading_id = ?",
                    [wash_trading_id]
                ).fetchall()
                
                wash_trading_data.append({
                    'main_record': {
                        'id': main_record[0],
                        'trading_type': main_record[2],
                        'pair_count': main_record[3],
                        'total_volume': float(main_record[4]) if main_record[4] else 0,
                        'avg_time_gap': main_record[5],
                        'risk_score': float(main_record[6]) if main_record[6] else 0,
                        'detection_method': main_record[7]
                    },
                    'same_account_details': [
                        {
                            'user_id': detail[2],
                            'contract_name': detail[3],
                            'long_position_id': detail[4],
                            'short_position_id': detail[5],
                            'long_open_time': detail[6],
                            'short_open_time': detail[7]
                        } for detail in same_account_details
                    ],
                    'cross_account_details': [
                        {
                            'user_a_id': detail[2],
                            'user_b_id': detail[3],
                            'contract_name': detail[4]
                        } for detail in cross_account_details
                    ]
                })
            
            return {
                'total_wash_trading_groups': len(wash_trading_data),
                'details': wash_trading_data
            }
            
        except Exception as e:
            self.logger.error(f"查询对敲交易详情失败: {e}")
            return {}
    
    def _query_hft_details(self, conn, result_id: int) -> Dict:
        """查询高频交易专门详情"""
        try:
            hft_records = conn.execute(
                "SELECT * FROM high_frequency_trading_details WHERE algorithm_result_id = ?",
                [result_id]
            ).fetchall()
            
            if not hft_records:
                return {}
            
            hft_data = []
            for record in hft_records:
                # 解析risk_indicators JSON
                risk_indicators = {}
                try:
                    risk_indicators = json.loads(record[9]) if record[9] else {}
                except:
                    pass
                
                hft_data.append({
                    'user_id': record[2],
                    'contract_name': record[3],
                    'trade_count': record[4],
                    'avg_holding_time': record[5],
                    'max_frequency': float(record[6]) if record[6] else 0,
                    'volume_concentration': float(record[7]) if record[7] else 0,
                    'time_pattern': record[8],
                    'risk_indicators': risk_indicators
                })
            
            return {
                'total_hft_cases': len(hft_data),
                'avg_max_frequency': sum(d['max_frequency'] for d in hft_data) / len(hft_data) if hft_data else 0,
                'details': hft_data
            }
            
        except Exception as e:
            self.logger.error(f"查询高频交易详情失败: {e}")
            return {}
    
    def _query_brush_trading_details(self, conn, result_id: int) -> Dict:
        """查询刷量/对敲交易专门详情（修复：查询wash_trading_pairs表）"""
        try:
            # 修复：查询正确的wash_trading_pairs表
            brush_records = conn.execute(
                "SELECT * FROM wash_trading_pairs WHERE result_id = ?",
                [result_id]
            ).fetchall()
            
            if not brush_records:
                return {}
            
            brush_data = []
            for record in brush_records:
                # 根据wash_trading_pairs表结构解析记录
                # 表结构: id, result_id, pair_index, user_a_id, user_b_id, contract_name, ...
                try:
                    # 计算异常评分（基于时间差和金额）
                    time_diff_score = min(100, abs(record[24] or 0) + abs(record[25] or 0)) / 10  # open_time_diff + close_time_diff
                    volume_score = (record[26] or 0) / 10000  # total_amount
                    abnormal_score = min(100, time_diff_score + volume_score)
                    
                    brush_data.append({
                        'user_id': record[3],  # user_a_id
                        'contract_name': record[5],  # contract_name
                        'pattern_type': 'wash_trading',  # 固定为对敲交易
                        'cycle_count': 1,  # 每个记录代表一个交易对
                        'avg_cycle_duration': (record[24] or 0) + (record[25] or 0),  # 开仓+平仓时间差
                        'volume_regularity': min(1.0, (record[26] or 0) / 100000),  # 基于总金额的规律性
                        'time_regularity': min(1.0, 60 / max(1, abs(record[24] or 1)+ abs(record[25] or 1))),  # 基于时间差的规律性
                        'abnormal_score': abnormal_score,
                        'pattern_details': {
                            'pair_index': record[2],
                            'user_b_id': record[4],
                            'total_amount': record[26],
                            'net_profit': record[27],
                            'open_time_diff': record[24],
                            'close_time_diff': record[25]
                        }
                    })
                except Exception as parse_error:
                    self.logger.warning(f"解析对敲记录失败: {parse_error}")
                    continue
            
            return {
                'total_brush_cases': len(brush_data),
                'avg_abnormal_score': sum(d['abnormal_score'] for d in brush_data) / len(brush_data) if brush_data else 0,
                'details': brush_data
            }
            
        except Exception as e:
            self.logger.error(f"查询刷量交易详情失败: {e}")
            return {}
    
    def _query_funding_arbitrage_details(self, conn, result_id: int) -> Dict:
        """查询资金费率套利专门详情"""
        try:
            funding_records = conn.execute(
                "SELECT * FROM funding_rate_arbitrage_details WHERE algorithm_result_id = ?",
                [result_id]
            ).fetchall()
            
            if not funding_records:
                return {}
            
            funding_data = []
            for record in funding_records:
                # 解析market_conditions JSON
                market_conditions = {}
                try:
                    market_conditions = json.loads(record[9]) if record[9] else {}
                except:
                    pass
                
                funding_data.append({
                    'user_id': record[2],
                    'contract_name': record[3],
                    'funding_rate': float(record[4]) if record[4] else 0,
                    'position_size': float(record[5]) if record[5] else 0,
                    'holding_duration': record[6],
                    'estimated_profit': float(record[7]) if record[7] else 0,
                    'risk_exposure': float(record[8]) if record[8] else 0,
                    'market_conditions': market_conditions
                })
            
            return {
                'total_arbitrage_cases': len(funding_data),
                'total_estimated_profit': sum(d['estimated_profit'] for d in funding_data),
                'avg_position_size': sum(d['position_size'] for d in funding_data) / len(funding_data) if funding_data else 0,
                'details': funding_data
            }
            
        except Exception as e:
            self.logger.error(f"查询资金费率套利详情失败: {e}")
            return {}
    
    def _build_enhanced_response(self, conn, results: List) -> Dict:
        """构建增强的响应格式，包含专门表数据"""
        try:
            if not results:
                return {}
            
            # 获取基础响应
            base_response = self._build_complete_response(conn, results)
            
            # 获取result_id用于查询专门表
            result_id = results[0][0]  # algorithm_results.id
            self.logger.info(f"构建增强响应，result_id: {result_id}")
            
            # 查询专门算法详情
            specialized_details = self.query_specialized_details(result_id)
            self.logger.info(f"查询到专门详情: {bool(specialized_details)}, 类型: {list(specialized_details.keys()) if specialized_details else 'None'}")
            
            # 合并到响应中
            if specialized_details:
                base_response['specialized_details'] = specialized_details
                
                # 添加增强的汇总统计
                enhanced_summary = self._calculate_enhanced_summary(specialized_details)
                base_response['enhanced_summary'] = enhanced_summary
                self.logger.info("增强响应构建成功")
            else:
                self.logger.warning("没有专门详情，跳过增强功能")
            
            return base_response
            
        except Exception as e:
            self.logger.error(f"构建增强响应失败: {e}")
            import traceback
            traceback.print_exc()
            return self._build_complete_response(conn, results)
    
    def _calculate_enhanced_summary(self, specialized_details: Dict) -> Dict:
        """计算增强的汇总统计"""
        summary = {
            'specialized_analysis': {}
        }
        
        # 对敲交易汇总
        if 'wash_trading' in specialized_details:
            wash_data = specialized_details['wash_trading']
            summary['specialized_analysis']['wash_trading'] = {
                'total_groups': wash_data.get('total_wash_trading_groups', 0),
                'total_pairs': sum(detail['main_record']['pair_count'] for detail in wash_data.get('details', [])),
                'avg_time_gap': sum(detail['main_record']['avg_time_gap'] for detail in wash_data.get('details', [])) / len(wash_data.get('details', [])) if wash_data.get('details') else 0
            }
        
        # 高频交易汇总
        if 'high_frequency_trading' in specialized_details:
            hft_data = specialized_details['high_frequency_trading']
            summary['specialized_analysis']['high_frequency_trading'] = {
                'total_cases': hft_data.get('total_hft_cases', 0),
                'avg_max_frequency': hft_data.get('avg_max_frequency', 0)
            }
        
        # 刷量交易汇总
        if 'brush_trading' in specialized_details:
            brush_data = specialized_details['brush_trading']
            summary['specialized_analysis']['brush_trading'] = {
                'total_cases': brush_data.get('total_brush_cases', 0),
                'avg_abnormal_score': brush_data.get('avg_abnormal_score', 0)
            }
        
        # 套利交易汇总
        if 'funding_arbitrage' in specialized_details:
            funding_data = specialized_details['funding_arbitrage']
            summary['specialized_analysis']['funding_arbitrage'] = {
                'total_cases': funding_data.get('total_arbitrage_cases', 0),
                'total_estimated_profit': funding_data.get('total_estimated_profit', 0),
                'avg_position_size': funding_data.get('avg_position_size', 0)
            }
        
        return summary
    
    def _get_all_specialized_data_for_enhancement(self, conn, result_id: int) -> Dict:
        """获取所有专门表数据用于增强基础风险事件"""
        specialized_data = {
            'wash_trading': {},
            'high_frequency_trading': {},
            'brush_trading': {},
            'funding_arbitrage': {}
        }
        
        try:
            # 获取对敲交易专门数据
            wash_data = self._query_wash_trading_details(conn, result_id)
            if wash_data:
                specialized_data['wash_trading'] = wash_data
            
            # 获取高频交易专门数据
            hft_data = self._query_hft_details(conn, result_id)
            if hft_data:
                specialized_data['high_frequency_trading'] = hft_data
            
            # 获取刷量交易专门数据
            brush_data = self._query_brush_trading_details(conn, result_id)
            if brush_data:
                specialized_data['brush_trading'] = brush_data
            
            # 获取套利交易专门数据
            funding_data = self._query_funding_arbitrage_details(conn, result_id)
            if funding_data:
                specialized_data['funding_arbitrage'] = funding_data
                
            return specialized_data
                
        except Exception as e:
            self.logger.error(f"获取专门表数据失败: {e}")
            return specialized_data
    
    def _get_enhanced_data_for_risk(self, specialized_data: Dict, detection_type: str, 
                                  member_id: str, contract_name: str) -> Dict:
        """根据风险类型和标识符从专门表数据中获取增强数据"""
        enhanced_data = {}
        
        try:
            # 对敲交易增强（已移除废弃的wash_trading_optimized）
            if detection_type in ['suspected_wash_trading', 'wash_trading']:
                wash_data = specialized_data.get('wash_trading', {})
                for detail in wash_data.get('details', []):
                    main_record = detail.get('main_record', {})
                    same_account_details = detail.get('same_account_details', [])
                    cross_account_details = detail.get('cross_account_details', [])
                    
                    # 查找匹配的对敲详情
                    for same_detail in same_account_details:
                        if same_detail.get('user_id') == member_id and same_detail.get('contract_name') == contract_name:
                            enhanced_data.update({
                                'wash_trading_pairs': [
                                    {
                                        'long_position_id': same_detail.get('long_position_id'),
                                        'short_position_id': same_detail.get('short_position_id'),
                                        'long_open_time': same_detail.get('long_open_time'),
                                        'short_open_time': same_detail.get('short_open_time')
                                    }
                                ],
                                'wash_trading_type': 'same_account',
                                'wash_trading_group_id': main_record.get('id'),
                                'avg_time_gap': main_record.get('avg_time_gap', 0)
                            })
                            break
                    
                    for cross_detail in cross_account_details:
                        if (cross_detail.get('user_a_id') == member_id or cross_detail.get('user_b_id') == member_id) and \
                           cross_detail.get('contract_name') == contract_name:
                            enhanced_data.update({
                                'wash_trading_type': 'cross_account',
                                'wash_trading_group_id': main_record.get('id'),
                                'counterparty_user': cross_detail.get('user_b_id') if cross_detail.get('user_a_id') == member_id else cross_detail.get('user_a_id')
                            })
                            break
            
            # 高频交易增强
            elif detection_type in ['high_frequency_trading', 'hft', 'high_frequency']:
                hft_data = specialized_data.get('high_frequency_trading', {})
                for detail in hft_data.get('details', []):
                    if detail.get('user_id') == member_id and detail.get('contract_name') == contract_name:
                        enhanced_data.update({
                            'max_frequency': detail.get('max_frequency', 0),
                            'avg_holding_time': detail.get('avg_holding_time', 0),
                            'volume_concentration': detail.get('volume_concentration', 0),
                            'time_pattern': detail.get('time_pattern', ''),
                            'risk_indicators': detail.get('risk_indicators', {})
                        })
                        break
            
            # 刷量交易增强
            elif detection_type in ['regular_brush_trading', 'brush_trading', 'volume_manipulation']:
                brush_data = specialized_data.get('brush_trading', {})
                for detail in brush_data.get('details', []):
                    if detail.get('user_id') == member_id and detail.get('contract_name') == contract_name:
                        enhanced_data.update({
                            'pattern_type': detail.get('pattern_type', ''),
                            'cycle_count': detail.get('cycle_count', 0),
                            'avg_cycle_duration': detail.get('avg_cycle_duration', 0),
                            'volume_regularity': detail.get('volume_regularity', 0),
                            'time_regularity': detail.get('time_regularity', 0),
                            'abnormal_score': detail.get('abnormal_score', 0),
                            'pattern_details': detail.get('pattern_details', {})
                        })
                        break
            
            # 套利交易增强
            elif detection_type in ['funding_rate_arbitrage', 'funding_arbitrage']:
                funding_data = specialized_data.get('funding_arbitrage', {})
                for detail in funding_data.get('details', []):
                    if detail.get('user_id') == member_id and detail.get('contract_name') == contract_name:
                        enhanced_data.update({
                            'arbitrage_efficiency': detail.get('arbitrage_efficiency', 0),
                            'estimated_profit': detail.get('estimated_profit', 0),
                            'position_size': detail.get('position_size', 0),
                            'funding_rate_diff': detail.get('funding_rate_diff', 0),
                            'market_conditions': detail.get('market_conditions', {})
                        })
                        break
            
            return enhanced_data
            
        except Exception as e:
            self.logger.error(f"获取增强数据失败: {e}")
            return {}
    
    def _build_complete_response(self, conn, results: List) -> Dict:
        """构建完整的响应格式，包含从专门表增强的contract_risks"""
        try:
            if not results:
                return {}
            
            # 获取所有result_ids
            result_ids = [result[0] for result in results]  # id字段
            result_ids_str = ','.join(map(str, result_ids))
            
            # 查询所有相关的风险事件详情
            contract_risks_sql = f"""
            SELECT * FROM contract_risk_details 
            WHERE algorithm_result_id IN ({result_ids_str})
            ORDER BY created_at
            """
            risk_details = conn.execute(contract_risks_sql).fetchall()
            
            # 重构contract_risks数组，并从专门表获取增强数据
            contract_risks = []
            result_id = result_ids[0]  # 使用第一个result_id查询专门表
            
            # 预先查询所有专门表数据
            specialized_data = self._get_all_specialized_data_for_enhancement(conn, result_id)
            
            for detail in risk_details:
                try:
                    # 解析数据库字段
                    risk_event = {
                        'member_id': detail[2],  # member_id
                        'contract_name': detail[3],  # contract_name
                        'detection_type': detail[4],  # detection_type
                        'detection_method': detail[5],  # detection_method
                        'risk_level': detail[6],  # risk_level
                        'risk_score': float(detail[7]) if detail[7] else 0,  # risk_score
                        'abnormal_volume': float(detail[8]) if detail[8] else 0,  # abnormal_volume
                        'trade_count': int(detail[9]) if detail[9] else 0,  # trade_count
                        'time_range': detail[10],  # time_range
                    }
                    
                    # 解析counterparty_ids
                    try:
                        counterparty_ids = json.loads(detail[11]) if detail[11] else []
                        risk_event['counterparty_ids'] = counterparty_ids
                    except:
                        risk_event['counterparty_ids'] = []
                    
                    # ========== 关键修改：使用专门表数据而不是additional_data ==========
                    # 根据detection_type从专门表获取增强数据
                    detection_type = risk_event.get('detection_type', '')
                    member_id = risk_event.get('member_id', '')
                    contract_name = risk_event.get('contract_name', '')
                    
                    enhanced_data = self._get_enhanced_data_for_risk(
                        specialized_data, detection_type, member_id, contract_name
                    )
                    
                    if enhanced_data:
                        risk_event.update(enhanced_data)
                        self.logger.debug(f"增强风险事件数据: {detection_type} - {member_id}")
                    else:
                        # 如果没有专门表数据，回退到additional_data（向后兼容）
                        try:
                            additional_data = json.loads(detail[12]) if detail[12] else {}
                            risk_event.update(additional_data)
                            self.logger.debug(f"使用additional_data回退: {detection_type} - {member_id}")
                        except:
                            pass
                    
                    contract_risks.append(risk_event)
                    
                except Exception as e:
                    self.logger.error(f"解析风险事件失败: {e}")
                    continue
            
            # 构建主结果
            main_result = results[0]
            indicators = {}
            try:
                indicators = json.loads(main_result[11]) if main_result[11] else {}  # 修正：indicators在索引11
            except:
                pass
            
            # 计算汇总统计
            summary = self._calculate_summary_stats(contract_risks)
            
            response = {
                'contract_risks': contract_risks,
                'summary': summary,
                'indicators': indicators,
                'algorithm_type': main_result[2],  # algorithm_type
                'total_analyzed': len(contract_risks),
                'risks_found': len(contract_risks),
                'created_at': main_result[12],  # 修正：created_at在索引12
                'updated_at': main_result[13]   # 修正：updated_at在索引13
            }
            
            self.logger.info(f"重构完整响应成功: contract_risks数量={len(contract_risks)}")
            return response
            
        except Exception as e:
            self.logger.error(f"构建完整响应失败: {e}")
            return {
                'contract_risks': [],
                'summary': {},
                'indicators': {},
                'algorithm_type': results[0][2] if results else 'unknown'
            }
    
    def _calculate_summary_stats(self, contract_risks: List[Dict]) -> Dict:
        """计算汇总统计"""
        if not contract_risks:
            return {
                'risk_distribution': {'high': 0, 'medium': 0, 'low': 0},
                'total_volume': 0,
                'total_trades': 0,
                'avg_risk_score': 0,
                'detection_type_distribution': {}
            }
        
        total_volume = sum(risk.get('abnormal_volume', 0) for risk in contract_risks)
        total_trades = sum(risk.get('trade_count', 0) for risk in contract_risks)
        avg_risk_score = sum(risk.get('risk_score', 0) for risk in contract_risks) / len(contract_risks)
        
        # 计算风险等级分布
        risk_distribution = {'high': 0, 'medium': 0, 'low': 0}
        for risk in contract_risks:
            risk_level = risk.get('risk_level', 'medium')
            
            # 统一映射风险等级
            if str(risk_level).lower() in ['高', 'high', 'critical']:
                risk_distribution['high'] += 1
            elif str(risk_level).lower() in ['低', 'low']:
                risk_distribution['low'] += 1
            elif str(risk_level).lower() in ['中', 'medium', 'normal']:
                risk_distribution['medium'] += 1
            else:
                # 默认为中风险
                risk_distribution['medium'] += 1
        
        # 按检测类型分组统计
        detection_type_stats = {}
        for risk in contract_risks:
            detection_type = risk.get('detection_type', 'unknown')
            if detection_type not in detection_type_stats:
                detection_type_stats[detection_type] = 0
            detection_type_stats[detection_type] += 1
        
        return {
            'risk_distribution': risk_distribution,  # 添加风险等级分布
            'total_volume': total_volume,
            'total_trades': total_trades,
            'avg_risk_score': avg_risk_score,
            'detection_type_distribution': detection_type_stats
        }
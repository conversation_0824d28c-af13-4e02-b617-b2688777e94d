#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户交易行为分析数据库初始化脚本
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from database.duckdb_manager import DuckDBManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UserBehaviorStorageInitializer:
    """用户行为分析存储初始化器"""
    
    def __init__(self):
        self.db_manager = DuckDBManager()
        self.schema_dir = Path(__file__).parent / "schema"
        
    def initialize_storage(self):
        """初始化用户行为分析存储"""
        try:
            logger.info("开始初始化用户行为分析数据库...")
            
            # 1. 创建用户行为分析表
            self._create_user_behavior_tables()
            
            # 2. 验证表结构
            self._verify_tables()
            
            logger.info("用户行为分析数据库初始化完成！")
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    def _create_user_behavior_tables(self):
        """创建用户行为分析表"""
        # 优先使用统一的user_trading_profiles表创建脚本
        unified_sql_file = self.schema_dir / "user_trading_profiles_unified.sql"
        fallback_sql_file = self.schema_dir / "user_behavior_tables.sql"

        # 选择要使用的SQL文件
        if unified_sql_file.exists():
            sql_file = unified_sql_file
            logger.info("使用统一的user_trading_profiles表创建脚本...")
        elif fallback_sql_file.exists():
            sql_file = fallback_sql_file
            logger.info("使用传统的用户行为分析表创建脚本...")
        else:
            raise FileNotFoundError(f"SQL文件不存在: {unified_sql_file} 或 {fallback_sql_file}")

        logger.info(f"创建用户行为分析表，使用文件: {sql_file.name}")

        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        # 分割SQL语句并执行
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]

        for i, statement in enumerate(sql_statements):
            try:
                logger.debug(f"执行SQL语句 {i+1}/{len(sql_statements)}")
                self.db_manager.execute_sql(statement)
            except Exception as e:
                logger.error(f"执行SQL语句失败 ({i+1}): {e}")
                logger.error(f"SQL内容: {statement[:200]}...")
                raise

        logger.info("用户行为分析表创建完成")
    
    def _verify_tables(self):
        """验证表结构"""
        logger.info("验证表结构...")
        
        # 检查主要表是否存在 - 使用DuckDB的INFORMATION_SCHEMA
        required_tables = [
            'user_trading_profiles'
        ]
        
        for table_name in required_tables:
            try:
                # 使用DuckDB的方式检查表是否存在
                result = self.db_manager.fetch_all(
                    "SELECT table_name FROM information_schema.tables WHERE table_name = ?",
                    [table_name]
                )
                
                if not result:
                    raise Exception(f"表 {table_name} 创建失败")
                
                logger.info(f"✓ 表 {table_name} 创建成功")
            except Exception as e:
                # 如果INFORMATION_SCHEMA查询失败，尝试直接查询表
                try:
                    self.db_manager.fetch_all(f"SELECT 1 FROM {table_name} LIMIT 1")
                    logger.info(f"✓ 表 {table_name} 创建成功")
                except:
                    raise Exception(f"表 {table_name} 创建失败: {e}")
        
        logger.info("表结构验证完成")
    
    def create_sample_data(self):
        """创建示例数据（用于测试）"""
        logger.info("创建示例数据...")
        
        try:
            # 插入示例用户交易画像数据
            sample_data = {
                'member_id': 'test_user_001',
                'analysis_date': '2024-01-15',
                'analysis_period_start': '2024-01-01 00:00:00',
                'analysis_period_end': '2024-01-31 23:59:59',
                'total_volume': 150000.00,
                'total_trades': 45,
                'win_rate': 0.6667,
                'profit_factor': 1.85,
                'professional_score': 72.5,
                'trader_type': '半专业交易员',
                'fund_scale_category': '中户'
            }
            
            # 先删除可能存在的示例数据
            delete_sql = "DELETE FROM user_trading_profiles WHERE member_id = ?"
            self.db_manager.execute_sql(delete_sql, [sample_data['member_id']])
            
            # 获取下一个ID
            next_id_result = self.db_manager.fetch_all("SELECT nextval('user_trading_profiles_id_seq') as next_id")
            next_id = next_id_result[0]['next_id']
            
            # 插入新的示例数据（手动指定ID）
            sql = """
            INSERT INTO user_trading_profiles 
            (id, member_id, analysis_date, analysis_period_start, analysis_period_end,
             total_volume, total_trades, win_rate, profit_factor, 
             professional_score, trader_type, fund_scale_category)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            self.db_manager.execute_sql(sql, [
                next_id,
                sample_data['member_id'],
                sample_data['analysis_date'],
                sample_data['analysis_period_start'],
                sample_data['analysis_period_end'],
                sample_data['total_volume'],
                sample_data['total_trades'],
                sample_data['win_rate'],
                sample_data['profit_factor'],
                sample_data['professional_score'],
                sample_data['trader_type'],
                sample_data['fund_scale_category']
            ])
            
            logger.info("示例数据创建完成")
            
        except Exception as e:
            logger.error(f"创建示例数据失败: {e}")
            raise
    
    def check_storage_status(self):
        """检查存储状态"""
        logger.info("检查用户行为分析存储状态...")
        
        try:
            # 检查用户交易画像表
            result = self.db_manager.fetch_all(
                "SELECT COUNT(*) as count FROM user_trading_profiles"
            )
            profile_count = result[0]['count'] if result else 0
            
            status = {
                'user_trading_profiles': profile_count,
                'status': 'ready'
            }
            
            logger.info(f"存储状态: {status}")
            return status
            
        except Exception as e:
            logger.error(f"检查存储状态失败: {e}")
            return {'status': 'error', 'error': str(e)}

def main():
    """主函数"""
    initializer = UserBehaviorStorageInitializer()
    
    # 初始化存储
    if initializer.initialize_storage():
        print("✓ 用户行为分析数据库初始化成功")
        
        # 创建示例数据
        try:
            initializer.create_sample_data()
            print("✓ 示例数据创建成功")
        except Exception as e:
            print(f"⚠ 示例数据创建失败: {e}")
        
        # 检查状态
        status = initializer.check_storage_status()
        print(f"✓ 存储状态检查完成: {status}")
        
    else:
        print("✗ 用户行为分析数据库初始化失败")
        sys.exit(1)

if __name__ == "__main__":
    main() 
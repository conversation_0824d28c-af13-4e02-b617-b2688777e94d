#!/bin/bash

# =============================================================================
# 风险链路分析工具 - Linux生产环境启动脚本
# 支持IP地址配置、虚拟环境管理、Redis配置和启动
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# =============================================================================
# 配置参数 - 可根据实际环境修改
# =============================================================================

# 服务器配置
SERVER_HOST=${SERVER_HOST:-"0.0.0.0"}  # 监听所有接口，可通过环境变量覆盖
FRONTEND_PORT=${FRONTEND_PORT:-3000}
BACKEND_PORT=${BACKEND_PORT:-5005}

# 虚拟环境配置
VENV_NAME=${VENV_NAME:-"risk_analysis_env"}
VENV_PATH=${VENV_PATH:-"./venv"}
PYTHON_VERSION=${PYTHON_VERSION:-"python3"}

# Redis配置
REDIS_HOST=${REDIS_HOST:-"localhost"}
REDIS_PORT=${REDIS_PORT:-6379}
REDIS_PASSWORD=${REDIS_PASSWORD:-""}
REDIS_DB=${REDIS_DB:-0}

# 环境配置
export FLASK_ENV=production
export NODE_ENV=production

# =============================================================================
# 工具函数
# =============================================================================

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "命令 $1 未找到，请先安装"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warn "端口 $port 已被占用"
        return 1
    fi
    return 0
}

# 停止端口上的进程
kill_port() {
    local port=$1
    log_step "停止端口 $port 上的进程..."
    
    # 尝试优雅停止
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    if [ -n "$pids" ]; then
        echo $pids | xargs kill -TERM 2>/dev/null || true
        sleep 3
        
        # 强制停止仍在运行的进程
        pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$pids" ]; then
            echo $pids | xargs kill -9 2>/dev/null || true
            sleep 1
        fi
    fi
}

# 检查Redis连接
check_redis() {
    log_step "检查Redis连接..."
    
    local redis_cmd="redis-cli"
    if [ -n "$REDIS_PASSWORD" ]; then
        redis_cmd="redis-cli -a $REDIS_PASSWORD"
    fi
    
    if [ "$REDIS_HOST" != "localhost" ] && [ "$REDIS_HOST" != "127.0.0.1" ]; then
        redis_cmd="$redis_cmd -h $REDIS_HOST -p $REDIS_PORT"
    fi
    
    if ! $redis_cmd ping >/dev/null 2>&1; then
        log_error "无法连接到Redis服务器 ($REDIS_HOST:$REDIS_PORT)"
        log_info "请确保Redis服务已启动，或运行: sudo systemctl start redis"
        return 1
    fi
    
    log_info "Redis连接正常"
    return 0
}

# 启动Redis服务
start_redis() {
    log_step "启动Redis服务..."
    
    # 检查Redis是否已经运行
    if check_redis; then
        log_info "Redis已在运行"
        return 0
    fi
    
    # 尝试启动Redis服务
    if command -v systemctl &> /dev/null; then
        sudo systemctl start redis-server || sudo systemctl start redis
        sleep 2
    elif command -v service &> /dev/null; then
        sudo service redis-server start || sudo service redis start
        sleep 2
    else
        log_warn "无法自动启动Redis，请手动启动Redis服务"
        return 1
    fi
    
    # 再次检查连接
    if ! check_redis; then
        log_error "Redis启动失败"
        return 1
    fi
    
    log_info "Redis启动成功"
    return 0
}

# =============================================================================
# 主要功能函数
# =============================================================================

# 环境检查
check_environment() {
    log_step "检查运行环境..."

    # 检查必要的命令
    check_command $PYTHON_VERSION
    check_command npm
    check_command node

    # 检查Python版本
    local python_ver=$($PYTHON_VERSION --version 2>&1 | cut -d' ' -f2)
    log_info "Python版本: $python_ver"

    # 检查Node.js版本
    local node_ver=$(node --version)
    log_info "Node.js版本: $node_ver"

    # 检查python3-venv是否可用
    if ! $PYTHON_VERSION -m venv --help >/dev/null 2>&1; then
        log_error "python3-venv模块未安装"
        log_info "请运行: sudo apt install python3-venv python3-full"
        exit 1
    fi

    # 检查项目目录
    if [ ! -f "backend/app.py" ]; then
        log_error "未找到backend/app.py，请确保在项目根目录运行此脚本"
        exit 1
    fi

    if [ ! -f "package.json" ]; then
        log_error "未找到package.json，请确保在项目根目录运行此脚本"
        exit 1
    fi

    log_info "环境检查完成"
}

# 设置虚拟环境
setup_virtualenv() {
    log_step "设置Python虚拟环境..."

    # 创建虚拟环境（如果不存在）
    if [ ! -d "$VENV_PATH" ]; then
        log_info "创建虚拟环境: $VENV_PATH"
        $PYTHON_VERSION -m venv "$VENV_PATH"
    fi

    # 激活虚拟环境
    source "$VENV_PATH/bin/activate"

    # 验证虚拟环境是否正确激活
    if [ -z "$VIRTUAL_ENV" ]; then
        log_error "虚拟环境激活失败 - VIRTUAL_ENV未设置"
        exit 1
    fi

    # 获取绝对路径进行比较
    local abs_venv_path=$(realpath "$VENV_PATH")
    if [[ "$VIRTUAL_ENV" != "$abs_venv_path" ]]; then
        log_error "虚拟环境路径不匹配"
        log_error "期望: $abs_venv_path"
        log_error "实际: $VIRTUAL_ENV"
        exit 1
    fi

    log_info "虚拟环境已激活: $VIRTUAL_ENV"

    # 升级pip（在虚拟环境中）
    log_info "升级pip..."
    python -m pip install --upgrade pip

    # 安装依赖
    log_info "安装Python依赖..."
    if [ -f "backend/requirements.txt" ]; then
        python -m pip install -r backend/requirements.txt
    else
        log_error "未找到backend/requirements.txt"
        exit 1
    fi

    log_info "虚拟环境设置完成"
}

# 安装前端依赖
install_frontend_deps() {
    log_step "安装前端依赖..."
    
    if [ ! -d "node_modules" ]; then
        log_info "安装npm依赖..."
        npm install
    else
        log_info "npm依赖已存在，跳过安装"
    fi
    
    log_info "前端依赖安装完成"
}

# 构建前端
build_frontend() {
    log_step "构建前端应用..."
    
    log_info "运行前端构建..."
    npm run build
    
    log_info "前端构建完成"
}

# 停止现有服务
stop_services() {
    log_step "停止现有服务..."

    # 停止Python应用
    pkill -f "python.*app.py" 2>/dev/null || true
    pkill -f "gunicorn" 2>/dev/null || true

    # 停止前端服务
    pkill -f "webpack.*serve" 2>/dev/null || true
    pkill -f "node.*webpack" 2>/dev/null || true
    pkill -f "npm.*start" 2>/dev/null || true

    # 停止端口上的进程
    kill_port $BACKEND_PORT
    kill_port $FRONTEND_PORT

    sleep 2
    log_info "现有服务已停止"
}

# 设置环境变量
setup_environment() {
    log_step "设置环境变量..."
    
    # 导出Redis配置
    export REDIS_HOST=$REDIS_HOST
    export REDIS_PORT=$REDIS_PORT
    export REDIS_DB=$REDIS_DB
    if [ -n "$REDIS_PASSWORD" ]; then
        export REDIS_PASSWORD=$REDIS_PASSWORD
    fi
    
    # 导出应用配置
    export FLASK_ENV=production
    export NODE_ENV=production
    
    log_info "环境变量设置完成"
}

# 启动后端服务
start_backend() {
    log_step "启动后端服务..."

    # 确保在虚拟环境中
    source "$VENV_PATH/bin/activate"

    # 验证虚拟环境是否正确激活
    if [ -z "$VIRTUAL_ENV" ]; then
        log_error "虚拟环境未正确激活 - VIRTUAL_ENV未设置"
        exit 1
    fi

    # 进入后端目录
    cd backend

    # 启动Flask应用
    log_info "启动Flask应用 (端口: $BACKEND_PORT)..."
    nohup "$VIRTUAL_ENV/bin/python" app.py > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!

    # 返回项目根目录
    cd ..

    # 等待后端启动
    log_info "等待后端服务启动..."
    sleep 5

    # 检查后端是否启动成功
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        log_error "后端服务启动失败，查看日志: logs/backend.log"
        if [ -f "logs/backend.log" ]; then
            log_error "最后几行日志:"
            tail -10 logs/backend.log
        fi
        exit 1
    fi

    log_info "后端服务启动成功 (PID: $BACKEND_PID)"
    echo $BACKEND_PID > .backend.pid
}

# 启动前端服务（使用测试环境的3000端口）
start_frontend() {
    log_step "启动前端服务..."

    # 启动前端开发服务器
    log_info "启动前端服务 (端口: $FRONTEND_PORT)..."
    nohup npm start > logs/frontend.log 2>&1 &
    FRONTEND_PID=$!

    # 等待前端启动
    log_info "等待前端服务启动..."
    sleep 5

    # 检查前端是否启动成功
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        log_error "前端服务启动失败"
        exit 1
    fi

    log_info "前端服务启动成功 (PID: $FRONTEND_PID)"
    echo $FRONTEND_PID > .frontend.pid
}

# 显示服务信息
show_service_info() {
    log_step "服务信息"
    
    echo "======================================"
    echo "🚀 风险链路分析工具已启动"
    echo "======================================"
    echo "环境: 生产环境 (Production)"
    echo "后端地址: http://$SERVER_HOST:$BACKEND_PORT"
    echo "前端地址: http://$SERVER_HOST:$FRONTEND_PORT"
    echo "Redis: $REDIS_HOST:$REDIS_PORT"
    echo "======================================"
    echo "日志文件: logs/backend.log, logs/frontend.log"
    echo "PID文件: .backend.pid, .frontend.pid"
    echo "======================================"
}

# 清理函数
cleanup() {
    log_step "清理进程..."

    if [ -f ".backend.pid" ]; then
        local pid=$(cat .backend.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            log_info "后端进程已停止 (PID: $pid)"
        fi
        rm -f .backend.pid
    fi

    if [ -f ".frontend.pid" ]; then
        local pid=$(cat .frontend.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            log_info "前端进程已停止 (PID: $pid)"
        fi
        rm -f .frontend.pid
    fi

    log_info "清理完成"
}

# =============================================================================
# 主程序
# =============================================================================

main() {
    log_info "启动风险链路分析工具 - Linux生产环境"
    
    # 确保脚本从项目根目录运行
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    cd "$SCRIPT_DIR"
    
    # 创建日志目录
    mkdir -p logs
    
    # 设置信号处理
    trap cleanup EXIT INT TERM
    
    # 执行启动流程
    check_environment
    stop_services
    start_redis
    setup_virtualenv
    install_frontend_deps
    build_frontend
    setup_environment
    start_backend
    start_frontend
    show_service_info
    
    # 保持脚本运行
    log_info "按 Ctrl+C 停止服务"
    while true; do
        sleep 10
        # 检查后端进程是否还在运行
        if [ -f ".backend.pid" ]; then
            local pid=$(cat .backend.pid)
            if ! kill -0 $pid 2>/dev/null; then
                log_error "后端进程意外停止"
                exit 1
            fi
        fi
        # 检查前端进程是否还在运行
        if [ -f ".frontend.pid" ]; then
            local pid=$(cat .frontend.pid)
            if ! kill -0 $pid 2>/dev/null; then
                log_error "前端进程意外停止"
                exit 1
            fi
        fi
    done
}

# 检查是否直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

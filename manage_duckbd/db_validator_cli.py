#!/usr/bin/env python3
"""
数据库验证命令行工具
用于验证、扫描和清理数据库文件
"""

import sys
import os
import argparse

# 添加backend路径到sys.path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
backend_path = os.path.join(project_root, 'backend')
sys.path.insert(0, backend_path)

def scan_databases(args):
    """扫描数据库文件"""
    try:
        from database.db_validator import db_validator
        
        print("🔍 扫描数据库文件...")
        report = db_validator.generate_report()
        print(report)
        
    except Exception as e:
        print(f"❌ 扫描失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def validate_path(args):
    """验证数据库路径"""
    try:
        from database.db_validator import validate_and_fix_db_path
        
        db_path = args.path
        print(f"🔍 验证数据库路径: {db_path}")
        
        fixed_path, warnings = validate_and_fix_db_path(db_path)
        
        if warnings:
            print("\n⚠️  警告信息:")
            for warning in warnings:
                print(f"   - {warning}")
        
        print(f"\n✅ 验证结果:")
        print(f"   原始路径: {db_path}")
        print(f"   修正路径: {fixed_path}")
        
        if fixed_path != db_path:
            print("   📝 路径已被修正")
        else:
            print("   ✅ 路径有效")
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        sys.exit(1)

def cleanup_files(args):
    """清理无效文件"""
    try:
        from database.db_validator import db_validator
        
        dry_run = not args.force
        
        if dry_run:
            print("🔍 预览模式 - 扫描可清理的文件...")
        else:
            print("🧹 清理模式 - 删除无效文件...")
        
        result = db_validator.cleanup_invalid_files(dry_run=dry_run)
        
        print(f"\n📊 清理结果:")
        
        if result['deleted']:
            action = "将删除" if dry_run else "已删除"
            print(f"   {action}的文件 ({len(result['deleted'])}):")
            for file in result['deleted']:
                print(f"     - {file}")
        
        if result['skipped']:
            print(f"   跳过的文件 ({len(result['skipped'])}):")
            for file in result['skipped']:
                print(f"     - {file}")
        
        if result['errors']:
            print(f"   错误 ({len(result['errors'])}):")
            for error in result['errors']:
                print(f"     - {error}")
        
        if not any(result.values()):
            print("   ✅ 没有发现需要清理的文件")
        
        if dry_run and result['deleted']:
            print(f"\n💡 提示: 使用 --force 参数实际执行删除操作")
            
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def show_config(args):
    """显示数据库配置"""
    try:
        from config.database_config import db_config
        
        print("📋 数据库配置信息:")
        print(f"   项目根目录: {db_config.project_root}")
        print(f"   主数据库路径: {db_config.get_main_db_path()}")
        print(f"   数据目录: {db_config.DB_CONFIG['DATA_DIR']}")
        print(f"   备份目录: {db_config.get_backup_dir()}")
        print(f"   日志文件: {db_config.get_log_file()}")
        print(f"   测试数据库: {db_config.get_test_db_path()}")
        
        # 检查目录是否存在
        print(f"\n📁 目录状态:")
        dirs_to_check = [
            ("数据目录", db_config.DB_CONFIG['DATA_DIR']),
            ("备份目录", db_config.get_backup_dir()),
            ("临时目录", db_config.DB_CONFIG['TEMP_DIRECTORY'])
        ]
        
        for name, path in dirs_to_check:
            exists = "✅ 存在" if os.path.exists(path) else "❌ 不存在"
            print(f"   {name}: {exists}")
        
        # 检查主数据库文件
        main_db = db_config.get_main_db_path()
        if os.path.exists(main_db):
            size_mb = os.path.getsize(main_db) / 1024 / 1024
            print(f"\n💾 主数据库:")
            print(f"   状态: ✅ 存在")
            print(f"   大小: {size_mb:.2f} MB")
        else:
            print(f"\n💾 主数据库:")
            print(f"   状态: ❌ 不存在")
            
    except Exception as e:
        print(f"❌ 获取配置失败: {e}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description="数据库验证和管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python db_validator_cli.py scan                    # 扫描所有数据库文件
  python db_validator_cli.py validate /path/to/db    # 验证数据库路径
  python db_validator_cli.py cleanup                 # 预览可清理的文件
  python db_validator_cli.py cleanup --force         # 实际清理文件
  python db_validator_cli.py config                  # 显示配置信息
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # scan 命令
    scan_parser = subparsers.add_parser('scan', help='扫描数据库文件')
    scan_parser.set_defaults(func=scan_databases)
    
    # validate 命令
    validate_parser = subparsers.add_parser('validate', help='验证数据库路径')
    validate_parser.add_argument('path', help='要验证的数据库路径')
    validate_parser.set_defaults(func=validate_path)
    
    # cleanup 命令
    cleanup_parser = subparsers.add_parser('cleanup', help='清理无效文件')
    cleanup_parser.add_argument('--force', action='store_true', help='实际执行删除（默认为预览模式）')
    cleanup_parser.set_defaults(func=cleanup_files)
    
    # config 命令
    config_parser = subparsers.add_parser('config', help='显示配置信息')
    config_parser.set_defaults(func=show_config)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # 执行对应的函数
    args.func(args)

if __name__ == "__main__":
    main()
